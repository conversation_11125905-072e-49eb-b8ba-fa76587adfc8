func configureFirebase() {
    // Configure Firebase
    FirebaseApp.configure()
    
    // Explicitly disable Analytics collection
    FirebaseConfiguration.shared.setAnalyticsCollectionEnabled(false)
    
    // Set up Firebase Messaging
    Messaging.messaging().delegate = self
    
    // Register for remote notifications
    UNUserNotificationCenter.current().delegate = self
    let authOptions: UNAuthorizationOptions = [.alert, .badge, .sound]
    UNUserNotificationCenter.current().requestAuthorization(
        options: authOptions,
        completionHandler: { _, _ in }
    )
    UIApplication.shared.registerForRemoteNotifications()
}
//
//  AppDelegate.swift
//  NXC EV Solutions
//
//  Created by <PERSON><PERSON><PERSON> on 15/07/21.
//

import UIKit
import IQKeyboardManagerSwift
import GoogleMaps
import UserNotifications
import SnapKit
import JGProgressHUD
import FirebaseMessaging
import Firebase
//import FirebaseAnalytics
import LanguageManager_iOS
import Starscream

// Comment: We've added Gujarati support directly to the LanguageManager-iOS library
// by modifying the Languages enum, so we no longer need this custom class.

@main
class AppDelegate: UIResponder, UIApplicationDelegate,UNUserNotificationCenterDelegate,MessagingDelegate {


    // MARK: - All IBOutlets
    var window: UIWindow?
    var storyboard:UIStoryboard = UIStoryboard()
    var navigationController:UINavigationController = UINavigationController()
    var progressView:JGProgressHUD = JGProgressHUD()
    static let shared = UIApplication.shared.delegate as! AppDelegate
    //    var progressView:JGProgressHUD = JGProgressHUD()

    var strLoginNo:String = String()
    var userProfileDetails:[String:Any] = [:]

    var isFromSplash:String = String()
    var isFromProfile:String = String()
    var isFromOTPScan:String = String()
    var isFromStart:String = String()
    var isFromOTPView:Bool = Bool()
    var isFromStartView:Bool = Bool()
    //    var socket: WebSocket!

    var strCharges:Double = Double()
    var strHMS:String = String()
    var strUsedUnit:Double = Double()
    var strAvailBalance:Double = Double()
    var strChargePoint:String = String()
    var strAccBalance:String = String()
    var strTxnDuration:String = String()

    var lat:Double = Double()
    var lng:Double = Double()
    let locationManager: CLLocationManager = CLLocationManager()
    //    var arrWalletHistory:[WalletHistory] = []

    var strOTP:String = String()
    var strVerifyOTP:String = String()
    var strSuccess:String = String()

    var strStopEvent:String = String()
    var mainCollHeight:CGFloat = CGFloat()

    var strFAQID:String = String()
    var strFAQIndex:String = String()

    var strConnectorID:String = String()
    var strConnectorStatus:String = String()
    var strTransactionID:String = String()

    //    var arrCType:[ConnectorType] = []
    var strStopStatus:String = String()
    //    var chargerDetails:[Charger] = []
    var isFromFilter:Bool = Bool()
    var strTableHeight:CGFloat = CGFloat()
    var strVehicleID:String = String()
    var strCSID:String = String()
    var tableIndexTag = 0

    var typeID:String = String()
    var nearMeList:[NearMeList] = []
    var filteredNearMeList:[NearMeList] = []

    var intPaymentTab:Int = Int()

    var isFromMenu = Bool()
    var strTypeID = String()
    var currentLanguage = "en"

    var strChargerType:String = "0"
    var strRatings:String = ""

    var isStartEvent = Bool()

    var transType = String()
    var transValue = String()

    //    var progressView:JGProgressHUD = JGProgressHUD()
    var viewBg = UIView()
    var viewProfile = UIView()
    var tableList = UITableView()

    var imgProfile = UIImageView()
    var lblUsername = UILabel()
    var lblEmailID = UILabel()

    var arrList:[String] = [String]()
    var intSelected:Int = 1
    var userDict:NSDictionary = NSDictionary()
    var payUserDict:NSDictionary = NSDictionary()

    var arrImages:NSArray = NSArray()
    var arrImagesGray:NSArray = NSArray()

    var isSlideInMenuPresented = false
    lazy var slideInMenuPadding: CGFloat = self.window!.frame.width * 0.30

    var profileDetails:[ProfileDetails] = []
//    var transType = String()
//    var transValue = String()
    var isConnectorDataFetched:Bool = false

    var strFavourite:String = ""
    var selectedIndex = Int()
    var romID = String()
    var replaceTagID = String()

    var socket: WebSocket!
    var isFromNDCP = Bool()

    var arrCType:[ConnectorType] = []
    var strAddressID:String = String()

    var isVehicleEdit:String = String()
    var isProfileEdit:String = String()

    var isNearMeListFetched = "0"
    var gameURL = String()

    var tabIndex:Int = 1
    var isMenuOpen = 0

    var isSwipeBack = 0
    var strLanguage = "1"
    var isFromScan = "0"


    // MARK: - Application Delegate
    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {

//        print("Configuration.baseURL:-",Configuration.baseURL)
//        print("Configuration.baseURL:-",Bundle.main.infoDictionary?["BASE_URL"])

        // Suppress Auto Layout constraint warnings
        UserDefaults.standard.setValue(false, forKey: "_UIConstraintBasedLayoutLogUnsatisfiable")

        storyboard = UIStoryboard(name: "Main", bundle: nil)
        navigationController = self.window?.rootViewController as! UINavigationController
        self.window?.rootViewController = navigationController
        self.window?.makeKeyAndVisible()

        IQKeyboardManager.shared.enable = true

        // Configure Firebase
        FirebaseApp.configure()

        // Explicitly disable Analytics collection
//        Analytics.setAnalyticsCollectionEnabled(false)

        // Set up Firebase Messaging
        Messaging.messaging().delegate = self
        GMSServices.provideAPIKey(Constants.Map_Key)
        //        GMSPlacesClient.provideAPIKey(Constants.Places_Key)
        //
        locationManager.delegate = self
        locationManager.requestWhenInUseAuthorization()
//        locationManager.requestAlwaysAuthorization()

        if #available(iOS 10.0, *) {
          // For iOS 10 display notification (sent via APNS)
          UNUserNotificationCenter.current().delegate = self

          let authOptions: UNAuthorizationOptions = [.alert, .sound]
          UNUserNotificationCenter.current().requestAuthorization(
            options: authOptions,
            completionHandler: {_, _ in })
        } else {
          let settings: UIUserNotificationSettings =
          UIUserNotificationSettings(types: [.alert, .sound], categories: nil)
          application.registerUserNotificationSettings(settings)
        }

        application.registerForRemoteNotifications()

        window!.overrideUserInterfaceStyle = .light

        if UserDefaults.standard.object(forKey: Constants.IS_ONBOARDING) == nil {
            UserDefaults.standard.set("0", forKey: Constants.IS_ONBOARDING)
        }

        if UserDefaults.standard.object(forKey: Constants.USER_ID) == nil {
            UserDefaults.standard.set("0", forKey: Constants.USER_ID)
        }

        if UserDefaults.standard.object(forKey: Constants.IS_LOGIN) == nil {
            UserDefaults.standard.set("0", forKey: Constants.IS_LOGIN)
        }

        if UserDefaults.standard.object(forKey: Constants.IS_OTP) == nil {
            UserDefaults.standard.set("0", forKey: Constants.IS_OTP)
        }

        if UserDefaults.standard.object(forKey: Constants.IS_PASSCODE) == nil {
            UserDefaults.standard.set("0", forKey: Constants.IS_PASSCODE)
        }

        if UserDefaults.standard.object(forKey: Constants.PIN) == nil {
            UserDefaults.standard.set("0", forKey: Constants.PIN)
        }

        if UserDefaults.standard.object(forKey: Constants.USER_PROFILE) == nil {
            UserDefaults.standard.set("0", forKey: Constants.USER_PROFILE)
        }

        if UserDefaults.standard.object(forKey: Constants.MOBILE_PIN) == nil {
            UserDefaults.standard.set("0", forKey: Constants.MOBILE_PIN)
        }

        if UserDefaults.standard.object(forKey: Constants.PHONE) == nil {
            UserDefaults.standard.set("0", forKey: Constants.PHONE)
        }

        if UserDefaults.standard.object(forKey: Constants.PROFILE_EDIT) == nil {
            UserDefaults.standard.set("0", forKey: Constants.PROFILE_EDIT)
        }

        if UserDefaults.standard.object(forKey: Constants.VEHICLE_EDIT) == nil {
            UserDefaults.standard.set("0", forKey: Constants.VEHICLE_EDIT)
        }

        if UserDefaults.standard.object(forKey: Constants.BASE_URL) == nil {
            UserDefaults.standard.set("0", forKey: Constants.BASE_URL)
        }

        if UserDefaults.standard.object(forKey: Constants.WEBSOCKET_URL) == nil {
            UserDefaults.standard.set("0", forKey: Constants.WEBSOCKET_URL)
        }

        if UserDefaults.standard.object(forKey: Constants.JWT_TOKEN) == nil {
            UserDefaults.standard.set("", forKey: Constants.JWT_TOKEN)
        }

        if UserDefaults.standard.object(forKey: Constants.IS_NDCP) == nil {
            UserDefaults.standard.set("0", forKey: Constants.IS_NDCP)
        }

        if UserDefaults.standard.object(forKey: Constants.IS_CHARGING) == nil {
            UserDefaults.standard.set("0", forKey: Constants.IS_CHARGING)
        }

        if UserDefaults.standard.object(forKey: Constants.IS_TXN_STOPPED) == nil {
            UserDefaults.standard.set("0", forKey: Constants.IS_TXN_STOPPED)
        }

        if UserDefaults.standard.object(forKey: Constants.LATITUDE) == nil {
            UserDefaults.standard.set("0", forKey: Constants.LATITUDE)
        }

        if UserDefaults.standard.object(forKey: Constants.LONGITUDE) == nil {
            UserDefaults.standard.set("0", forKey: Constants.LONGITUDE)
        }

        if UserDefaults.standard.object(forKey: Constants.ISCHARGING_DROPDOWN_CENTER) == nil {
            UserDefaults.standard.set("0", forKey: Constants.ISCHARGING_DROPDOWN_CENTER)
        }

        // Set default language only if it's the first launch
        if UserDefaults.standard.object(forKey: Constants.LANGUAGE) == nil {
            print("🌐 First launch - setting default language to English")
            UserDefaults.standard.set("1", forKey: Constants.LANGUAGE)
            // This will initialize the LanguageManager with English
            LanguageManager.shared.defaultLanguage = .en
        }

        // Register Gujarati language
        // We've added Gujarati to the Languages enum, so we can use it directly

        // Add observer for language changes
        NotificationCenter.default.addObserver(self,
                                              selector: #selector(languageChanged),
                                              name: NSNotification.Name("LANGUAGE_CHANGED"),
                                              object: nil)

        // Initialize language settings
        if let languageValue = UserDefaults.standard.string(forKey: Constants.LANGUAGE) {
            self.strLanguage = languageValue
            print("🌐 Setting language from UserDefaults at launch: \(languageValue)")

            // Set the language in LanguageManager and Bundle
            if languageValue == "1" {
                print("🇬🇧 Setting language to English at launch")
                // Force the language to English
                LanguageManager.shared.setLanguage(language: .en)
                Bundle.setLanguage("en")
                self.currentLanguage = "en"
            } else if languageValue == "2" {
                print("🇮🇳 Setting language to Gujarati at launch")
                // Force the language to Gujarati
                LanguageManager.shared.setLanguage(language: .gu)
                Bundle.setLanguage("gu")
                self.currentLanguage = "gu"

                // Force UI update
                NotificationCenter.default.post(name: NSNotification.Name("LANGUAGE_CHANGED"), object: nil)
            } else if languageValue == "3" {
                print("🇮🇳 Setting language to Hindi at launch")
                // Force the language to Hindi
                LanguageManager.shared.setLanguage(language: .hi)
                Bundle.setLanguage("hi")
                self.currentLanguage = "hi"

                // Force UI update
                NotificationCenter.default.post(name: NSNotification.Name("LANGUAGE_CHANGED"), object: nil)
            }
        } else {
            // Default to English if no language is set
            self.strLanguage = "1"
            self.currentLanguage = "en"
            LanguageManager.shared.setLanguage(language: .en)
            Bundle.setLanguage("en")
            UserDefaults.standard.set("1", forKey: Constants.LANGUAGE)
            print("✅ Set default language to English at launch")
        }

        // Override the LanguageManager's internal storage to ensure it uses our language
        // This is necessary because LanguageManager has its own UserDefaults keys
        if let languageValue = UserDefaults.standard.string(forKey: Constants.LANGUAGE) {
            if languageValue == "1" {
                UserDefaults.standard.set("en", forKey: "LanguageManagerSelectedLanguage")
            } else if languageValue == "2" {
                UserDefaults.standard.set("gu", forKey: "LanguageManagerSelectedLanguage")
            } else if languageValue == "3" {
                UserDefaults.standard.set("hi", forKey: "LanguageManagerSelectedLanguage")
            }
        }

        return true
    }

    func messaging(_ messaging: Messaging, didReceiveRegistrationToken fcmToken: String?) {

        Messaging.messaging().token { token, error in
            if let error = error {
                print("Error fetching FCM registration token: \(error)")
            } else if let token = token {
                print("Remote instance ID token: \(token)")
                UserDefaults.standard.set("\(token)", forKey: Constants.FCM_TOKEN)
            }
        }
    }


    func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
        Messaging.messaging().apnsToken = deviceToken
    }

    func userNotificationCenter(_ center: UNUserNotificationCenter, didReceive response: UNNotificationResponse, withCompletionHandler completionHandler: @escaping () -> Void) {
        print("didReceive:-",completionHandler)
        //        completionHandler([.alert, .sound])

    }

    func application(_ application: UIApplication, didReceiveRemoteNotification userInfo: [AnyHashable : Any], fetchCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void) {
        print("didReceiveRemoteNotification:-",completionHandler)
        //        completionHandler([.alert, .sound])

    }

    func userNotificationCenter(_ center: UNUserNotificationCenter, willPresent notification: UNNotification, withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {
        print("willPresent:-")
        completionHandler([.alert, .sound])
    }


    func applicationWillEnterForeground(_ application: UIApplication) {
        let appDelegate = UIApplication.shared.delegate as! AppDelegate
        if let topController = appDelegate.window!.visibleViewController() {
            print("The view controller you're looking at is: \(topController)")
            let vc = ChargingVC.instantiate(appStoryboard: .Charging)
            if topController == vc {
                var request = URLRequest(url: URL(string: Constants.WEBSOCKET_URL)!)
                request.timeoutInterval = 1
                AppDelegate.shared.socket = WebSocket(request: request)
                if AppDelegate.shared.socket.isConnected == true {
                    print("Websocket is already connected")
                } else {
                    print("Websocket connection established")
                    AppDelegate.shared.socket.delegate = vc
                    AppDelegate.shared.socket.connect()
                }
            }
        }
    }

    func applicationDidBecomeActive(_ application: UIApplication) {
        print("🔄 applicationDidBecomeActive called")

        // Restore saved language when app becomes active
        if let languageValue = UserDefaults.standard.string(forKey: Constants.LANGUAGE) {
            print("🌐 Restoring language from UserDefaults: \(languageValue)")
            self.strLanguage = languageValue

            // Set the language in LanguageManager and Bundle
            if languageValue == "1" {
                print("🇬🇧 Setting language to English")
                LanguageManager.shared.setLanguage(language: .en)
                Bundle.setLanguage("en")
                self.currentLanguage = "en"

                // Update LanguageManager's internal storage
                UserDefaults.standard.set("en", forKey: "LanguageManagerSelectedLanguage")
            } else if languageValue == "2" {
                print("🇮🇳 Setting language to Gujarati")
                LanguageManager.shared.setLanguage(language: .gu)
                Bundle.setLanguage("gu")
                self.currentLanguage = "gu"

                // Update LanguageManager's internal storage
                UserDefaults.standard.set("gu", forKey: "LanguageManagerSelectedLanguage")

                // Force UI update
                NotificationCenter.default.post(name: NSNotification.Name("LANGUAGE_CHANGED"), object: nil)
            } else if languageValue == "3" {
                print("🇮🇳 Setting language to Hindi")
                LanguageManager.shared.setLanguage(language: .hi)
                Bundle.setLanguage("hi")
                self.currentLanguage = "hi"

                // Update LanguageManager's internal storage
                UserDefaults.standard.set("hi", forKey: "LanguageManagerSelectedLanguage")

                // Force UI update
                NotificationCenter.default.post(name: NSNotification.Name("LANGUAGE_CHANGED"), object: nil)
            }
        } else {
            print("⚠️ No language found in UserDefaults")
        }
    }

    //MARK: - APIKey Logout
    func apiKeyLogout() {
        hideHUD()
        UserDefaults.standard.set("0", forKey: Constants.USER_ID)
        UserDefaults.standard.set("0", forKey: Constants.IS_LOGIN)
        UserDefaults.standard.set("0", forKey: Constants.IS_OTP)
        UserDefaults.standard.set("0", forKey: Constants.VEHICLE_EDIT)
        UserDefaults.standard.set("0", forKey: Constants.PROFILE_EDIT)
        UserDefaults.standard.set("0", forKey: Constants.IS_NDCP)
        UserDefaults.standard.set("", forKey: Constants.JWT_TOKEN)
        let vc = LoginVC.instantiate(appStoryboard: .PreLogin)
        self.navigationController.pushViewController(vc, animated: false)
        return
    }

    //MARK: - HUD Methods
    func showHUD() {
        progressView = JGProgressHUD(style: .dark)
        progressView.textLabel.text = "Please wait"
        progressView.show(in: window!)
    }

    func hideHUD() {
        progressView.dismiss()
    }

    func showHUDWithText(message:String) {
        progressView = JGProgressHUD(style: .dark)
        progressView.textLabel.text = message
        progressView.show(in: window!)
    }

    lazy var menuView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemGray5
        return view
    }()

    lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemBackground
        return view
    }()

    func OpenCloseDrawer() {
        UIView.animate(withDuration: 0.5, delay: 0, usingSpringWithDamping: 0.8, initialSpringVelocity: 0, options: .curveEaseInOut) {
            self.containerView.frame.origin.x = self.isSlideInMenuPresented ? 0 : self.containerView.frame.width - self.slideInMenuPadding
        } completion: { (finished) in
            print("Animation finished: \(finished)")
            self.isSlideInMenuPresented.toggle()
        }
    }

    // MARK: - Language Methods
    @objc func languageChanged() {
       // print("🔄 Language changed notification received")

        // Check if language setting exists in UserDefaults
        if let languageValue = UserDefaults.standard.string(forKey: Constants.LANGUAGE) {
        //    print("🌐 Current language from UserDefaults: \(languageValue)")

            // Update LanguageManager's internal storage to ensure it uses our language
            // This is necessary because LanguageManager has its own UserDefaults keys
            if languageValue == "1" {
                UserDefaults.standard.set("en", forKey: "LanguageManagerSelectedLanguage")
            } else if languageValue == "2" {
                UserDefaults.standard.set("gu", forKey: "LanguageManagerSelectedLanguage")
            } else if languageValue == "3" {
                UserDefaults.standard.set("hi", forKey: "LanguageManagerSelectedLanguage")
            }
        }

        // Force UI update for all windows
        for window in UIApplication.shared.windows {
          //  print("🪟 Refreshing window: \(window)")
            for view in window.subviews {
                view.removeFromSuperview()
                window.addSubview(view)
            }
        }

        // Force reload of view controllers
        if let rootViewController = UIApplication.shared.windows.first?.rootViewController {
        //    print("🎮 Refreshing root view controller: \(type(of: rootViewController))")
            refreshViewController(rootViewController)
        }
    }

    private func refreshViewController(_ viewController: UIViewController) {
        // Refresh child view controllers
        for childVC in viewController.children {
            refreshViewController(childVC)
        }

        // Refresh the view controller's view
        viewController.viewDidLayoutSubviews()

        // If it's a table view controller, reload the table
        if let tableVC = viewController as? UITableViewController {
            tableVC.tableView.reloadData()
        }

        // If it's a collection view controller, reload the collection
        if let collectionVC = viewController as? UICollectionViewController {
            collectionVC.collectionView.reloadData()
        }
    }
}


extension String {
    func localized(_ lang:String) ->String {
        let path = Bundle.main.path(forResource: lang, ofType: "lproj")
        let bundle = Bundle(path: path!)
        return NSLocalizedString(self, tableName: nil, bundle: bundle!, value: "", comment: "")
    }
}


extension Bundle {
    static func swizzleLocalization() {
        let orginalSelector = #selector(localizedString(forKey:value:table:))
        guard let orginalMethod = class_getInstanceMethod(self, orginalSelector) else { return }

        let mySelector = #selector(myLocaLizedString(forKey:value:table:))
        guard let myMethod = class_getInstanceMethod(self, mySelector) else { return }

        if class_addMethod(self, orginalSelector, method_getImplementation(myMethod), method_getTypeEncoding(myMethod)) {
            class_replaceMethod(self, mySelector, method_getImplementation(orginalMethod), method_getTypeEncoding(orginalMethod))
        } else {
            method_exchangeImplementations(orginalMethod, myMethod)
        }
    }

    @objc private func myLocaLizedString(forKey key: String,value: String?, table: String?) -> String {
        guard let appDelegate = UIApplication.shared.delegate as? AppDelegate,
              let bundlePath = Bundle.main.path(forResource: appDelegate.currentLanguage, ofType: "lproj"),
              let bundle = Bundle(path: bundlePath) else {
                  return Bundle.main.myLocaLizedString(forKey: key, value: value, table: table)
              }
        return bundle.myLocaLizedString(forKey: key, value: value, table: table)
    }
}

extension AppDelegate: CLLocationManagerDelegate {

    func locationManager(_ manager: CLLocationManager, didChangeAuthorization status: CLAuthorizationStatus) {
        switch status {
        case .restricted:
            print("Location access was restricted.")
        case .denied:
            print("User denied access to location.")
            // Display the map using the default location.
        //            mapView.isHidden = false
        case .notDetermined:
            print("Location status not determined.")
        case .authorizedAlways:
            locationManager.startUpdatingLocation()
        case .authorizedWhenInUse:
            locationManager.startUpdatingLocation()
            print("Location status is OK.")
        @unknown default:
            print("You chose other option!")
        }
    }

    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {

        let location: CLLocation = locations.last!

        AppDelegate.shared.lat = Double(location.coordinate.latitude)
        AppDelegate.shared.lng = Double(location.coordinate.longitude)

        UserDefaults.standard.setValue(AppDelegate.shared.lat, forKey: Constants.LATITUDE)
        UserDefaults.standard.setValue(AppDelegate.shared.lng, forKey: Constants.LONGITUDE)

//        print("AppDelegate lat:-",AppDelegate.shared.lat)
//        print("AppDelegate lng:-",AppDelegate.shared.lng)

        let doubleLat = String(format: "%.2f", ceil(AppDelegate.shared.lat*100)/100) // "3.15"
        let doubleLng = String(format: "%.2f", ceil(AppDelegate.shared.lng*100)/100) // "3.15"

        locationManager.stopUpdatingLocation()
    }

    func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
        print(error)
    }
}



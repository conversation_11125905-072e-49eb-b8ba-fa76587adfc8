//
//  AppStoryboards.swift
//  GopiNew
//
//  Created by <PERSON><PERSON><PERSON> on 02/11/20.
//

import Foundation
import UIKit
@available(iOS 13.0, *)
enum Appstoryboard: String {
    
    case PreLogin
    case Home
    case Help
    case Account
    case Transaction
    case Receipt
    case Explore
    case Charge
    case MyTask
    case Profile
    case Vehicle
    case Charger
    case Wallet
    case Language
    case Complain
    case RFID
    case Charging
    
    var instance: UIStoryboard {
        return UIStoryboard(name: self.rawValue, bundle: Bundle.main)
    }
    
    func initialViewController() -> UIViewController? {
        return instance.instantiateInitialViewController()
    }
    
    // Sample usage:
    // let shoppingController = AppStoryboards.Shopping.viewController(viewControllerClass: ShoppingViewController.self)
    func viewController<T: UIViewController>(viewControllerClass: T.Type) -> T {
        let storyboardID = (viewControllerClass as UIViewController.Type).storyboardID
        return instance.instantiateViewController(withIdentifier: storyboardID) as! T
    }
}
@available(iOS 13.0, *)
extension UIViewController {
    // The Storyboard ID for the initial View Controller has to be defined with the same name as the view controller name
    class var storyboardID : String {
        return "\(self)"
    }
    
    static func instantiate(appStoryboard: Appstoryboard) -> Self {
        return appStoryboard.viewController(viewControllerClass: self)
    }
}

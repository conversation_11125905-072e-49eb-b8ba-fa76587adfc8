<svg width="560" height="560" viewBox="0 0 560 560" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M280 510C418.071 510 530 398.071 530 260C530 121.929 418.071 10 280 10C141.929 10 30 121.929 30 260C30 398.071 141.929 510 280 510Z" fill="url(#paint0_linear)"/>
<path d="M368.542 166.25L366.875 166.562L311.25 188.125L248.75 166.25L190 186.042C187.812 186.771 186.25 188.646 186.25 191.042V348.542C186.25 351.458 188.542 353.75 191.458 353.75L193.125 353.437L248.75 331.875L311.25 353.75L370 333.958C372.187 333.229 373.75 331.354 373.75 328.958V171.458C373.75 168.542 371.458 166.25 368.542 166.25ZM259.167 191.979L300.833 206.563V328.021L259.167 313.437V191.979ZM207.083 202.292L238.333 191.771V313.646L207.083 325.729V202.292ZM352.917 317.708L321.667 328.229V206.458L352.917 194.375V317.708Z" fill="white"/>
</g>
<defs>
<filter id="filter0_d" x="0" y="0" width="560" height="560" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="20"/>
<feGaussianBlur stdDeviation="15"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0156863 0 0 0 0 0.494118 0 0 0 0 0.427451 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<linearGradient id="paint0_linear" x1="280" y1="10" x2="280" y2="510" gradientUnits="userSpaceOnUse">
<stop stop-color="#00917C"/>
<stop offset="1" stop-color="#047E6D"/>
</linearGradient>
</defs>
</svg>

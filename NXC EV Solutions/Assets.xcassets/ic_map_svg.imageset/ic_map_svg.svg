<svg width="160" height="160" viewBox="0 0 160 160" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M80 110C107.614 110 130 87.6142 130 60C130 32.3858 107.614 10 80 10C52.3858 10 30 32.3858 30 60C30 87.6142 52.3858 110 80 110Z" fill="url(#paint0_linear)"/>
<path d="M97.7083 41.25L97.375 41.3125L86.25 45.625L73.75 41.25L62 45.2083C61.5625 45.3542 61.25 45.7292 61.25 46.2083V77.7083C61.25 78.2917 61.7083 78.75 62.2917 78.75L62.625 78.6875L73.75 74.375L86.25 78.75L98 74.7917C98.4375 74.6458 98.75 74.2708 98.75 73.7917V42.2917C98.75 41.7083 98.2917 41.25 97.7083 41.25ZM75.8333 46.3958L84.1667 49.3125V73.6042L75.8333 70.6875V46.3958ZM65.4167 48.4583L71.6667 46.3542V70.7292L65.4167 73.1458V48.4583ZM94.5833 71.5417L88.3333 73.6458V49.2917L94.5833 46.875V71.5417Z" fill="white"/>
</g>
<defs>
<filter id="filter0_d" x="0" y="0" width="160" height="160" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="20"/>
<feGaussianBlur stdDeviation="15"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0156863 0 0 0 0 0.494118 0 0 0 0 0.427451 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<linearGradient id="paint0_linear" x1="80" y1="10" x2="80" y2="110" gradientUnits="userSpaceOnUse">
<stop stop-color="#00917C"/>
<stop offset="1" stop-color="#047E6D"/>
</linearGradient>
</defs>
</svg>

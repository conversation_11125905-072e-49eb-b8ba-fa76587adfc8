//
//  BundleExtension.swift
//  iCharge
//
//  Created by Developer on 27/01/21.
//  Copyright © 2021 nxcControls. All rights reserved.
//

import Foundation
import UIKit
import ObjectiveC

// Key for associated object
private var bundleKey: UInt8 = 0

class AnyLanguageBundle: Bundle, @unchecked Sendable {
    override func localizedString(forKey key: String,
                                  value: String?,
                                  table tableName: String?) -> String {
        guard let path = objc_getAssociatedObject(self, &bundleKey) as? String,
              let bundle = Bundle(path: path) else {
            print("⚠️ Failed to get language bundle path")
            return super.localizedString(forKey: key, value: value, table: tableName)
        }

        // For debugging
     //   print("🔤 Loading localization from path: \(path)")
     //   print("🔑 Key: \(key), Table: \(tableName ?? "nil")")

        // Get the localized string from the language bundle
        let localizedString = bundle.localizedString(forKey: key, value: value, table: tableName)

        // If the localized string is the same as the key and it's an Interface Builder element,
        // try to find a translation in Localizable.strings
        if localizedString == key && (key.contains(".text") || key.contains(".normalTitle") || key.contains(".placeholder")) {
            // Extract the actual text from the IB element ID
            let components = key.components(separatedBy: ".")
            if components.count > 1 {
                // Get the part before the dot (the ID)
                let id = components[0]

                // Try to find a translation for the ID in Localizable.strings
                let localizedID = bundle.localizedString(forKey: id, value: nil, table: "Localizable")
                if localizedID != id {
                 //   print("📝 Found translation in Localizable.strings: \(localizedID)")
                    return localizedID
                }

                // If we have a text property, try to find a translation for the value
                if let value = value, !value.isEmpty {
                    let localizedValue = bundle.localizedString(forKey: value, value: nil, table: "Localizable")
                    if localizedValue != value {
                 //       print("📝 Found translation for value in Localizable.strings: \(localizedValue)")
                        return localizedValue
                    }
                }
            }
        }

       // print("📝 Localized string: \(localizedString)")
        return localizedString
    }
}

extension Bundle {
    class func setLanguage(_ language: String) {
        // Print for debugging
       // print("🌐 Setting language to: \(language)")

        // Check if the language resource exists
        if let resourcePath = Bundle.main.path(forResource: language, ofType: "lproj") {
        //    print("✅ Found resource path for \(language): \(resourcePath)")

            // List all files in the language directory for debugging
            do {
                let fileManager = FileManager.default
                let files = try fileManager.contentsOfDirectory(atPath: resourcePath)
             //   print("📁 Files in \(language).lproj: \(files)")
            } catch {
                print("❌ Error listing files in \(language).lproj: \(error)")
            }
        } else {
            print("⚠️ Warning: No resource path found for language: \(language)")

            // List all available .lproj directories
            do {
                let fileManager = FileManager.default
                if let bundlePath = Bundle.main.resourcePath {
                    let contents = try fileManager.contentsOfDirectory(atPath: bundlePath)
                    let lprojDirs = contents.filter { $0.hasSuffix(".lproj") }
                 //   print("📚 Available .lproj directories: \(lprojDirs)")
                }
            } catch {
                print("❌ Error listing .lproj directories: \(error)")
            }
        }

        // Swizzle the Bundle class
        object_setClass(Bundle.main, AnyLanguageBundle.self)

        // Set the language bundle path
        objc_setAssociatedObject(Bundle.main, &bundleKey, Bundle.main.path(forResource: language, ofType: "lproj"), .OBJC_ASSOCIATION_RETAIN_NONATOMIC)

        // Force UI update
        DispatchQueue.main.async {
            // Post notification for language change
            NotificationCenter.default.post(name: NSNotification.Name("LANGUAGE_CHANGED"), object: nil)
        }
    }
}

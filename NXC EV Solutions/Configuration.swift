//
//  Configuration.swift
//  Configuration
//
//  Created by Dev<PERSON>per on 25/09/21.
//

import Foundation

enum Configuration: String {

    // MARK: - Configurations

//    case staging
//    case production
//    case release
    
    case debugLive
    case debugLocal
    case release

    // MARK: - Current Configuration

    static let current: Configuration = {
        guard let rawValue = Bundle.main.infoDictionary?["Configuration"] as? String else {
            fatalError("No Configuration Found")
        }

        guard let configuration = Configuration(rawValue: rawValue.lowercased()) else {
            fatalError("Invalid Configuration")
        }

        return configuration
    }()

    // MARK: - Base URL

    static var baseURL: URL {
        switch current {
        case .debugLocal:
            print("https://staging.cocoacasts.com")
            return URL(string: "https://staging.cocoacasts.com")!
        case .debugLive, .release:
            print("https://cocoacasts.com")
            return URL(string: "https://cocoacasts.com")!
        }
    }

}

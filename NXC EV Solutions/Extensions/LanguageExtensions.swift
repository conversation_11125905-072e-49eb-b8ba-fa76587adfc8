import LanguageManager_iOS

// Extend the Languages enum to add Gujarati support
extension Languages {
    static let gu = Languages(rawValue: "gu")
}

// Make sure the extension is properly accessible
// Add this if needed to ensure the extension is loaded
@_cdecl("forceLoadLanguageExtensions")
func forceLoadLanguageExtensions() {
    // This function is just to ensure the extension is loaded
    // It doesn't need to do anything
}

// Call this function in AppDelegate to ensure the extension is loaded
func registerGujaratiLanguage() {
    // Force the compiler to include our extension
    let _ = Languages(rawValue: "gu")
    print("Gujarati language registered with raw value: \(Languages(rawValue: "gu")?.rawValue ?? "nil")")
}

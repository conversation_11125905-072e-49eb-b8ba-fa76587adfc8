//
//  SOTabBarSetting.swift
//  Pods-SOTabBar_Example
//
//  Created by <PERSON><PERSON> on 11/01/2020.
//

import Foundation
import UIKit

// Here you can customize the tab bar to meet your neededs
public struct SOTabBarSetting {
    
    public static var tabBarHeight: CGFloat = 66
    public static var tabBarTintColor: UIColor = UIColor(red: 250/255, green: 51/255, blue: 24/255, alpha: 1)
    public static var tabBarBackground: UIColor = UIColor.white
    public static var tabBarCircleSize = CGSize(width: 65, height: 65)
    public static var tabBarSizeImage: CGFloat = 25
    public static var tabBarShadowColor = UIColor.lightGray.cgColor
    public static var tabBarSizeSelectedImage: CGFloat = 20
    public static var tabBarAnimationDurationTime: Double = 0.4
    
}

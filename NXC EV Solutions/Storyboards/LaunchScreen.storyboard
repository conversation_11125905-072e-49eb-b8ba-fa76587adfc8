<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="17701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="01J-lp-oVM">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17703"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="EHf-IW-A2E">
            <objects>
                <viewController id="01J-lp-oVM" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Ze5-6b-2t3">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="uaf-nh-XOm">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </view>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="3bz-zZ-U45">
                                <rect key="frame" x="9.5" y="183.5" width="356" height="300"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_nxc_ev" translatesAutoresizingMaskIntoConstraints="NO" id="DdD-SQ-DVr">
                                        <rect key="frame" x="0.0" y="0.0" width="356" height="300"/>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="300" id="hPn-Pc-Sko"/>
                                        </constraints>
                                    </imageView>
                                </subviews>
                            </stackView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="6Tk-OE-BBY"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="uaf-nh-XOm" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" id="4Zn-DB-FZY"/>
                            <constraint firstItem="3bz-zZ-U45" firstAttribute="width" secondItem="6Tk-OE-BBY" secondAttribute="width" multiplier="0.95" id="B3h-bp-6ox"/>
                            <constraint firstItem="uaf-nh-XOm" firstAttribute="bottom" secondItem="6Tk-OE-BBY" secondAttribute="bottom" id="GkW-hc-3eL"/>
                            <constraint firstItem="uaf-nh-XOm" firstAttribute="trailing" secondItem="6Tk-OE-BBY" secondAttribute="trailing" id="U2o-tA-S9K"/>
                            <constraint firstItem="uaf-nh-XOm" firstAttribute="top" secondItem="6Tk-OE-BBY" secondAttribute="top" id="VBd-vR-FSt"/>
                            <constraint firstItem="3bz-zZ-U45" firstAttribute="centerY" secondItem="Ze5-6b-2t3" secondAttribute="centerY" id="ojH-AD-i78"/>
                            <constraint firstItem="3bz-zZ-U45" firstAttribute="centerX" secondItem="Ze5-6b-2t3" secondAttribute="centerX" id="tRX-2d-rjs"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="iYj-Kq-Ea1" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="93" y="316"/>
        </scene>
    </scenes>
    <resources>
        <image name="ic_nxc_ev" width="512" height="201"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>

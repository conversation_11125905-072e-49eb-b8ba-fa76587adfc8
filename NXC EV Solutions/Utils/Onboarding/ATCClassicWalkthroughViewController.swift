//
//  ATCClassicWalkthroughViewController.swift
//  DashboardApp
//
//  Created by <PERSON><PERSON><PERSON> on 8/13/18.
//  Copyright © 2018 Instamobile. All rights reserved.
//

import UIKit

class ATCClassicWalkthroughViewController: UIViewController {
    @IBOutlet var containerView: UIView!
    @IBOutlet var imageContainerView: UIView!
    @IBOutlet var imageView: UIImageView!
    @IBOutlet var titleLabel: UILabel!
    @IBOutlet var subtitleLabel: UILabel!
    
    let model: ATCWalkthroughModel
    
    init(model: ATCWalkthroughModel,
         nibName nibNameOrNil: String?,
         bundle nibBundleOrNil: Bundle?) {
        self.model = model
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        imageView.image = UIImage(named: model.icon)
        imageView.clipsToBounds = true
        imageContainerView.backgroundColor = .white
        
        titleLabel.text = model.title
        
        if UIDevice.current.userInterfaceIdiom == .phone {
            titleLabel.font = UIFont.boldSystemFont(ofSize: 20.0)
        } else {
            titleLabel.font = UIFont.boldSystemFont(ofSize: 24.0)
        }
        
        titleLabel.textColor = Constants.primaryColor
        subtitleLabel.attributedText = NSAttributedString(string: model.subtitle)
        
        if UIDevice.current.userInterfaceIdiom == .phone {
            subtitleLabel.font = UIFont.boldSystemFont(ofSize: 16.0)
        } else {
            subtitleLabel.font = UIFont.boldSystemFont(ofSize: 20.0)
        }
                
        subtitleLabel.textColor = Constants.primaryColor
        containerView.backgroundColor = .white
    }
}

//
//  ATCWalkthroughViewController.swift
//  DashboardApp
//
//  Created by <PERSON><PERSON><PERSON> on 8/13/18.
//  Copyright © 2018 Instamobile. All rights reserved.
//

import UIKit

protocol ATCWalkthroughViewControllerDelegate: class {
    func walkthroughViewControllerDidFinishFlow(_ vc: ATCWalkthroughViewController)
}

class ATCWalkthroughViewController: UIViewController, UIPageViewControllerDataSource, UIPageViewControllerDelegate {

    
    // MARK: - All IBOutlets
    @IBOutlet var pageControl: UIPageControl!
    @IBOutlet weak var skipButton: UIButton!
    @IBOutlet weak var nextButton: UIButton!
    @IBOutlet weak var startButton: UIButton!
    
    weak var delegate: ATCWalkthroughViewControllerDelegate?
    
    let viewControllers: [UIViewController]
    var pageIndex = 0
    let pageController: UIPageViewController
    let fakeVC: UIViewController
    
    
    init(nibName nibNameOrNil: String?,
         bundle nibBundleOrNil: Bundle?,
         viewControllers: [UIViewController]) {
        self.viewControllers = viewControllers
        self.pageController = UIPageViewController(transitionStyle: .scroll, navigationOrientation: .horizontal, options: nil)
        self.fakeVC = UIViewController()
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
        self.pageController.dataSource = self
        self.pageController.delegate = self
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    
    // MARK: - View LifeCycle
    override func viewDidLoad() {
        pageController.setViewControllers([viewControllers[0]], direction: .forward, animated: true, completion: nil)
        self.addChildViewControllerWithView(pageController)
        pageControl.numberOfPages = viewControllers.count
        self.view.bringSubviewToFront(pageControl)
        self.view.bringSubviewToFront(skipButton)
        self.view.bringSubviewToFront(startButton)
        self.view.bringSubviewToFront(nextButton)

        skipButton.addTarget(self, action: #selector(handleSkip), for: .touchUpInside)
        super.viewDidLoad()
        
        startButton.isHidden = true
    }
    
    override func viewDidLayoutSubviews() {
        nextButton.maskClipCorner(cornerRadius: Int(nextButton.frame.height/2))
        startButton.maskClipCorner(cornerRadius: Int(nextButton.frame.height/2))
    }
    
    @objc func handleSkip() {
        //print("Clicked on Skip Button")
        UserDefaults.standard.set("1", forKey: Constants.IS_ONBOARDING)
        self.removeChildViewController(self.pageController)
        self.delegate?.walkthroughViewControllerDidFinishFlow(self)
    }
    
    // MARK: - Button Actions
    @IBAction func nextAction(_ sender: UIButton) {
        self.pageController.goToNextPage()
        if let lastPushedVC = self.pageController.viewControllers?.last {
            if let index = index(of: lastPushedVC) {
                pageControl.currentPage = index
//                print("Current Page index:- ", index)
                if index == 2 {
                    self.skipButton.isHidden = true
                    self.startButton.isHidden = false
                    self.nextButton.isHidden = true
                } else {
                    self.skipButton.isHidden = false
                    self.startButton.isHidden = true
                    self.nextButton.isHidden = false
                }
            } else {
                
            }
        }
    }
    
    @IBAction func startAction(_ sender: UIButton) {
        UserDefaults.standard.set("1", forKey: Constants.IS_ONBOARDING)
        self.removeChildViewController(self.pageController)
        self.delegate?.walkthroughViewControllerDidFinishFlow(self)
    }

        
    // MARK: - PageView Controller
    public func pageViewController(_ pageViewController: UIPageViewController, viewControllerBefore viewController: UIViewController) -> UIViewController? {
        print("viewControllerBefore")
        if let index = self.index(of: viewController) {
            if index == 0 {
                return nil
            }
            return viewControllers[index - 1]
        }
        return nil
    }
    
    public func pageViewController(_ pageViewController: UIPageViewController, viewControllerAfter viewController: UIViewController) -> UIViewController? {
        print("viewControllerAfter")
        if let index = self.index(of: viewController) {
            if index + 1 >= viewControllers.count {
                return fakeVC
            }
            return viewControllers[index + 1]
        }
        return nil
    }
    
    public func pageViewController(_ pageViewController: UIPageViewController, didFinishAnimating finished: Bool, previousViewControllers: [UIViewController], transitionCompleted completed: Bool) {
        print("didFinishAnimating")
        if !completed {
            return
        }
        if let lastPushedVC = pageViewController.viewControllers?.last {
            if let index = index(of: lastPushedVC) {
                pageControl.currentPage = index
//                print("Current Page index:- ", index)
                if index == 2 {
                    self.skipButton.isHidden = true
                    self.startButton.isHidden = false
                    self.nextButton.isHidden = true
                } else {
                    self.skipButton.isHidden = false
                    self.startButton.isHidden = true
                    self.nextButton.isHidden = false
                }
            } else {
                
            }
        }
    }
    
    public func pageViewController(_ pageViewController: UIPageViewController, willTransitionTo pendingViewControllers: [UIViewController]) {
        print("willTransitionTo")
        if pendingViewControllers.first == self.fakeVC {
            self.removeChildViewController(self.pageController)
            self.delegate?.walkthroughViewControllerDidFinishFlow(self)
        }
    }
    
    private func index(of viewController: UIViewController) -> Int? {
        print("index")
        for (index, vc) in viewControllers.enumerated() {
            if viewController == vc {
                return index
            }
        }
        return nil
    }
}
extension UIPageViewController {
    func goToNextPage(animated: Bool = true, completion: ((Bool) -> Void)? = nil) {
        if let currentViewController = viewControllers?[0] {
            if let nextPage = dataSource?.pageViewController(self, viewControllerAfter: currentViewController) {
                setViewControllers([nextPage], direction: .forward, animated: animated, completion: completion)
            }
        }
    }
    
    func goToNextPage1(animated: Bool = true, completion: ((Bool) -> Void)? = nil) {
        if let currentViewController = viewControllers?[0] {
            
        }
    }

    func goToPreviousPage(animated: Bool = true, completion: ((Bool) -> Void)? = nil) {
        if let currentViewController = viewControllers?[0] {
            if let previousPage = dataSource?.pageViewController(self, viewControllerBefore: currentViewController){
                setViewControllers([previousPage], direction: .reverse, animated: true, completion: completion)
            }
        }
    }
}

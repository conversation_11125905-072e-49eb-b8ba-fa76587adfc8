<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="19162" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="19144"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="ATCWalkthroughViewController" customModule="NXC_EV_Solutions" customModuleProvider="target">
            <connections>
                <outlet property="nextButton" destination="MSX-RA-whx" id="E1q-1o-hfP"/>
                <outlet property="pageControl" destination="15L-4h-hYT" id="InI-Mt-Mz8"/>
                <outlet property="skipButton" destination="fhv-xL-xB0" id="POC-ux-a9p"/>
                <outlet property="startButton" destination="Enu-SO-EnL" id="aRQ-bP-vR0"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <pageControl opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" numberOfPages="3" translatesAutoresizingMaskIntoConstraints="NO" id="15L-4h-hYT">
                    <rect key="frame" x="110" y="561" width="155.5" height="26"/>
                    <color key="pageIndicatorTintColor" systemColor="systemGray4Color"/>
                    <color key="currentPageIndicatorTintColor" name="Primary"/>
                </pageControl>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="fhv-xL-xB0">
                    <rect key="frame" x="16" y="615" width="44" height="40"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="44" id="HCd-Vj-Ggv"/>
                        <constraint firstAttribute="height" constant="40" id="R4B-z3-eKo"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="20"/>
                    <state key="normal" title="Skip">
                        <color key="titleColor" name="PrimarySelection"/>
                    </state>
                </button>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="MSX-RA-whx">
                    <rect key="frame" x="259" y="615" width="100" height="40"/>
                    <color key="backgroundColor" name="Primary"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="100" id="LPZ-o1-7R6"/>
                        <constraint firstAttribute="height" constant="40" id="sOT-8d-mmb"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="20"/>
                    <state key="normal" title="Next">
                        <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <connections>
                        <action selector="nextAction:" destination="-1" eventType="touchUpInside" id="Es8-wA-H83"/>
                    </connections>
                </button>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Enu-SO-EnL">
                    <rect key="frame" x="137.5" y="615" width="100" height="40"/>
                    <color key="backgroundColor" name="Primary"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="40" id="mXT-U5-x8l"/>
                        <constraint firstAttribute="width" constant="100" id="tkF-kz-pM3"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="20"/>
                    <state key="normal" title="Start">
                        <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <connections>
                        <action selector="startAction:" destination="-1" eventType="touchUpInside" id="g5F-02-Al4"/>
                    </connections>
                </button>
            </subviews>
            <viewLayoutGuide key="safeArea" id="fnl-2z-Ty3"/>
            <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
            <constraints>
                <constraint firstItem="Enu-SO-EnL" firstAttribute="centerX" secondItem="i5M-Pr-FkT" secondAttribute="centerX" id="3Pm-PR-inS"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="MSX-RA-whx" secondAttribute="trailing" constant="16" id="6rG-k2-ewX"/>
                <constraint firstItem="MSX-RA-whx" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="Enu-SO-EnL" secondAttribute="trailing" constant="8" id="Hcy-SY-OZ2"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="bottom" secondItem="15L-4h-hYT" secondAttribute="bottom" constant="80" id="ICv-uv-zBE"/>
                <constraint firstItem="fhv-xL-xB0" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" constant="16" id="Pv1-Pi-sAg"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="bottom" secondItem="fhv-xL-xB0" secondAttribute="bottom" constant="12" id="oYK-ei-aNl"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="bottom" secondItem="Enu-SO-EnL" secondAttribute="bottom" constant="12" id="wB1-qN-ONz"/>
                <constraint firstItem="15L-4h-hYT" firstAttribute="centerX" secondItem="fnl-2z-Ty3" secondAttribute="centerX" id="yOu-mT-LsU"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="bottom" secondItem="MSX-RA-whx" secondAttribute="bottom" constant="12" id="zfC-Dl-5I2"/>
            </constraints>
            <point key="canvasLocation" x="136.80000000000001" y="152.47376311844079"/>
        </view>
    </objects>
    <resources>
        <namedColor name="Primary">
            <color red="0.016000000759959221" green="0.49399998784065247" blue="0.42699998617172241" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="PrimarySelection">
            <color red="0.59999999999999998" green="0.59999999999999998" blue="0.59999999999999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="systemGray4Color">
            <color red="0.81960784313725488" green="0.81960784313725488" blue="0.83921568627450982" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>

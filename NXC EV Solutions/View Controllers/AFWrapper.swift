//
//  AFWrapper.swift
//  AFWrapper
//
//  Created by <PERSON><PERSON><PERSON> on 31/08/21.
//

//import Foundation
//import UIKit
//import Alamofire
//import SwiftyJSON


//class AFWrapper: NSObject {
////    class func requestGETURL(_ strURL: String, success:@escaping (JSON) -> Void, failure:@escaping (Error) -> Void) {
//
//
//    class func requestGETURL(_ strURL: String, success:@escaping ([String : Any]) -&gt; Void, failure:@escaping (Error) -&gt; Void) {
//
//        AF.request(strURL).responseJSON { (responseObject) -&gt; Void in
//
//            print(responseObject)
//
////            if responseObject.result.isSuccess {
////                let resJson = JSON(responseObject.result.value!)
////                success(resJson)
////            }
////            if responseObject.result.isFailure {
////                let error : Error = responseObject.result.error!
////                failure(error)
////            }
//
//            switch responseObject.result {
//
//            case .success(let value):
//                print("Alamo value: \(value)")
//                let resJson = JSON(value)
//                success(resJson)
//                break
//            case .failure(let error):
//                print("Alamo error: \(error)")
//                let error : Error = error
//                failure(error)
//                break
//            }
//        }
//    }
//
//    class func requestPOSTURL(_ strURL : String, params : [String : AnyObject]?, headers : HTTPHeaders?, success:@escaping (JSON) -> Void, failure:@escaping (Error) -> Void){
//
//        AF.request(strURL, method: .post, parameters: params, encoding: JSONEncoding.default, headers: headers).responseJSON { (responseObject) -> Void in
//
//            print(responseObject)
//
////            if responseObject.result.isSuccess {
////                let resJson = JSON(responseObject.result.value!)
////                success(resJson)
////            }
////            if responseObject.result.isFailure {
////                let error : Error = responseObject.result.error!
////                failure(error)
////            }
//            switch responseObject.result {
//
//            case .success(let value):
//                print("Alamo value: \(value)")
//                let resJson = JSON(value)
//                success(resJson)
//                break
//            case .failure(let error):
//                print("Alamo error: \(error)")
//                let error : Error = error
//                failure(error)
//                break
//            }
//        }
//    }
//}

//
//  AboutUsVC.swift
//  AboutUsVC
//
//  Created by <PERSON><PERSON><PERSON> on 29/09/21.
//

import UIKit
import WebKit
import Alamofire
class AboutUsVC: UIViewController {

    
    // MARK: - IBOutlets
    @IBOutlet weak var backButton: UIButton!
    @IBOutlet weak var webDetails: WKWebView!
    
    
    // MARK: - View LifeCycle Methods
    override func viewDidLoad() {
        super.viewDidLoad()

        webDetails.navigationDelegate = self
        getAboutUsURL()
    }
    
    override func viewDidAppear(_ animated: Bool) {
        AppDelegate.shared.isSwipeBack = 1
        navigationController?.interactivePopGestureRecognizer?.delegate = self
        navigationController?.interactivePopGestureRecognizer?.isEnabled = true
    }
    
    override func viewDidLayoutSubviews() {
        backButton.addBorder(color: Constants.backArrowBorderColor!, width: Int(1))
        backButton.maskClipCorner(cornerRadius: 10)
    }

    // MARK: - Button Actions
    @IBAction func backButtonTapped(_ sender: UIButton) {
        navigationController?.popViewController(animated: true)
    }
    
    
    // MARK: - WebService
    func getAboutUsURL() {
        AppDelegate.shared.showHUD()
        let url = Constants.BASE_URL + API.ABOUT_US
        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]
        AF.request(url, method: .get, parameters: nil, encoding:  URLEncoding.default, headers: headers).responseJSON { response in
            print(response)
            AppDelegate.shared.hideHUD()
            switch response.result {
            case .success:
                
                let JSON = response.value as! NSDictionary
                print(JSON)
                
                if "\(JSON["code"]!)" == "200" && "\(JSON["status"]!)" == "1" {
                    self.webDetails.load(URLRequest(url: URL(string: "\(JSON["data"]!)")!))
                    AppDelegate.shared.showHUD()
                } else {
                    AppDelegate.shared.apiKeyLogout()
                }
                break
            case .failure(let error):
                print(error)
                AppDelegate.shared.hideHUD()
            }
        }
    }
}
extension AboutUsVC: WKNavigationDelegate {
    func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        AppDelegate.shared.hideHUD()
    }
}
extension AboutUsVC:UIGestureRecognizerDelegate {
    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldBeRequiredToFailBy otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        return true
    }
}

//
//  AddComplainVC.swift
//  AddComplainVC
//
//  Created by Developer on 17/08/21.
//

import UIKit
import DropDown
import Alamofire

class AddComplainVC: UIViewController {

    // MARK: - IBOutlets
    @IBOutlet weak var lblComplainType: UILabel!
    @IBOutlet weak var lblTransaction: UILabel!
    @IBOutlet weak var txtViewDetails: UITextView!
    @IBOutlet weak var btnSubmit: UIButton!
    @IBOutlet weak var btnComplainType: UIButton!
    @IBOutlet weak var btnTransaction: UIButton!

    @IBOutlet weak var btnBack: UIButton!

    var complainTypeDropDown = DropDown()
    var transactionIDDropDown = DropDown()

    var arrComplain:[String] = []
    var paramAddComplain:[String:Any] = [:]
    var paramComplainFilter:[String:Any] = [:]

    var strTransID = String()
    var strTypeID = String()
    var strDesc = String()

    var complainList:[ComplainType] = []
    var transactionList:[LastTransaction] = []
    var complaintFilterList:[ComplaintFilterType] = []


    // MARK: - View LifeCycle Methods
    override func viewDidLoad() {
        super.viewDidLoad()

        arrComplain = ["Charging","Charger","Payment"]

        txtViewDetails.textColor = Constants.textNotSelectedColor
        txtViewDetails.text = "Enter Description".localiz()

        txtViewDetails.delegate = self

        getUserComplainType()
        setupDropDowns()
    }

    override func viewDidAppear(_ animated: Bool) {
        AppDelegate.shared.isSwipeBack = 1
        navigationController?.interactivePopGestureRecognizer?.delegate = self
        navigationController?.interactivePopGestureRecognizer?.isEnabled = true
    }


    override func viewDidLayoutSubviews() {

        btnSubmit.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        txtViewDetails.addBorder(color: Constants.textNotSelectedColor!, width: Int(1))
        txtViewDetails.maskClipCorner(cornerRadius: 8)

        complainTypeDropDown.backgroundColor = .white
        complainTypeDropDown.textColor = .black
        complainTypeDropDown.selectedTextColor = .white
        complainTypeDropDown.selectionBackgroundColor = Constants.primaryColor!

        transactionIDDropDown.backgroundColor = .white
        transactionIDDropDown.textColor = .black
        transactionIDDropDown.selectedTextColor = .white
        transactionIDDropDown.selectionBackgroundColor = Constants.primaryColor!

        complainTypeDropDown.maskClipCorner(cornerRadius: 8)
        transactionIDDropDown.maskClipCorner(cornerRadius: 8)

        btnBack.addBorder(color: Constants.backArrowBorderColor!, width: Int(1))
        btnBack.maskClipCorner(cornerRadius: 10)
    }


    //MARK: -  DropDown Methods
    func setupDropDowns() {
        setupComplainDropDown()
        setupTransactionDropDown()
    }

    func setupComplainDropDown() {
        complainTypeDropDown.anchorView = btnComplainType
        complainTypeDropDown.bottomOffset = CGPoint(x: 0, y: btnComplainType.bounds.height)
        complainTypeDropDown.selectionAction = { [weak self] (index, item) in
            self!.lblComplainType.text = "\(item)"
            self!.strTypeID = "\(self!.complainList[index].ucm_id)"
            AppDelegate.shared.strTypeID = "\(self!.complainList[index].ucm_id)"
            self!.lblTransaction.text = ""
            self!.strTransID = ""
            AppDelegate.shared.strTransactionID = ""
            self!.complaintFilterType()
        }
    }

    func setupTransactionDropDown() {
        transactionIDDropDown.anchorView = btnTransaction
        transactionIDDropDown.bottomOffset = CGPoint(x: 0, y: btnTransaction.bounds.height)
        transactionIDDropDown.selectionAction = { [weak self] (index, item) in
            self!.lblTransaction.text = "\(item)"
            // Store the id instead of the code for API calls
            if index < self!.complaintFilterList.count {
                self!.strTransID = self!.complaintFilterList[index].id
                AppDelegate.shared.strTransactionID = self!.complaintFilterList[index].id
            }
        }
    }


    // MARK: - Button Actions
    @IBAction func backAction(_ sender: UIButton) {
        navigationController?.popViewController(animated: true)
    }

    @IBAction func submitAction(_ sender: UIButton) {
        if txtViewDetails.text == "Enter description".localiz() {
            strDesc = ""
        } else {
            strDesc = txtViewDetails.text
        }
        if strTypeID == "" {
            alert(title: "Complaint Type".localiz(), message: "Please select complaint type".localiz())
        } else if strTransID == "" {
            alert(title: "Transaction".localiz(), message: "Please select transaction".localiz())
        } else if txtViewDetails.text == "Enter Description".localiz() || txtViewDetails.text.isEmpty {
            alert(title: "Description".localiz(), message: "Please enter description".localiz())
        } else {
            postUserComplain()
        }
    }

    @IBAction func complainTypeAction(_ sender: UIButton) {
        complainTypeDropDown.show()
    }

    @IBAction func transactionAction(_ sender: UIButton) {
        transactionIDDropDown.show()
    }

    // MARK: - WebService
    func getUserComplainType() {

        let url = Constants.BASE_URL + API.GET_USER_COMPLAIN_TYPE
        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]
        AF.request(url, method: .get, parameters: nil, encoding:  URLEncoding.default, headers: headers).responseJSON { response in
            print(response)
            AppDelegate.shared.hideHUD()
            switch response.result {
            case .success:

                let JSON = response.value as! NSDictionary
                print(JSON)

                if "\(JSON["code"]!)" == "200" && "\(JSON["status"]!)" == "1" {
                    let jsonData = JSON["data"] as! NSDictionary
                    self.complainList.removeAll()
                    for item in jsonData["complain_data"] as! NSArray {
                        self.complainList.append(ComplainType(dic: item as! NSDictionary))
                    }

                    for item in self.complainList {
                        self.complainTypeDropDown.dataSource.append(item.ucm_name)
                    }

                    if AppDelegate.shared.isFromMenu == false {
                        for item in self.complainList {
                            if AppDelegate.shared.strTypeID == item.ucm_id {
                                self.lblComplainType.text = "\(item.ucm_name)"
                                self.strTypeID = "\(item.ucm_id)"
                            }
                        }
                    }
                } else {
                    AppDelegate.shared.hideHUD()
                    if "\(JSON["code"]!)" == "100" {
                        AppDelegate.shared.apiKeyLogout()
                    }
                }
                break
            case .failure(let error):
                print(error)
                AppDelegate.shared.hideHUD()
            }
        }
    }

    func postUserComplain() {

        AppDelegate.shared.showHUD()
        let url = Constants.BASE_URL + API.ADD_USER_COMPLAIN
        paramAddComplain = ["uc_description"  : "\(strDesc)",
                            "uc_type_id"      : "\(AppDelegate.shared.strTypeID)",
                            "uc_trans_id"     : "\(AppDelegate.shared.strTransactionID)"]
        print("Add Complaint Parameters: \(paramAddComplain)")
        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]
        AF.request(url, method: .post, parameters: paramAddComplain, encoding:  URLEncoding.default, headers: headers).responseJSON { response in

            AppDelegate.shared.hideHUD()
            switch response.result {
            case .success:

                let JSON = response.value as! NSDictionary
                print(JSON)

                if "\(JSON["code"]!)" == "200" && "\(JSON["status"]!)" == "1" {

                    let alertController = UIAlertController(title: NSLocalizedString("Complaint Added", comment: ""), message: NSLocalizedString("\(JSON["msg"]!)", comment: ""), preferredStyle: .alert)
                    let settingsAction = UIAlertAction(title: NSLocalizedString("OK", comment: ""), style: .default) { (UIAlertAction) in
                        self.navigationController?.popViewController(animated: true)
                    }
                    alertController.addAction(settingsAction)
                    self.present(alertController, animated: true, completion: nil)

                } else {
                    AppDelegate.shared.hideHUD()
                    if "\(JSON["code"]!)" == "100" {
                        AppDelegate.shared.apiKeyLogout()
                    }
                }
                break
            case .failure(let error):
                print(error)
                self.alert(title: "", message: "\(error.localizedDescription)")
                AppDelegate.shared.hideHUD()
            }
        }
    }


    func complaintFilterType() {

        AppDelegate.shared.showHUD()
        let url = Constants.BASE_URL + API.COMPLAIN_FILTER_TYPE
        paramComplainFilter = ["complain_type"  : "\(AppDelegate.shared.strTypeID)"]
        print("paramComplainFilter:-",paramComplainFilter)
        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]
        AF.request(url, method: .post, parameters: paramComplainFilter, encoding:  URLEncoding.default, headers: headers).responseJSON { response in

            AppDelegate.shared.hideHUD()
            switch response.result {
            case .success:

                let JSON = response.value as! NSDictionary
                print(JSON)

                if "\(JSON["code"]!)" == "200" && "\(JSON["status"]!)" == "1" {
                    let jsonData = JSON["data"] as! NSDictionary

                    self.transactionList.removeAll()
                    // Clear complaintFilterList and dropdown data source before adding new items
                    self.complaintFilterList.removeAll()
                    self.transactionIDDropDown.dataSource.removeAll()

                    for item in jsonData["type_data"] as! NSArray {
                        self.complaintFilterList.append(ComplaintFilterType(dic: item as! NSDictionary))
                    }
                    print(self.complaintFilterList)

                    for item in self.complaintFilterList {
                        self.transactionIDDropDown.dataSource.append(item.code)
                    }

                } else {
                    AppDelegate.shared.hideHUD()
                    if "\(JSON["code"]!)" == "100" {
                        AppDelegate.shared.apiKeyLogout()
                    }
                }
                break
            case .failure(let error):
                print(error)
                self.alert(title: "", message: "\(error.localizedDescription)")
                AppDelegate.shared.hideHUD()
            }
        }
    }
}
extension AddComplainVC: UITextViewDelegate {

    func textViewDidBeginEditing(_ textView: UITextView) {
        if textView.text == "Enter Description".localiz() {
            textView.text = nil
            textView.textColor = .black
        }
    }

    func textViewDidEndEditing(_ textView: UITextView) {
        if textView.text.isEmpty {
            textView.text = "Enter Description".localiz()
            textView.textColor = Constants.textNotSelectedColor
        }
    }
}
extension AddComplainVC:UIGestureRecognizerDelegate {
    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldBeRequiredToFailBy otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        return true
    }
}

//
//  AddMoneyVC.swift
//  AddMoneyVC
//
//  Created by Developer on 16/08/21.
//

import UIKit
import Alamofire
import SDWebImage
import WebKit

// MARK: - Payment Method Models
struct PaymentMethod {
    var id: Int
    var name: String
    var icon: String

    init(dic: NSDictionary) {
        self.id = dic["id"] as? Int ?? 0
        self.name = dic["name"] as? String ?? ""
        self.icon = dic["icon"] as? String ?? ""
    }
}

struct PaymentMethodResponse {
    var status: Bool
    var code: Int
    var message: String
    var paymentMethods: [PaymentMethod]

    init(dic: NSDictionary) {
        self.status = dic["status"] as? Bool ?? false
        self.code = dic["code"] as? Int ?? 0
        self.message = dic["msg"] as? String ?? ""

        self.paymentMethods = []
        if let data = dic["data"] as? NSDictionary,
           let paymentMethodData = data["payment_method_data"] as? NSArray {
            for item in paymentMethodData {
                if let methodDict = item as? NSDictionary {
                    self.paymentMethods.append(PaymentMethod(dic: methodDict))
                }
            }
        }
    }
}

// MARK: - Initiate Payment Models
struct InitiatePaymentResponse {
    var status: Bool
    var code: Int
    var message: String
    var paymentUrl: String

    init(dic: NSDictionary) {
        self.status = dic["status"] as? Bool ?? false
        self.code = dic["code"] as? Int ?? 0
        self.message = dic["msg"] as? String ?? ""

        self.paymentUrl = ""
        if let data = dic["data"] as? String {
            self.paymentUrl = dic["data"] as? String ?? ""
        }
    }
}
class AddMoneyVC: UIViewController {


    // MARK: - IBOutlets
    @IBOutlet weak var txtAmount: UITextField!
    @IBOutlet weak var viewRazor: UIView!
    @IBOutlet weak var btnProceed: UIButton!
    @IBOutlet weak var btnBack: UIButton!

    @IBOutlet weak var scrollDetails: UIScrollView!

    @IBOutlet weak var viewPromoCode: UIView!
    @IBOutlet weak var txtPromoCode: UITextField!
    @IBOutlet weak var btnApplyPromo: UIButton!

    @IBOutlet weak var lblPromoNotValid: UILabel!

    @IBOutlet weak var viewNote: UIView!

    @IBOutlet weak var viewOfferCoupons: UIView!
    @IBOutlet weak var tableCoupon: UITableView!
    @IBOutlet weak var tableHeight: NSLayoutConstraint!

    @IBOutlet weak var viewBgTerms: UIView!
    @IBOutlet weak var viewMainTerms: UIView!
    @IBOutlet weak var tableTerms: UITableView!

    @IBOutlet weak var viewBanner: UIView!
    @IBOutlet weak var scrollBanners: UIScrollView!
    @IBOutlet weak var pageController: UIPageControl!

    @IBOutlet weak var lblTitle: UILabel!
    @IBOutlet weak var lblEnterAmountTitle: UILabel!
    @IBOutlet weak var lblEnterPromoTitle: UILabel!

    @IBOutlet weak var lblNoteTitle: UILabel!
    @IBOutlet weak var lblPromotionsTitle: UILabel!

    @IBOutlet weak var lblTCTitle: UILabel!
    @IBOutlet weak var btnDone: UIButton!

    // MARK: - Payment Method Selection
    @IBOutlet weak var viewPaymentMethods: UIView!
    @IBOutlet weak var collectionPaymentMethods: UICollectionView!
    @IBOutlet weak var lblSelectPaymentMethod: UILabel!

    var paymentMethods: [PaymentMethod] = []
    var selectedPaymentMethod: PaymentMethod?

    // MARK: - Payment WebView
    var paymentWebView: WKWebView?

    var frame = CGRect.zero
    var arrImage:[String] = []


    // MARK: - View LifeCycle Methods
    override func viewDidLoad() {
        super.viewDidLoad()

        setupPaymentMethodsCollection()
        hideRazorpayView()
        fetchPaymentMethods() // Fetch immediately, no delay

        tableCoupon.delegate = self
        tableCoupon.dataSource = self

        tableTerms.delegate = self
        tableTerms.dataSource = self

        viewBgTerms.isHidden = true
        lblPromoNotValid.isHidden = true

        tableCoupon.reloadData()
        DispatchQueue.main.async {
            self.tableCoupon.reloadData()
            self.tableHeight.constant = 10 * 160 //self.tableCoupon.contentSize.height + 50
            self.scrollDetails.contentSize.height = self.viewOfferCoupons.frame.origin.y + self.viewOfferCoupons.frame.height + 16
            print("self.tableHeight.constant:-",self.tableHeight.constant)
            print("self.scrollDetails.contentSize.height:-",self.tableHeight.constant)
        }

        scrollBanners.delegate = self
        pageController.isUserInteractionEnabled = false

    }

    override func viewWillAppear(_ animated: Bool) {
//        lblTitle.text = "Add Money".localiz()
//        lblEnterAmountTitle.text = "Enter Amount".localiz()
//        lblEnterPromoTitle.text = "Enter Promo Code".localiz()
//        lblNoteTitle.text = "Note : Money should be added in multiples of 10".localiz()
//        lblPromotionsTitle.text = "Promotions".localiz()
//        lblTCTitle.text = "Terms and Conditions".localiz()
    }

    override func viewDidAppear(_ animated: Bool) {
        AppDelegate.shared.isSwipeBack = 1
        navigationController?.interactivePopGestureRecognizer?.delegate = self
        navigationController?.interactivePopGestureRecognizer?.isEnabled = true
        arrImage = ["ic_promo","ic_promo","ic_promo"]
        setupScreens()
    }

    func setupPaymentMethodsCollection() {
        // Check if collection view outlet is connected
        guard let collectionView = collectionPaymentMethods else {
            print("Payment methods collection view outlet not connected yet")
            return
        }

        collectionView.delegate = self
        collectionView.dataSource = self

        // Register cell for collection view (using programmatic cell)
        collectionView.register(PaymentMethodCollectionCell.self, forCellWithReuseIdentifier: "PaymentMethodCell")

        // Set collection view layout
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.minimumInteritemSpacing = 10
        layout.minimumLineSpacing = 10
        collectionView.collectionViewLayout = layout
    }

    func hideRazorpayView() {
        // Don't hide the Razorpay view - we need it for payment methods
        // Just clear its content and show loading message
        viewRazor?.isHidden = false

        // Create a loading label that will be replaced when payment methods load
        createLoadingLabel()
    }

    func createLoadingLabel() {
        guard let razorView = viewRazor else { return }

        let loadingLabel = UILabel()
        loadingLabel.text = "Loading payment methods..."
        loadingLabel.textAlignment = .center
        loadingLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        loadingLabel.textColor = Constants.primaryColor
        loadingLabel.backgroundColor = UIColor.systemGray6
        loadingLabel.layer.cornerRadius = 8
        loadingLabel.layer.masksToBounds = true
        loadingLabel.tag = 9999 // Tag to identify this label later

        // Add the loading label where Razorpay view was
        razorView.superview?.insertSubview(loadingLabel, aboveSubview: razorView)
        loadingLabel.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            loadingLabel.topAnchor.constraint(equalTo: razorView.topAnchor),
            loadingLabel.leadingAnchor.constraint(equalTo: razorView.leadingAnchor),
            loadingLabel.trailingAnchor.constraint(equalTo: razorView.trailingAnchor),
            loadingLabel.heightAnchor.constraint(equalToConstant: 44) // Keep original height
        ])
    }

    func createPaymentMethodUI() {
        // Remove any existing loading label or payment method container
        if let loadingLabel = viewRazor?.superview?.viewWithTag(9999) {
            loadingLabel.removeFromSuperview()
        }
        if let existingContainer = viewRazor?.superview?.viewWithTag(8888) {
            existingContainer.removeFromSuperview()
        }

        guard let razorView = viewRazor else { return }

        // REPLACE the Razorpay view content instead of adding new elements
        // Clear the existing Razorpay view content
        razorView.subviews.forEach { $0.removeFromSuperview() }

        // Configure the existing Razorpay view as payment method container
        razorView.backgroundColor = UIColor.systemBackground
        razorView.layer.cornerRadius = 8
        razorView.layer.borderWidth = 1
        razorView.layer.borderColor = UIColor.systemGray4.cgColor
        razorView.tag = 8888 // Tag to identify this container
        razorView.isHidden = false // Make sure it's visible

        // Update the height constraint of the existing Razorpay view
        for constraint in razorView.constraints {
            if constraint.firstAttribute == .height {
                constraint.constant = 50 // Slightly taller than original 44px
                break
            }
        }

        // Create horizontal stack view for payment methods
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.distribution = .fillEqually
        stackView.spacing = 8
        stackView.alignment = .fill
        stackView.tag = 7777 // Tag for later reference
        razorView.addSubview(stackView)

        stackView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            stackView.topAnchor.constraint(equalTo: razorView.topAnchor, constant: 6),
            stackView.leadingAnchor.constraint(equalTo: razorView.leadingAnchor, constant: 8),
            stackView.trailingAnchor.constraint(equalTo: razorView.trailingAnchor, constant: -8),
            stackView.bottomAnchor.constraint(equalTo: razorView.bottomAnchor, constant: -6)
        ])

        // Add payment method buttons
        for (index, paymentMethod) in paymentMethods.enumerated() {
            let button = createOptimizedPaymentMethodButton(for: paymentMethod, index: index)
            stackView.addArrangedSubview(button)
        }

        // Auto-select the first payment method
        if !paymentMethods.isEmpty {
            selectedPaymentMethod = paymentMethods[0]
            updatePaymentMethodSelection(selectedIndex: 0)
        }

        // Force layout update
        razorView.layoutIfNeeded()
    }

    func createOptimizedPaymentMethodButton(for paymentMethod: PaymentMethod, index: Int) -> UIButton {
        let button = UIButton(type: .custom)
        button.backgroundColor = UIColor.systemBackground
        button.layer.cornerRadius = 6
        button.layer.borderWidth = 2
        button.layer.borderColor = UIColor.systemGray5.cgColor
        button.tag = index

        // Ensure button is interactive
        button.isUserInteractionEnabled = true
        button.clipsToBounds = false

        // Payment method icon - use full button area with proper aspect ratio
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.backgroundColor = UIColor.clear
        imageView.isUserInteractionEnabled = false
        button.addSubview(imageView)

        imageView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            imageView.topAnchor.constraint(equalTo: button.topAnchor, constant: 4),
            imageView.leadingAnchor.constraint(equalTo: button.leadingAnchor, constant: 4),
            imageView.trailingAnchor.constraint(equalTo: button.trailingAnchor, constant: -4),
            imageView.bottomAnchor.constraint(equalTo: button.bottomAnchor, constant: -4)
        ])

        // Load icon from URL with proper aspect ratio (275x183)
        if let url = URL(string: paymentMethod.icon) {
            imageView.sd_setImage(with: url, placeholderImage: UIImage(named: "ic_rupees")) { [weak imageView] (image, error, cacheType, url) in
                if error != nil {
                    imageView?.image = UIImage(named: "ic_rupees")
                }
            }
        } else {
            imageView.image = UIImage(named: "ic_rupees")
        }

        // Add tap action with proper target
        button.addTarget(self, action: #selector(optimizedPaymentMethodSelected(_:)), for: .touchUpInside)

        // Add touch feedback
        button.addTarget(self, action: #selector(paymentMethodTouchDown(_:)), for: .touchDown)
        button.addTarget(self, action: #selector(paymentMethodTouchUp(_:)), for: [.touchUpInside, .touchUpOutside, .touchCancel])

        return button
    }

    @objc func paymentMethodTouchDown(_ sender: UIButton) {
        UIView.animate(withDuration: 0.1) {
            sender.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
            sender.alpha = 0.8
        }
    }

    @objc func paymentMethodTouchUp(_ sender: UIButton) {
        UIView.animate(withDuration: 0.1) {
            sender.transform = CGAffineTransform.identity
            sender.alpha = 1.0
        }
    }

    @objc func optimizedPaymentMethodSelected(_ sender: UIButton) {
        let index = sender.tag
        selectedPaymentMethod = paymentMethods[index]

        // Update visual selection
        updatePaymentMethodSelection(selectedIndex: index)

        print("Selected payment method: \(paymentMethods[index].name)")
    }

    func updatePaymentMethodSelection(selectedIndex: Int) {
        // Find the stack view containing payment method buttons
        // Now the Razorpay view itself is the container (tag 8888)
        guard let containerView = viewRazor,
              containerView.tag == 8888,
              let stackView = containerView.viewWithTag(7777) as? UIStackView else {
            return
        }

        // Update all button appearances
        for (i, view) in stackView.arrangedSubviews.enumerated() {
            if let button = view as? UIButton {
                if i == selectedIndex {
                    // Selected state - prominent visual feedback
                    button.layer.borderColor = Constants.primaryColor?.cgColor
                    button.layer.borderWidth = 3
                    button.backgroundColor = Constants.primaryColor?.withAlphaComponent(0.2)

                    // Add selection animation
                    UIView.animate(withDuration: 0.3, delay: 0, usingSpringWithDamping: 0.7, initialSpringVelocity: 0.5, options: [.curveEaseInOut], animations: {
                        button.transform = CGAffineTransform(scaleX: 1.05, y: 1.05)
                    }) { _ in
                        UIView.animate(withDuration: 0.2) {
                            button.transform = CGAffineTransform.identity
                        }
                    }
                } else {
                    // Unselected state
                    button.layer.borderColor = UIColor.systemGray5.cgColor
                    button.layer.borderWidth = 2
                    button.backgroundColor = UIColor.systemBackground
                    button.transform = CGAffineTransform.identity
                }
            }
        }
    }

    override func viewDidLayoutSubviews() {
        btnProceed.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        // viewRazor.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!) // Removed - Razorpay view replaced with payment method selection

        txtAmount.addBorder(color: Constants.textNotSelectedColor!, width: Int(1))
        txtAmount.maskClipCorner(cornerRadius: 8)

        viewPromoCode.addBorder(color: Constants.textNotSelectedColor!, width: Int(1))
        viewPromoCode.maskClipCorner(cornerRadius: 8)

        txtAmount.setLeftPaddingPoints(12)
        txtPromoCode.setLeftPaddingPoints(12)

        viewNote.maskClipCorner(cornerRadius: 10)

        btnBack.addBorder(color: Constants.backArrowBorderColor!, width: Int(1))
        btnBack.maskClipCorner(cornerRadius: 10)

        viewOfferCoupons.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)

        //isScrollEnabled of table view  should be dissable because our table is inside scrollview.
        tableCoupon.isScrollEnabled = false

        //if above tableView.contentSize.height   not zero and giving acurate value then proceed further and update our parent scroll view contentsize height for height.
//        print(tableCoupon.contentSize.height)

        //place some bottom peeding as you want
        let bottomPedding:CGFloat = 30

        //Finally update your scrollview content size with newly created table height + bottom pedding.
        scrollDetails.contentSize = CGSize.init(width: scrollDetails.contentSize.width, height:tableCoupon.contentSize.height + bottomPedding)

        viewMainTerms.roundCorners(corners: [.topLeft, .topRight], radius: 12)

    }


    // MARK: - Button Actions
    @IBAction func backAction(_ sender: UIButton) {
        navigationController?.popViewController(animated: true)
    }

    @IBAction func applyPromoTapped(_ sender: UIButton) {

    }

    @objc func termsOpenTapped(_ sender: UIButton) {
        viewBgTerms.isHidden = false
    }

    @objc func copyCodeTapped(_ sender: UIButton) {
        self.showToast(message: "Coupon Code Copied!", font: .systemFont(ofSize: 12.0))
    }

    @IBAction func termsDoneTapped(_ sender: UIButton) {
        viewBgTerms.isHidden = true
    }

    @IBAction func proceedAction(_ sender: UIButton) {
        let value = Int(self.txtAmount.text!)
        if self.txtAmount.text == "" {
            self.alert(title: "Enter Amount".localiz(), message: "Please enter amount to continue with the transaction".localiz())

        } else if value! % 10 != 0 {
            self.alert(title: "Enter Amount".localiz(), message: "Please add amount in multiples of 10".localiz())

        } else if selectedPaymentMethod == nil {
            self.alert(title: "Alert", message: "Please select a payment method")

        } else {
            // Proceed with initiate payment API call
            print("Payment method selected: \(selectedPaymentMethod!.name)")
            initiatePayment(amount: value!, paymentId: selectedPaymentMethod!.id)
        }
    }

    func convertImageToBase64String (img: UIImage) -> String {
        return img.jpegData(compressionQuality: 1)?.base64EncodedString() ?? ""
    }

    // MARK: - Payment Method API
    func fetchPaymentMethods() {
        AppDelegate.shared.showHUD()
        let url = Constants.BASE_URL + API.PAYMENT_METHOD
        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]

        AF.request(url, method: .get, parameters: nil, encoding: URLEncoding.default, headers: headers).responseJSON { response in
            print(response)
            AppDelegate.shared.hideHUD()

            switch response.result {
            case .success:
                let JSON = response.value as! NSDictionary
                print(JSON)

                if "\(JSON["code"]!)" == "200" && "\(JSON["status"]!)" == "1" {
                    let paymentMethodResponse = PaymentMethodResponse(dic: JSON)
                    self.paymentMethods = paymentMethodResponse.paymentMethods

                    DispatchQueue.main.async {
                        // Update collection view if connected
                        self.collectionPaymentMethods?.reloadData()

                        // Create the improved payment method UI immediately
                        if !self.paymentMethods.isEmpty {
                            self.createPaymentMethodUI()
                        }
                    }
                } else {
                    if "\(JSON["code"]!)" == "100" {
                        AppDelegate.shared.apiKeyLogout()
                    } else {
                        self.alert(title: "Error", message: "\(JSON["msg"]!)")
                    }
                }
                break
            case .failure(let error):
                print(error)
                self.alert(title: "Error", message: "Failed to fetch payment methods. Please try again.")
            }
        }
    }

    // MARK: - Initiate Payment API
    func initiatePayment(amount: Int, paymentId: Int) {
        AppDelegate.shared.showHUD()
        let url = Constants.BASE_URL + API.INITIATE_PAYMENT
        let parameters = [
            "amount": amount,
            "payment_id": paymentId,
            "jwt": UserDefaults.standard.value(forKey: Constants.JWT_TOKEN) as? String ?? ""
        ] as [String : Any]

        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]

        print("Initiating payment with parameters: \(parameters)")

        AF.request(url, method: .post, parameters: parameters, encoding: URLEncoding.default, headers: headers).responseJSON { response in
            print("Initiate Payment Response: \(response)")
            AppDelegate.shared.hideHUD()

            switch response.result {
            case .success:
                let JSON = response.value as! NSDictionary
                print("Initiate Payment JSON: \(JSON)")

                if "\(JSON["code"]!)" == "200" && "\(JSON["status"]!)" == "1" {
                    let initiatePaymentResponse = InitiatePaymentResponse(dic: JSON)

                    if !initiatePaymentResponse.paymentUrl.isEmpty {
                        DispatchQueue.main.async {
                            self.setupPaymentWebView(with: initiatePaymentResponse.paymentUrl)
                        }
                    } else {
                        self.alert(title: "Error", message: "Payment URL not found in response")
                    }
                } else {
                    if "\(JSON["code"]!)" == "100" {
                        AppDelegate.shared.apiKeyLogout()
                    } else {
                        self.alert(title: "Error", message: "\(JSON["msg"]!)")
                    }
                }
                break
            case .failure(let error):
                print("Initiate Payment Error: \(error)")
                self.alert(title: "Error", message: "Failed to initiate payment. Please try again.")
            }
        }
    }

    // MARK: - Payment WebView Setup
    func setupPaymentWebView(with paymentUrl: String) {
        guard let url = URL(string: paymentUrl) else {
            self.alert(title: "Error", message: "Invalid payment URL")
            return
        }

        // Create webview configuration
        let webConfiguration = WKWebViewConfiguration()

        // Create the webview
        paymentWebView = WKWebView(frame: view.bounds, configuration: webConfiguration)
        paymentWebView?.navigationDelegate = self

        // Add webview to the main view
        if let webView = paymentWebView {
            view.addSubview(webView)
            webView.translatesAutoresizingMaskIntoConstraints = false
            NSLayoutConstraint.activate([
                webView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
                webView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
                webView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
                webView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
            ])

            // Add a close button to allow users to cancel payment
            addCloseButtonToWebView(webView: webView)

            // Load the payment URL
            let request = URLRequest(url: url)
            webView.load(request)

            print("Loading payment URL: \(paymentUrl)")
        }
    }

    func addCloseButtonToWebView(webView: WKWebView) {
        let closeButton = UIButton(type: .system)
        closeButton.setTitle("✕", for: .normal)
        closeButton.titleLabel?.font = UIFont.systemFont(ofSize: 24, weight: .bold)
        closeButton.setTitleColor(.white, for: .normal)
        closeButton.backgroundColor = UIColor.black.withAlphaComponent(0.7)
        closeButton.layer.cornerRadius = 20
        closeButton.addTarget(self, action: #selector(closePaymentWebView), for: .touchUpInside)

        webView.addSubview(closeButton)
        closeButton.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            closeButton.topAnchor.constraint(equalTo: webView.safeAreaLayoutGuide.topAnchor, constant: 10),
            closeButton.trailingAnchor.constraint(equalTo: webView.trailingAnchor, constant: -10),
            closeButton.widthAnchor.constraint(equalToConstant: 40),
            closeButton.heightAnchor.constraint(equalToConstant: 40)
        ])
    }

    @objc func closePaymentWebView() {
        paymentWebView?.removeFromSuperview()
        paymentWebView = nil
        print("Payment webview closed by user")
    }

    // MARK: - Setup Page Controller
    func setupScreens() {
        print("arrImage:-",arrImage)
        for index in 0..<arrImage.count {
            // 1.
            frame.origin.x = scrollBanners.frame.size.width * CGFloat(index)
            frame.size = scrollBanners.frame.size

            // 2.
            let imgView = UIImageView(frame: frame)
            imgView.contentMode = .scaleToFill
            imgView.image = UIImage(named: arrImage[index])
//            let imagePath = arrImage[index]
//            imgView.sd_setImage(with: URL(string: imagePath)) { (image, error, type, url) in
//                if (error != nil) {
//                    imgView.image = UIImage(named: "ic_new_nxc_logo")
//                } else {
//                    imgView.image = image
//                }
//            }
            self.scrollBanners.addSubview(imgView)

            if arrImage.count == 0 {
                imgView.image = UIImage(named: "ic_new_nxc_logo")
            }
        }

        // 3.
        scrollBanners.contentSize = CGSize(width: (scrollBanners.frame.size.width * CGFloat(arrImage.count)), height: scrollBanners.frame.size.height)
    }
}

// MARK: - Payment Method Collection Cell
class PaymentMethodCollectionCell: UICollectionViewCell {

    private let containerView = UIView()
    private let imageView = UIImageView()
    private let nameLabel = UILabel()
    private let selectionView = UIView()

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }

    private func setupUI() {
        // Container view
        containerView.layer.cornerRadius = 8
        containerView.layer.borderWidth = 1
        containerView.layer.borderColor = UIColor.lightGray.cgColor
        containerView.backgroundColor = UIColor.systemBackground
        contentView.addSubview(containerView)

        // Selection view
        selectionView.layer.cornerRadius = 8
        selectionView.backgroundColor = Constants.primaryColor?.withAlphaComponent(0.1)
        selectionView.layer.borderWidth = 2
        selectionView.layer.borderColor = Constants.primaryColor?.cgColor
        selectionView.isHidden = true
        contentView.addSubview(selectionView)

        // Image view
        imageView.contentMode = .scaleAspectFit
        imageView.layer.cornerRadius = 4
        containerView.addSubview(imageView)

        // Name label
        nameLabel.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        nameLabel.textAlignment = .center
        nameLabel.numberOfLines = 2
        containerView.addSubview(nameLabel)

        // Setup constraints
        setupConstraints()
    }

    private func setupConstraints() {
        containerView.translatesAutoresizingMaskIntoConstraints = false
        selectionView.translatesAutoresizingMaskIntoConstraints = false
        imageView.translatesAutoresizingMaskIntoConstraints = false
        nameLabel.translatesAutoresizingMaskIntoConstraints = false

        NSLayoutConstraint.activate([
            // Container view
            containerView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 4),
            containerView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 4),
            containerView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -4),
            containerView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -4),

            // Selection view
            selectionView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 4),
            selectionView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 4),
            selectionView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -4),
            selectionView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -4),

            // Image view
            imageView.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 8),
            imageView.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 8),
            imageView.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -8),
            imageView.heightAnchor.constraint(equalToConstant: 40),

            // Name label
            nameLabel.topAnchor.constraint(equalTo: imageView.bottomAnchor, constant: 4),
            nameLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 4),
            nameLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -4),
            nameLabel.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -4)
        ])
    }

    func configure(with paymentMethod: PaymentMethod, isSelected: Bool) {
        nameLabel.text = paymentMethod.name

        // Load payment method icon
        if let url = URL(string: paymentMethod.icon) {
            imageView.sd_setImage(with: url, placeholderImage: UIImage(named: "ic_rupees"))
        } else {
            imageView.image = UIImage(named: "ic_rupees")
        }

        // Update selection state
        selectionView.isHidden = !isSelected
        if isSelected {
            containerView.layer.borderColor = Constants.primaryColor?.cgColor
            containerView.layer.borderWidth = 2
        } else {
            containerView.layer.borderColor = UIColor.lightGray.cgColor
            containerView.layer.borderWidth = 1
        }
    }
}

// MARK: - Collection View Methods
extension AddMoneyVC: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {

    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return paymentMethods.count
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "PaymentMethodCell", for: indexPath) as! PaymentMethodCollectionCell

        let paymentMethod = paymentMethods[indexPath.item]
        cell.configure(with: paymentMethod, isSelected: selectedPaymentMethod?.id == paymentMethod.id)

        return cell
    }

    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        selectedPaymentMethod = paymentMethods[indexPath.item]
        collectionView.reloadData()
    }

    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return CGSize(width: 120, height: 80)
    }
}
extension AddMoneyVC:UIGestureRecognizerDelegate {
    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldBeRequiredToFailBy otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        return true
    }
}
extension AddMoneyVC: UITableViewDelegate, UITableViewDataSource {

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        if tableView == tableCoupon {
            return 10
        } else {
            return 10
        }
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        if tableView == tableCoupon {
            let cell = tableView.dequeueReusableCell(withIdentifier: "cell", for: indexPath) as! OfferCouponCell
            if indexPath.row == 9 {
                cell.imgOfferLine.alpha = 0
            } else {
                cell.imgOfferLine.alpha = 1
            }
            cell.btnTerms.tag = indexPath.row
            cell.btnTerms.addTarget(self, action: #selector(self.termsOpenTapped(_:)), for: .touchUpInside)

            cell.btnCopyCode.tag = indexPath.row
            cell.btnCopyCode.addTarget(self, action: #selector(self.copyCodeTapped(_:)), for: .touchUpInside)

            cell.layoutIfNeeded()
            cell.updateConstraints()
            return cell
        } else {
            let cell = tableView.dequeueReusableCell(withIdentifier: "cell", for: indexPath) as! TermsCell
            cell.layoutIfNeeded()
            cell.updateConstraints()
            return cell
        }
    }
}
extension AddMoneyVC: UIScrollViewDelegate {
    func scrollViewDidEndDecelerating(_ scrollView: UIScrollView) {
        let pageNumber = scrollView.contentOffset.x / scrollView.frame.size.width
        print("pageNumber1:-",pageNumber)
        pageController.currentPage = Int(pageNumber)
    }

    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        let pageNumber = scrollView.contentOffset.x / scrollView.frame.size.width
        print("pageNumber2:-",pageNumber)
        pageController.currentPage = Int(pageNumber)
    }
}
extension AddMoneyVC: UIPageViewControllerDelegate {

    func pageViewController(_ pageViewController: UIPageViewController, willTransitionTo pendingViewControllers: [UIViewController]) {
        let pageNumber = scrollBanners.contentOffset.x / scrollBanners.frame.size.width
        print("pageNumber2:-",pageNumber)
        pageController.currentPage = Int(pageNumber)
    }
}

// MARK: - WKNavigationDelegate
extension AddMoneyVC: WKNavigationDelegate {

    func webView(_ webView: WKWebView, didStartProvisionalNavigation navigation: WKNavigation!) {
        print("WebView started loading: \(webView.url?.absoluteString ?? "Unknown URL")")
        AppDelegate.shared.showHUD()
    }

    func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        print("WebView finished loading: \(webView.url?.absoluteString ?? "Unknown URL")")
        AppDelegate.shared.hideHUD()
    }

    func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
        print("WebView failed to load: \(error.localizedDescription)")
        AppDelegate.shared.hideHUD()
        self.alert(title: "Error", message: "Failed to load payment page. Please try again.")
    }

    func webView(_ webView: WKWebView, decidePolicyFor navigationAction: WKNavigationAction, decisionHandler: @escaping (WKNavigationActionPolicy) -> Void) {
        guard let url = navigationAction.request.url else {
            decisionHandler(.cancel)
            return
        }

        print("WebView navigation to: \(url.absoluteString)")

        // Handle payment completion or cancellation based on URL patterns
        // You can customize these URL patterns based on your payment gateway's response URLs
        if url.absoluteString.contains("payment_success") || url.absoluteString.contains("success") {
            // Payment successful
            DispatchQueue.main.async {
                self.handlePaymentSuccess()
            }
            decisionHandler(.cancel)
        } else if url.absoluteString.contains("payment_failed") || url.absoluteString.contains("failed") || url.absoluteString.contains("cancel") {
            // Payment failed or cancelled
            DispatchQueue.main.async {
                self.handlePaymentFailure()
            }
            decisionHandler(.cancel)
        } else {
            // Continue with normal navigation
            decisionHandler(.allow)
        }
    }

    // MARK: - Payment Result Handlers
    func handlePaymentSuccess() {
        paymentWebView?.removeFromSuperview()
        paymentWebView = nil

        let alertController = UIAlertController(title: "Success", message: "Payment completed successfully!", preferredStyle: .alert)
        let okAction = UIAlertAction(title: "OK", style: .default) { _ in
            // Navigate back or refresh wallet balance
            self.navigationController?.popViewController(animated: true)
        }
        alertController.addAction(okAction)
        self.present(alertController, animated: true, completion: nil)
    }

    func handlePaymentFailure() {
        paymentWebView?.removeFromSuperview()
        paymentWebView = nil

        self.alert(title: "Payment Failed", message: "Payment was not completed. Please try again.")
    }
}

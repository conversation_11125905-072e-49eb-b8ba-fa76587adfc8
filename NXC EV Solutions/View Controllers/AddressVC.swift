//
//  AddressVC.swift
//  AddressVC
//
//  Created by Developer on 27/08/21.
//

import UIKit
import Alamofire
import DropDown

class AddressVC: UIViewController {
    
    // MARK: - IBOutlets
    @IBOutlet weak var scrollDetails: UIScrollView!
    @IBOutlet weak var viewMain: UIView!
    @IBOutlet weak var flatTextField: UITextField!
    @IBOutlet weak var areaTextfield: UITextField!
    @IBOutlet weak var landmarkTextField: UITextField!
    @IBOutlet weak var pincodeTextField: UITextField!
    @IBOutlet weak var stateLabel: UILabel!
    @IBOutlet weak var cityLabel: UILabel!
    
    @IBOutlet weak var addProfileButton: UIButton!
    
    @IBOutlet weak var stateButton: UIButton!
    @IBOutlet weak var cityButton: UIButton!
    
    var paramEditProfile:[String:Any] = [:]
    var paramState:[String:Any] = [:]
    var paramCity:[String:Any] = [:]
    
    var stateList:[StateList] = []
    var cityList:[CityList] = []
    
    var strStateID:String = String()
    var strCityID:String = String()
    
    let stateDropDown = DropDown()
    let cityDropDown = DropDown()
    
    var userDetails:[ProfileDetails] = []
    var profileDetails:[ProfileDetails] = []
    
    // MARK: - View LifeCycle Methods
    override func viewDidLoad() {
        super.viewDidLoad()
        
        getState()
        setupDropDowns()
        
        pincodeTextField.delegate = self
    }
    
    override func viewWillAppear(_ animated: Bool) {
        stateLabel.text = ""
        cityLabel.text = ""
        if AppDelegate.shared.isProfileEdit == "1" {
            self.viewProfile()
        }
    }
    
    override func viewDidAppear(_ animated: Bool) {
        AppDelegate.shared.isSwipeBack = 1
        navigationController?.interactivePopGestureRecognizer?.delegate = self
        navigationController?.interactivePopGestureRecognizer?.isEnabled = true
    }
    
    override func viewDidLayoutSubviews() {
        addProfileButton.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        scrollDetails.roundCorners(corners: [.topLeft,.topRight], radius: 16)

        cityDropDown.backgroundColor = .white
        cityDropDown.textColor = .black
        cityDropDown.selectedTextColor = .white
        cityDropDown.selectionBackgroundColor = Constants.primaryColor!
        
        stateDropDown.backgroundColor = .white
        stateDropDown.textColor = .black
        stateDropDown.selectedTextColor = .white
        stateDropDown.selectionBackgroundColor = Constants.primaryColor!
        
        cityDropDown.maskClipCorner(cornerRadius: 8)
        stateDropDown.maskClipCorner(cornerRadius: 8)
    }
    
    //MARK: -  DropDown Methods
    func setupDropDowns() {
        setupStateDropDown()
        setupCityDropDown()
    }
    
    func setupStateDropDown() {
        stateDropDown.anchorView = stateButton
        stateDropDown.bottomOffset = CGPoint(x: 0, y: stateButton.bounds.height)
        stateDropDown.selectionAction = { [weak self] (index, item) in
            self?.stateLabel.text = item
            self?.strStateID = "\(self!.stateList[index].state_id)"
            self?.cityLabel.text = ""
            self?.strCityID = ""
            self?.getCity()
        }
    }
    
    func setupCityDropDown() {
        cityDropDown.anchorView = cityButton
        cityDropDown.bottomOffset = CGPoint(x: 0, y: cityButton.bounds.height)
        cityDropDown.selectionAction = { [weak self] (index, item) in
            self?.cityLabel.text = item
            self?.strCityID = "\(self!.cityList[index].city_id)"
        }
    }
    
    // MARK: - Button Actions
    @IBAction func backTapped(_ sender: UIButton) {
        navigationController?.popViewController(animated: true)
    }
    
    @IBAction func stateTapped(_ sender: UIButton) {
        stateDropDown.show()
    }
    
    @IBAction func cityTapped(_ sender: UIButton) {
        cityDropDown.show()
    }
    
    @IBAction func addProfileTapped(_ sender: UIButton) {
        //        resignTextField()
        if self.flatTextField.text?.count == 0 {
            self.alert(title: "House No.", message: "Please enter flat/house no, buiding")
            
        } else if self.areaTextfield.text?.count == 0 {
            self.alert(title: "Area, Street", message: "Please enter area, street")
            
        } else if self.landmarkTextField.text?.count == 0 {
            self.alert(title: "Landmark", message: "Please enter landmark")
            
        } else if self.pincodeTextField.text?.count == 0 {
            self.alert(title: "Pincode", message: "Please enter pincode")
            
        } else if self.pincodeTextField.text?.count == 0 {
            self.alert(title: "State", message: "Please enter state")
            
        }  else if self.pincodeTextField.text?.count == 0 {
            self.alert(title: "Town/City", message: "Please enter town/city")
            
        } else {
            postProfile()
        }
    }
    
    
    // MARK: - WebService
    func getState() {
        
        //AppDelegate.shared.showHUD()
        let url = Constants.BASE_URL + API.GET_STATE
        let paramState = ["country_id":"1"]
        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]
        AF.request(url, method: .post, parameters: paramState, encoding:  URLEncoding.default, headers: headers).responseJSON { response in
            print(response)
            AppDelegate.shared.hideHUD()
            switch response.result {
            case .success:
                
                let JSON = response.value as! NSDictionary
                print(JSON)
                
                if "\(JSON["code"]!)" == "200" && "\(JSON["status"]!)" == "1" {
                    
                    let jsonData = JSON["data"] as! NSDictionary
                    print(jsonData)
                    
                    self.stateList.removeAll()
                    self.stateDropDown.dataSource.removeAll()
                    for item in jsonData["state_data"] as! NSArray {
                        self.stateList.append(StateList(dic: item as! NSDictionary))
                    }
                    
                    print(self.stateList)
                    for item in self.stateList {
                        self.stateDropDown.dataSource.append(item.state_name)
                    }
                    self.stateDropDown.reloadAllComponents()
                    
                } else {
                    AppDelegate.shared.apiKeyLogout()
                }
                break
            case .failure(let error):
                print(error)
                AppDelegate.shared.hideHUD()
            }
        }
    }
    
    func getCity() {
        
        //        AppDelegate.shared.showHUD()
        let url = Constants.BASE_URL + API.GET_CITY
        let paramCity = ["state_id":"\(self.strStateID)"]
        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]
        AF.request(url, method: .post, parameters: paramCity, encoding:  URLEncoding.default, headers: headers).responseJSON { response in
            print(response)
            AppDelegate.shared.hideHUD()
            switch response.result {
            case .success:
                
                let JSON = response.value as! NSDictionary
                print(JSON)
                
                if "\(JSON["code"]!)" == "200" && "\(JSON["status"]!)" == "1" {

                    let jsonData = JSON["data"] as! NSDictionary
                    print(jsonData)
                
                    self.cityList.removeAll()
                    self.cityDropDown.dataSource.removeAll()
                    for item in jsonData["city_data"] as! NSArray {
                        self.cityList.append(CityList(dic: item as! NSDictionary))
                    }
                    
                    print(self.cityList)
                    for item in self.cityList {
                        self.cityDropDown.dataSource.append(item.city_name)
                    }
                    self.cityDropDown.reloadAllComponents()
                    
                } else {
                    AppDelegate.shared.hideHUD()
                }
                break
            case .failure(let error):
                print(error)
                //            self.alert(title: "", message: "\(error.localizedDescription)")
                AppDelegate.shared.hideHUD()
            }
        }
    }

    
    func postProfile() {
        let url = Constants.BASE_URL + API.EDIT_PROFILE
        print("AppDelegate.shared.profileDetails.count:-",AppDelegate.shared.profileDetails.count)
        
        // Initialize with default values in case profileDetails is empty
        var firstName = ""
        var lastName = ""
        var birthDay = ""
        var gender = ""
        var email = ""
        var gst = ""
        
        // Get values from profileDetails if available
        if !AppDelegate.shared.profileDetails.isEmpty {
            let item = AppDelegate.shared.profileDetails[0]
            firstName = item.first_name
            lastName = item.last_name
            birthDay = item.birth_day
            
            // Convert gender string to numeric value
            if item.gender == "Male" {
                gender = "1"
            } else if item.gender == "Female" {
                gender = "2"
            } else {
                gender = item.gender // Keep original if it's already numeric
            }
            
            // Handle null email properly
            email = item.e_mail == "<null>" ? "" : item.e_mail
            gst = item.gst
        }
        
        // Build parameters
        paramEditProfile = ["user_id"           : "\(UserDefaults.standard.object(forKey: Constants.USER_ID)!)",
                            "first_name"        : firstName,
                            "last_name"         : lastName,
                            "birth_date"        : birthDay,
                            "gender"            : gender,
                            "email"             : email,
                            "state_id"          : "\(self.strStateID)",
                            "city_id"           : "\(self.strCityID)",
                            "zipcode"           : "\(self.pincodeTextField.text!)",
                            "area"              : "\(self.areaTextfield.text!)",
                            "flat_house_no"     : "\(self.flatTextField.text!)",
                            "flat_landmark"     : "\(self.landmarkTextField.text!)",
                            "gst_no"            : gst]

        print("paramEditProfile:-",paramEditProfile)
        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]
        
        // Add better error handling
        AF.request(url, method: .post, parameters: paramEditProfile, encoding: URLEncoding.default, headers: headers)
            .validate()
            .responseJSON { response in
                print(response)
                AppDelegate.shared.hideHUD()
                
                if let data = response.data, let rawString = String(data: data, encoding: .utf8) {
                    print("Raw response: \(rawString)")
                }
                
                switch response.result {
                case .success:
                    if let JSON = response.value as? NSDictionary {
                        print(JSON)
                        
                        if "\(JSON["code"]!)" == "200" && "\(JSON["status"]!)" == "1" {
                            UserDefaults.standard.set("1", forKey: Constants.PROFILE_EDIT)
                            
                            if AppDelegate.shared.isFromOTPView == true || AppDelegate.shared.isProfileEdit == "0" {
                                let alertController = UIAlertController(title: NSLocalizedString("Profile added", comment: ""), message: NSLocalizedString("Your profile added successfully", comment: ""), preferredStyle: .alert)
                                let settingsAction = UIAlertAction(title: NSLocalizedString("OK", comment: ""), style: .default) { (UIAlertAction) in
                                    if "\(UserDefaults.standard.value(forKey: Constants.VEHICLE_EDIT)!)" == "0" {
                                        AppDelegate.shared.isFromStartView = true
                                        let vc = VehicleVC.instantiate(appStoryboard: .Vehicle)
                                        self.navigationController?.pushViewController(vc, animated: true)
                                    } else {
                                        let vc = HomeTabVC.instantiate(appStoryboard: .Home)
                                        AppDelegate.shared.intPaymentTab = 0
                                        self.navigationController?.pushViewController(vc, animated: true)
                                    }
                                }
                                alertController.addAction(settingsAction)
                                self.present(alertController, animated: true, completion: nil)
                            } else {
                                let alertController = UIAlertController(title: NSLocalizedString("Profile edited", comment: ""), message: NSLocalizedString("Your profile edited successfully", comment: ""), preferredStyle: .alert)
                                let settingsAction = UIAlertAction(title: NSLocalizedString("OK", comment: ""), style: .default) { (UIAlertAction) in
                                    let vc = ProfileDetailsVC.instantiate(appStoryboard: .Profile)
                                    self.navigationController?.pushViewController(vc, animated: true)
                                }
                                alertController.addAction(settingsAction)
                                self.present(alertController, animated: true, completion: nil)
                            }
                        } else {
                            AppDelegate.shared.hideHUD()
                            if "\(JSON["code"]!)" == "100" {
                                AppDelegate.shared.apiKeyLogout()
                            } else {
                                // Show error message
                                self.alert(title: "Error", message: "\(JSON["msg"] ?? "Unknown error")")
                            }
                        }
                    } else {
                        self.alert(title: "Error", message: "Invalid response format")
                    }
                    break
                case .failure(let error):
                    print(error)
                    
                    // Provide more specific error messages
                    if let underlyingError = error.underlyingError as NSError? {
                        if underlyingError.domain == NSCocoaErrorDomain && underlyingError.code == 3840 {
                            self.alert(title: "Server Error", message: "The server returned an invalid response. Please try again later.")
                        } else {
                            self.alert(title: "Connection Error", message: "\(error.localizedDescription)")
                        }
                    } else {
                        self.alert(title: "Error", message: "\(error.localizedDescription)")
                    }
                    
                    AppDelegate.shared.hideHUD()
                }
            }
    }
    
    func viewProfile() {
        AppDelegate.shared.showHUD()
        let url = Constants.BASE_URL + API.VIEW_PROFILE
        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]
        AF.request(url, method: .get, parameters: nil, encoding:  URLEncoding.default, headers: headers).responseJSON { response in
            print(response)
            AppDelegate.shared.hideHUD()
            switch response.result {
            case .success:

                let JSON = response.value as! NSDictionary
                print(JSON)

                if "\(JSON["code"]!)" == "200" && "\(JSON["status"]!)" == "1" {

                    let jsonData = JSON["data"] as! NSDictionary
                    print(jsonData)

                    self.profileDetails.removeAll()
                    
                    // Fix: "profile_data" is a dictionary, not an array
                    if let profileData = jsonData["profile_data"] as? NSDictionary {
                        self.profileDetails.append(ProfileDetails(dic: profileData))
                    }
                    
                    // Update UI with profile data
                    if let item = self.profileDetails.first {
                        self.flatTextField.text = item.flat_house_no
                        self.areaTextfield.text = item.area
                        self.landmarkTextField.text = item.flat_landmark
                        self.pincodeTextField.text = item.zipcode
                        
                        self.stateLabel.text = item.state_name
                        self.strStateID = item.state_id
                        
                        self.cityLabel.text = item.city_name
                        self.strCityID = item.city_id
                        self.getCity()
                    }
                    
                } else {
                    if "\(JSON["code"]!)" == "100" {
                        AppDelegate.shared.apiKeyLogout()
                    }
                }
                break
            case .failure(let error):
                print(error)
                AppDelegate.shared.hideHUD()
            }
        }
    }
}
extension AddressVC: UITextFieldDelegate {
    
    func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
        
        if textField == pincodeTextField {
            let maxLength = 6
            let currentString: NSString = textField.text! as NSString
            let newString: NSString =
            currentString.replacingCharacters(in: range, with: string) as NSString
            return newString.length <= maxLength
        } else {
            return true
        }
    }
}
extension AddressVC:UIGestureRecognizerDelegate {
    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldBeRequiredToFailBy otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        return true
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="19455" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="19454"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="collection view cell content view" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--Map ChargerVC-->
        <scene sceneID="fbf-0h-czf">
            <objects>
                <viewController storyboardIdentifier="MapChargerVC" id="Wvk-ka-vHd" customClass="MapChargerVC" customModule="NXC_EV_Solutions" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Ev8-kZ-zOZ">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="bBW-Vh-D1k" customClass="GMSMapView">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            </view>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="SXO-fY-bIM">
                                <rect key="frame" x="309" y="532" width="50" height="50"/>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="50" id="BFT-sB-vup"/>
                                    <constraint firstAttribute="width" constant="50" id="jWE-dw-JwG"/>
                                </constraints>
                                <inset key="imageEdgeInsets" minX="12" minY="12" maxX="12" maxY="12"/>
                                <state key="normal" image="ic_whatsapp"/>
                                <connections>
                                    <action selector="btnWATapped:" destination="Wvk-ka-vHd" eventType="touchUpInside" id="JCv-7b-6sh"/>
                                </connections>
                            </button>
                            <stackView opaque="NO" contentMode="scaleToFill" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="Mba-vs-AEa">
                                <rect key="frame" x="8" y="8" width="359" height="44"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Su4-5Y-DFc">
                                        <rect key="frame" x="0.0" y="0.0" width="44" height="44"/>
                                        <color key="backgroundColor" name="PrimaryColor"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="44" id="cZB-oF-c3q"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="8" minY="8" maxX="8" maxY="8"/>
                                        <state key="normal" image="ic_menu"/>
                                        <connections>
                                            <action selector="menuAction:" destination="Wvk-ka-vHd" eventType="touchUpInside" id="31C-KO-250"/>
                                        </connections>
                                    </button>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="mfV-dt-9Wm">
                                        <rect key="frame" x="48" y="0.0" width="311" height="44"/>
                                        <subviews>
                                            <searchBar contentMode="redraw" placeholder="Search Charge Station" backgroundImage="nil" translatesAutoresizingMaskIntoConstraints="NO" id="IiN-sd-NDe">
                                                <rect key="frame" x="2" y="0.0" width="261" height="44"/>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <color key="tintColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <color key="barTintColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <textInputTraits key="textInputTraits"/>
                                            </searchBar>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Zv2-lT-LJy">
                                                <rect key="frame" x="265" y="0.0" width="44" height="44"/>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="44" id="rXH-Ec-SKh"/>
                                                </constraints>
                                                <inset key="imageEdgeInsets" minX="12" minY="12" maxX="12" maxY="12"/>
                                                <state key="normal" image="ic_filter"/>
                                                <connections>
                                                    <action selector="filterAction:" destination="Wvk-ka-vHd" eventType="touchUpInside" id="xtc-LF-uKf"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="Zv2-lT-LJy" firstAttribute="top" secondItem="mfV-dt-9Wm" secondAttribute="top" id="Gfh-1b-aI5"/>
                                            <constraint firstItem="Zv2-lT-LJy" firstAttribute="leading" secondItem="IiN-sd-NDe" secondAttribute="trailing" constant="2" id="Jnk-8t-uQm"/>
                                            <constraint firstAttribute="bottom" secondItem="IiN-sd-NDe" secondAttribute="bottom" id="NOH-Bj-lxl"/>
                                            <constraint firstAttribute="bottom" secondItem="Zv2-lT-LJy" secondAttribute="bottom" id="Pea-y0-yBb"/>
                                            <constraint firstItem="IiN-sd-NDe" firstAttribute="leading" secondItem="mfV-dt-9Wm" secondAttribute="leading" constant="2" id="QIL-c7-aik"/>
                                            <constraint firstItem="IiN-sd-NDe" firstAttribute="top" secondItem="mfV-dt-9Wm" secondAttribute="top" id="aWd-gX-lvM"/>
                                            <constraint firstAttribute="trailing" secondItem="Zv2-lT-LJy" secondAttribute="trailing" constant="2" id="ic7-ab-C7Q"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="height" constant="44" id="ynS-wN-qg8"/>
                                </constraints>
                            </stackView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="O5y-nB-BYp">
                                <rect key="frame" x="56" y="56" width="263" height="250"/>
                                <subviews>
                                    <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="-1" estimatedSectionHeaderHeight="-1" sectionFooterHeight="-1" estimatedSectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="QoS-xM-TrR">
                                        <rect key="frame" x="4" y="4" width="255" height="242"/>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <prototypes>
                                            <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" reuseIdentifier="cell" rowHeight="100" id="Y3s-75-ShB" customClass="ChargeStationSearchCell" customModule="NXC_EV_Solutions" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="44.5" width="255" height="100"/>
                                                <autoresizingMask key="autoresizingMask"/>
                                                <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="Y3s-75-ShB" id="Lnk-5I-CVr">
                                                    <rect key="frame" x="0.0" y="0.0" width="255" height="100"/>
                                                    <autoresizingMask key="autoresizingMask"/>
                                                    <subviews>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="KEi-WB-D49">
                                                            <rect key="frame" x="0.0" y="0.0" width="255" height="100"/>
                                                            <subviews>
                                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_searchEV" translatesAutoresizingMaskIntoConstraints="NO" id="yWV-EA-x9G">
                                                                    <rect key="frame" x="4" y="4" width="20" height="20"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="height" constant="20" id="0Af-S4-507"/>
                                                                        <constraint firstAttribute="width" constant="20" id="7mm-M9-XM5"/>
                                                                    </constraints>
                                                                </imageView>
                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="NXC Controls. Pvt. Ltd." textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="rev-Ms-TC2">
                                                                    <rect key="frame" x="26" y="4" width="225" height="20"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="height" constant="20" id="cJt-J6-ZDG"/>
                                                                    </constraints>
                                                                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="15"/>
                                                                    <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                    <nil key="highlightedColor"/>
                                                                </label>
                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="P6r-bc-NsK">
                                                                    <rect key="frame" x="4" y="26" width="247" height="65"/>
                                                                    <fontDescription key="fontDescription" type="system" pointSize="13"/>
                                                                    <color key="textColor" name="PrimarySelection"/>
                                                                    <nil key="highlightedColor"/>
                                                                </label>
                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="286-hh-Aq2">
                                                                    <rect key="frame" x="4" y="95" width="247" height="1"/>
                                                                    <color key="backgroundColor" name="PartitionBG"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="height" constant="1" id="E2H-MM-aQE"/>
                                                                    </constraints>
                                                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                    <nil key="textColor"/>
                                                                    <nil key="highlightedColor"/>
                                                                </label>
                                                            </subviews>
                                                            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                            <constraints>
                                                                <constraint firstItem="P6r-bc-NsK" firstAttribute="leading" secondItem="KEi-WB-D49" secondAttribute="leading" constant="4" id="ErE-mC-X5f"/>
                                                                <constraint firstItem="P6r-bc-NsK" firstAttribute="top" secondItem="rev-Ms-TC2" secondAttribute="bottom" constant="2" id="Ffz-yl-QxH"/>
                                                                <constraint firstItem="rev-Ms-TC2" firstAttribute="leading" secondItem="yWV-EA-x9G" secondAttribute="trailing" constant="2" id="IMP-Vv-3Sy"/>
                                                                <constraint firstItem="286-hh-Aq2" firstAttribute="top" secondItem="P6r-bc-NsK" secondAttribute="bottom" constant="4" id="KaX-Ia-i7F"/>
                                                                <constraint firstItem="yWV-EA-x9G" firstAttribute="leading" secondItem="KEi-WB-D49" secondAttribute="leading" constant="4" id="PBZ-Gq-Mhk"/>
                                                                <constraint firstAttribute="trailing" secondItem="rev-Ms-TC2" secondAttribute="trailing" constant="4" id="PfS-LQ-AKQ"/>
                                                                <constraint firstAttribute="trailing" secondItem="286-hh-Aq2" secondAttribute="trailing" constant="4" id="QUo-ds-cex"/>
                                                                <constraint firstItem="286-hh-Aq2" firstAttribute="leading" secondItem="KEi-WB-D49" secondAttribute="leading" constant="4" id="SBC-GT-py4"/>
                                                                <constraint firstItem="rev-Ms-TC2" firstAttribute="top" secondItem="KEi-WB-D49" secondAttribute="top" constant="4" id="kAo-gW-bfk"/>
                                                                <constraint firstAttribute="bottom" secondItem="286-hh-Aq2" secondAttribute="bottom" constant="4" id="qpx-4n-QF8"/>
                                                                <constraint firstAttribute="trailing" secondItem="P6r-bc-NsK" secondAttribute="trailing" constant="4" id="ucA-9b-2Z4"/>
                                                                <constraint firstItem="yWV-EA-x9G" firstAttribute="top" secondItem="KEi-WB-D49" secondAttribute="top" constant="4" id="xYO-4f-vtn"/>
                                                            </constraints>
                                                        </view>
                                                    </subviews>
                                                    <constraints>
                                                        <constraint firstItem="KEi-WB-D49" firstAttribute="leading" secondItem="Lnk-5I-CVr" secondAttribute="leading" id="4OC-cs-At9"/>
                                                        <constraint firstItem="KEi-WB-D49" firstAttribute="top" secondItem="Lnk-5I-CVr" secondAttribute="top" id="B3l-ia-6uo"/>
                                                        <constraint firstAttribute="trailing" secondItem="KEi-WB-D49" secondAttribute="trailing" id="KhK-pr-hE2"/>
                                                        <constraint firstAttribute="bottom" secondItem="KEi-WB-D49" secondAttribute="bottom" id="Wxp-zK-SdJ"/>
                                                    </constraints>
                                                </tableViewCellContentView>
                                                <connections>
                                                    <outlet property="addressLabel" destination="P6r-bc-NsK" id="H1G-JT-8GR"/>
                                                    <outlet property="chargeStationNameLabel" destination="rev-Ms-TC2" id="e8s-gV-p9B"/>
                                                    <outlet property="viewMain" destination="KEi-WB-D49" id="djA-Ui-c2n"/>
                                                </connections>
                                            </tableViewCell>
                                        </prototypes>
                                    </tableView>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="QoS-xM-TrR" firstAttribute="top" secondItem="O5y-nB-BYp" secondAttribute="top" constant="4" id="J34-6A-eQF"/>
                                    <constraint firstItem="QoS-xM-TrR" firstAttribute="leading" secondItem="O5y-nB-BYp" secondAttribute="leading" constant="4" id="RXj-v7-kEb"/>
                                    <constraint firstAttribute="trailing" secondItem="QoS-xM-TrR" secondAttribute="trailing" constant="4" id="VSb-AE-d2Z"/>
                                    <constraint firstAttribute="bottom" secondItem="QoS-xM-TrR" secondAttribute="bottom" constant="4" id="eUr-s1-bk3"/>
                                    <constraint firstAttribute="height" constant="250" id="tFu-ik-HFW"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Qn9-wE-jUb">
                                <rect key="frame" x="0.0" y="77" width="375" height="590"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_topArrow" translatesAutoresizingMaskIntoConstraints="NO" id="g7g-hj-9KU">
                                        <rect key="frame" x="180" y="4" width="15" height="15"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="15" id="1ij-d0-UeH"/>
                                            <constraint firstAttribute="width" constant="15" id="GWu-9d-XdP"/>
                                        </constraints>
                                    </imageView>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="qXt-k0-DKR">
                                        <rect key="frame" x="9.5" y="19" width="356" height="519"/>
                                        <subviews>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Y9T-Hk-gwr">
                                                <rect key="frame" x="0.0" y="0.0" width="356" height="215"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="Ggc-hT-x4W">
                                                        <rect key="frame" x="9" y="8" width="338" height="45"/>
                                                        <subviews>
                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_address" translatesAutoresizingMaskIntoConstraints="NO" id="rZs-2r-o60">
                                                                <rect key="frame" x="0.0" y="3.5" width="38" height="38"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="38" id="Lb1-KT-K9d"/>
                                                                    <constraint firstAttribute="height" constant="38" id="kKE-0H-Xmg"/>
                                                                </constraints>
                                                            </imageView>
                                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="2" translatesAutoresizingMaskIntoConstraints="NO" id="ylH-ao-eRF">
                                                                <rect key="frame" x="46" y="3" width="292" height="39.5"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" text="NXC Controls Pvt. Ltd." lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="eiS-G6-a9a">
                                                                        <rect key="frame" x="0.0" y="0.0" width="292" height="20.5"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="Nx1-3Q-7iH">
                                                                        <rect key="frame" x="0.0" y="22.5" width="292" height="17"/>
                                                                        <subviews>
                                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_location_icon" translatesAutoresizingMaskIntoConstraints="NO" id="ipV-ac-k7r">
                                                                                <rect key="frame" x="0.0" y="2.5" width="9" height="12"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="width" constant="9" id="a1G-E8-mm6"/>
                                                                                    <constraint firstAttribute="height" constant="12" id="hYp-2O-whb"/>
                                                                                </constraints>
                                                                            </imageView>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Nikol, Ahmedabad" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="fNT-DM-ReR">
                                                                                <rect key="frame" x="13" y="0.0" width="279" height="17"/>
                                                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                                <color key="textColor" name="SecondaryGrayText"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                        </subviews>
                                                                    </stackView>
                                                                </subviews>
                                                            </stackView>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="45" id="QjX-AO-Azf"/>
                                                        </constraints>
                                                    </stackView>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="1Z2-Mn-xQ7">
                                                        <rect key="frame" x="0.0" y="57" width="356" height="1"/>
                                                        <color key="backgroundColor" name="PartitionBG"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="1" id="FW9-pf-80w"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                        <nil key="textColor"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <stackView opaque="NO" contentMode="scaleToFill" alignment="top" spacing="6" translatesAutoresizingMaskIntoConstraints="NO" id="UFZ-Xz-gjO">
                                                        <rect key="frame" x="9" y="66" width="338" height="40"/>
                                                        <subviews>
                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_ticked" translatesAutoresizingMaskIntoConstraints="NO" id="ZD8-Y3-8ys">
                                                                <rect key="frame" x="0.0" y="0.0" width="18" height="18"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="18" id="562-7L-bjt"/>
                                                                    <constraint firstAttribute="height" constant="18" id="ss6-h5-UWI"/>
                                                                </constraints>
                                                            </imageView>
                                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" translatesAutoresizingMaskIntoConstraints="NO" id="JAN-d8-hdm">
                                                                <rect key="frame" x="24" y="0.0" width="134" height="34"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" text="12:00 AM - 11:59 PM" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Wki-Lq-QVH">
                                                                        <rect key="frame" x="0.0" y="0.0" width="134" height="17"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" text="Open" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="9T6-3P-Foa">
                                                                        <rect key="frame" x="0.0" y="17" width="134" height="17"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <color key="textColor" name="WalletAddText"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                            <stackView opaque="NO" alpha="0.0" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" translatesAutoresizingMaskIntoConstraints="NO" id="bTO-Cv-soe">
                                                                <rect key="frame" x="164" y="0.0" width="174" height="40"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" text="~ ₹ 0.40/kWh" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="uLv-cR-zAn">
                                                                        <rect key="frame" x="0.0" y="0.0" width="174" height="40"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="40" id="OSe-Ly-DpH"/>
                                                            <constraint firstItem="bTO-Cv-soe" firstAttribute="height" secondItem="UFZ-Xz-gjO" secondAttribute="height" id="bwW-ES-m9a"/>
                                                        </constraints>
                                                    </stackView>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="5f3-uK-pzZ">
                                                        <rect key="frame" x="0.0" y="110" width="356" height="1"/>
                                                        <color key="backgroundColor" name="PartitionBG"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="1" id="2ZY-CM-jM1"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                        <nil key="textColor"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="8IH-cr-szb">
                                                        <rect key="frame" x="9" y="119" width="338" height="88"/>
                                                        <subviews>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="JbP-Js-M17">
                                                                <rect key="frame" x="0.0" y="5" width="77.5" height="78"/>
                                                                <subviews>
                                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="rsB-jk-DRe">
                                                                        <rect key="frame" x="3" y="3" width="71.5" height="72"/>
                                                                        <subviews>
                                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_3 pin" translatesAutoresizingMaskIntoConstraints="NO" id="d1B-57-O2K">
                                                                                <rect key="frame" x="0.0" y="0.0" width="71.5" height="54"/>
                                                                            </imageView>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Commando" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="zQU-ea-0nL">
                                                                                <rect key="frame" x="0.0" y="54" width="71.5" height="18"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="height" constant="18" id="e0t-dr-uhj"/>
                                                                                </constraints>
                                                                                <fontDescription key="fontDescription" type="system" pointSize="13"/>
                                                                                <color key="textColor" red="0.1529411765" green="0.14117647059999999" blue="0.14117647059999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                        </subviews>
                                                                    </stackView>
                                                                </subviews>
                                                                <color key="backgroundColor" red="0.96470588239999999" green="0.96470588239999999" blue="0.96862745100000003" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" secondItem="JbP-Js-M17" secondAttribute="height" multiplier="1:1" id="43f-DF-GHA"/>
                                                                    <constraint firstItem="rsB-jk-DRe" firstAttribute="leading" secondItem="JbP-Js-M17" secondAttribute="leading" constant="3" id="Neh-ZO-8cL"/>
                                                                    <constraint firstItem="rsB-jk-DRe" firstAttribute="top" secondItem="JbP-Js-M17" secondAttribute="top" constant="3" id="QHa-Xu-62I"/>
                                                                    <constraint firstAttribute="trailing" secondItem="rsB-jk-DRe" secondAttribute="trailing" constant="3" id="Xk5-ib-FQD"/>
                                                                    <constraint firstAttribute="bottom" secondItem="rsB-jk-DRe" secondAttribute="bottom" constant="3" id="fLp-0V-XwD"/>
                                                                </constraints>
                                                            </view>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="En0-35-AnC">
                                                                <rect key="frame" x="89.5" y="5" width="78" height="78"/>
                                                                <subviews>
                                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="r7p-sb-Ure">
                                                                        <rect key="frame" x="3" y="3" width="72" height="72"/>
                                                                        <subviews>
                                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_3 pin" translatesAutoresizingMaskIntoConstraints="NO" id="dnY-xO-NJG">
                                                                                <rect key="frame" x="0.0" y="0.0" width="72" height="54"/>
                                                                            </imageView>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="AC Type 2" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Fq5-dI-BBm">
                                                                                <rect key="frame" x="5" y="54" width="62" height="18"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="height" constant="18" id="1vx-RG-aZx"/>
                                                                                </constraints>
                                                                                <fontDescription key="fontDescription" type="system" pointSize="13"/>
                                                                                <color key="textColor" red="0.1529411765" green="0.14117647059999999" blue="0.14117647059999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                        </subviews>
                                                                    </stackView>
                                                                </subviews>
                                                                <color key="backgroundColor" red="0.96470588239999999" green="0.96470588239999999" blue="0.96862745100000003" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                <constraints>
                                                                    <constraint firstItem="r7p-sb-Ure" firstAttribute="top" secondItem="En0-35-AnC" secondAttribute="top" constant="3" id="3Eo-Gt-mMH"/>
                                                                    <constraint firstAttribute="width" secondItem="En0-35-AnC" secondAttribute="height" multiplier="1:1" id="adI-k8-65V"/>
                                                                    <constraint firstAttribute="trailing" secondItem="r7p-sb-Ure" secondAttribute="trailing" constant="3" id="hLy-Pw-xMd"/>
                                                                    <constraint firstAttribute="bottom" secondItem="r7p-sb-Ure" secondAttribute="bottom" constant="3" id="r8b-Am-TKf"/>
                                                                    <constraint firstItem="r7p-sb-Ure" firstAttribute="leading" secondItem="En0-35-AnC" secondAttribute="leading" constant="3" id="sy5-BG-kgC"/>
                                                                </constraints>
                                                            </view>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="fIm-c3-BQK">
                                                                <rect key="frame" x="179.5" y="5" width="78" height="78"/>
                                                                <subviews>
                                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="Q0D-Fm-GCR">
                                                                        <rect key="frame" x="3" y="3" width="72" height="72"/>
                                                                        <subviews>
                                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_3 pin" translatesAutoresizingMaskIntoConstraints="NO" id="ZrE-bF-6yM">
                                                                                <rect key="frame" x="0.0" y="0.0" width="72" height="54"/>
                                                                            </imageView>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="CHAdeMO" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="bhx-jm-CwN">
                                                                                <rect key="frame" x="4" y="54" width="64" height="18"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="height" constant="18" id="0uR-yV-Vi4"/>
                                                                                </constraints>
                                                                                <fontDescription key="fontDescription" type="system" pointSize="13"/>
                                                                                <color key="textColor" red="0.15294117647058825" green="0.14117647058823529" blue="0.14117647058823529" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                        </subviews>
                                                                    </stackView>
                                                                </subviews>
                                                                <color key="backgroundColor" red="0.96470588235294119" green="0.96470588235294119" blue="0.96862745098039216" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                <constraints>
                                                                    <constraint firstItem="Q0D-Fm-GCR" firstAttribute="top" secondItem="fIm-c3-BQK" secondAttribute="top" constant="3" id="MSy-QJ-r8i"/>
                                                                    <constraint firstAttribute="trailing" secondItem="Q0D-Fm-GCR" secondAttribute="trailing" constant="3" id="Ryg-N9-tSA"/>
                                                                    <constraint firstAttribute="bottom" secondItem="Q0D-Fm-GCR" secondAttribute="bottom" constant="3" id="ass-le-dgK"/>
                                                                    <constraint firstItem="Q0D-Fm-GCR" firstAttribute="leading" secondItem="fIm-c3-BQK" secondAttribute="leading" constant="3" id="dup-xO-Dcr"/>
                                                                    <constraint firstAttribute="width" secondItem="fIm-c3-BQK" secondAttribute="height" multiplier="1:1" id="eBv-gg-BbJ"/>
                                                                </constraints>
                                                            </view>
                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_rightDirections" translatesAutoresizingMaskIntoConstraints="NO" id="tju-Ml-rnD">
                                                                <rect key="frame" x="269.5" y="32" width="68.5" height="24"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="24" id="oti-Iq-MH7"/>
                                                                </constraints>
                                                            </imageView>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstItem="JbP-Js-M17" firstAttribute="width" secondItem="8IH-cr-szb" secondAttribute="width" multiplier="0.23" id="BB2-LS-mHV"/>
                                                            <constraint firstItem="En0-35-AnC" firstAttribute="width" secondItem="8IH-cr-szb" secondAttribute="width" multiplier="0.23" id="d1g-YU-fY1"/>
                                                            <constraint firstItem="fIm-c3-BQK" firstAttribute="width" secondItem="8IH-cr-szb" secondAttribute="width" multiplier="0.23" id="wu2-G2-U1m"/>
                                                        </constraints>
                                                    </stackView>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="e4p-QG-DZY">
                                                        <rect key="frame" x="0.0" y="214" width="356" height="1"/>
                                                        <color key="backgroundColor" name="PartitionBG"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="1" id="cja-Qr-ags"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                        <nil key="textColor"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="12q-hM-0k9">
                                                        <rect key="frame" x="288" y="138" width="50" height="50"/>
                                                        <constraints>
                                                            <constraint firstAttribute="width" constant="50" id="VmD-Tt-rro"/>
                                                            <constraint firstAttribute="height" constant="50" id="jTc-xY-ggq"/>
                                                        </constraints>
                                                        <color key="tintColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                        <connections>
                                                            <action selector="connectorMoreDetails:" destination="Wvk-ka-vHd" eventType="touchUpInside" id="pUN-Cn-pIB"/>
                                                        </connections>
                                                    </button>
                                                </subviews>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <constraints>
                                                    <constraint firstItem="12q-hM-0k9" firstAttribute="centerY" secondItem="tju-Ml-rnD" secondAttribute="centerY" id="1AE-Oq-90h"/>
                                                    <constraint firstItem="Ggc-hT-x4W" firstAttribute="top" secondItem="Y9T-Hk-gwr" secondAttribute="top" constant="8" id="4dK-5v-WP0"/>
                                                    <constraint firstItem="Ggc-hT-x4W" firstAttribute="centerX" secondItem="Y9T-Hk-gwr" secondAttribute="centerX" id="AIJ-Qe-H5C"/>
                                                    <constraint firstItem="8IH-cr-szb" firstAttribute="centerX" secondItem="Y9T-Hk-gwr" secondAttribute="centerX" id="Goc-QS-wvY"/>
                                                    <constraint firstItem="UFZ-Xz-gjO" firstAttribute="width" secondItem="Y9T-Hk-gwr" secondAttribute="width" multiplier="0.95" id="Jyc-7c-mZF"/>
                                                    <constraint firstItem="5f3-uK-pzZ" firstAttribute="top" secondItem="UFZ-Xz-gjO" secondAttribute="bottom" constant="4" id="L3c-U9-cRS"/>
                                                    <constraint firstItem="1Z2-Mn-xQ7" firstAttribute="top" secondItem="Ggc-hT-x4W" secondAttribute="bottom" constant="4" id="N3u-5B-URD"/>
                                                    <constraint firstItem="UFZ-Xz-gjO" firstAttribute="centerX" secondItem="Y9T-Hk-gwr" secondAttribute="centerX" id="O0i-q4-OdB"/>
                                                    <constraint firstItem="1Z2-Mn-xQ7" firstAttribute="centerX" secondItem="Y9T-Hk-gwr" secondAttribute="centerX" id="Rbp-5i-3xQ"/>
                                                    <constraint firstItem="e4p-QG-DZY" firstAttribute="centerX" secondItem="Y9T-Hk-gwr" secondAttribute="centerX" id="as7-Z2-va5"/>
                                                    <constraint firstAttribute="bottom" secondItem="e4p-QG-DZY" secondAttribute="bottom" id="cgU-O7-q3E"/>
                                                    <constraint firstItem="5f3-uK-pzZ" firstAttribute="width" secondItem="Y9T-Hk-gwr" secondAttribute="width" id="ddl-ez-x59"/>
                                                    <constraint firstItem="8IH-cr-szb" firstAttribute="top" secondItem="5f3-uK-pzZ" secondAttribute="bottom" constant="8" id="enn-8P-egF"/>
                                                    <constraint firstItem="12q-hM-0k9" firstAttribute="centerX" secondItem="tju-Ml-rnD" secondAttribute="centerX" id="fBL-wf-Ugr"/>
                                                    <constraint firstItem="8IH-cr-szb" firstAttribute="width" secondItem="Y9T-Hk-gwr" secondAttribute="width" multiplier="0.95" id="jmu-0a-wMi"/>
                                                    <constraint firstItem="1Z2-Mn-xQ7" firstAttribute="width" secondItem="Y9T-Hk-gwr" secondAttribute="width" id="k6B-6d-y04"/>
                                                    <constraint firstItem="e4p-QG-DZY" firstAttribute="width" secondItem="Y9T-Hk-gwr" secondAttribute="width" id="sA2-oT-hbn"/>
                                                    <constraint firstItem="Ggc-hT-x4W" firstAttribute="width" secondItem="Y9T-Hk-gwr" secondAttribute="width" multiplier="0.95" id="uRQ-1t-Gpa"/>
                                                    <constraint firstItem="5f3-uK-pzZ" firstAttribute="centerX" secondItem="Y9T-Hk-gwr" secondAttribute="centerX" id="ubf-20-H9s"/>
                                                    <constraint firstItem="UFZ-Xz-gjO" firstAttribute="top" secondItem="1Z2-Mn-xQ7" secondAttribute="bottom" constant="8" id="xF4-bi-uXc"/>
                                                    <constraint firstAttribute="bottom" secondItem="8IH-cr-szb" secondAttribute="bottom" constant="8" id="zyL-BE-AQN"/>
                                                </constraints>
                                            </view>
                                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="prototypes" style="plain" separatorStyle="none" allowsSelection="NO" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="jZD-8s-NOa">
                                                <rect key="frame" x="0.0" y="219" width="356" height="300"/>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="300" id="Ozh-vW-ZuX"/>
                                                </constraints>
                                                <prototypes>
                                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" reuseIdentifier="cell" id="6UT-SE-zVp" customClass="ConnectorCell" customModule="NXC_EV_Solutions" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="44.5" width="356" height="99"/>
                                                        <autoresizingMask key="autoresizingMask"/>
                                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="6UT-SE-zVp" id="WK8-z7-frC">
                                                            <rect key="frame" x="0.0" y="0.0" width="356" height="99"/>
                                                            <autoresizingMask key="autoresizingMask"/>
                                                            <subviews>
                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="UTg-Tt-jCc">
                                                                    <rect key="frame" x="4" y="4" width="348" height="91"/>
                                                                    <subviews>
                                                                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="e3o-aG-Q0P">
                                                                            <rect key="frame" x="4" y="0.0" width="340" height="85"/>
                                                                            <subviews>
                                                                                <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="NSs-Qj-d7N">
                                                                                    <rect key="frame" x="0.0" y="0.0" width="340" height="24"/>
                                                                                    <subviews>
                                                                                        <stackView opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="J9j-B0-pKr">
                                                                                            <rect key="frame" x="0.0" y="0.0" width="129" height="24"/>
                                                                                            <subviews>
                                                                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_AC Type-1" translatesAutoresizingMaskIntoConstraints="NO" id="IeP-u4-6WQ">
                                                                                                    <rect key="frame" x="0.0" y="0.0" width="30" height="24"/>
                                                                                                    <constraints>
                                                                                                        <constraint firstAttribute="height" constant="30" id="1BM-HY-elq"/>
                                                                                                        <constraint firstAttribute="width" constant="30" id="D1P-WQ-Fvn"/>
                                                                                                    </constraints>
                                                                                                </imageView>
                                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Commando" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="btT-06-Cyy">
                                                                                                    <rect key="frame" x="30" y="0.0" width="99" height="24"/>
                                                                                                    <constraints>
                                                                                                        <constraint firstAttribute="height" constant="24" id="gvv-fu-wke"/>
                                                                                                    </constraints>
                                                                                                    <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                                                    <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                                    <nil key="highlightedColor"/>
                                                                                                </label>
                                                                                            </subviews>
                                                                                        </stackView>
                                                                                        <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="VMo-Hi-yp5">
                                                                                            <rect key="frame" x="133" y="0.0" width="119" height="24"/>
                                                                                            <subviews>
                                                                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_charger_icon" translatesAutoresizingMaskIntoConstraints="NO" id="Pub-z4-05L">
                                                                                                    <rect key="frame" x="0.0" y="3" width="18" height="18"/>
                                                                                                    <constraints>
                                                                                                        <constraint firstAttribute="height" constant="18" id="FDf-Fl-Lo3"/>
                                                                                                        <constraint firstAttribute="width" constant="18" id="Pwf-T4-C2I"/>
                                                                                                    </constraints>
                                                                                                </imageView>
                                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="DC" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="pSf-Ob-Jhw">
                                                                                                    <rect key="frame" x="22" y="0.0" width="97" height="24"/>
                                                                                                    <constraints>
                                                                                                        <constraint firstAttribute="height" constant="24" id="Pvj-G8-xQz"/>
                                                                                                    </constraints>
                                                                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                                                    <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                                    <nil key="highlightedColor"/>
                                                                                                </label>
                                                                                            </subviews>
                                                                                        </stackView>
                                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" text="~ ₹ 0.40/kWh" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="zso-un-qmD">
                                                                                            <rect key="frame" x="211" y="0.0" width="129" height="24"/>
                                                                                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                                            <nil key="textColor"/>
                                                                                            <nil key="highlightedColor"/>
                                                                                        </label>
                                                                                    </subviews>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="height" constant="24" id="BEQ-ae-EP9"/>
                                                                                        <constraint firstItem="J9j-B0-pKr" firstAttribute="width" secondItem="NSs-Qj-d7N" secondAttribute="width" multiplier="0.38" id="cvT-jW-vu2"/>
                                                                                        <constraint firstItem="VMo-Hi-yp5" firstAttribute="width" secondItem="NSs-Qj-d7N" secondAttribute="width" multiplier="0.35" id="s1E-ba-29B"/>
                                                                                    </constraints>
                                                                                </stackView>
                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="i3z-TA-ROI">
                                                                                    <rect key="frame" x="0.0" y="28" width="340" height="1"/>
                                                                                    <color key="backgroundColor" name="PartitionBG"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="height" constant="1" id="qlv-r2-grS"/>
                                                                                    </constraints>
                                                                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                                    <nil key="textColor"/>
                                                                                    <nil key="highlightedColor"/>
                                                                                </label>
                                                                                <stackView opaque="NO" contentMode="scaleToFill" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="zkm-Wq-Ywq">
                                                                                    <rect key="frame" x="0.0" y="33" width="221" height="24"/>
                                                                                    <subviews>
                                                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_connector" translatesAutoresizingMaskIntoConstraints="NO" id="Ds8-3d-HWR">
                                                                                            <rect key="frame" x="0.0" y="0.0" width="30" height="24"/>
                                                                                            <constraints>
                                                                                                <constraint firstAttribute="height" constant="30" id="JRe-pk-fWg"/>
                                                                                                <constraint firstAttribute="width" constant="30" id="Z3l-oN-ITR"/>
                                                                                            </constraints>
                                                                                        </imageView>
                                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Connector Status" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="cxx-QU-3OM">
                                                                                            <rect key="frame" x="34" y="0.0" width="187" height="24"/>
                                                                                            <constraints>
                                                                                                <constraint firstAttribute="height" constant="24" id="9q7-j0-RRq"/>
                                                                                            </constraints>
                                                                                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                                            <color key="textColor" name="Primary"/>
                                                                                            <nil key="highlightedColor"/>
                                                                                        </label>
                                                                                    </subviews>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="height" constant="24" id="72B-Y8-rom"/>
                                                                                    </constraints>
                                                                                </stackView>
                                                                                <stackView opaque="NO" contentMode="scaleToFill" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="Ia2-5y-jLR">
                                                                                    <rect key="frame" x="0.0" y="61" width="340" height="24"/>
                                                                                    <subviews>
                                                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="65D-SO-hZg">
                                                                                            <rect key="frame" x="0.0" y="0.0" width="77.5" height="24"/>
                                                                                            <subviews>
                                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="2 Available" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="4HJ-Qn-SQe">
                                                                                                    <rect key="frame" x="4" y="4" width="69.5" height="16"/>
                                                                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                                                    <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                                    <nil key="highlightedColor"/>
                                                                                                </label>
                                                                                            </subviews>
                                                                                            <color key="backgroundColor" red="0.19607843137254902" green="0.58431372549019611" blue="0.18823529411764706" alpha="0.29999999999999999" colorSpace="custom" customColorSpace="sRGB"/>
                                                                                            <constraints>
                                                                                                <constraint firstAttribute="bottom" secondItem="4HJ-Qn-SQe" secondAttribute="bottom" constant="4" id="50u-wA-6ba"/>
                                                                                                <constraint firstAttribute="trailing" secondItem="4HJ-Qn-SQe" secondAttribute="trailing" constant="4" id="QRt-Lk-csK"/>
                                                                                                <constraint firstItem="4HJ-Qn-SQe" firstAttribute="leading" secondItem="65D-SO-hZg" secondAttribute="leading" constant="4" id="Y6e-CK-Rsp"/>
                                                                                                <constraint firstItem="4HJ-Qn-SQe" firstAttribute="top" secondItem="65D-SO-hZg" secondAttribute="top" constant="4" id="qHq-FX-W52"/>
                                                                                            </constraints>
                                                                                        </view>
                                                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="zsb-9a-ej6">
                                                                                            <rect key="frame" x="81.5" y="0.0" width="102" height="24"/>
                                                                                            <subviews>
                                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="2 UnAvailable" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="hOu-6R-Mou">
                                                                                                    <rect key="frame" x="4" y="4" width="94" height="16"/>
                                                                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                                                    <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                                    <nil key="highlightedColor"/>
                                                                                                </label>
                                                                                            </subviews>
                                                                                            <color key="backgroundColor" red="0.90196078431372551" green="0.0" blue="0.0" alpha="0.29999999999999999" colorSpace="custom" customColorSpace="sRGB"/>
                                                                                            <constraints>
                                                                                                <constraint firstItem="hOu-6R-Mou" firstAttribute="leading" secondItem="zsb-9a-ej6" secondAttribute="leading" constant="4" id="9JF-YY-E4S"/>
                                                                                                <constraint firstItem="hOu-6R-Mou" firstAttribute="top" secondItem="zsb-9a-ej6" secondAttribute="top" constant="4" id="A0p-Xn-NHq"/>
                                                                                                <constraint firstAttribute="trailing" secondItem="hOu-6R-Mou" secondAttribute="trailing" constant="4" id="Zhw-kY-GeB"/>
                                                                                                <constraint firstAttribute="bottom" secondItem="hOu-6R-Mou" secondAttribute="bottom" constant="4" id="s8m-qt-JQm"/>
                                                                                            </constraints>
                                                                                        </view>
                                                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="AVD-rW-0Wh">
                                                                                            <rect key="frame" x="187.5" y="0.0" width="68" height="24"/>
                                                                                            <subviews>
                                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="2 In Use" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="t8c-B0-8rj">
                                                                                                    <rect key="frame" x="4" y="4" width="60" height="16"/>
                                                                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                                                    <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                                    <nil key="highlightedColor"/>
                                                                                                </label>
                                                                                            </subviews>
                                                                                            <color key="backgroundColor" red="0.92941176470588238" green="0.37647058823529411" blue="0.13725490196078433" alpha="0.29999999999999999" colorSpace="custom" customColorSpace="sRGB"/>
                                                                                            <constraints>
                                                                                                <constraint firstAttribute="trailing" secondItem="t8c-B0-8rj" secondAttribute="trailing" constant="4" id="PKl-A4-vam"/>
                                                                                                <constraint firstItem="t8c-B0-8rj" firstAttribute="top" secondItem="AVD-rW-0Wh" secondAttribute="top" constant="4" id="fV7-tr-6G1"/>
                                                                                                <constraint firstAttribute="bottom" secondItem="t8c-B0-8rj" secondAttribute="bottom" constant="4" id="h1S-my-ckm"/>
                                                                                                <constraint firstItem="t8c-B0-8rj" firstAttribute="leading" secondItem="AVD-rW-0Wh" secondAttribute="leading" constant="4" id="qLf-Sp-oLW"/>
                                                                                            </constraints>
                                                                                        </view>
                                                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Qs3-Cy-Ud8">
                                                                                            <rect key="frame" x="259.5" y="0.0" width="80.5" height="24"/>
                                                                                            <subviews>
                                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="2 Reserved" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="UFq-A4-L9U">
                                                                                                    <rect key="frame" x="4" y="4" width="72.5" height="16"/>
                                                                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                                                    <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                                    <nil key="highlightedColor"/>
                                                                                                </label>
                                                                                            </subviews>
                                                                                            <color key="backgroundColor" red="0.0" green="0.45882352941176469" blue="0.8901960784313725" alpha="0.14999999999999999" colorSpace="custom" customColorSpace="sRGB"/>
                                                                                            <constraints>
                                                                                                <constraint firstAttribute="trailing" secondItem="UFq-A4-L9U" secondAttribute="trailing" constant="4" id="Fxd-eM-J4X"/>
                                                                                                <constraint firstItem="UFq-A4-L9U" firstAttribute="leading" secondItem="Qs3-Cy-Ud8" secondAttribute="leading" constant="4" id="NGp-Ui-6OW"/>
                                                                                                <constraint firstItem="UFq-A4-L9U" firstAttribute="top" secondItem="Qs3-Cy-Ud8" secondAttribute="top" constant="4" id="cPT-Cc-m5B"/>
                                                                                                <constraint firstAttribute="bottom" secondItem="UFq-A4-L9U" secondAttribute="bottom" constant="4" id="gGc-Xa-zyk"/>
                                                                                            </constraints>
                                                                                        </view>
                                                                                    </subviews>
                                                                                    <constraints>
                                                                                        <constraint firstItem="AVD-rW-0Wh" firstAttribute="width" secondItem="Ia2-5y-jLR" secondAttribute="width" multiplier="0.2" id="VYQ-kZ-zKv"/>
                                                                                        <constraint firstItem="zsb-9a-ej6" firstAttribute="width" secondItem="Ia2-5y-jLR" secondAttribute="width" multiplier="0.3" id="YsW-ap-VqY"/>
                                                                                        <constraint firstAttribute="height" constant="24" id="gWO-IL-Sjd"/>
                                                                                    </constraints>
                                                                                </stackView>
                                                                            </subviews>
                                                                            <constraints>
                                                                                <constraint firstItem="zkm-Wq-Ywq" firstAttribute="width" secondItem="e3o-aG-Q0P" secondAttribute="width" multiplier="0.65" id="jB8-SU-h7a"/>
                                                                            </constraints>
                                                                        </stackView>
                                                                        <label opaque="NO" userInteractionEnabled="NO" alpha="0.0" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="s37-Zh-qER">
                                                                            <rect key="frame" x="0.0" y="90.5" width="348" height="0.5"/>
                                                                            <color key="backgroundColor" name="PrimaryTextColor"/>
                                                                            <constraints>
                                                                                <constraint firstAttribute="height" constant="0.5" id="EUH-55-h1c"/>
                                                                            </constraints>
                                                                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                            <nil key="textColor"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                    </subviews>
                                                                    <color key="backgroundColor" red="0.96470588235294119" green="0.96470588235294119" blue="0.96862745098039216" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                    <constraints>
                                                                        <constraint firstItem="e3o-aG-Q0P" firstAttribute="leading" secondItem="UTg-Tt-jCc" secondAttribute="leading" constant="4" id="6zc-zI-8Vm"/>
                                                                        <constraint firstItem="s37-Zh-qER" firstAttribute="leading" secondItem="UTg-Tt-jCc" secondAttribute="leading" id="97e-tm-4hc"/>
                                                                        <constraint firstAttribute="trailing" secondItem="e3o-aG-Q0P" secondAttribute="trailing" constant="4" id="Krb-ty-ePa"/>
                                                                        <constraint firstAttribute="bottom" secondItem="s37-Zh-qER" secondAttribute="bottom" id="RCY-ev-WhC"/>
                                                                        <constraint firstAttribute="trailing" secondItem="s37-Zh-qER" secondAttribute="trailing" id="ow6-p8-sRB"/>
                                                                        <constraint firstItem="e3o-aG-Q0P" firstAttribute="top" secondItem="UTg-Tt-jCc" secondAttribute="top" id="uD3-Nf-NAz"/>
                                                                        <constraint firstAttribute="bottom" secondItem="e3o-aG-Q0P" secondAttribute="bottom" constant="6" id="x8j-Tz-Kd5"/>
                                                                    </constraints>
                                                                </view>
                                                            </subviews>
                                                            <constraints>
                                                                <constraint firstItem="UTg-Tt-jCc" firstAttribute="top" secondItem="WK8-z7-frC" secondAttribute="top" constant="4" id="Hws-db-Uc2"/>
                                                                <constraint firstAttribute="trailing" secondItem="UTg-Tt-jCc" secondAttribute="trailing" constant="4" id="bGs-VJ-hgf"/>
                                                                <constraint firstAttribute="bottom" secondItem="UTg-Tt-jCc" secondAttribute="bottom" constant="4" id="bdc-40-PCn"/>
                                                                <constraint firstItem="UTg-Tt-jCc" firstAttribute="leading" secondItem="WK8-z7-frC" secondAttribute="leading" constant="4" id="onB-0x-XUh"/>
                                                            </constraints>
                                                        </tableViewCellContentView>
                                                        <connections>
                                                            <outlet property="imgConnectorName" destination="IeP-u4-6WQ" id="uto-he-sVb"/>
                                                            <outlet property="imgConnectorType" destination="Pub-z4-05L" id="qYu-jC-881"/>
                                                            <outlet property="lblApproxPrice" destination="zso-un-qmD" id="rNm-5C-lIm"/>
                                                            <outlet property="lblAvail" destination="4HJ-Qn-SQe" id="OiZ-IO-7TJ"/>
                                                            <outlet property="lblBooked" destination="UFq-A4-L9U" id="dEd-UJ-JuZ"/>
                                                            <outlet property="lblConnectorName" destination="btT-06-Cyy" id="3SV-41-yf9"/>
                                                            <outlet property="lblConnectorType" destination="pSf-Ob-Jhw" id="jCx-CD-iGf"/>
                                                            <outlet property="lblInUse" destination="t8c-B0-8rj" id="u9A-GG-TKV"/>
                                                            <outlet property="lblTotalConnectorStatus" destination="cxx-QU-3OM" id="M7V-wr-3nU"/>
                                                            <outlet property="lblUnavail" destination="hOu-6R-Mou" id="Krb-b7-Aae"/>
                                                            <outlet property="viewAvail" destination="65D-SO-hZg" id="BEX-kq-fsF"/>
                                                            <outlet property="viewBooked" destination="Qs3-Cy-Ud8" id="7bo-39-F5O"/>
                                                            <outlet property="viewInUse" destination="AVD-rW-0Wh" id="sFo-eF-Gbv"/>
                                                            <outlet property="viewMain" destination="UTg-Tt-jCc" id="o9g-jO-im7"/>
                                                            <outlet property="viewUnavail" destination="zsb-9a-ej6" id="q7L-vZ-nzm"/>
                                                        </connections>
                                                    </tableViewCell>
                                                </prototypes>
                                            </tableView>
                                        </subviews>
                                    </stackView>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="dtu-qe-Dyv">
                                        <rect key="frame" x="0.0" y="546" width="375" height="44"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="8 km . 30 min" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Cje-Vi-T3X">
                                                <rect key="frame" x="12" y="13" width="97" height="18"/>
                                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="15"/>
                                                <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Get Directions" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Ri2-ij-h1G">
                                                <rect key="frame" x="222" y="13" width="102.5" height="18"/>
                                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="15"/>
                                                <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_direction" translatesAutoresizingMaskIntoConstraints="NO" id="NUQ-Ni-tOz">
                                                <rect key="frame" x="336.5" y="9" width="26.5" height="26"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" secondItem="NUQ-Ni-tOz" secondAttribute="height" multiplier="1:1" id="ptv-uD-GH3"/>
                                                </constraints>
                                            </imageView>
                                        </subviews>
                                        <color key="backgroundColor" name="Primary"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="44" id="A7f-bx-Zu2"/>
                                            <constraint firstItem="Ri2-ij-h1G" firstAttribute="centerY" secondItem="dtu-qe-Dyv" secondAttribute="centerY" id="KpR-kX-8i1"/>
                                            <constraint firstItem="NUQ-Ni-tOz" firstAttribute="leading" secondItem="Ri2-ij-h1G" secondAttribute="trailing" constant="12" id="LSC-1u-Vo6"/>
                                            <constraint firstItem="Cje-Vi-T3X" firstAttribute="centerY" secondItem="dtu-qe-Dyv" secondAttribute="centerY" id="NzF-k5-TkZ"/>
                                            <constraint firstItem="NUQ-Ni-tOz" firstAttribute="height" secondItem="dtu-qe-Dyv" secondAttribute="height" multiplier="0.6" id="S1R-Zz-nVD"/>
                                            <constraint firstAttribute="trailing" secondItem="NUQ-Ni-tOz" secondAttribute="trailing" constant="12" id="TcU-sQ-ReO"/>
                                            <constraint firstItem="Cje-Vi-T3X" firstAttribute="leading" secondItem="dtu-qe-Dyv" secondAttribute="leading" constant="12" id="eLV-jX-R0h"/>
                                            <constraint firstItem="NUQ-Ni-tOz" firstAttribute="centerY" secondItem="dtu-qe-Dyv" secondAttribute="centerY" id="zjk-Qr-p13"/>
                                        </constraints>
                                    </view>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="C7A-Ed-Bvk">
                                        <rect key="frame" x="187.5" y="546" width="187.5" height="44"/>
                                        <color key="tintColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                        <connections>
                                            <action selector="getDirectionAction:" destination="Wvk-ka-vHd" eventType="touchUpInside" id="h5Q-EC-llw"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="6fw-jl-dUj">
                                        <rect key="frame" x="165.5" y="-10.5" width="44" height="44"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="44" id="6I2-Qr-K99"/>
                                            <constraint firstAttribute="width" constant="44" id="Hea-B7-tRj"/>
                                        </constraints>
                                        <color key="tintColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                        <connections>
                                            <action selector="showDetails:" destination="Wvk-ka-vHd" eventType="touchUpInside" id="aS0-Ln-l1m"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="C7A-Ed-Bvk" firstAttribute="trailing" secondItem="dtu-qe-Dyv" secondAttribute="trailing" id="1wI-pd-mAD"/>
                                    <constraint firstItem="C7A-Ed-Bvk" firstAttribute="height" secondItem="dtu-qe-Dyv" secondAttribute="height" id="4kA-7h-ZKz"/>
                                    <constraint firstItem="dtu-qe-Dyv" firstAttribute="leading" secondItem="Qn9-wE-jUb" secondAttribute="leading" id="BuH-9s-uv2"/>
                                    <constraint firstItem="6fw-jl-dUj" firstAttribute="centerY" secondItem="g7g-hj-9KU" secondAttribute="centerY" id="D0R-IH-rBw"/>
                                    <constraint firstItem="qXt-k0-DKR" firstAttribute="width" secondItem="Qn9-wE-jUb" secondAttribute="width" multiplier="0.95" id="Df8-FR-Ywq"/>
                                    <constraint firstItem="g7g-hj-9KU" firstAttribute="centerX" secondItem="Qn9-wE-jUb" secondAttribute="centerX" id="MJA-9G-HJi"/>
                                    <constraint firstItem="6fw-jl-dUj" firstAttribute="centerX" secondItem="g7g-hj-9KU" secondAttribute="centerX" id="QtJ-Ym-ZLd"/>
                                    <constraint firstAttribute="bottom" secondItem="dtu-qe-Dyv" secondAttribute="bottom" id="bKi-zI-WX9"/>
                                    <constraint firstItem="g7g-hj-9KU" firstAttribute="top" secondItem="Qn9-wE-jUb" secondAttribute="top" constant="4" id="dpi-We-NZH"/>
                                    <constraint firstItem="qXt-k0-DKR" firstAttribute="top" secondItem="g7g-hj-9KU" secondAttribute="bottom" id="jQI-Ne-Zup"/>
                                    <constraint firstAttribute="trailing" secondItem="dtu-qe-Dyv" secondAttribute="trailing" id="jiQ-Zw-Vbu"/>
                                    <constraint firstItem="dtu-qe-Dyv" firstAttribute="top" secondItem="qXt-k0-DKR" secondAttribute="bottom" constant="8" id="qSc-D2-Xlp"/>
                                    <constraint firstItem="C7A-Ed-Bvk" firstAttribute="centerY" secondItem="dtu-qe-Dyv" secondAttribute="centerY" id="v6o-So-zvN"/>
                                    <constraint firstItem="qXt-k0-DKR" firstAttribute="centerX" secondItem="Qn9-wE-jUb" secondAttribute="centerX" id="wDG-sl-VR9"/>
                                    <constraint firstItem="C7A-Ed-Bvk" firstAttribute="width" secondItem="dtu-qe-Dyv" secondAttribute="width" multiplier="0.5" id="wf7-dO-OQp"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="dCD-kI-t83">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Jvf-rD-IL2">
                                        <rect key="frame" x="0.0" y="0.0" width="300" height="667"/>
                                        <subviews>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="SFY-i1-Xap">
                                                <rect key="frame" x="12" y="8" width="276" height="68"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="2" translatesAutoresizingMaskIntoConstraints="NO" id="5Vg-q3-1FS">
                                                        <rect key="frame" x="12" y="12" width="252" height="44"/>
                                                        <subviews>
                                                            <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="WKF-LK-iAV">
                                                                <rect key="frame" x="0.0" y="0.0" width="226" height="44"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="J" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="07S-FT-ZRW">
                                                                        <rect key="frame" x="0.0" y="0.0" width="44" height="44"/>
                                                                        <color key="backgroundColor" name="Primary"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="width" constant="44" id="CWg-b6-uDx"/>
                                                                            <constraint firstAttribute="height" constant="44" id="HdK-vX-76N"/>
                                                                        </constraints>
                                                                        <fontDescription key="fontDescription" type="boldSystem" pointSize="22"/>
                                                                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="skZ-oZ-Mn8">
                                                                        <rect key="frame" x="56" y="1.5" width="170" height="41.5"/>
                                                                        <subviews>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Jobin Macwan" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="36F-WG-BsU">
                                                                                <rect key="frame" x="0.0" y="0.0" width="170" height="20.5"/>
                                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                                <nil key="textColor"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Avail Balance : ₹ 25000" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="8BW-UZ-1hH">
                                                                                <rect key="frame" x="0.0" y="24.5" width="170" height="17"/>
                                                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                                <color key="textColor" name="Primary"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                        </subviews>
                                                                    </stackView>
                                                                </subviews>
                                                            </stackView>
                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_logout" translatesAutoresizingMaskIntoConstraints="NO" id="ctJ-n7-QzE">
                                                                <rect key="frame" x="228" y="10" width="24" height="24"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="24" id="9uJ-Qr-knk"/>
                                                                    <constraint firstAttribute="height" constant="24" id="ogg-SX-bGG"/>
                                                                </constraints>
                                                            </imageView>
                                                        </subviews>
                                                    </stackView>
                                                </subviews>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="bottom" secondItem="5Vg-q3-1FS" secondAttribute="bottom" constant="12" id="8tR-hW-R7o"/>
                                                    <constraint firstItem="5Vg-q3-1FS" firstAttribute="top" secondItem="SFY-i1-Xap" secondAttribute="top" constant="12" id="AUa-Tr-9LD"/>
                                                    <constraint firstItem="5Vg-q3-1FS" firstAttribute="leading" secondItem="SFY-i1-Xap" secondAttribute="leading" constant="12" id="OtP-ub-5Il"/>
                                                    <constraint firstAttribute="trailing" secondItem="5Vg-q3-1FS" secondAttribute="trailing" constant="12" id="tId-tt-7dJ"/>
                                                </constraints>
                                            </view>
                                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="-1" estimatedSectionHeaderHeight="-1" sectionFooterHeight="-1" estimatedSectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="SsS-gz-ruj">
                                                <rect key="frame" x="12" y="88" width="276" height="128"/>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="128" id="8SU-nz-D5r"/>
                                                </constraints>
                                                <prototypes>
                                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="none" indentationWidth="10" reuseIdentifier="cell" rowHeight="50" id="j4o-Vi-mFd" customClass="MenuCell" customModule="NXC_EV_Solutions" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="44.5" width="276" height="50"/>
                                                        <autoresizingMask key="autoresizingMask"/>
                                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="j4o-Vi-mFd" id="b1z-1P-PLS">
                                                            <rect key="frame" x="0.0" y="0.0" width="276" height="50"/>
                                                            <autoresizingMask key="autoresizingMask"/>
                                                            <subviews>
                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="FGC-mC-8AO">
                                                                    <rect key="frame" x="0.0" y="4" width="276" height="42"/>
                                                                    <subviews>
                                                                        <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="arj-vA-MFM">
                                                                            <rect key="frame" x="12" y="2" width="258" height="38"/>
                                                                            <subviews>
                                                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="atH-CD-wEA">
                                                                                    <rect key="frame" x="0.0" y="10" width="18" height="18"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="height" constant="18" id="FwF-4i-nbB"/>
                                                                                        <constraint firstAttribute="width" constant="18" id="nSp-nJ-xGB"/>
                                                                                    </constraints>
                                                                                </imageView>
                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Profile" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Ype-Iv-eCW">
                                                                                    <rect key="frame" x="34" y="0.0" width="224" height="38"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="height" constant="44" id="vNl-eE-M3L"/>
                                                                                    </constraints>
                                                                                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="14"/>
                                                                                    <nil key="textColor"/>
                                                                                    <nil key="highlightedColor"/>
                                                                                </label>
                                                                            </subviews>
                                                                        </stackView>
                                                                    </subviews>
                                                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="bottom" secondItem="arj-vA-MFM" secondAttribute="bottom" constant="2" id="9zs-1e-fIZ"/>
                                                                        <constraint firstItem="arj-vA-MFM" firstAttribute="top" secondItem="FGC-mC-8AO" secondAttribute="top" constant="2" id="GTw-nA-iC6"/>
                                                                        <constraint firstAttribute="trailing" secondItem="arj-vA-MFM" secondAttribute="trailing" constant="6" id="PCz-x5-CqP"/>
                                                                        <constraint firstItem="arj-vA-MFM" firstAttribute="leading" secondItem="FGC-mC-8AO" secondAttribute="leading" constant="12" id="oXJ-hY-hTY"/>
                                                                    </constraints>
                                                                </view>
                                                            </subviews>
                                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstItem="FGC-mC-8AO" firstAttribute="top" secondItem="b1z-1P-PLS" secondAttribute="top" constant="4" id="93w-Ou-AVe"/>
                                                                <constraint firstAttribute="bottom" secondItem="FGC-mC-8AO" secondAttribute="bottom" constant="4" id="BUO-XU-57h"/>
                                                                <constraint firstAttribute="trailing" secondItem="FGC-mC-8AO" secondAttribute="trailing" id="alK-WM-qyI"/>
                                                                <constraint firstItem="FGC-mC-8AO" firstAttribute="leading" secondItem="b1z-1P-PLS" secondAttribute="leading" id="cav-83-l4Z"/>
                                                            </constraints>
                                                        </tableViewCellContentView>
                                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <connections>
                                                            <outlet property="imgMenu" destination="atH-CD-wEA" id="bmh-GE-wZC"/>
                                                            <outlet property="lblMenu" destination="Ype-Iv-eCW" id="YJj-is-amj"/>
                                                            <outlet property="viewMain" destination="FGC-mC-8AO" id="au2-bC-Pej"/>
                                                        </connections>
                                                    </tableViewCell>
                                                </prototypes>
                                            </tableView>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="VCx-G1-yhb">
                                                <rect key="frame" x="242" y="20" width="44" height="44"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="44" id="Ub5-g8-zmd"/>
                                                    <constraint firstAttribute="width" secondItem="VCx-G1-yhb" secondAttribute="height" multiplier="1:1" id="bpQ-q7-IJe"/>
                                                </constraints>
                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                <connections>
                                                    <action selector="logoutTapped:" destination="Wvk-ka-vHd" eventType="touchUpInside" id="gme-pj-dbl"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="SFY-i1-Xap" firstAttribute="leading" secondItem="Jvf-rD-IL2" secondAttribute="leading" constant="12" id="7gm-zs-4Kq"/>
                                            <constraint firstAttribute="trailing" secondItem="SsS-gz-ruj" secondAttribute="trailing" constant="12" id="Cw8-a8-tsj"/>
                                            <constraint firstItem="SsS-gz-ruj" firstAttribute="leading" secondItem="Jvf-rD-IL2" secondAttribute="leading" constant="12" id="EN5-Ng-J2w"/>
                                            <constraint firstItem="VCx-G1-yhb" firstAttribute="centerY" secondItem="ctJ-n7-QzE" secondAttribute="centerY" id="U4k-4u-eD5"/>
                                            <constraint firstItem="VCx-G1-yhb" firstAttribute="centerX" secondItem="ctJ-n7-QzE" secondAttribute="centerX" id="awx-3q-xAO"/>
                                            <constraint firstItem="SsS-gz-ruj" firstAttribute="top" secondItem="SFY-i1-Xap" secondAttribute="bottom" constant="12" id="joG-Uo-pSe"/>
                                            <constraint firstAttribute="trailing" secondItem="SFY-i1-Xap" secondAttribute="trailing" constant="12" id="o4K-Yy-cOv"/>
                                            <constraint firstItem="SFY-i1-Xap" firstAttribute="top" secondItem="Jvf-rD-IL2" secondAttribute="top" constant="8" id="xE4-BU-h4U"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.75" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="bottom" secondItem="Jvf-rD-IL2" secondAttribute="bottom" id="Pk8-hm-RzA"/>
                                    <constraint firstItem="Jvf-rD-IL2" firstAttribute="top" secondItem="dCD-kI-t83" secondAttribute="top" id="TDA-Kp-iGN"/>
                                    <constraint firstItem="Jvf-rD-IL2" firstAttribute="leading" secondItem="dCD-kI-t83" secondAttribute="leading" id="Zog-G3-Im9"/>
                                    <constraint firstItem="Jvf-rD-IL2" firstAttribute="width" secondItem="dCD-kI-t83" secondAttribute="width" multiplier="0.8" id="kcW-5N-Jar"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="pOc-08-t2H"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="pOc-08-t2H" firstAttribute="trailing" secondItem="dCD-kI-t83" secondAttribute="trailing" id="0P7-DG-0to"/>
                            <constraint firstItem="Qn9-wE-jUb" firstAttribute="leading" secondItem="pOc-08-t2H" secondAttribute="leading" id="3gb-ew-Do6"/>
                            <constraint firstItem="Mba-vs-AEa" firstAttribute="top" secondItem="pOc-08-t2H" secondAttribute="top" constant="8" id="6JU-wZ-0D0"/>
                            <constraint firstItem="pOc-08-t2H" firstAttribute="bottom" secondItem="dCD-kI-t83" secondAttribute="bottom" id="8T5-Sd-9nS"/>
                            <constraint firstItem="dCD-kI-t83" firstAttribute="leading" secondItem="pOc-08-t2H" secondAttribute="leading" id="9iq-X0-PZ7"/>
                            <constraint firstItem="dCD-kI-t83" firstAttribute="top" secondItem="pOc-08-t2H" secondAttribute="top" id="CEw-QL-Gnj"/>
                            <constraint firstItem="pOc-08-t2H" firstAttribute="trailing" secondItem="Mba-vs-AEa" secondAttribute="trailing" constant="8" id="L4k-cC-TEm"/>
                            <constraint firstItem="bBW-Vh-D1k" firstAttribute="leading" secondItem="pOc-08-t2H" secondAttribute="leading" id="P5X-QX-XU1"/>
                            <constraint firstItem="pOc-08-t2H" firstAttribute="trailing" secondItem="Qn9-wE-jUb" secondAttribute="trailing" id="Pm4-p0-yDp"/>
                            <constraint firstItem="O5y-nB-BYp" firstAttribute="leading" secondItem="mfV-dt-9Wm" secondAttribute="leading" id="Tsj-gg-RTE"/>
                            <constraint firstItem="pOc-08-t2H" firstAttribute="bottom" secondItem="SXO-fY-bIM" secondAttribute="bottom" constant="85" id="XhQ-rY-D4w"/>
                            <constraint firstItem="bBW-Vh-D1k" firstAttribute="top" secondItem="pOc-08-t2H" secondAttribute="top" id="bKi-Ld-CJk"/>
                            <constraint firstItem="O5y-nB-BYp" firstAttribute="trailing" secondItem="IiN-sd-NDe" secondAttribute="trailing" id="cj5-iK-zbh"/>
                            <constraint firstItem="pOc-08-t2H" firstAttribute="trailing" secondItem="bBW-Vh-D1k" secondAttribute="trailing" id="d5l-7w-CYW"/>
                            <constraint firstItem="Mba-vs-AEa" firstAttribute="leading" secondItem="pOc-08-t2H" secondAttribute="leading" constant="8" id="egC-e7-8SY"/>
                            <constraint firstItem="pOc-08-t2H" firstAttribute="bottom" secondItem="Qn9-wE-jUb" secondAttribute="bottom" id="esS-fy-0g0"/>
                            <constraint firstItem="pOc-08-t2H" firstAttribute="bottom" secondItem="bBW-Vh-D1k" secondAttribute="bottom" id="kjK-ff-yvl"/>
                            <constraint firstItem="pOc-08-t2H" firstAttribute="trailing" secondItem="SXO-fY-bIM" secondAttribute="trailing" constant="16" id="mcf-7o-RVZ"/>
                            <constraint firstItem="O5y-nB-BYp" firstAttribute="top" secondItem="Mba-vs-AEa" secondAttribute="bottom" constant="4" id="tMS-yp-UwN"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="btnFilter" destination="Zv2-lT-LJy" id="cbI-Hg-K4G"/>
                        <outlet property="btnMenu" destination="Su4-5Y-DFc" id="B3U-jp-CTh"/>
                        <outlet property="btnWA" destination="SXO-fY-bIM" id="H3F-sX-ij3"/>
                        <outlet property="imgConnector1" destination="d1B-57-O2K" id="4xm-S0-XA9"/>
                        <outlet property="imgConnector2" destination="dnY-xO-NJG" id="Jf8-KL-dcV"/>
                        <outlet property="imgConnector3" destination="ZrE-bF-6yM" id="fg5-5R-x7M"/>
                        <outlet property="imgStation" destination="rZs-2r-o60" id="4xo-6f-vEa"/>
                        <outlet property="imgUpDown" destination="g7g-hj-9KU" id="0B8-My-51k"/>
                        <outlet property="lblBalance" destination="8BW-UZ-1hH" id="QPQ-d0-cpc"/>
                        <outlet property="lblConnector1" destination="zQU-ea-0nL" id="Pbu-H3-L1j"/>
                        <outlet property="lblConnector2" destination="Fq5-dI-BBm" id="wKy-o9-c2R"/>
                        <outlet property="lblConnector3" destination="bhx-jm-CwN" id="wcO-gM-rOf"/>
                        <outlet property="lblConnectorPower" destination="uLv-cR-zAn" id="9WE-2F-QZY"/>
                        <outlet property="lblInitial" destination="07S-FT-ZRW" id="oT0-rI-xKA"/>
                        <outlet property="lblKmMin" destination="Cje-Vi-T3X" id="7Aw-7v-Wb9"/>
                        <outlet property="lblStationAdd1" destination="eiS-G6-a9a" id="u26-11-oor"/>
                        <outlet property="lblStationAdd2" destination="fNT-DM-ReR" id="0bJ-7n-9q3"/>
                        <outlet property="lblStationStatus" destination="9T6-3P-Foa" id="e3r-ro-q1B"/>
                        <outlet property="lblStationTimings" destination="Wki-Lq-QVH" id="be8-wv-KGI"/>
                        <outlet property="lblUserName" destination="36F-WG-BsU" id="F0o-JB-Ria"/>
                        <outlet property="mapView" destination="bBW-Vh-D1k" id="dZe-kZ-o6u"/>
                        <outlet property="searchBar" destination="IiN-sd-NDe" id="AlT-LA-7eP"/>
                        <outlet property="searchListTableView" destination="QoS-xM-TrR" id="YeL-jd-TRg"/>
                        <outlet property="tableConnectorDetails" destination="jZD-8s-NOa" id="1Hz-bZ-xYD"/>
                        <outlet property="tableConnectorHeight" destination="Ozh-vW-ZuX" id="ai6-fT-m3Y"/>
                        <outlet property="tableHeight" destination="8SU-nz-D5r" id="HX2-Dw-GMT"/>
                        <outlet property="tableMenu" destination="SsS-gz-ruj" id="5lF-0Y-Soi"/>
                        <outlet property="topStack" destination="Mba-vs-AEa" id="PfW-MT-L9A"/>
                        <outlet property="viewBgDetails" destination="Qn9-wE-jUb" id="5v3-mE-pOp"/>
                        <outlet property="viewBgMenu" destination="dCD-kI-t83" id="5IO-hT-xox"/>
                        <outlet property="viewConnector1" destination="JbP-Js-M17" id="SMZ-Ba-MFM"/>
                        <outlet property="viewConnector2" destination="En0-35-AnC" id="7eQ-01-MXM"/>
                        <outlet property="viewConnector3" destination="fIm-c3-BQK" id="gUn-FP-3e5"/>
                        <outlet property="viewConnectorDetails" destination="Y9T-Hk-gwr" id="NH0-fv-6cN"/>
                        <outlet property="viewDirection" destination="dtu-qe-Dyv" id="hVS-UW-yGx"/>
                        <outlet property="viewMainMenu" destination="Jvf-rD-IL2" id="v9g-5b-fyf"/>
                        <outlet property="viewProfile" destination="SFY-i1-Xap" id="1EL-po-B0K"/>
                        <outlet property="viewSearch" destination="mfV-dt-9Wm" id="t0F-2m-FiX"/>
                        <outlet property="viewSearchHeight" destination="tFu-ik-HFW" id="mOg-TS-dsj"/>
                        <outlet property="viewSearchList" destination="O5y-nB-BYp" id="4bH-bi-55i"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="at0-IX-LHv" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-79.200000000000003" y="69.715142428785612"/>
        </scene>
        <!--FilterVC-->
        <scene sceneID="UDV-Eo-eE9">
            <objects>
                <viewController storyboardIdentifier="FilterVC" id="VuP-6y-aAn" customClass="FilterVC" customModule="NXC_EV_Solutions" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="wfz-yA-eWJ">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="d0M-Hc-lwA">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ftV-Ji-XTJ">
                                        <rect key="frame" x="12" y="5" width="34" height="34"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="34" id="UXh-KH-kKt"/>
                                            <constraint firstAttribute="height" constant="34" id="xQ9-Wo-wgX"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="8" minY="6" maxX="8" maxY="6"/>
                                        <state key="normal" image="ic_back_arrow"/>
                                        <connections>
                                            <action selector="backAction:" destination="VuP-6y-aAn" eventType="touchUpInside" id="FYS-51-1Zn"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Filter" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="tLu-Qf-XHO">
                                        <rect key="frame" x="167" y="12" width="41" height="20.5"/>
                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                        <color key="textColor" red="0.070588235294117646" green="0.070588235294117646" blue="0.070588235294117646" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="tLu-Qf-XHO" firstAttribute="centerY" secondItem="d0M-Hc-lwA" secondAttribute="centerY" id="LeE-qW-egN"/>
                                    <constraint firstItem="tLu-Qf-XHO" firstAttribute="centerX" secondItem="d0M-Hc-lwA" secondAttribute="centerX" id="gXc-BV-3eg"/>
                                    <constraint firstItem="ftV-Ji-XTJ" firstAttribute="leading" secondItem="d0M-Hc-lwA" secondAttribute="leading" constant="12" id="i64-6x-z8m"/>
                                    <constraint firstItem="ftV-Ji-XTJ" firstAttribute="centerY" secondItem="d0M-Hc-lwA" secondAttribute="centerY" id="py6-tP-zk1"/>
                                    <constraint firstAttribute="height" constant="44" id="tp5-aD-6PN"/>
                                </constraints>
                            </view>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="JR4-a8-lJj">
                                <rect key="frame" x="19" y="611" width="337.5" height="44"/>
                                <color key="backgroundColor" name="PrimaryColor"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="44" id="q9z-1C-ctf"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                <state key="normal" title="Apply">
                                    <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </state>
                                <connections>
                                    <action selector="applyAction:" destination="VuP-6y-aAn" eventType="touchUpInside" id="mgi-1h-sKN"/>
                                </connections>
                            </button>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Uxf-C4-YBh">
                                <rect key="frame" x="0.0" y="52" width="375" height="551"/>
                                <subviews>
                                    <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Ytt-xQ-Y9w">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="613"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="Fo8-pj-3Wn">
                                                <rect key="frame" x="12" y="8" width="351" height="605"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="YTw-IZ-LmL">
                                                        <rect key="frame" x="0.0" y="0.0" width="351" height="74"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="By Charger Type" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="g5Z-Um-eed">
                                                                <rect key="frame" x="0.0" y="0.0" width="351" height="18"/>
                                                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="15"/>
                                                                <color key="textColor" red="0.070588235289999995" green="0.070588235289999995" blue="0.070588235289999995" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="khb-r8-OTp">
                                                                <rect key="frame" x="0.0" y="30" width="351" height="44"/>
                                                                <subviews>
                                                                    <button opaque="NO" tag="101" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="umz-ID-vmw">
                                                                        <rect key="frame" x="0.0" y="0.0" width="111.5" height="44"/>
                                                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="15"/>
                                                                        <state key="normal" title="All">
                                                                            <color key="titleColor" name="PrimarySelection"/>
                                                                        </state>
                                                                        <connections>
                                                                            <action selector="chargerTypeActions:" destination="VuP-6y-aAn" eventType="touchUpInside" id="wId-pv-zBn"/>
                                                                        </connections>
                                                                    </button>
                                                                    <button opaque="NO" tag="102" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="BSU-Z8-uDd">
                                                                        <rect key="frame" x="119.5" y="0.0" width="112" height="44"/>
                                                                        <color key="backgroundColor" name="PrimaryColor"/>
                                                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="15"/>
                                                                        <state key="normal" title="Public">
                                                                            <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        </state>
                                                                        <connections>
                                                                            <action selector="chargerTypeActions:" destination="VuP-6y-aAn" eventType="touchUpInside" id="mCb-Lj-6nH"/>
                                                                        </connections>
                                                                    </button>
                                                                    <button opaque="NO" tag="103" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="9QX-tp-wuj">
                                                                        <rect key="frame" x="239.5" y="0.0" width="111.5" height="44"/>
                                                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="15"/>
                                                                        <state key="normal" title="Private">
                                                                            <color key="titleColor" name="PrimarySelection"/>
                                                                        </state>
                                                                        <connections>
                                                                            <action selector="chargerTypeActions:" destination="VuP-6y-aAn" eventType="touchUpInside" id="HPS-db-ySy"/>
                                                                        </connections>
                                                                    </button>
                                                                </subviews>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="44" id="wwP-Tr-0ZM"/>
                                                                </constraints>
                                                            </stackView>
                                                        </subviews>
                                                    </stackView>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="ZLe-XM-7Gg">
                                                        <rect key="frame" x="0.0" y="86" width="351" height="205"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="By Connector Type" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="bRt-yY-VhB">
                                                                <rect key="frame" x="0.0" y="0.0" width="351" height="18"/>
                                                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="15"/>
                                                                <color key="textColor" red="0.070588235289999995" green="0.070588235289999995" blue="0.070588235289999995" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" dataMode="prototypes" translatesAutoresizingMaskIntoConstraints="NO" id="rWe-aF-H3l">
                                                                <rect key="frame" x="0.0" y="30" width="351" height="175"/>
                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="175" id="62k-KY-Rax"/>
                                                                </constraints>
                                                                <collectionViewFlowLayout key="collectionViewLayout" minimumLineSpacing="0.0" minimumInteritemSpacing="4" id="UO5-S3-fhh">
                                                                    <size key="itemSize" width="141" height="74"/>
                                                                    <size key="headerReferenceSize" width="0.0" height="0.0"/>
                                                                    <size key="footerReferenceSize" width="0.0" height="0.0"/>
                                                                    <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                                                                </collectionViewFlowLayout>
                                                                <cells>
                                                                    <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" reuseIdentifier="cell" id="0ae-XM-zda" customClass="FilterCell" customModule="NXC_EV_Solutions" customModuleProvider="target">
                                                                        <rect key="frame" x="0.0" y="0.0" width="141" height="74"/>
                                                                        <autoresizingMask key="autoresizingMask"/>
                                                                        <collectionViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" id="Dxc-up-UFh">
                                                                            <rect key="frame" x="0.0" y="0.0" width="141" height="74"/>
                                                                            <autoresizingMask key="autoresizingMask"/>
                                                                            <subviews>
                                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="bk9-sy-iyy">
                                                                                    <rect key="frame" x="0.0" y="4" width="85.5" height="66"/>
                                                                                    <subviews>
                                                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="2VT-PX-uxL">
                                                                                            <rect key="frame" x="8" y="21" width="24" height="24"/>
                                                                                            <constraints>
                                                                                                <constraint firstAttribute="height" constant="24" id="6Ob-sU-qp5"/>
                                                                                                <constraint firstAttribute="width" constant="24" id="D1I-JT-rtZ"/>
                                                                                            </constraints>
                                                                                        </imageView>
                                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" lineBreakMode="wordWrap" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="W9o-Kd-lhv">
                                                                                            <rect key="frame" x="36" y="0.0" width="37.5" height="66"/>
                                                                                            <constraints>
                                                                                                <constraint firstAttribute="height" constant="44" id="qw8-pE-sCo"/>
                                                                                            </constraints>
                                                                                            <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                                            <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                            <nil key="highlightedColor"/>
                                                                                        </label>
                                                                                    </subviews>
                                                                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                    <constraints>
                                                                                        <constraint firstItem="W9o-Kd-lhv" firstAttribute="leading" secondItem="2VT-PX-uxL" secondAttribute="trailing" constant="4" id="EbX-Rb-Wed"/>
                                                                                        <constraint firstAttribute="bottom" secondItem="W9o-Kd-lhv" secondAttribute="bottom" id="Mff-gJ-hus"/>
                                                                                        <constraint firstItem="W9o-Kd-lhv" firstAttribute="top" secondItem="bk9-sy-iyy" secondAttribute="top" id="Pi2-T8-zOa"/>
                                                                                        <constraint firstItem="2VT-PX-uxL" firstAttribute="centerY" secondItem="bk9-sy-iyy" secondAttribute="centerY" id="SmB-y6-QxT"/>
                                                                                        <constraint firstItem="2VT-PX-uxL" firstAttribute="leading" secondItem="bk9-sy-iyy" secondAttribute="leading" constant="8" id="cuF-CZ-bo9"/>
                                                                                        <constraint firstAttribute="trailing" secondItem="W9o-Kd-lhv" secondAttribute="trailing" constant="12" id="qg2-A1-giT"/>
                                                                                    </constraints>
                                                                                </view>
                                                                            </subviews>
                                                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                            <constraints>
                                                                                <constraint firstItem="bk9-sy-iyy" firstAttribute="top" secondItem="Dxc-up-UFh" secondAttribute="top" constant="4" id="OW8-Wl-Pnm"/>
                                                                                <constraint firstAttribute="bottom" secondItem="bk9-sy-iyy" secondAttribute="bottom" constant="4" id="dNF-14-Da3"/>
                                                                                <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="bk9-sy-iyy" secondAttribute="trailing" id="kJl-VH-gO7"/>
                                                                                <constraint firstItem="bk9-sy-iyy" firstAttribute="leading" secondItem="Dxc-up-UFh" secondAttribute="leading" id="xOB-7t-kpp"/>
                                                                            </constraints>
                                                                        </collectionViewCellContentView>
                                                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <size key="customSize" width="141" height="74"/>
                                                                        <connections>
                                                                            <outlet property="imgType" destination="2VT-PX-uxL" id="cRs-lU-WoM"/>
                                                                            <outlet property="lblType" destination="W9o-Kd-lhv" id="hUj-sf-jPa"/>
                                                                            <outlet property="viewMain" destination="bk9-sy-iyy" id="Bsy-Mb-8NV"/>
                                                                        </connections>
                                                                    </collectionViewCell>
                                                                </cells>
                                                            </collectionView>
                                                        </subviews>
                                                    </stackView>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="Gy8-Xi-U2R">
                                                        <rect key="frame" x="0.0" y="303" width="351" height="178"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="By Ratings" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="VYK-fD-jA8">
                                                                <rect key="frame" x="0.0" y="0.0" width="351" height="18"/>
                                                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="15"/>
                                                                <color key="textColor" red="0.070588235289999995" green="0.070588235289999995" blue="0.070588235289999995" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="tmE-QG-hvA">
                                                                <rect key="frame" x="0.0" y="30" width="351" height="148"/>
                                                                <subviews>
                                                                    <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="fes-JA-fUb">
                                                                        <rect key="frame" x="0.0" y="0.0" width="351" height="34"/>
                                                                        <subviews>
                                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" tag="104" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_uncheck" highlightedImage="ic_checked" translatesAutoresizingMaskIntoConstraints="NO" id="zLc-47-0RW">
                                                                                <rect key="frame" x="0.0" y="5" width="24" height="24"/>
                                                                                <color key="tintColor" name="PrimaryTextColorLight"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="width" constant="24" id="4dL-LG-WgR"/>
                                                                                    <constraint firstAttribute="height" constant="24" id="6y3-wp-ek2"/>
                                                                                </constraints>
                                                                            </imageView>
                                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_ratings" translatesAutoresizingMaskIntoConstraints="NO" id="qGx-MP-ayD">
                                                                                <rect key="frame" x="32" y="9" width="16" height="16"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="width" constant="16" id="hHa-ex-NHE"/>
                                                                                    <constraint firstAttribute="height" constant="16" id="yPP-Hl-cdP"/>
                                                                                </constraints>
                                                                            </imageView>
                                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_ratings" translatesAutoresizingMaskIntoConstraints="NO" id="bTs-uj-WNs">
                                                                                <rect key="frame" x="56" y="9" width="16" height="16"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="width" constant="16" id="BmY-X2-vkb"/>
                                                                                    <constraint firstAttribute="height" constant="16" id="sTq-b8-6Ku"/>
                                                                                </constraints>
                                                                            </imageView>
                                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_ratings" translatesAutoresizingMaskIntoConstraints="NO" id="4gs-VZ-jwc">
                                                                                <rect key="frame" x="80" y="9" width="16" height="16"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="height" constant="16" id="3cn-iR-JS0"/>
                                                                                    <constraint firstAttribute="width" constant="16" id="Ko2-ha-GWG"/>
                                                                                </constraints>
                                                                            </imageView>
                                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_ratings" translatesAutoresizingMaskIntoConstraints="NO" id="gOy-wz-ACl">
                                                                                <rect key="frame" x="104" y="9" width="16" height="16"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="width" constant="16" id="1ZK-3q-Hkh"/>
                                                                                    <constraint firstAttribute="height" constant="16" id="GUD-El-w7J"/>
                                                                                </constraints>
                                                                            </imageView>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="&amp; Up" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="dy2-VQ-N2L">
                                                                                <rect key="frame" x="128" y="0.0" width="223" height="34"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="height" constant="34" id="JfY-gz-hk2"/>
                                                                                </constraints>
                                                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                        </subviews>
                                                                    </stackView>
                                                                    <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="1YT-RD-D17">
                                                                        <rect key="frame" x="0.0" y="38" width="351" height="34"/>
                                                                        <subviews>
                                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" tag="103" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_uncheck" highlightedImage="ic_checked" translatesAutoresizingMaskIntoConstraints="NO" id="wDA-wW-8ST">
                                                                                <rect key="frame" x="0.0" y="5" width="24" height="24"/>
                                                                                <color key="tintColor" name="PrimaryTextColorLight"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="width" constant="24" id="bKp-bA-NFm"/>
                                                                                    <constraint firstAttribute="height" constant="24" id="iw0-2v-j6X"/>
                                                                                </constraints>
                                                                            </imageView>
                                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_ratings" translatesAutoresizingMaskIntoConstraints="NO" id="FVb-JC-8IP">
                                                                                <rect key="frame" x="32" y="9" width="16" height="16"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="width" constant="16" id="V2A-BL-2Tz"/>
                                                                                    <constraint firstAttribute="height" constant="16" id="efA-cA-uhJ"/>
                                                                                </constraints>
                                                                            </imageView>
                                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_ratings" translatesAutoresizingMaskIntoConstraints="NO" id="lBX-Bv-yrN">
                                                                                <rect key="frame" x="56" y="9" width="16" height="16"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="height" constant="16" id="F8C-EY-yTL"/>
                                                                                    <constraint firstAttribute="width" constant="16" id="LXh-g9-pHu"/>
                                                                                </constraints>
                                                                            </imageView>
                                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_ratings" translatesAutoresizingMaskIntoConstraints="NO" id="xy4-k1-M6P">
                                                                                <rect key="frame" x="80" y="9" width="16" height="16"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="width" constant="16" id="CHE-Oc-ESH"/>
                                                                                    <constraint firstAttribute="height" constant="16" id="EGd-kQ-LoK"/>
                                                                                </constraints>
                                                                            </imageView>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="&amp; Up" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Q1z-VQ-hcO">
                                                                                <rect key="frame" x="104" y="0.0" width="247" height="34"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="height" constant="34" id="ZQ8-CI-gpL"/>
                                                                                </constraints>
                                                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                        </subviews>
                                                                    </stackView>
                                                                    <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="UWP-eM-1dD">
                                                                        <rect key="frame" x="0.0" y="76" width="351" height="34"/>
                                                                        <subviews>
                                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" tag="102" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_uncheck" highlightedImage="ic_checked" translatesAutoresizingMaskIntoConstraints="NO" id="sQ6-p7-qW7">
                                                                                <rect key="frame" x="0.0" y="5" width="24" height="24"/>
                                                                                <color key="tintColor" name="PrimaryTextColorLight"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="height" constant="24" id="eKu-1C-iiH"/>
                                                                                    <constraint firstAttribute="width" constant="24" id="g84-Eh-6qr"/>
                                                                                </constraints>
                                                                            </imageView>
                                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_ratings" translatesAutoresizingMaskIntoConstraints="NO" id="zGN-l2-0Ew">
                                                                                <rect key="frame" x="32" y="9" width="16" height="16"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="width" constant="16" id="7d6-wb-LK5"/>
                                                                                    <constraint firstAttribute="height" constant="16" id="a94-Xk-dRh"/>
                                                                                </constraints>
                                                                            </imageView>
                                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_ratings" translatesAutoresizingMaskIntoConstraints="NO" id="9Zp-bD-Kum">
                                                                                <rect key="frame" x="56" y="9" width="16" height="16"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="height" constant="16" id="l5l-Kr-dFf"/>
                                                                                    <constraint firstAttribute="width" constant="16" id="oQt-PO-XaS"/>
                                                                                </constraints>
                                                                            </imageView>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="&amp; Up" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Qgz-V5-vHM">
                                                                                <rect key="frame" x="80" y="0.0" width="271" height="34"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="height" constant="34" id="LnD-hg-liX"/>
                                                                                </constraints>
                                                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                        </subviews>
                                                                    </stackView>
                                                                    <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="cYg-E2-2Zf">
                                                                        <rect key="frame" x="0.0" y="114" width="351" height="34"/>
                                                                        <subviews>
                                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" tag="101" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_uncheck" highlightedImage="ic_checked" translatesAutoresizingMaskIntoConstraints="NO" id="oXa-7x-cWX">
                                                                                <rect key="frame" x="0.0" y="5" width="24" height="24"/>
                                                                                <color key="tintColor" name="PrimaryTextColorLight"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="height" constant="24" id="d5V-qI-IM0"/>
                                                                                    <constraint firstAttribute="width" constant="24" id="dKh-Zg-rN6"/>
                                                                                </constraints>
                                                                            </imageView>
                                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_ratings" translatesAutoresizingMaskIntoConstraints="NO" id="WpJ-3c-YF3">
                                                                                <rect key="frame" x="32" y="9" width="16" height="16"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="height" constant="16" id="8fE-MH-cr0"/>
                                                                                    <constraint firstAttribute="width" constant="16" id="kS2-en-eYr"/>
                                                                                </constraints>
                                                                            </imageView>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="&amp; Up" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="QL8-rP-WE2">
                                                                                <rect key="frame" x="56" y="0.0" width="295" height="34"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="height" constant="34" id="iEy-pa-UMG"/>
                                                                                </constraints>
                                                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                        </subviews>
                                                                    </stackView>
                                                                </subviews>
                                                            </stackView>
                                                        </subviews>
                                                    </stackView>
                                                    <stackView opaque="NO" contentMode="scaleToFill" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="gdH-bp-qNv">
                                                        <rect key="frame" x="0.0" y="493" width="351" height="34"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Only Favourite" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ZnA-2B-hrE">
                                                                <rect key="frame" x="0.0" y="0.0" width="294" height="34"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="34" id="Iyy-SM-NFN"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="15"/>
                                                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <switch opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" contentHorizontalAlignment="center" contentVerticalAlignment="center" on="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1Kw-Iu-Zr1">
                                                                <rect key="frame" x="302" y="0.0" width="51" height="34"/>
                                                                <color key="onTintColor" name="PrimaryColor"/>
                                                            </switch>
                                                        </subviews>
                                                    </stackView>
                                                    <stackView opaque="NO" contentMode="scaleToFill" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="EeK-33-ZbD">
                                                        <rect key="frame" x="0.0" y="539" width="351" height="34"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Only Available Chargers" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="g0b-ZD-VaN">
                                                                <rect key="frame" x="0.0" y="0.0" width="294" height="34"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="34" id="R2t-Ag-mvE"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="15"/>
                                                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <switch opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" contentHorizontalAlignment="center" contentVerticalAlignment="center" on="YES" translatesAutoresizingMaskIntoConstraints="NO" id="ZXN-Yp-MJo">
                                                                <rect key="frame" x="302" y="0.0" width="51" height="34"/>
                                                                <color key="onTintColor" name="PrimaryColor"/>
                                                            </switch>
                                                        </subviews>
                                                    </stackView>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Z8U-ml-Av7">
                                                        <rect key="frame" x="0.0" y="585" width="351" height="4"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="4" id="dke-cw-MHH"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                        <nil key="textColor"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="gfE-MX-zXj">
                                                        <rect key="frame" x="0.0" y="601" width="351" height="4"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="4" id="7Im-Yg-pvr"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                        <nil key="textColor"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                            </stackView>
                                            <button opaque="NO" tag="104" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="VJB-70-76y">
                                                <rect key="frame" x="12" y="341" width="351" height="34"/>
                                                <connections>
                                                    <action selector="ratingsAction:" destination="VuP-6y-aAn" eventType="touchUpInside" id="Vs5-Mx-Med"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" tag="103" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="cMR-fi-Bfd">
                                                <rect key="frame" x="12" y="379" width="351" height="34"/>
                                                <connections>
                                                    <action selector="ratingsAction:" destination="VuP-6y-aAn" eventType="touchUpInside" id="yYA-vK-ZjY"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" tag="102" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="32y-IG-lUj">
                                                <rect key="frame" x="12" y="417" width="351" height="34"/>
                                                <connections>
                                                    <action selector="ratingsAction:" destination="VuP-6y-aAn" eventType="touchUpInside" id="dpo-cx-qlM"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" tag="101" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="NW1-zy-03C">
                                                <rect key="frame" x="12" y="455" width="351" height="34"/>
                                                <connections>
                                                    <action selector="ratingsAction:" destination="VuP-6y-aAn" eventType="touchUpInside" id="IKP-By-Ugd"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstItem="VJB-70-76y" firstAttribute="centerY" secondItem="fes-JA-fUb" secondAttribute="centerY" id="3uN-SR-HP9"/>
                                            <constraint firstItem="VJB-70-76y" firstAttribute="centerX" secondItem="fes-JA-fUb" secondAttribute="centerX" id="87s-lc-raD"/>
                                            <constraint firstAttribute="trailing" secondItem="Fo8-pj-3Wn" secondAttribute="trailing" constant="12" id="B51-Oo-PCR"/>
                                            <constraint firstItem="NW1-zy-03C" firstAttribute="centerX" secondItem="cYg-E2-2Zf" secondAttribute="centerX" id="FwZ-6h-Ute"/>
                                            <constraint firstItem="cMR-fi-Bfd" firstAttribute="height" secondItem="1YT-RD-D17" secondAttribute="height" id="J1j-M3-c4E"/>
                                            <constraint firstItem="NW1-zy-03C" firstAttribute="centerY" secondItem="cYg-E2-2Zf" secondAttribute="centerY" id="M9L-ty-Gh2"/>
                                            <constraint firstItem="32y-IG-lUj" firstAttribute="centerY" secondItem="UWP-eM-1dD" secondAttribute="centerY" id="O4F-OR-0db"/>
                                            <constraint firstItem="NW1-zy-03C" firstAttribute="height" secondItem="cYg-E2-2Zf" secondAttribute="height" id="Oaw-RX-uNh"/>
                                            <constraint firstItem="cMR-fi-Bfd" firstAttribute="centerX" secondItem="1YT-RD-D17" secondAttribute="centerX" id="Xno-NC-Efi"/>
                                            <constraint firstItem="32y-IG-lUj" firstAttribute="height" secondItem="UWP-eM-1dD" secondAttribute="height" id="Yp8-7F-qPz"/>
                                            <constraint firstItem="VJB-70-76y" firstAttribute="width" secondItem="fes-JA-fUb" secondAttribute="width" id="aoa-GS-8BP"/>
                                            <constraint firstItem="cMR-fi-Bfd" firstAttribute="width" secondItem="1YT-RD-D17" secondAttribute="width" id="bSa-nE-gb7"/>
                                            <constraint firstItem="32y-IG-lUj" firstAttribute="centerX" secondItem="UWP-eM-1dD" secondAttribute="centerX" id="bVY-kj-VHG"/>
                                            <constraint firstAttribute="bottom" secondItem="Fo8-pj-3Wn" secondAttribute="bottom" id="dix-nl-4dL"/>
                                            <constraint firstItem="Fo8-pj-3Wn" firstAttribute="leading" secondItem="Ytt-xQ-Y9w" secondAttribute="leading" constant="12" id="k5m-nG-Une"/>
                                            <constraint firstItem="32y-IG-lUj" firstAttribute="width" secondItem="UWP-eM-1dD" secondAttribute="width" id="klT-Bq-vgJ"/>
                                            <constraint firstItem="VJB-70-76y" firstAttribute="height" secondItem="fes-JA-fUb" secondAttribute="height" id="oBr-4e-o6K"/>
                                            <constraint firstItem="NW1-zy-03C" firstAttribute="width" secondItem="cYg-E2-2Zf" secondAttribute="width" id="q4n-Rc-27U"/>
                                            <constraint firstItem="Fo8-pj-3Wn" firstAttribute="top" secondItem="Ytt-xQ-Y9w" secondAttribute="top" constant="8" id="qUp-9U-THx"/>
                                            <constraint firstItem="cMR-fi-Bfd" firstAttribute="centerY" secondItem="1YT-RD-D17" secondAttribute="centerY" id="tQL-AV-Tvk"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="Ytt-xQ-Y9w" secondAttribute="trailing" id="0WJ-M7-vgk"/>
                                    <constraint firstItem="Ytt-xQ-Y9w" firstAttribute="centerX" secondItem="tPr-uC-5F0" secondAttribute="centerX" id="7wF-OV-Pgf"/>
                                    <constraint firstItem="Ytt-xQ-Y9w" firstAttribute="leading" secondItem="tPr-uC-5F0" secondAttribute="leading" id="LSy-6L-Kbd"/>
                                    <constraint firstItem="xaC-aB-GMd" firstAttribute="bottom" secondItem="Ytt-xQ-Y9w" secondAttribute="bottom" id="Nvd-gV-urV"/>
                                    <constraint firstItem="Ytt-xQ-Y9w" firstAttribute="top" secondItem="tPr-uC-5F0" secondAttribute="top" id="Nwa-sr-3qa"/>
                                    <constraint firstItem="Ytt-xQ-Y9w" firstAttribute="top" secondItem="xaC-aB-GMd" secondAttribute="top" id="UbP-RN-dwc"/>
                                    <constraint firstItem="xaC-aB-GMd" firstAttribute="trailing" secondItem="Ytt-xQ-Y9w" secondAttribute="trailing" id="awa-6Z-lQr"/>
                                    <constraint firstItem="Ytt-xQ-Y9w" firstAttribute="centerX" secondItem="Uxf-C4-YBh" secondAttribute="centerX" id="c6C-W5-Vh8"/>
                                    <constraint firstAttribute="bottom" secondItem="Ytt-xQ-Y9w" secondAttribute="bottom" id="dWj-J5-HOu"/>
                                    <constraint firstItem="Ytt-xQ-Y9w" firstAttribute="leading" secondItem="xaC-aB-GMd" secondAttribute="leading" id="z9v-4u-abX"/>
                                </constraints>
                                <viewLayoutGuide key="contentLayoutGuide" id="xaC-aB-GMd"/>
                                <viewLayoutGuide key="frameLayoutGuide" id="tPr-uC-5F0"/>
                            </scrollView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="LAS-kg-RHE"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="Uxf-C4-YBh" firstAttribute="leading" secondItem="LAS-kg-RHE" secondAttribute="leading" id="7ap-mh-gk3"/>
                            <constraint firstItem="LAS-kg-RHE" firstAttribute="trailing" secondItem="d0M-Hc-lwA" secondAttribute="trailing" id="M5W-WD-bnp"/>
                            <constraint firstItem="Uxf-C4-YBh" firstAttribute="leading" secondItem="d0M-Hc-lwA" secondAttribute="trailing" constant="-375" id="R7z-Ev-l3g"/>
                            <constraint firstItem="JR4-a8-lJj" firstAttribute="centerX" secondItem="wfz-yA-eWJ" secondAttribute="centerX" id="THg-iA-puA"/>
                            <constraint firstItem="LAS-kg-RHE" firstAttribute="bottom" secondItem="JR4-a8-lJj" secondAttribute="bottom" constant="12" id="WNO-ON-kB2"/>
                            <constraint firstItem="JR4-a8-lJj" firstAttribute="top" secondItem="Uxf-C4-YBh" secondAttribute="bottom" constant="8" id="a9b-QJ-dv2"/>
                            <constraint firstItem="d0M-Hc-lwA" firstAttribute="top" secondItem="LAS-kg-RHE" secondAttribute="top" id="b68-rW-63w"/>
                            <constraint firstItem="Uxf-C4-YBh" firstAttribute="top" secondItem="d0M-Hc-lwA" secondAttribute="bottom" constant="8" id="iKw-n0-f8f"/>
                            <constraint firstItem="Uxf-C4-YBh" firstAttribute="trailing" secondItem="LAS-kg-RHE" secondAttribute="trailing" id="oNe-hd-dts"/>
                            <constraint firstItem="d0M-Hc-lwA" firstAttribute="leading" secondItem="LAS-kg-RHE" secondAttribute="leading" id="oOW-6a-T8P"/>
                            <constraint firstItem="JR4-a8-lJj" firstAttribute="width" secondItem="LAS-kg-RHE" secondAttribute="width" multiplier="0.9" id="p0P-hh-C7E"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="btnAll" destination="umz-ID-vmw" id="POe-zp-glY"/>
                        <outlet property="btnApply" destination="JR4-a8-lJj" id="ppt-Uv-Ctp"/>
                        <outlet property="btnBack" destination="ftV-Ji-XTJ" id="uVa-10-3s0"/>
                        <outlet property="btnPrivate" destination="9QX-tp-wuj" id="Xun-1Y-yRS"/>
                        <outlet property="btnPublic" destination="BSU-Z8-uDd" id="G7c-t2-fQk"/>
                        <outlet property="collectionHeight" destination="62k-KY-Rax" id="Ch9-0k-DvJ"/>
                        <outlet property="collectionType" destination="rWe-aF-H3l" id="8hQ-50-5IU"/>
                        <outlet property="img1" destination="oXa-7x-cWX" id="Yty-KK-RuY"/>
                        <outlet property="img2" destination="sQ6-p7-qW7" id="g4u-Lv-BnS"/>
                        <outlet property="img3" destination="wDA-wW-8ST" id="ATS-d0-U4b"/>
                        <outlet property="img4" destination="zLc-47-0RW" id="zZy-x6-cTl"/>
                        <outlet property="scrollDetails" destination="Uxf-C4-YBh" id="4Sv-rK-BRg"/>
                        <outlet property="stackViewOnlyFavourite" destination="gdH-bp-qNv" id="FaV-Or-1tE"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="3dz-mV-h4c" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="602.39999999999998" y="69.715142428785612"/>
        </scene>
        <!--Connector DetailsVC-->
        <scene sceneID="uYN-vT-g6c">
            <objects>
                <viewController storyboardIdentifier="ConnectorDetailsVC" id="0qj-13-M06" customClass="ConnectorDetailsVC" customModule="NXC_EV_Solutions" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="zEx-b0-hjY">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="X1F-mP-35d">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="HQr-JQ-FLp">
                                        <rect key="frame" x="12" y="5" width="34" height="34"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="34" id="8qj-nT-weN"/>
                                            <constraint firstAttribute="height" constant="34" id="pid-Uw-kLm"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="8" minY="6" maxX="8" maxY="6"/>
                                        <state key="normal" image="ic_back_arrow"/>
                                        <connections>
                                            <action selector="backAction:" destination="0qj-13-M06" eventType="touchUpInside" id="eE6-eA-iJV"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="EV Charging Station" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="AWQ-0y-610">
                                        <rect key="frame" x="108" y="12" width="159.5" height="20.5"/>
                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                        <color key="textColor" red="0.070588235289999995" green="0.070588235289999995" blue="0.070588235289999995" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <button opaque="NO" alpha="0.0" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="vXj-AW-t2w">
                                        <rect key="frame" x="329" y="5" width="34" height="34"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="34" id="Xqk-Hc-58k"/>
                                            <constraint firstAttribute="width" constant="34" id="qPw-b9-8sn"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="6" minY="6" maxX="6" maxY="6"/>
                                        <state key="normal" image="ic_transaction_history"/>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="AWQ-0y-610" firstAttribute="centerX" secondItem="X1F-mP-35d" secondAttribute="centerX" id="7fG-7u-ipv"/>
                                    <constraint firstItem="HQr-JQ-FLp" firstAttribute="centerY" secondItem="X1F-mP-35d" secondAttribute="centerY" id="Ixm-U2-Lwh"/>
                                    <constraint firstAttribute="trailing" secondItem="vXj-AW-t2w" secondAttribute="trailing" constant="12" id="XyO-mp-Hgt"/>
                                    <constraint firstItem="AWQ-0y-610" firstAttribute="centerY" secondItem="X1F-mP-35d" secondAttribute="centerY" id="i9f-58-PQN"/>
                                    <constraint firstAttribute="height" constant="44" id="vQK-Fk-tuF"/>
                                    <constraint firstItem="HQr-JQ-FLp" firstAttribute="leading" secondItem="X1F-mP-35d" secondAttribute="leading" constant="12" id="vtx-Iv-bfI"/>
                                    <constraint firstItem="vXj-AW-t2w" firstAttribute="centerY" secondItem="X1F-mP-35d" secondAttribute="centerY" id="yLv-lI-P2N"/>
                                </constraints>
                            </view>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" translatesAutoresizingMaskIntoConstraints="NO" id="278-ey-WjL">
                                <rect key="frame" x="4" y="44" width="367" height="563"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="ftz-cJ-gma">
                                        <rect key="frame" x="8" y="0.0" width="351" height="791"/>
                                        <subviews>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Z56-as-Hlx">
                                                <rect key="frame" x="0.0" y="0.0" width="351" height="791"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="btm-Bs-cxc">
                                                        <rect key="frame" x="4" y="4" width="343" height="783"/>
                                                        <subviews>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="pJx-ri-Ce1">
                                                                <rect key="frame" x="0.0" y="0.0" width="343" height="279"/>
                                                                <subviews>
                                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="wnB-G2-ZfZ">
                                                                        <rect key="frame" x="0.0" y="0.0" width="343" height="279"/>
                                                                        <subviews>
                                                                            <imageView hidden="YES" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="hll-2I-AKg">
                                                                                <rect key="frame" x="0.0" y="-150" width="343" height="150"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="height" constant="150" id="aTb-vC-K0I"/>
                                                                                </constraints>
                                                                            </imageView>
                                                                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" pagingEnabled="YES" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" translatesAutoresizingMaskIntoConstraints="NO" id="j7d-cV-iLE">
                                                                                <rect key="frame" x="0.0" y="0.0" width="343" height="175"/>
                                                                                <subviews>
                                                                                    <view clipsSubviews="YES" alpha="0.0" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="PNt-45-K3v">
                                                                                        <rect key="frame" x="0.0" y="0.0" width="343" height="175"/>
                                                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                                                    </view>
                                                                                </subviews>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="trailing" secondItem="PNt-45-K3v" secondAttribute="trailing" id="0oR-vg-bdh"/>
                                                                                    <constraint firstItem="PNt-45-K3v" firstAttribute="leading" secondItem="j7d-cV-iLE" secondAttribute="leading" id="U0x-k7-ckL"/>
                                                                                    <constraint firstItem="PNt-45-K3v" firstAttribute="centerX" secondItem="j7d-cV-iLE" secondAttribute="centerX" id="eoi-wn-fZl"/>
                                                                                    <constraint firstItem="PNt-45-K3v" firstAttribute="centerY" secondItem="j7d-cV-iLE" secondAttribute="centerY" id="hZi-oM-b9j"/>
                                                                                    <constraint firstAttribute="bottom" secondItem="PNt-45-K3v" secondAttribute="bottom" id="pPR-zz-w24"/>
                                                                                    <constraint firstAttribute="height" constant="175" id="qz9-5y-LG3"/>
                                                                                    <constraint firstItem="PNt-45-K3v" firstAttribute="top" secondItem="j7d-cV-iLE" secondAttribute="top" id="x8p-Dy-5qP"/>
                                                                                </constraints>
                                                                                <viewLayoutGuide key="contentLayoutGuide" id="Etb-Kb-K0h"/>
                                                                                <viewLayoutGuide key="frameLayoutGuide" id="zSv-Hb-tsr"/>
                                                                            </scrollView>
                                                                            <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="gxA-XD-hcU">
                                                                                <rect key="frame" x="8.5" y="175" width="326" height="45"/>
                                                                                <subviews>
                                                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="2" translatesAutoresizingMaskIntoConstraints="NO" id="zDf-ZP-l55">
                                                                                        <rect key="frame" x="0.0" y="3" width="326" height="39.5"/>
                                                                                        <subviews>
                                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" text="NXC Controls Pvt. Ltd." lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="lHx-tG-f3I">
                                                                                                <rect key="frame" x="0.0" y="0.0" width="326" height="20.5"/>
                                                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                                                <nil key="textColor"/>
                                                                                                <nil key="highlightedColor"/>
                                                                                            </label>
                                                                                            <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="G28-xP-kZI">
                                                                                                <rect key="frame" x="0.0" y="22.5" width="326" height="17"/>
                                                                                                <subviews>
                                                                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_location_icon" translatesAutoresizingMaskIntoConstraints="NO" id="itH-7D-aOE">
                                                                                                        <rect key="frame" x="0.0" y="0.5" width="16" height="16"/>
                                                                                                        <constraints>
                                                                                                            <constraint firstAttribute="height" constant="16" id="dKj-cY-vAN"/>
                                                                                                            <constraint firstAttribute="width" constant="16" id="nCQ-ie-A7j"/>
                                                                                                        </constraints>
                                                                                                    </imageView>
                                                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Nikol, Ahmedabad" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="zxw-5t-ylD">
                                                                                                        <rect key="frame" x="24" y="0.0" width="302" height="17"/>
                                                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                                                        <color key="textColor" name="SecondaryGrayText"/>
                                                                                                        <nil key="highlightedColor"/>
                                                                                                    </label>
                                                                                                </subviews>
                                                                                            </stackView>
                                                                                        </subviews>
                                                                                    </stackView>
                                                                                </subviews>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="height" constant="45" id="6Tg-wz-B3p"/>
                                                                                </constraints>
                                                                            </stackView>
                                                                            <stackView opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="aSl-iR-LnF">
                                                                                <rect key="frame" x="8.5" y="220" width="326" height="25"/>
                                                                                <subviews>
                                                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="pvi-60-lyw" customClass="CosmosView" customModule="Cosmos">
                                                                                        <rect key="frame" x="0.0" y="0.0" width="98" height="25"/>
                                                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                                                        <constraints>
                                                                                            <constraint firstAttribute="height" constant="25" id="6VJ-D4-cZP"/>
                                                                                        </constraints>
                                                                                        <userDefinedRuntimeAttributes>
                                                                                            <userDefinedRuntimeAttribute type="number" keyPath="rating">
                                                                                                <real key="value" value="3"/>
                                                                                            </userDefinedRuntimeAttribute>
                                                                                            <userDefinedRuntimeAttribute type="number" keyPath="starSize">
                                                                                                <real key="value" value="20"/>
                                                                                            </userDefinedRuntimeAttribute>
                                                                                        </userDefinedRuntimeAttributes>
                                                                                    </view>
                                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="4.5 Out Of 5" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="DPZ-3t-9E6">
                                                                                        <rect key="frame" x="98" y="0.0" width="228" height="25"/>
                                                                                        <constraints>
                                                                                            <constraint firstAttribute="height" constant="25" id="9ue-RU-xRD"/>
                                                                                        </constraints>
                                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                                        <color key="textColor" name="SecondaryGrayText"/>
                                                                                        <nil key="highlightedColor"/>
                                                                                    </label>
                                                                                </subviews>
                                                                                <constraints>
                                                                                    <constraint firstItem="pvi-60-lyw" firstAttribute="width" secondItem="aSl-iR-LnF" secondAttribute="width" multiplier="0.3" id="SSO-P9-OYd"/>
                                                                                </constraints>
                                                                            </stackView>
                                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Czo-bF-cme">
                                                                                <rect key="frame" x="0.0" y="245" width="343" height="34"/>
                                                                                <subviews>
                                                                                    <stackView opaque="NO" contentMode="scaleToFill" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="3mP-Ma-Tll">
                                                                                        <rect key="frame" x="104.5" y="7" width="134.5" height="20"/>
                                                                                        <subviews>
                                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Get Directions" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="fVd-fL-pdP">
                                                                                                <rect key="frame" x="0.0" y="0.0" width="102.5" height="20"/>
                                                                                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="15"/>
                                                                                                <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                                                <nil key="highlightedColor"/>
                                                                                            </label>
                                                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_direction" translatesAutoresizingMaskIntoConstraints="NO" id="ZLd-lr-mLN">
                                                                                                <rect key="frame" x="114.5" y="0.0" width="20" height="20"/>
                                                                                                <constraints>
                                                                                                    <constraint firstAttribute="height" constant="20" id="SLo-bL-AXi"/>
                                                                                                    <constraint firstAttribute="width" constant="20" id="gjg-uE-g7a"/>
                                                                                                </constraints>
                                                                                            </imageView>
                                                                                        </subviews>
                                                                                    </stackView>
                                                                                </subviews>
                                                                                <color key="backgroundColor" name="Primary"/>
                                                                                <constraints>
                                                                                    <constraint firstItem="3mP-Ma-Tll" firstAttribute="centerY" secondItem="Czo-bF-cme" secondAttribute="centerY" id="SMW-9i-RN2"/>
                                                                                    <constraint firstItem="3mP-Ma-Tll" firstAttribute="centerX" secondItem="Czo-bF-cme" secondAttribute="centerX" id="esU-vu-gSx"/>
                                                                                    <constraint firstAttribute="height" constant="34" id="riI-Ll-OLo"/>
                                                                                </constraints>
                                                                            </view>
                                                                        </subviews>
                                                                        <constraints>
                                                                            <constraint firstItem="Czo-bF-cme" firstAttribute="width" secondItem="wnB-G2-ZfZ" secondAttribute="width" id="DEH-OA-loC"/>
                                                                            <constraint firstItem="j7d-cV-iLE" firstAttribute="width" secondItem="wnB-G2-ZfZ" secondAttribute="width" id="WIc-Wt-7mc"/>
                                                                            <constraint firstItem="aSl-iR-LnF" firstAttribute="width" secondItem="wnB-G2-ZfZ" secondAttribute="width" multiplier="0.95" id="Xxb-zd-Cwe"/>
                                                                            <constraint firstItem="hll-2I-AKg" firstAttribute="width" secondItem="wnB-G2-ZfZ" secondAttribute="width" id="tOZ-Pm-Tlv"/>
                                                                            <constraint firstItem="gxA-XD-hcU" firstAttribute="width" secondItem="wnB-G2-ZfZ" secondAttribute="width" multiplier="0.95" id="yea-G1-w3W"/>
                                                                        </constraints>
                                                                    </stackView>
                                                                    <pageControl opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" numberOfPages="3" translatesAutoresizingMaskIntoConstraints="NO" id="vTr-yw-xOU">
                                                                        <rect key="frame" x="94" y="145" width="155.5" height="26"/>
                                                                        <color key="pageIndicatorTintColor" white="0.66666666666666663" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <color key="currentPageIndicatorTintColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                    </pageControl>
                                                                </subviews>
                                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                                <constraints>
                                                                    <constraint firstItem="wnB-G2-ZfZ" firstAttribute="leading" secondItem="pJx-ri-Ce1" secondAttribute="leading" id="06B-MY-2NZ"/>
                                                                    <constraint firstItem="vTr-yw-xOU" firstAttribute="bottom" secondItem="j7d-cV-iLE" secondAttribute="bottom" constant="-4" id="MC1-xS-k7j"/>
                                                                    <constraint firstItem="wnB-G2-ZfZ" firstAttribute="top" secondItem="pJx-ri-Ce1" secondAttribute="top" id="UaP-ck-gfI"/>
                                                                    <constraint firstAttribute="trailing" secondItem="wnB-G2-ZfZ" secondAttribute="trailing" id="Zb1-r5-ppI"/>
                                                                    <constraint firstAttribute="bottom" secondItem="wnB-G2-ZfZ" secondAttribute="bottom" id="cvd-EJ-8Qn"/>
                                                                    <constraint firstItem="vTr-yw-xOU" firstAttribute="centerX" secondItem="j7d-cV-iLE" secondAttribute="centerX" id="zB0-ug-1QE"/>
                                                                </constraints>
                                                            </view>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="oRd-fE-tja">
                                                                <rect key="frame" x="0.0" y="291" width="343" height="140"/>
                                                                <subviews>
                                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="BEt-J3-l8Q">
                                                                        <rect key="frame" x="0.0" y="0.0" width="343" height="140"/>
                                                                        <subviews>
                                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="0Uu-sY-pqY">
                                                                                <rect key="frame" x="0.0" y="0.0" width="343" height="40"/>
                                                                                <subviews>
                                                                                    <stackView opaque="NO" contentMode="scaleToFill" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="bQi-6s-bzC">
                                                                                        <rect key="frame" x="88" y="11" width="167.5" height="18"/>
                                                                                        <subviews>
                                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Charge Station Timings" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="WQE-I7-zQp">
                                                                                                <rect key="frame" x="0.0" y="0.0" width="167.5" height="18"/>
                                                                                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="15"/>
                                                                                                <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                                                <nil key="highlightedColor"/>
                                                                                            </label>
                                                                                        </subviews>
                                                                                    </stackView>
                                                                                </subviews>
                                                                                <color key="backgroundColor" name="Primary"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="height" constant="40" id="00n-ff-MGe"/>
                                                                                    <constraint firstItem="bQi-6s-bzC" firstAttribute="centerX" secondItem="0Uu-sY-pqY" secondAttribute="centerX" id="UEY-5V-Xtb"/>
                                                                                    <constraint firstItem="bQi-6s-bzC" firstAttribute="centerY" secondItem="0Uu-sY-pqY" secondAttribute="centerY" id="ylK-E7-RK3"/>
                                                                                </constraints>
                                                                            </view>
                                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Iqm-5T-LVC">
                                                                                <rect key="frame" x="0.0" y="40" width="343" height="100"/>
                                                                                <subviews>
                                                                                    <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" dataMode="prototypes" translatesAutoresizingMaskIntoConstraints="NO" id="7Gh-ge-4dI">
                                                                                        <rect key="frame" x="8" y="8" width="327" height="52"/>
                                                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                                                        <collectionViewFlowLayout key="collectionViewLayout" automaticEstimatedItemSize="YES" minimumLineSpacing="0.0" minimumInteritemSpacing="0.0" id="Vls-V2-art">
                                                                                            <size key="itemSize" width="128" height="128"/>
                                                                                            <size key="headerReferenceSize" width="0.0" height="0.0"/>
                                                                                            <size key="footerReferenceSize" width="0.0" height="0.0"/>
                                                                                            <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                                                                                        </collectionViewFlowLayout>
                                                                                        <cells>
                                                                                            <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" reuseIdentifier="cell" id="e6r-bc-1Xi" customClass="ConnectorTimeCell" customModule="NXC_EV_Solutions" customModuleProvider="target">
                                                                                                <rect key="frame" x="0.0" y="0.0" width="50" height="50"/>
                                                                                                <autoresizingMask key="autoresizingMask"/>
                                                                                                <collectionViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" id="fBc-1n-KkO">
                                                                                                    <rect key="frame" x="0.0" y="0.0" width="50" height="50"/>
                                                                                                    <autoresizingMask key="autoresizingMask"/>
                                                                                                    <subviews>
                                                                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="F98-Vf-DKg">
                                                                                                            <rect key="frame" x="5" y="5" width="40" height="40"/>
                                                                                                            <subviews>
                                                                                                                <stackView opaque="NO" contentMode="scaleAspectFit" axis="vertical" alignment="center" spacing="1" translatesAutoresizingMaskIntoConstraints="NO" id="znb-11-qEI">
                                                                                                                    <rect key="frame" x="0.0" y="2" width="40" height="32"/>
                                                                                                                    <subviews>
                                                                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="We" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="6Ah-i8-M1y">
                                                                                                                            <rect key="frame" x="9" y="0.0" width="22" height="26"/>
                                                                                                                            <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                                                                            <nil key="textColor"/>
                                                                                                                            <nil key="highlightedColor"/>
                                                                                                                        </label>
                                                                                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_white_circle" translatesAutoresizingMaskIntoConstraints="NO" id="YDB-xz-Eeh">
                                                                                                                            <rect key="frame" x="17.5" y="27" width="5" height="5"/>
                                                                                                                            <constraints>
                                                                                                                                <constraint firstAttribute="height" constant="5" id="gui-ZU-DlO"/>
                                                                                                                                <constraint firstAttribute="width" constant="5" id="oWb-gs-ZHP"/>
                                                                                                                            </constraints>
                                                                                                                        </imageView>
                                                                                                                    </subviews>
                                                                                                                </stackView>
                                                                                                            </subviews>
                                                                                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                                            <constraints>
                                                                                                                <constraint firstItem="znb-11-qEI" firstAttribute="top" secondItem="F98-Vf-DKg" secondAttribute="top" constant="2" id="5Ku-lf-e7u"/>
                                                                                                                <constraint firstAttribute="bottom" secondItem="znb-11-qEI" secondAttribute="bottom" constant="6" id="9hi-yE-MnM"/>
                                                                                                                <constraint firstAttribute="height" constant="40" id="E26-TI-mXc"/>
                                                                                                                <constraint firstItem="znb-11-qEI" firstAttribute="leading" secondItem="F98-Vf-DKg" secondAttribute="leading" id="OqT-Vv-W7v"/>
                                                                                                                <constraint firstAttribute="width" constant="40" id="bQj-Dp-4SL"/>
                                                                                                                <constraint firstAttribute="trailing" secondItem="znb-11-qEI" secondAttribute="trailing" id="yQa-88-Pov"/>
                                                                                                            </constraints>
                                                                                                        </view>
                                                                                                    </subviews>
                                                                                                    <constraints>
                                                                                                        <constraint firstItem="F98-Vf-DKg" firstAttribute="centerY" secondItem="fBc-1n-KkO" secondAttribute="centerY" id="jFk-sg-uTo"/>
                                                                                                        <constraint firstItem="F98-Vf-DKg" firstAttribute="centerX" secondItem="fBc-1n-KkO" secondAttribute="centerX" id="oo8-2t-vCO"/>
                                                                                                    </constraints>
                                                                                                </collectionViewCellContentView>
                                                                                                <size key="customSize" width="50" height="50"/>
                                                                                                <connections>
                                                                                                    <outlet property="imgTime" destination="YDB-xz-Eeh" id="Uij-mN-n69"/>
                                                                                                    <outlet property="lblTime" destination="6Ah-i8-M1y" id="2HD-md-OYg"/>
                                                                                                    <outlet property="viewMain" destination="F98-Vf-DKg" id="Fj4-1o-7Td"/>
                                                                                                </connections>
                                                                                            </collectionViewCell>
                                                                                        </cells>
                                                                                    </collectionView>
                                                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_station_timings" translatesAutoresizingMaskIntoConstraints="NO" id="IKA-tD-T7a">
                                                                                        <rect key="frame" x="12" y="70" width="20" height="20"/>
                                                                                        <constraints>
                                                                                            <constraint firstAttribute="width" constant="20" id="Iaf-j4-sBN"/>
                                                                                            <constraint firstAttribute="height" constant="20" id="b7k-jY-cyi"/>
                                                                                        </constraints>
                                                                                    </imageView>
                                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Wed . 10:00 - 17:00" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Cw4-zZ-7Cx">
                                                                                        <rect key="frame" x="36" y="68" width="126.5" height="24"/>
                                                                                        <constraints>
                                                                                            <constraint firstAttribute="height" constant="24" id="MDr-62-em8"/>
                                                                                        </constraints>
                                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                                        <color key="textColor" red="0.94901960784313721" green="0.50980392156862742" blue="0.31764705882352939" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                                        <nil key="highlightedColor"/>
                                                                                    </label>
                                                                                </subviews>
                                                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                                                <constraints>
                                                                                    <constraint firstItem="7Gh-ge-4dI" firstAttribute="top" secondItem="Iqm-5T-LVC" secondAttribute="top" constant="8" id="3RZ-54-XhT"/>
                                                                                    <constraint firstItem="Cw4-zZ-7Cx" firstAttribute="leading" secondItem="IKA-tD-T7a" secondAttribute="trailing" constant="4" id="6BR-y9-rtM"/>
                                                                                    <constraint firstItem="Cw4-zZ-7Cx" firstAttribute="top" secondItem="7Gh-ge-4dI" secondAttribute="bottom" constant="8" id="A1n-0b-RPV"/>
                                                                                    <constraint firstAttribute="bottom" secondItem="Cw4-zZ-7Cx" secondAttribute="bottom" constant="8" id="Iqf-sb-JND"/>
                                                                                    <constraint firstAttribute="height" constant="100" id="Jik-Ed-2OV"/>
                                                                                    <constraint firstItem="IKA-tD-T7a" firstAttribute="centerY" secondItem="Cw4-zZ-7Cx" secondAttribute="centerY" id="ShG-Sk-IRm"/>
                                                                                    <constraint firstAttribute="trailing" secondItem="7Gh-ge-4dI" secondAttribute="trailing" constant="8" id="V8v-pM-U1m"/>
                                                                                    <constraint firstItem="IKA-tD-T7a" firstAttribute="leading" secondItem="Iqm-5T-LVC" secondAttribute="leading" constant="12" id="j4t-E8-keH"/>
                                                                                    <constraint firstItem="7Gh-ge-4dI" firstAttribute="leading" secondItem="Iqm-5T-LVC" secondAttribute="leading" constant="8" id="v7F-Gy-IFE"/>
                                                                                </constraints>
                                                                            </view>
                                                                        </subviews>
                                                                    </stackView>
                                                                </subviews>
                                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                                <constraints>
                                                                    <constraint firstItem="BEt-J3-l8Q" firstAttribute="top" secondItem="oRd-fE-tja" secondAttribute="top" id="2MH-oi-Jea"/>
                                                                    <constraint firstAttribute="bottom" secondItem="BEt-J3-l8Q" secondAttribute="bottom" id="3sf-Dz-KIa"/>
                                                                    <constraint firstItem="BEt-J3-l8Q" firstAttribute="leading" secondItem="oRd-fE-tja" secondAttribute="leading" id="7cv-HR-FJI"/>
                                                                    <constraint firstAttribute="trailing" secondItem="BEt-J3-l8Q" secondAttribute="trailing" id="XYN-If-dAN"/>
                                                                </constraints>
                                                            </view>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Yys-5w-eUy">
                                                                <rect key="frame" x="0.0" y="443" width="343" height="340"/>
                                                                <subviews>
                                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="i1N-nE-x8V">
                                                                        <rect key="frame" x="0.0" y="0.0" width="343" height="340"/>
                                                                        <subviews>
                                                                            <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Ibv-sg-5EO">
                                                                                <rect key="frame" x="0.0" y="0.0" width="343" height="340"/>
                                                                                <subviews>
                                                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="gB3-Qh-Vcn">
                                                                                        <rect key="frame" x="4" y="8" width="335" height="324"/>
                                                                                        <subviews>
                                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="By Charger Type" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ckT-sQ-Qoh">
                                                                                                <rect key="frame" x="0.0" y="0.0" width="335" height="24"/>
                                                                                                <constraints>
                                                                                                    <constraint firstAttribute="height" constant="24" id="J7g-Wb-PPM"/>
                                                                                                </constraints>
                                                                                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="16"/>
                                                                                                <color key="textColor" red="0.070588235289999995" green="0.070588235289999995" blue="0.070588235289999995" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                                                <nil key="highlightedColor"/>
                                                                                            </label>
                                                                                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" scrollEnabled="NO" dataMode="prototypes" style="plain" separatorStyle="none" allowsSelection="NO" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="1" sectionFooterHeight="1" translatesAutoresizingMaskIntoConstraints="NO" id="fNG-d5-JtD">
                                                                                                <rect key="frame" x="0.0" y="24" width="335" height="300"/>
                                                                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                                                                <constraints>
                                                                                                    <constraint firstAttribute="height" constant="300" id="UIo-yW-LMN"/>
                                                                                                </constraints>
                                                                                                <prototypes>
                                                                                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" reuseIdentifier="cell" rowHeight="90" id="qYO-Di-yvH" customClass="ConnectorDetailsCell" customModule="NXC_EV_Solutions" customModuleProvider="target">
                                                                                                        <rect key="frame" x="0.0" y="44.5" width="335" height="90"/>
                                                                                                        <autoresizingMask key="autoresizingMask"/>
                                                                                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="qYO-Di-yvH" id="m0q-ih-Nk7">
                                                                                                            <rect key="frame" x="0.0" y="0.0" width="335" height="90"/>
                                                                                                            <autoresizingMask key="autoresizingMask"/>
                                                                                                            <subviews>
                                                                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Zj7-tB-97W">
                                                                                                                    <rect key="frame" x="4" y="4" width="327" height="82"/>
                                                                                                                    <subviews>
                                                                                                                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="fXu-2z-rYk">
                                                                                                                            <rect key="frame" x="4" y="0.0" width="319" height="82"/>
                                                                                                                            <subviews>
                                                                                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Lhy-xq-lFI">
                                                                                                                                    <rect key="frame" x="0.0" y="0.0" width="319" height="82"/>
                                                                                                                                    <subviews>
                                                                                                                                        <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="6" translatesAutoresizingMaskIntoConstraints="NO" id="eTO-uF-uRh">
                                                                                                                                            <rect key="frame" x="6" y="2" width="307" height="30"/>
                                                                                                                                            <subviews>
                                                                                                                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_uncheck" translatesAutoresizingMaskIntoConstraints="NO" id="1eA-Ns-Kjx">
                                                                                                                                                    <rect key="frame" x="0.0" y="7.5" width="15" height="15"/>
                                                                                                                                                    <constraints>
                                                                                                                                                        <constraint firstAttribute="height" constant="15" id="d7F-hq-aaJ"/>
                                                                                                                                                        <constraint firstAttribute="width" constant="15" id="kq9-xf-pFr"/>
                                                                                                                                                    </constraints>
                                                                                                                                                </imageView>
                                                                                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Available" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="yZ9-rB-tOg">
                                                                                                                                                    <rect key="frame" x="21" y="6" width="125" height="18"/>
                                                                                                                                                    <constraints>
                                                                                                                                                        <constraint firstAttribute="width" constant="125" id="mRm-7N-9EE"/>
                                                                                                                                                    </constraints>
                                                                                                                                                    <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                                                                                                    <color key="textColor" name="Primary"/>
                                                                                                                                                    <nil key="highlightedColor"/>
                                                                                                                                                </label>
                                                                                                                                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="bottom" translatesAutoresizingMaskIntoConstraints="NO" id="62b-TA-XxQ">
                                                                                                                                                    <rect key="frame" x="152" y="0.0" width="155" height="30"/>
                                                                                                                                                    <subviews>
                                                                                                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Plug 1" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="eD9-6i-Kt0">
                                                                                                                                                            <rect key="frame" x="114" y="0.0" width="41" height="30"/>
                                                                                                                                                            <constraints>
                                                                                                                                                                <constraint firstAttribute="height" constant="30" id="v2G-sW-dc8"/>
                                                                                                                                                            </constraints>
                                                                                                                                                            <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                                                                                                            <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                                                                                            <nil key="highlightedColor"/>
                                                                                                                                                        </label>
                                                                                                                                                    </subviews>
                                                                                                                                                    <constraints>
                                                                                                                                                        <constraint firstAttribute="height" constant="30" id="zjX-Ik-nM6"/>
                                                                                                                                                    </constraints>
                                                                                                                                                </stackView>
                                                                                                                                            </subviews>
                                                                                                                                        </stackView>
                                                                                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="5Dw-fK-ju7">
                                                                                                                                            <rect key="frame" x="0.0" y="33" width="319" height="1"/>
                                                                                                                                            <color key="backgroundColor" name="PartitionBG"/>
                                                                                                                                            <constraints>
                                                                                                                                                <constraint firstAttribute="height" constant="1" id="Jsz-zW-zSe"/>
                                                                                                                                            </constraints>
                                                                                                                                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                                                                                            <nil key="textColor"/>
                                                                                                                                            <nil key="highlightedColor"/>
                                                                                                                                        </label>
                                                                                                                                        <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="hgQ-a7-ZmN">
                                                                                                                                            <rect key="frame" x="208" y="38" width="103" height="38"/>
                                                                                                                                            <subviews>
                                                                                                                                                <stackView opaque="NO" contentMode="scaleToFill" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="JOY-6Y-74Z">
                                                                                                                                                    <rect key="frame" x="0.0" y="7" width="103" height="24"/>
                                                                                                                                                    <subviews>
                                                                                                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="₹ 0.80" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="pGc-Tc-huC">
                                                                                                                                                            <rect key="frame" x="0.0" y="0.0" width="44.5" height="24"/>
                                                                                                                                                            <constraints>
                                                                                                                                                                <constraint firstAttribute="height" constant="24" id="plI-3J-i9D"/>
                                                                                                                                                            </constraints>
                                                                                                                                                            <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                                                                                                            <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                                                                                            <nil key="highlightedColor"/>
                                                                                                                                                        </label>
                                                                                                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Per kWh" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="0a3-N0-uzB">
                                                                                                                                                            <rect key="frame" x="48.5" y="0.0" width="54.5" height="24"/>
                                                                                                                                                            <constraints>
                                                                                                                                                                <constraint firstAttribute="height" constant="24" id="vcE-LA-X09"/>
                                                                                                                                                            </constraints>
                                                                                                                                                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                                                                                                            <color key="textColor" white="0.0" alpha="0.5" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                                                                                            <nil key="highlightedColor"/>
                                                                                                                                                        </label>
                                                                                                                                                    </subviews>
                                                                                                                                                </stackView>
                                                                                                                                            </subviews>
                                                                                                                                        </stackView>
                                                                                                                                        <stackView opaque="NO" contentMode="scaleToFill" alignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="QS0-5X-jpY">
                                                                                                                                            <rect key="frame" x="8" y="42" width="142" height="30"/>
                                                                                                                                            <subviews>
                                                                                                                                                <stackView opaque="NO" contentMode="scaleToFill" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="BL6-WY-cQ5">
                                                                                                                                                    <rect key="frame" x="0.0" y="3" width="53" height="24"/>
                                                                                                                                                    <subviews>
                                                                                                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="55 kWh" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="JMj-PQ-4eo">
                                                                                                                                                            <rect key="frame" x="0.0" y="0.0" width="53" height="24"/>
                                                                                                                                                            <constraints>
                                                                                                                                                                <constraint firstAttribute="height" constant="24" id="q2G-fN-9xX"/>
                                                                                                                                                            </constraints>
                                                                                                                                                            <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                                                                                                            <color key="textColor" white="0.0" alpha="0.5" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                                                                                            <nil key="highlightedColor"/>
                                                                                                                                                        </label>
                                                                                                                                                    </subviews>
                                                                                                                                                </stackView>
                                                                                                                                                <stackView opaque="NO" contentMode="scaleToFill" alignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="cp5-6d-BOM">
                                                                                                                                                    <rect key="frame" x="57" y="0.0" width="85" height="30"/>
                                                                                                                                                    <subviews>
                                                                                                                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_chargerSpeed" translatesAutoresizingMaskIntoConstraints="NO" id="vxK-D3-zJD">
                                                                                                                                                            <rect key="frame" x="0.0" y="5" width="15" height="20"/>
                                                                                                                                                            <constraints>
                                                                                                                                                                <constraint firstAttribute="width" constant="15" id="Dp0-3l-pnN"/>
                                                                                                                                                                <constraint firstAttribute="height" constant="20" id="jzZ-Px-Vti"/>
                                                                                                                                                            </constraints>
                                                                                                                                                        </imageView>
                                                                                                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Fast" textAlignment="left" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="vRn-75-yxY">
                                                                                                                                                            <rect key="frame" x="15" y="3" width="85" height="24"/>
                                                                                                                                                            <constraints>
                                                                                                                                                                <constraint firstAttribute="width" constant="85" id="SlL-ES-O2z"/>
                                                                                                                                                                <constraint firstAttribute="height" constant="24" id="SxA-dC-kye"/>
                                                                                                                                                            </constraints>
                                                                                                                                                            <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                                                                                                            <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                                                                                            <nil key="highlightedColor"/>
                                                                                                                                                        </label>
                                                                                                                                                    </subviews>
                                                                                                                                                    <constraints>
                                                                                                                                                        <constraint firstAttribute="height" constant="30" id="qTx-gk-3Zj"/>
                                                                                                                                                    </constraints>
                                                                                                                                                </stackView>
                                                                                                                                            </subviews>
                                                                                                                                        </stackView>
                                                                                                                                    </subviews>
                                                                                                                                    <color key="backgroundColor" red="0.96470588235294119" green="0.96470588235294119" blue="0.96862745098039216" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                                                                                    <constraints>
                                                                                                                                        <constraint firstItem="hgQ-a7-ZmN" firstAttribute="top" secondItem="5Dw-fK-ju7" secondAttribute="bottom" constant="4" id="2Lt-67-5DQ"/>
                                                                                                                                        <constraint firstAttribute="bottom" secondItem="hgQ-a7-ZmN" secondAttribute="bottom" constant="6" id="5DI-IE-x8x"/>
                                                                                                                                        <constraint firstItem="5Dw-fK-ju7" firstAttribute="leading" secondItem="Lhy-xq-lFI" secondAttribute="leading" id="77o-fb-Wi6"/>
                                                                                                                                        <constraint firstItem="eTO-uF-uRh" firstAttribute="leading" secondItem="Lhy-xq-lFI" secondAttribute="leading" constant="6" id="Dv6-jH-OqU"/>
                                                                                                                                        <constraint firstAttribute="trailing" secondItem="hgQ-a7-ZmN" secondAttribute="trailing" constant="8" id="EIz-Ek-cKl"/>
                                                                                                                                        <constraint firstAttribute="trailing" secondItem="5Dw-fK-ju7" secondAttribute="trailing" id="FyK-8h-pBa"/>
                                                                                                                                        <constraint firstItem="QS0-5X-jpY" firstAttribute="centerY" secondItem="hgQ-a7-ZmN" secondAttribute="centerY" id="HXQ-XS-Gm9"/>
                                                                                                                                        <constraint firstItem="eTO-uF-uRh" firstAttribute="top" secondItem="Lhy-xq-lFI" secondAttribute="top" constant="2" id="PbA-2E-kBd"/>
                                                                                                                                        <constraint firstItem="5Dw-fK-ju7" firstAttribute="top" secondItem="eTO-uF-uRh" secondAttribute="bottom" constant="1" id="RMt-VS-8B7"/>
                                                                                                                                        <constraint firstItem="QS0-5X-jpY" firstAttribute="leading" secondItem="Lhy-xq-lFI" secondAttribute="leading" constant="8" id="rka-Yf-e9f"/>
                                                                                                                                        <constraint firstAttribute="trailing" secondItem="eTO-uF-uRh" secondAttribute="trailing" constant="6" id="wel-Fm-k4I"/>
                                                                                                                                    </constraints>
                                                                                                                                </view>
                                                                                                                            </subviews>
                                                                                                                        </stackView>
                                                                                                                    </subviews>
                                                                                                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                                                    <constraints>
                                                                                                                        <constraint firstItem="fXu-2z-rYk" firstAttribute="leading" secondItem="Zj7-tB-97W" secondAttribute="leading" constant="4" id="5GJ-3f-uRo"/>
                                                                                                                        <constraint firstAttribute="bottom" secondItem="fXu-2z-rYk" secondAttribute="bottom" id="AbF-2W-a1p"/>
                                                                                                                        <constraint firstItem="fXu-2z-rYk" firstAttribute="top" secondItem="Zj7-tB-97W" secondAttribute="top" id="LbL-2d-3sp"/>
                                                                                                                        <constraint firstAttribute="trailing" secondItem="fXu-2z-rYk" secondAttribute="trailing" constant="4" id="QKo-wf-glR"/>
                                                                                                                    </constraints>
                                                                                                                </view>
                                                                                                            </subviews>
                                                                                                            <constraints>
                                                                                                                <constraint firstItem="Zj7-tB-97W" firstAttribute="leading" secondItem="m0q-ih-Nk7" secondAttribute="leading" constant="4" id="EnO-qD-HhP"/>
                                                                                                                <constraint firstItem="Zj7-tB-97W" firstAttribute="top" secondItem="m0q-ih-Nk7" secondAttribute="top" constant="4" id="PMH-4D-gQz"/>
                                                                                                                <constraint firstAttribute="bottom" secondItem="Zj7-tB-97W" secondAttribute="bottom" constant="4" id="hqq-8m-H9y"/>
                                                                                                                <constraint firstAttribute="trailing" secondItem="Zj7-tB-97W" secondAttribute="trailing" constant="4" id="vlN-Ze-6jX"/>
                                                                                                            </constraints>
                                                                                                        </tableViewCellContentView>
                                                                                                        <connections>
                                                                                                            <outlet property="imgAvailable" destination="1eA-Ns-Kjx" id="jXe-GS-e0B"/>
                                                                                                            <outlet property="imgSpeedType" destination="vxK-D3-zJD" id="vN5-ag-WHF"/>
                                                                                                            <outlet property="lblAvailable" destination="yZ9-rB-tOg" id="mfi-af-wkS"/>
                                                                                                            <outlet property="lblChargeName" destination="eD9-6i-Kt0" id="KLr-HG-V3d"/>
                                                                                                            <outlet property="lblPrice" destination="pGc-Tc-huC" id="kVj-pa-U8k"/>
                                                                                                            <outlet property="lblSpeed" destination="JMj-PQ-4eo" id="HOz-Ti-9Q8"/>
                                                                                                            <outlet property="lblSpeedType" destination="vRn-75-yxY" id="mwh-8x-atz"/>
                                                                                                            <outlet property="viewMain" destination="Lhy-xq-lFI" id="JT2-Pe-6ff"/>
                                                                                                        </connections>
                                                                                                    </tableViewCell>
                                                                                                </prototypes>
                                                                                            </tableView>
                                                                                        </subviews>
                                                                                    </stackView>
                                                                                </subviews>
                                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                <constraints>
                                                                                    <constraint firstItem="gB3-Qh-Vcn" firstAttribute="top" secondItem="Ibv-sg-5EO" secondAttribute="top" constant="8" id="7ub-z5-Bqc"/>
                                                                                    <constraint firstAttribute="bottom" secondItem="gB3-Qh-Vcn" secondAttribute="bottom" constant="8" id="BNM-Yn-amf"/>
                                                                                    <constraint firstItem="gB3-Qh-Vcn" firstAttribute="leading" secondItem="Ibv-sg-5EO" secondAttribute="leading" constant="4" id="K7E-dE-MjX"/>
                                                                                    <constraint firstAttribute="trailing" secondItem="gB3-Qh-Vcn" secondAttribute="trailing" constant="4" id="nQs-Gh-SzI"/>
                                                                                </constraints>
                                                                            </view>
                                                                        </subviews>
                                                                    </stackView>
                                                                </subviews>
                                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="trailing" secondItem="i1N-nE-x8V" secondAttribute="trailing" id="78d-8v-aL8"/>
                                                                    <constraint firstItem="i1N-nE-x8V" firstAttribute="top" secondItem="Yys-5w-eUy" secondAttribute="top" id="9FY-Oj-5jv"/>
                                                                    <constraint firstAttribute="bottom" secondItem="i1N-nE-x8V" secondAttribute="bottom" id="Y2X-Tm-ILR"/>
                                                                    <constraint firstItem="i1N-nE-x8V" firstAttribute="leading" secondItem="Yys-5w-eUy" secondAttribute="leading" id="kkP-N6-i7E"/>
                                                                </constraints>
                                                            </view>
                                                        </subviews>
                                                    </stackView>
                                                </subviews>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <constraints>
                                                    <constraint firstItem="btm-Bs-cxc" firstAttribute="leading" secondItem="Z56-as-Hlx" secondAttribute="leading" constant="4" id="WmQ-Pf-ndW"/>
                                                    <constraint firstItem="btm-Bs-cxc" firstAttribute="top" secondItem="Z56-as-Hlx" secondAttribute="top" constant="4" id="aDT-an-gei"/>
                                                    <constraint firstAttribute="trailing" secondItem="btm-Bs-cxc" secondAttribute="trailing" constant="4" id="fEX-ek-GsN"/>
                                                    <constraint firstAttribute="bottom" secondItem="btm-Bs-cxc" secondAttribute="bottom" constant="4" id="hq1-YA-qft"/>
                                                </constraints>
                                            </view>
                                        </subviews>
                                    </stackView>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="ftz-cJ-gma" firstAttribute="trailing" secondItem="ZkG-iO-ysb" secondAttribute="trailing" id="7cS-Ya-IKa"/>
                                    <constraint firstItem="ftz-cJ-gma" firstAttribute="top" secondItem="ZkG-iO-ysb" secondAttribute="top" id="G18-e8-F94"/>
                                    <constraint firstItem="ftz-cJ-gma" firstAttribute="centerX" secondItem="dWc-ng-yGX" secondAttribute="centerX" id="RTp-8j-Q1G"/>
                                    <constraint firstItem="ftz-cJ-gma" firstAttribute="leading" secondItem="ZkG-iO-ysb" secondAttribute="leading" constant="8" id="tk8-rm-w3v"/>
                                    <constraint firstItem="ftz-cJ-gma" firstAttribute="bottom" secondItem="ZkG-iO-ysb" secondAttribute="bottom" id="umc-lJ-nfT"/>
                                </constraints>
                                <viewLayoutGuide key="contentLayoutGuide" id="ZkG-iO-ysb"/>
                                <viewLayoutGuide key="frameLayoutGuide" id="dWc-ng-yGX"/>
                            </scrollView>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Vgl-63-hUa">
                                <rect key="frame" x="12" y="293" width="351" height="34"/>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <connections>
                                    <action selector="getDirectionsTapped:" destination="0qj-13-M06" eventType="touchUpInside" id="TOb-Yh-DST"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="FJk-Cx-X2D">
                                <rect key="frame" x="0.0" y="339" width="375" height="40"/>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <connections>
                                    <action selector="openCloseTapped:" destination="0qj-13-M06" eventType="touchUpInside" id="SNz-1w-NyL"/>
                                </connections>
                            </button>
                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="tD8-ZG-aDa">
                                <rect key="frame" x="19" y="615" width="337.5" height="44"/>
                                <subviews>
                                    <button hidden="YES" opaque="NO" tag="102" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="bHG-nO-Uzq">
                                        <rect key="frame" x="0.0" y="0.0" width="0.0" height="44"/>
                                        <color key="backgroundColor" name="PrimaryColor"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="44" id="XJZ-mI-n7t"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                        <state key="normal" title="Reserve Slot">
                                            <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        </state>
                                        <connections>
                                            <action selector="reserveSlotTapped:" destination="0qj-13-M06" eventType="touchUpInside" id="Tzm-hB-2O1"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" tag="102" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="u7e-Yy-oMA">
                                        <rect key="frame" x="0.0" y="0.0" width="337.5" height="44"/>
                                        <color key="backgroundColor" name="PrimaryColor"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="44" id="30c-gp-efY"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                        <state key="normal" title="Start Charging">
                                            <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        </state>
                                        <connections>
                                            <action selector="startChargingTapped:" destination="0qj-13-M06" eventType="touchUpInside" id="5ik-TZ-vfD"/>
                                        </connections>
                                    </button>
                                </subviews>
                            </stackView>
                            <view alpha="0.0" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Igo-wt-fDC">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="esR-ea-bNy">
                                        <rect key="frame" x="0.0" y="372" width="375" height="295"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Book Charger Slot" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="u5G-Ox-GTA">
                                                <rect key="frame" x="12" y="8" width="128" height="34"/>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="34" id="5uk-8U-RBz"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" weight="medium" pointSize="15"/>
                                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="7ff-vj-SqK">
                                                <rect key="frame" x="337" y="8" width="34" height="34"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="34" id="HEd-b7-tcJ"/>
                                                    <constraint firstAttribute="height" constant="34" id="vnC-Hd-IaY"/>
                                                </constraints>
                                                <inset key="imageEdgeInsets" minX="10" minY="10" maxX="10" maxY="10"/>
                                                <state key="normal" image="ic_cancel"/>
                                                <connections>
                                                    <action selector="reservePopupCloseTapped:" destination="0qj-13-M06" eventType="touchUpInside" id="Enb-Ai-uPZ"/>
                                                </connections>
                                            </button>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="0JT-XR-sNd">
                                                <rect key="frame" x="19" y="67" width="337.5" height="72"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="UZq-tw-Ud2">
                                                        <rect key="frame" x="67.5" y="8" width="202.5" height="56"/>
                                                        <subviews>
                                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="801-Xr-npj">
                                                                <rect key="frame" x="0.0" y="0.0" width="79.5" height="56"/>
                                                                <subviews>
                                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="XgR-Kr-e7e">
                                                                        <rect key="frame" x="24.5" y="0.0" width="30" height="30"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="30" id="3jw-Wi-el4"/>
                                                                            <constraint firstAttribute="width" constant="30" id="RMc-EX-FwW"/>
                                                                        </constraints>
                                                                    </imageView>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" lineBreakMode="wordWrap" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="co1-gZ-WCE">
                                                                        <rect key="frame" x="22" y="38" width="35.5" height="18"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="gIG-pt-z3q">
                                                                <rect key="frame" x="91.5" y="0.0" width="25" height="56"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="25" id="Ruc-8a-Vgk"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                <nil key="textColor"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="x3n-0b-oBe">
                                                                <rect key="frame" x="128.5" y="0.0" width="74" height="56"/>
                                                                <subviews>
                                                                    <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="rLP-2u-utv">
                                                                        <rect key="frame" x="0.0" y="0.0" width="74" height="24"/>
                                                                        <subviews>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Unit" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="FHO-iM-Xag">
                                                                                <rect key="frame" x="0.0" y="0.0" width="35" height="24"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="height" constant="24" id="sWf-8D-g5F"/>
                                                                                </constraints>
                                                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                                <color key="textColor" white="0.0" alpha="0.57999999999999996" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Unit" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Mel-B0-xQh">
                                                                                <rect key="frame" x="39" y="0.0" width="35" height="24"/>
                                                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                                <nil key="textColor"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                        </subviews>
                                                                    </stackView>
                                                                    <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="QcP-jH-yRk">
                                                                        <rect key="frame" x="0.0" y="32" width="74" height="24"/>
                                                                        <subviews>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Price" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Fzb-Tw-CU5">
                                                                                <rect key="frame" x="0.0" y="0.0" width="35" height="24"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="height" constant="24" id="zOx-Gz-3f8"/>
                                                                                </constraints>
                                                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                                <color key="textColor" white="0.0" alpha="0.57999999999999996" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Unit" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="KUy-f3-Xtn">
                                                                                <rect key="frame" x="39" y="0.0" width="35" height="24"/>
                                                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                                <nil key="textColor"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                        </subviews>
                                                                    </stackView>
                                                                </subviews>
                                                            </stackView>
                                                        </subviews>
                                                    </stackView>
                                                </subviews>
                                                <color key="backgroundColor" red="0.46274509803921571" green="0.71372549019607845" blue="0.58039215686274503" alpha="0.10000000000000001" colorSpace="custom" customColorSpace="sRGB"/>
                                                <constraints>
                                                    <constraint firstItem="UZq-tw-Ud2" firstAttribute="width" secondItem="0JT-XR-sNd" secondAttribute="width" multiplier="0.6" id="Bry-kS-dA0"/>
                                                    <constraint firstAttribute="bottom" secondItem="UZq-tw-Ud2" secondAttribute="bottom" constant="8" id="HCA-jb-C8P"/>
                                                    <constraint firstItem="UZq-tw-Ud2" firstAttribute="top" secondItem="0JT-XR-sNd" secondAttribute="top" constant="8" id="id1-Zt-JAY"/>
                                                    <constraint firstItem="UZq-tw-Ud2" firstAttribute="centerX" secondItem="0JT-XR-sNd" secondAttribute="centerX" id="mat-Qh-LKJ"/>
                                                </constraints>
                                            </view>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="i02-K0-1HZ">
                                                <rect key="frame" x="295" y="8" width="34" height="34"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="34" id="YgO-Tv-8Qg"/>
                                                    <constraint firstAttribute="height" constant="34" id="oWv-bn-rAh"/>
                                                </constraints>
                                                <inset key="imageEdgeInsets" minX="8" minY="8" maxX="8" maxY="8"/>
                                                <state key="normal" image="ic_about_us"/>
                                                <connections>
                                                    <action selector="reserveInfoTapped:" destination="0qj-13-M06" eventType="touchUpInside" id="qg1-zw-uAV"/>
                                                </connections>
                                            </button>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="eDj-Dx-KlV">
                                                <rect key="frame" x="19" y="50" width="337.5" height="1"/>
                                                <color key="backgroundColor" name="PrimaryText"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="1" id="cfS-OV-BbD"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Select Reserve Time" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="aUf-j2-BWJ">
                                                <rect key="frame" x="12" y="155" width="132" height="20"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="20" id="i9p-LP-r8O"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                <color key="textColor" name="Primary"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="nVg-a9-lSh">
                                                <rect key="frame" x="12" y="187" width="351" height="32"/>
                                                <subviews>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="a1E-my-qBU">
                                                        <rect key="frame" x="0.0" y="0.0" width="79" height="32"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="15 min" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="SYV-a1-5kR">
                                                                <rect key="frame" x="4" y="4" width="71" height="24"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="24" id="ELU-Fb-WnL"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                <color key="textColor" name="SecondaryGrayText"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstItem="SYV-a1-5kR" firstAttribute="leading" secondItem="a1E-my-qBU" secondAttribute="leading" constant="4" id="WF1-JR-UYb"/>
                                                            <constraint firstAttribute="bottom" secondItem="SYV-a1-5kR" secondAttribute="bottom" constant="4" id="caV-Ku-aeb"/>
                                                            <constraint firstItem="SYV-a1-5kR" firstAttribute="top" secondItem="a1E-my-qBU" secondAttribute="top" constant="4" id="psf-z3-24r"/>
                                                            <constraint firstAttribute="trailing" secondItem="SYV-a1-5kR" secondAttribute="trailing" constant="4" id="uFR-fE-prX"/>
                                                        </constraints>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="BDx-Rn-AkJ">
                                                        <rect key="frame" x="91" y="0.0" width="78.5" height="32"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="30 min" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="t8d-Yi-pll">
                                                                <rect key="frame" x="4" y="4" width="70.5" height="24"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="24" id="DrB-3h-74N"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                <color key="textColor" name="SecondaryGrayText"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstAttribute="trailing" secondItem="t8d-Yi-pll" secondAttribute="trailing" constant="4" id="GdZ-wj-5YK"/>
                                                            <constraint firstItem="t8d-Yi-pll" firstAttribute="leading" secondItem="BDx-Rn-AkJ" secondAttribute="leading" constant="4" id="H4g-xk-LhP"/>
                                                            <constraint firstItem="t8d-Yi-pll" firstAttribute="top" secondItem="BDx-Rn-AkJ" secondAttribute="top" constant="4" id="H8n-yT-kfk"/>
                                                            <constraint firstAttribute="bottom" secondItem="t8d-Yi-pll" secondAttribute="bottom" constant="4" id="tfX-yr-q5w"/>
                                                        </constraints>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="WJP-13-Gch">
                                                        <rect key="frame" x="181.5" y="0.0" width="79" height="32"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="45 min" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="l2G-8i-Jkr">
                                                                <rect key="frame" x="4" y="4" width="71" height="24"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="24" id="xe0-qc-VmE"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                <color key="textColor" name="SecondaryGrayText"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstItem="l2G-8i-Jkr" firstAttribute="top" secondItem="WJP-13-Gch" secondAttribute="top" constant="4" id="5bK-GT-PMl"/>
                                                            <constraint firstAttribute="bottom" secondItem="l2G-8i-Jkr" secondAttribute="bottom" constant="4" id="LrQ-yt-SQ9"/>
                                                            <constraint firstItem="l2G-8i-Jkr" firstAttribute="leading" secondItem="WJP-13-Gch" secondAttribute="leading" constant="4" id="nUP-5h-6mD"/>
                                                            <constraint firstAttribute="trailing" secondItem="l2G-8i-Jkr" secondAttribute="trailing" constant="4" id="u7g-xK-XJM"/>
                                                        </constraints>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="T9X-ES-0tv">
                                                        <rect key="frame" x="272.5" y="0.0" width="78.5" height="32"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="60 min" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="sC1-r2-102">
                                                                <rect key="frame" x="4" y="4" width="70.5" height="24"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="24" id="sBO-HV-rzC"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                <color key="textColor" name="SecondaryGrayText"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstAttribute="bottom" secondItem="sC1-r2-102" secondAttribute="bottom" constant="4" id="G0M-Wi-5jY"/>
                                                            <constraint firstAttribute="trailing" secondItem="sC1-r2-102" secondAttribute="trailing" constant="4" id="IVX-Ls-lJH"/>
                                                            <constraint firstItem="sC1-r2-102" firstAttribute="top" secondItem="T9X-ES-0tv" secondAttribute="top" constant="4" id="kfV-13-vE3"/>
                                                            <constraint firstItem="sC1-r2-102" firstAttribute="leading" secondItem="T9X-ES-0tv" secondAttribute="leading" constant="4" id="uUB-C5-Cm8"/>
                                                        </constraints>
                                                    </view>
                                                </subviews>
                                            </stackView>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="CBp-Ti-QgI">
                                                <rect key="frame" x="19" y="235" width="337.5" height="44"/>
                                                <color key="backgroundColor" name="Primary"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="44" id="oUi-XQ-xly"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                                <state key="normal" title="Reserve Slot">
                                                    <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                </state>
                                                <connections>
                                                    <action selector="reserveSlotPopUpTapped:" destination="0qj-13-M06" eventType="touchUpInside" id="Dso-yY-QkW"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" tag="101" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="DGz-hN-FOZ">
                                                <rect key="frame" x="12" y="187" width="79" height="32"/>
                                                <color key="tintColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <state key="normal" title="Button"/>
                                                <buttonConfiguration key="configuration" style="plain" title=""/>
                                                <connections>
                                                    <action selector="reserveTime:" destination="0qj-13-M06" eventType="touchUpInside" id="DXG-oj-IZh"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" tag="102" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="mOb-pz-lWp">
                                                <rect key="frame" x="103" y="187" width="78.5" height="32"/>
                                                <color key="tintColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <state key="normal" title="Button"/>
                                                <buttonConfiguration key="configuration" style="plain"/>
                                                <connections>
                                                    <action selector="reserveTime:" destination="0qj-13-M06" eventType="touchUpInside" id="Qed-ul-OvY"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" tag="103" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Y4M-IX-L68">
                                                <rect key="frame" x="193.5" y="187" width="79" height="32"/>
                                                <color key="tintColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <state key="normal" title="Button"/>
                                                <buttonConfiguration key="configuration" style="plain"/>
                                                <connections>
                                                    <action selector="reserveTime:" destination="0qj-13-M06" eventType="touchUpInside" id="gMr-XI-jHB"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" tag="104" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="XLu-9i-E7Y">
                                                <rect key="frame" x="284.5" y="187" width="78.5" height="32"/>
                                                <color key="tintColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <state key="normal" title="Button"/>
                                                <buttonConfiguration key="configuration" style="plain"/>
                                                <connections>
                                                    <action selector="reserveTime:" destination="0qj-13-M06" eventType="touchUpInside" id="aB0-KJ-99b"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="mOb-pz-lWp" firstAttribute="centerY" secondItem="BDx-Rn-AkJ" secondAttribute="centerY" id="3Pj-dq-Mu7"/>
                                            <constraint firstItem="mOb-pz-lWp" firstAttribute="height" secondItem="BDx-Rn-AkJ" secondAttribute="height" id="4GY-mX-NkF"/>
                                            <constraint firstItem="eDj-Dx-KlV" firstAttribute="centerX" secondItem="esR-ea-bNy" secondAttribute="centerX" id="5CQ-3G-CDK"/>
                                            <constraint firstItem="Y4M-IX-L68" firstAttribute="height" secondItem="WJP-13-Gch" secondAttribute="height" id="5PN-pa-7JZ"/>
                                            <constraint firstItem="mOb-pz-lWp" firstAttribute="width" secondItem="BDx-Rn-AkJ" secondAttribute="width" id="6Ns-0O-Y0T"/>
                                            <constraint firstItem="mOb-pz-lWp" firstAttribute="centerX" secondItem="BDx-Rn-AkJ" secondAttribute="centerX" id="7nr-v9-L9o"/>
                                            <constraint firstItem="DGz-hN-FOZ" firstAttribute="width" secondItem="a1E-my-qBU" secondAttribute="width" id="C9e-Lh-D7A"/>
                                            <constraint firstItem="nVg-a9-lSh" firstAttribute="top" secondItem="aUf-j2-BWJ" secondAttribute="bottom" constant="12" id="DeJ-JW-fbW"/>
                                            <constraint firstItem="DGz-hN-FOZ" firstAttribute="height" secondItem="a1E-my-qBU" secondAttribute="height" id="Dsq-oX-pNd"/>
                                            <constraint firstAttribute="trailing" secondItem="7ff-vj-SqK" secondAttribute="trailing" constant="4" id="Eef-KT-CM1"/>
                                            <constraint firstItem="eDj-Dx-KlV" firstAttribute="top" secondItem="u5G-Ox-GTA" secondAttribute="bottom" constant="8" id="FRF-W7-xlJ"/>
                                            <constraint firstItem="0JT-XR-sNd" firstAttribute="width" secondItem="esR-ea-bNy" secondAttribute="width" multiplier="0.9" id="Ii9-ZR-SbQ"/>
                                            <constraint firstItem="0JT-XR-sNd" firstAttribute="top" secondItem="eDj-Dx-KlV" secondAttribute="bottom" constant="16" id="Olf-QT-ed7"/>
                                            <constraint firstItem="CBp-Ti-QgI" firstAttribute="width" secondItem="esR-ea-bNy" secondAttribute="width" multiplier="0.9" id="P40-RB-249"/>
                                            <constraint firstItem="CBp-Ti-QgI" firstAttribute="top" secondItem="nVg-a9-lSh" secondAttribute="bottom" constant="16" id="Qbq-8k-To2"/>
                                            <constraint firstItem="Y4M-IX-L68" firstAttribute="centerX" secondItem="WJP-13-Gch" secondAttribute="centerX" id="Rfd-X8-671"/>
                                            <constraint firstItem="nVg-a9-lSh" firstAttribute="leading" secondItem="esR-ea-bNy" secondAttribute="leading" constant="12" id="SDk-9X-k8y"/>
                                            <constraint firstItem="u5G-Ox-GTA" firstAttribute="leading" secondItem="esR-ea-bNy" secondAttribute="leading" constant="12" id="U0v-mr-Zf5"/>
                                            <constraint firstAttribute="trailing" secondItem="nVg-a9-lSh" secondAttribute="trailing" constant="12" id="Vvf-Jt-vGA"/>
                                            <constraint firstItem="Y4M-IX-L68" firstAttribute="width" secondItem="WJP-13-Gch" secondAttribute="width" id="Yai-3V-wKH"/>
                                            <constraint firstItem="eDj-Dx-KlV" firstAttribute="width" secondItem="esR-ea-bNy" secondAttribute="width" multiplier="0.9" id="b7B-jT-T9N"/>
                                            <constraint firstItem="DGz-hN-FOZ" firstAttribute="centerY" secondItem="a1E-my-qBU" secondAttribute="centerY" id="bhD-yB-cII"/>
                                            <constraint firstItem="XLu-9i-E7Y" firstAttribute="centerY" secondItem="T9X-ES-0tv" secondAttribute="centerY" id="bpM-eO-j6t"/>
                                            <constraint firstItem="CBp-Ti-QgI" firstAttribute="centerX" secondItem="esR-ea-bNy" secondAttribute="centerX" id="e5O-9B-U7N"/>
                                            <constraint firstItem="aUf-j2-BWJ" firstAttribute="top" secondItem="0JT-XR-sNd" secondAttribute="bottom" constant="16" id="eT2-b3-V59"/>
                                            <constraint firstItem="aUf-j2-BWJ" firstAttribute="leading" secondItem="esR-ea-bNy" secondAttribute="leading" constant="12" id="hGq-CC-fz2"/>
                                            <constraint firstItem="i02-K0-1HZ" firstAttribute="centerY" secondItem="7ff-vj-SqK" secondAttribute="centerY" id="lto-Jc-AAh"/>
                                            <constraint firstItem="0JT-XR-sNd" firstAttribute="centerX" secondItem="esR-ea-bNy" secondAttribute="centerX" id="m37-tK-lCf"/>
                                            <constraint firstItem="7ff-vj-SqK" firstAttribute="leading" secondItem="i02-K0-1HZ" secondAttribute="trailing" constant="8" id="oRo-Zf-YTn"/>
                                            <constraint firstItem="DGz-hN-FOZ" firstAttribute="centerX" secondItem="a1E-my-qBU" secondAttribute="centerX" id="p92-Cl-1l5"/>
                                            <constraint firstItem="7ff-vj-SqK" firstAttribute="top" secondItem="esR-ea-bNy" secondAttribute="top" constant="8" id="qJG-tU-jgr"/>
                                            <constraint firstItem="Y4M-IX-L68" firstAttribute="centerY" secondItem="WJP-13-Gch" secondAttribute="centerY" id="qch-qU-E6N"/>
                                            <constraint firstItem="XLu-9i-E7Y" firstAttribute="centerX" secondItem="T9X-ES-0tv" secondAttribute="centerX" id="rBN-e0-ETv"/>
                                            <constraint firstAttribute="bottom" secondItem="CBp-Ti-QgI" secondAttribute="bottom" constant="16" id="tXn-Iq-oGh"/>
                                            <constraint firstItem="XLu-9i-E7Y" firstAttribute="height" secondItem="T9X-ES-0tv" secondAttribute="height" id="xr0-85-Swf"/>
                                            <constraint firstItem="u5G-Ox-GTA" firstAttribute="top" secondItem="esR-ea-bNy" secondAttribute="top" constant="8" id="yI4-Pi-tMi"/>
                                            <constraint firstItem="XLu-9i-E7Y" firstAttribute="width" secondItem="T9X-ES-0tv" secondAttribute="width" id="zbu-Zl-731"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.75" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="bottom" secondItem="esR-ea-bNy" secondAttribute="bottom" id="0MV-ea-aMj"/>
                                    <constraint firstItem="esR-ea-bNy" firstAttribute="centerX" secondItem="Igo-wt-fDC" secondAttribute="centerX" id="fWa-IS-v1m"/>
                                    <constraint firstItem="esR-ea-bNy" firstAttribute="width" secondItem="Igo-wt-fDC" secondAttribute="width" id="uzP-bt-USW"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="Uuh-jt-5hX"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="Uuh-jt-5hX" firstAttribute="bottom" secondItem="tD8-ZG-aDa" secondAttribute="bottom" constant="8" id="20f-Fl-X2G"/>
                            <constraint firstItem="X1F-mP-35d" firstAttribute="leading" secondItem="Uuh-jt-5hX" secondAttribute="leading" id="2fF-ft-FOm"/>
                            <constraint firstItem="Igo-wt-fDC" firstAttribute="top" secondItem="Uuh-jt-5hX" secondAttribute="top" id="35y-PE-OOm"/>
                            <constraint firstItem="FJk-Cx-X2D" firstAttribute="width" secondItem="0Uu-sY-pqY" secondAttribute="width" multiplier="1.09329" id="5Qf-Ub-b4V"/>
                            <constraint firstAttribute="trailing" secondItem="278-ey-WjL" secondAttribute="trailing" constant="4" id="Ctv-4O-qTp"/>
                            <constraint firstItem="tD8-ZG-aDa" firstAttribute="top" secondItem="278-ey-WjL" secondAttribute="bottom" constant="8" id="HAC-Ms-gqH"/>
                            <constraint firstItem="X1F-mP-35d" firstAttribute="top" secondItem="Uuh-jt-5hX" secondAttribute="top" id="I6u-wQ-iUZ"/>
                            <constraint firstItem="FJk-Cx-X2D" firstAttribute="centerY" secondItem="0Uu-sY-pqY" secondAttribute="centerY" id="IAQ-SM-ncF"/>
                            <constraint firstItem="Igo-wt-fDC" firstAttribute="leading" secondItem="Uuh-jt-5hX" secondAttribute="leading" id="KFB-k6-s3V"/>
                            <constraint firstItem="Uuh-jt-5hX" firstAttribute="trailing" secondItem="X1F-mP-35d" secondAttribute="trailing" id="LlG-Ov-RVh"/>
                            <constraint firstItem="FJk-Cx-X2D" firstAttribute="height" secondItem="0Uu-sY-pqY" secondAttribute="height" id="NhL-G4-7F0"/>
                            <constraint firstItem="Vgl-63-hUa" firstAttribute="height" secondItem="Czo-bF-cme" secondAttribute="height" id="Qe5-cM-VCL"/>
                            <constraint firstItem="278-ey-WjL" firstAttribute="top" secondItem="X1F-mP-35d" secondAttribute="bottom" id="REM-lx-hgb"/>
                            <constraint firstItem="Vgl-63-hUa" firstAttribute="centerX" secondItem="Czo-bF-cme" secondAttribute="centerX" id="SFb-Pr-8N5"/>
                            <constraint firstItem="FJk-Cx-X2D" firstAttribute="centerX" secondItem="0Uu-sY-pqY" secondAttribute="centerX" id="TXC-gY-Oed"/>
                            <constraint firstItem="278-ey-WjL" firstAttribute="leading" secondItem="zEx-b0-hjY" secondAttribute="leading" constant="4" id="VFl-NU-GBr"/>
                            <constraint firstItem="Vgl-63-hUa" firstAttribute="centerY" secondItem="Czo-bF-cme" secondAttribute="centerY" id="W2s-2U-p3L"/>
                            <constraint firstItem="tD8-ZG-aDa" firstAttribute="centerX" secondItem="zEx-b0-hjY" secondAttribute="centerX" id="WdZ-Lr-JsH"/>
                            <constraint firstItem="Igo-wt-fDC" firstAttribute="trailing" secondItem="Uuh-jt-5hX" secondAttribute="trailing" id="eRr-g2-sDB"/>
                            <constraint firstItem="Vgl-63-hUa" firstAttribute="width" secondItem="Czo-bF-cme" secondAttribute="width" multiplier="1.02332" id="gbo-vn-Xke"/>
                            <constraint firstItem="tD8-ZG-aDa" firstAttribute="width" secondItem="Uuh-jt-5hX" secondAttribute="width" multiplier="0.9" id="sXp-0f-7mH"/>
                            <constraint firstItem="Igo-wt-fDC" firstAttribute="bottom" secondItem="Uuh-jt-5hX" secondAttribute="bottom" id="vA7-ED-gWt"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="btnBack" destination="HQr-JQ-FLp" id="6cf-jm-E9T"/>
                        <outlet property="btnHistory" destination="vXj-AW-t2w" id="B7b-n6-UZp"/>
                        <outlet property="btnInfoReserve" destination="i02-K0-1HZ" id="fgV-h7-mZm"/>
                        <outlet property="btnReserveSlot" destination="bHG-nO-Uzq" id="nCb-hQ-ByT"/>
                        <outlet property="btnReserveSlot1" destination="CBp-Ti-QgI" id="JbZ-8v-hGG"/>
                        <outlet property="btnStartCharging" destination="u7e-Yy-oMA" id="RIz-IY-Tba"/>
                        <outlet property="chargeStationRatings" destination="pvi-60-lyw" id="jGl-Zj-dRk"/>
                        <outlet property="collectionTime" destination="7Gh-ge-4dI" id="W4T-WY-bkL"/>
                        <outlet property="imgChargeStation" destination="hll-2I-AKg" id="aQo-Sl-KnA"/>
                        <outlet property="imgReserveConnector" destination="XgR-Kr-e7e" id="5T8-rj-1jq"/>
                        <outlet property="lblChargeStationAddress" destination="zxw-5t-ylD" id="ORw-0d-5cK"/>
                        <outlet property="lblChargeStationName" destination="lHx-tG-f3I" id="tJq-nP-OTe"/>
                        <outlet property="lblChargeStationReviews" destination="DPZ-3t-9E6" id="2T4-fs-V9Q"/>
                        <outlet property="lblReserve15" destination="SYV-a1-5kR" id="ygs-hc-r5B"/>
                        <outlet property="lblReserve30" destination="t8d-Yi-pll" id="8mS-rq-Ex8"/>
                        <outlet property="lblReserve45" destination="l2G-8i-Jkr" id="Zjp-sT-Cft"/>
                        <outlet property="lblReserve60" destination="sC1-r2-102" id="EMN-jP-bMe"/>
                        <outlet property="lblReserveConnectorName" destination="co1-gZ-WCE" id="swW-gu-Cmf"/>
                        <outlet property="lblReservePrice" destination="KUy-f3-Xtn" id="Kiy-PK-Cpy"/>
                        <outlet property="lblReserveUnit" destination="Mel-B0-xQh" id="D2i-Wk-Yq4"/>
                        <outlet property="lblTimeDetails" destination="Cw4-zZ-7Cx" id="A8M-lY-h6P"/>
                        <outlet property="pageController" destination="vTr-yw-xOU" id="CaQ-4D-rOL"/>
                        <outlet property="scrollChargerImages" destination="j7d-cV-iLE" id="3q2-Uv-Rti"/>
                        <outlet property="scrollDetails" destination="278-ey-WjL" id="oPP-cZ-ply"/>
                        <outlet property="tableConnector" destination="fNG-d5-JtD" id="uQg-bR-nuJ"/>
                        <outlet property="tableHeight" destination="UIo-yW-LMN" id="n5B-FH-1sk"/>
                        <outlet property="viewBgReserve" destination="Igo-wt-fDC" id="9QL-vq-ahi"/>
                        <outlet property="viewChargeStationInfo" destination="pJx-ri-Ce1" id="GkR-Im-Q3X"/>
                        <outlet property="viewConnectorListDetailInfo" destination="Ibv-sg-5EO" id="nB8-ni-TBf"/>
                        <outlet property="viewConnectorListInfo" destination="Yys-5w-eUy" id="qcH-V6-UqU"/>
                        <outlet property="viewGetDirections" destination="Czo-bF-cme" id="Nd1-Ug-OFu"/>
                        <outlet property="viewMainReserve" destination="esR-ea-bNy" id="AYo-Lv-WGt"/>
                        <outlet property="viewOpenClose" destination="0Uu-sY-pqY" id="m4R-dz-hTx"/>
                        <outlet property="viewOpenCloseDetails" destination="Iqm-5T-LVC" id="fbD-jW-Jzh"/>
                        <outlet property="viewOpenCloseInfo" destination="oRd-fE-tja" id="IRT-Mg-p24"/>
                        <outlet property="viewReserve15" destination="a1E-my-qBU" id="bcx-Po-b5g"/>
                        <outlet property="viewReserve30" destination="BDx-Rn-AkJ" id="TLE-oH-pwG"/>
                        <outlet property="viewReserve45" destination="WJP-13-Gch" id="0eB-nw-BgW"/>
                        <outlet property="viewReserve60" destination="T9X-ES-0tv" id="smL-Rw-YkQ"/>
                        <outlet property="viewReserveDetaiils" destination="0JT-XR-sNd" id="RF4-ec-BSB"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Wrx-mw-gOa" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1268" y="69.715142428785612"/>
        </scene>
    </scenes>
    <resources>
        <image name="ic_3 pin" width="155" height="165"/>
        <image name="ic_AC Type-1" width="1268" height="1526"/>
        <image name="ic_about_us" width="500" height="500"/>
        <image name="ic_address" width="500" height="425"/>
        <image name="ic_back_arrow" width="64" height="64"/>
        <image name="ic_cancel" width="64" height="64"/>
        <image name="ic_chargerSpeed" width="500" height="500"/>
        <image name="ic_charger_icon" width="500" height="500"/>
        <image name="ic_checked" width="500" height="500"/>
        <image name="ic_connector" width="500" height="360"/>
        <image name="ic_direction" width="500" height="500"/>
        <image name="ic_filter" width="500" height="501"/>
        <image name="ic_location_icon" width="100" height="115"/>
        <image name="ic_logout" width="24" height="24"/>
        <image name="ic_menu" width="500" height="500"/>
        <image name="ic_ratings" width="500" height="478"/>
        <image name="ic_rightDirections" width="64" height="64"/>
        <image name="ic_searchEV" width="128" height="128"/>
        <image name="ic_station_timings" width="24" height="24"/>
        <image name="ic_ticked" width="64" height="64"/>
        <image name="ic_topArrow" width="64" height="64"/>
        <image name="ic_transaction_history" width="500" height="500"/>
        <image name="ic_uncheck" width="500" height="500"/>
        <image name="ic_whatsapp" width="200" height="200"/>
        <image name="ic_white_circle" width="64" height="64"/>
        <image name="nil" width="128" height="128"/>
        <namedColor name="PartitionBG">
            <color red="0.9137254901960784" green="0.9137254901960784" blue="0.9137254901960784" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Primary">
            <color red="0.016000000759959221" green="0.49399998784065247" blue="0.42699998617172241" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="PrimaryColor">
            <color red="0.015686274509803921" green="0.49411764705882355" blue="0.42745098039215684" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="PrimarySelection">
            <color red="0.59999999999999998" green="0.59999999999999998" blue="0.59999999999999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="PrimaryText">
            <color red="0.72500002384185791" green="0.72899997234344482" blue="0.7839999794960022" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="PrimaryTextColor">
            <color red="0.32899999618530273" green="0.32400000095367432" blue="0.4779999852180481" alpha="0.33000001311302185" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="PrimaryTextColorLight">
            <color red="0.29019607843137257" green="0.29019607843137257" blue="0.29019607843137257" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="SecondaryGrayText">
            <color red="0.36470588235294116" green="0.36470588235294116" blue="0.36470588235294116" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="WalletAddText">
            <color red="0.23100000619888306" green="0.48600000143051147" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>

<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="19162" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="19144"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--Add ComplainVC-->
        <scene sceneID="s0d-6b-0kx">
            <objects>
                <viewController storyboardIdentifier="AddComplainVC" id="Y6W-OH-hqX" customClass="AddComplainVC" customModule="NXC_EV_Solutions" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="5EZ-qb-Rvc">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="aZj-l6-Hxp">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="M4m-HP-CqE">
                                        <rect key="frame" x="12" y="5" width="34" height="34"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="34" id="WTc-wO-SzI"/>
                                            <constraint firstAttribute="height" constant="34" id="ffz-ec-kgo"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="8" minY="6" maxX="8" maxY="6"/>
                                        <state key="normal" image="ic_back_arrow"/>
                                        <connections>
                                            <action selector="backAction:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="etj-yC-SsR"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Add Complaint" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Eqr-zE-w4a">
                                        <rect key="frame" x="129" y="12" width="117.5" height="20.5"/>
                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                        <color key="textColor" red="0.070588235289999995" green="0.070588235289999995" blue="0.070588235289999995" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="M4m-HP-CqE" firstAttribute="leading" secondItem="aZj-l6-Hxp" secondAttribute="leading" constant="12" id="39s-tV-VM4"/>
                                    <constraint firstItem="Eqr-zE-w4a" firstAttribute="centerY" secondItem="aZj-l6-Hxp" secondAttribute="centerY" id="bb5-Xz-yub"/>
                                    <constraint firstAttribute="height" constant="44" id="cBy-tM-FfZ"/>
                                    <constraint firstItem="M4m-HP-CqE" firstAttribute="centerY" secondItem="aZj-l6-Hxp" secondAttribute="centerY" id="egz-bO-2pq"/>
                                    <constraint firstItem="Eqr-zE-w4a" firstAttribute="centerX" secondItem="aZj-l6-Hxp" secondAttribute="centerX" id="qwn-FF-uOb"/>
                                </constraints>
                            </view>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="bed-6z-uTf">
                                <rect key="frame" x="19" y="60" width="337.5" height="220"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="0fM-iV-PqE">
                                        <rect key="frame" x="0.0" y="0.0" width="337.5" height="44"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Select Complaint Type" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="cMd-lq-sd1">
                                                <rect key="frame" x="8" y="1" width="302.5" height="42"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_dropdown" translatesAutoresizingMaskIntoConstraints="NO" id="qws-53-nFG">
                                                <rect key="frame" x="314.5" y="14.5" width="15" height="15"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="15" id="IPp-O8-k39"/>
                                                    <constraint firstAttribute="height" constant="15" id="vic-me-aUx"/>
                                                </constraints>
                                            </imageView>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="HkC-5W-Z7B">
                                                <rect key="frame" x="0.0" y="43" width="337.5" height="1"/>
                                                <color key="backgroundColor" name="PrimarySelection"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="1" id="ebI-k9-LMe"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="HkC-5W-Z7B" secondAttribute="trailing" id="1Rq-gp-p8E"/>
                                            <constraint firstItem="qws-53-nFG" firstAttribute="centerY" secondItem="0fM-iV-PqE" secondAttribute="centerY" id="39z-Gw-qz2"/>
                                            <constraint firstItem="cMd-lq-sd1" firstAttribute="leading" secondItem="0fM-iV-PqE" secondAttribute="leading" constant="8" id="5mk-6x-UgS"/>
                                            <constraint firstItem="HkC-5W-Z7B" firstAttribute="leading" secondItem="0fM-iV-PqE" secondAttribute="leading" id="65r-U2-5ep"/>
                                            <constraint firstItem="cMd-lq-sd1" firstAttribute="top" secondItem="0fM-iV-PqE" secondAttribute="top" constant="1" id="Ufb-BI-38g"/>
                                            <constraint firstAttribute="height" constant="44" id="VJU-bw-6oZ"/>
                                            <constraint firstAttribute="bottom" secondItem="HkC-5W-Z7B" secondAttribute="bottom" id="YSa-3A-SjE"/>
                                            <constraint firstAttribute="bottom" secondItem="cMd-lq-sd1" secondAttribute="bottom" constant="1" id="cwY-X9-TIw"/>
                                            <constraint firstItem="qws-53-nFG" firstAttribute="leading" secondItem="cMd-lq-sd1" secondAttribute="trailing" constant="4" id="kwA-df-Kyg"/>
                                            <constraint firstAttribute="trailing" secondItem="qws-53-nFG" secondAttribute="trailing" constant="8" id="yf1-Fd-wYc"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="BKf-46-cKR">
                                        <rect key="frame" x="0.0" y="60" width="337.5" height="44"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Select Transaction" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="GR4-Ad-bCz">
                                                <rect key="frame" x="8" y="1" width="302.5" height="42"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_dropdown" translatesAutoresizingMaskIntoConstraints="NO" id="4FA-C3-McD">
                                                <rect key="frame" x="314.5" y="14.5" width="15" height="15"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="15" id="7cG-OD-7sG"/>
                                                    <constraint firstAttribute="width" constant="15" id="w5X-42-PHR"/>
                                                </constraints>
                                            </imageView>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="qnL-Bv-BYV">
                                                <rect key="frame" x="0.0" y="43" width="337.5" height="1"/>
                                                <color key="backgroundColor" name="PrimarySelection"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="1" id="iiK-UY-QBW"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstItem="4FA-C3-McD" firstAttribute="centerY" secondItem="BKf-46-cKR" secondAttribute="centerY" id="1GG-ad-cUu"/>
                                            <constraint firstAttribute="bottom" secondItem="GR4-Ad-bCz" secondAttribute="bottom" constant="1" id="1ak-2h-aFe"/>
                                            <constraint firstItem="qnL-Bv-BYV" firstAttribute="leading" secondItem="BKf-46-cKR" secondAttribute="leading" id="9hB-BV-OHU"/>
                                            <constraint firstItem="GR4-Ad-bCz" firstAttribute="leading" secondItem="BKf-46-cKR" secondAttribute="leading" constant="8" id="aO0-Jf-kcj"/>
                                            <constraint firstAttribute="trailing" secondItem="qnL-Bv-BYV" secondAttribute="trailing" id="ewS-SH-yFO"/>
                                            <constraint firstItem="4FA-C3-McD" firstAttribute="leading" secondItem="GR4-Ad-bCz" secondAttribute="trailing" constant="4" id="kSv-ce-Nhr"/>
                                            <constraint firstAttribute="trailing" secondItem="4FA-C3-McD" secondAttribute="trailing" constant="8" id="mWg-9w-ZJz"/>
                                            <constraint firstItem="GR4-Ad-bCz" firstAttribute="top" secondItem="BKf-46-cKR" secondAttribute="top" constant="1" id="pGI-dI-Mfz"/>
                                            <constraint firstAttribute="bottom" secondItem="qnL-Bv-BYV" secondAttribute="bottom" id="rlT-Qd-DId"/>
                                            <constraint firstAttribute="height" constant="44" id="zd5-xw-LSU"/>
                                        </constraints>
                                    </view>
                                    <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" textAlignment="natural" translatesAutoresizingMaskIntoConstraints="NO" id="z2A-HW-g8p">
                                        <rect key="frame" x="0.0" y="120" width="337.5" height="100"/>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <color key="textColor" systemColor="labelColor"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                        <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                                    </textView>
                                </subviews>
                            </stackView>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="JZo-Np-SqZ">
                                <rect key="frame" x="19" y="607" width="337.5" height="44"/>
                                <color key="backgroundColor" name="Primary"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="44" id="5xc-6V-5um"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                <state key="normal" title="Submit">
                                    <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                </state>
                                <connections>
                                    <action selector="submitAction:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="2Dy-i9-5IJ"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="XJE-57-BrJ">
                                <rect key="frame" x="19" y="60" width="337.5" height="44"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="44" id="H88-Ob-hDd"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                <state key="normal">
                                    <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                </state>
                                <connections>
                                    <action selector="complainTypeAction:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="eJ6-Ij-LYx"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ImP-uA-Kkf">
                                <rect key="frame" x="19" y="120" width="337.5" height="44"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="44" id="Q79-cT-SaD"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                <state key="normal">
                                    <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                </state>
                                <connections>
                                    <action selector="transactionAction:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="2gA-6w-ly0"/>
                                </connections>
                            </button>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="vDu-zF-Fre"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="aZj-l6-Hxp" firstAttribute="leading" secondItem="vDu-zF-Fre" secondAttribute="leading" id="4i4-ki-RUj"/>
                            <constraint firstItem="XJE-57-BrJ" firstAttribute="height" secondItem="0fM-iV-PqE" secondAttribute="height" id="51A-y9-VmN"/>
                            <constraint firstItem="XJE-57-BrJ" firstAttribute="centerY" secondItem="0fM-iV-PqE" secondAttribute="centerY" id="9oe-vh-77x"/>
                            <constraint firstItem="bed-6z-uTf" firstAttribute="top" secondItem="aZj-l6-Hxp" secondAttribute="bottom" constant="16" id="CUX-1r-hIB"/>
                            <constraint firstItem="z2A-HW-g8p" firstAttribute="height" secondItem="vDu-zF-Fre" secondAttribute="height" multiplier="0.15" id="EqF-2W-sUm"/>
                            <constraint firstItem="vDu-zF-Fre" firstAttribute="bottom" secondItem="JZo-Np-SqZ" secondAttribute="bottom" constant="16" id="J0t-1F-9NS"/>
                            <constraint firstItem="ImP-uA-Kkf" firstAttribute="width" secondItem="BKf-46-cKR" secondAttribute="width" id="K9l-wr-yRJ"/>
                            <constraint firstItem="ImP-uA-Kkf" firstAttribute="centerY" secondItem="BKf-46-cKR" secondAttribute="centerY" id="XYb-E6-D62"/>
                            <constraint firstItem="JZo-Np-SqZ" firstAttribute="centerX" secondItem="5EZ-qb-Rvc" secondAttribute="centerX" id="aUX-Bf-1bR"/>
                            <constraint firstItem="bed-6z-uTf" firstAttribute="centerX" secondItem="5EZ-qb-Rvc" secondAttribute="centerX" id="cGa-dx-Eym"/>
                            <constraint firstItem="aZj-l6-Hxp" firstAttribute="top" secondItem="vDu-zF-Fre" secondAttribute="top" id="cqF-fH-zda"/>
                            <constraint firstItem="XJE-57-BrJ" firstAttribute="centerX" secondItem="0fM-iV-PqE" secondAttribute="centerX" id="dgI-Nd-p5E"/>
                            <constraint firstItem="ImP-uA-Kkf" firstAttribute="centerX" secondItem="BKf-46-cKR" secondAttribute="centerX" id="fqY-Mb-gkr"/>
                            <constraint firstItem="JZo-Np-SqZ" firstAttribute="width" secondItem="vDu-zF-Fre" secondAttribute="width" multiplier="0.9" id="o8X-BE-wza"/>
                            <constraint firstItem="bed-6z-uTf" firstAttribute="width" secondItem="vDu-zF-Fre" secondAttribute="width" multiplier="0.9" id="qAa-rZ-7Jl"/>
                            <constraint firstItem="XJE-57-BrJ" firstAttribute="width" secondItem="0fM-iV-PqE" secondAttribute="width" id="qV2-k6-yBI"/>
                            <constraint firstItem="vDu-zF-Fre" firstAttribute="trailing" secondItem="aZj-l6-Hxp" secondAttribute="trailing" id="tvM-ta-NXB"/>
                            <constraint firstItem="ImP-uA-Kkf" firstAttribute="height" secondItem="BKf-46-cKR" secondAttribute="height" id="zvr-6D-5bz"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="btnBack" destination="M4m-HP-CqE" id="sFi-eV-pef"/>
                        <outlet property="btnComplainType" destination="XJE-57-BrJ" id="9LH-98-yzo"/>
                        <outlet property="btnSubmit" destination="JZo-Np-SqZ" id="9DW-lx-HPj"/>
                        <outlet property="btnTransaction" destination="ImP-uA-Kkf" id="QTB-0i-9el"/>
                        <outlet property="lblComplainType" destination="cMd-lq-sd1" id="wsk-LJ-UoP"/>
                        <outlet property="lblTransaction" destination="GR4-Ad-bCz" id="m2J-ZR-tQx"/>
                        <outlet property="txtViewDetails" destination="z2A-HW-g8p" id="vdq-Qi-pxd"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Ief-a0-LHa" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="52" y="69.715142428785612"/>
        </scene>
        <!--Complain ListVC-->
        <scene sceneID="ZQc-dw-CLd">
            <objects>
                <viewController storyboardIdentifier="ComplainListVC" id="QZG-JB-lyq" customClass="ComplainListVC" customModule="NXC_EV_Solutions" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="YDN-ki-R6f">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="FHE-1f-H2t">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="4yD-QG-s9E">
                                        <rect key="frame" x="12" y="5" width="34" height="34"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="34" id="k2U-W1-bTg"/>
                                            <constraint firstAttribute="width" constant="34" id="xSZ-SA-zNV"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="8" minY="6" maxX="8" maxY="6"/>
                                        <state key="normal" image="ic_back_arrow"/>
                                        <connections>
                                            <action selector="backAction:" destination="QZG-JB-lyq" eventType="touchUpInside" id="vdd-ZU-Yvr"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Complaint Details" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ObQ-PK-bdq">
                                        <rect key="frame" x="117.5" y="12" width="140.5" height="20.5"/>
                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                        <color key="textColor" red="0.070588235289999995" green="0.070588235289999995" blue="0.070588235289999995" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Ay3-nm-BxT">
                                        <rect key="frame" x="329" y="5" width="34" height="34"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="34" id="Gkc-Rm-CV7"/>
                                            <constraint firstAttribute="height" constant="34" id="Pif-rq-EF5"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="8" minY="8" maxX="8" maxY="8"/>
                                        <state key="normal" image="ic_filter"/>
                                        <connections>
                                            <action selector="filterAction:" destination="QZG-JB-lyq" eventType="touchUpInside" id="rtO-ta-OLV"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="ObQ-PK-bdq" firstAttribute="centerX" secondItem="FHE-1f-H2t" secondAttribute="centerX" id="7du-qa-qC4"/>
                                    <constraint firstItem="ObQ-PK-bdq" firstAttribute="centerY" secondItem="FHE-1f-H2t" secondAttribute="centerY" id="IgL-2E-iJI"/>
                                    <constraint firstItem="4yD-QG-s9E" firstAttribute="centerY" secondItem="FHE-1f-H2t" secondAttribute="centerY" id="SxQ-aM-5LO"/>
                                    <constraint firstItem="Ay3-nm-BxT" firstAttribute="centerY" secondItem="FHE-1f-H2t" secondAttribute="centerY" id="bYH-1d-1DK"/>
                                    <constraint firstAttribute="trailing" secondItem="Ay3-nm-BxT" secondAttribute="trailing" constant="12" id="eVM-U2-ZsB"/>
                                    <constraint firstItem="4yD-QG-s9E" firstAttribute="leading" secondItem="FHE-1f-H2t" secondAttribute="leading" constant="12" id="uWR-Qk-YR7"/>
                                    <constraint firstAttribute="height" constant="44" id="y1R-Fy-QvI"/>
                                </constraints>
                            </view>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="2qI-8z-Rw8">
                                <rect key="frame" x="19" y="607" width="337.5" height="44"/>
                                <color key="backgroundColor" name="Primary"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="44" id="GRR-0Y-F3q"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                <state key="normal" title="Add Complaint">
                                    <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                </state>
                                <connections>
                                    <action selector="addComplainAction:" destination="QZG-JB-lyq" eventType="touchUpInside" id="HTn-NQ-xdU"/>
                                </connections>
                            </button>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="No Complain Available" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="yjV-hk-rD8">
                                <rect key="frame" x="0.0" y="44" width="375" height="547"/>
                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                <color key="textColor" name="Primary"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="none" allowsSelection="NO" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="9um-ao-WIb">
                                <rect key="frame" x="0.0" y="44" width="375" height="547"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <prototypes>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" reuseIdentifier="cell" rowHeight="250" id="MpL-V1-AXK" customClass="ComplainCell" customModule="NXC_EV_Solutions" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="44.5" width="375" height="250"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="MpL-V1-AXK" id="8Us-El-l5f">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="250"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="p1c-vo-jEs">
                                                    <rect key="frame" x="8" y="8" width="359" height="234"/>
                                                    <subviews>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Charger" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="wob-7Z-YWt">
                                                            <rect key="frame" x="0.0" y="0.0" width="359" height="44"/>
                                                            <color key="backgroundColor" name="Primary"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="44" id="gRI-LV-m4l"/>
                                                            </constraints>
                                                            <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="16"/>
                                                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="3jA-6k-OAp">
                                                            <rect key="frame" x="0.0" y="52" width="359" height="182"/>
                                                            <subviews>
                                                                <stackView opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="9I8-mO-zBr">
                                                                    <rect key="frame" x="9" y="0.0" width="341" height="48"/>
                                                                    <subviews>
                                                                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="89w-eS-5Eb">
                                                                            <rect key="frame" x="0.0" y="0.0" width="221.5" height="48"/>
                                                                            <subviews>
                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Transaction ID" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="3va-cH-kKh">
                                                                                    <rect key="frame" x="0.0" y="0.0" width="221.5" height="24"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="height" constant="24" id="gLx-S1-AXb"/>
                                                                                    </constraints>
                                                                                    <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="15"/>
                                                                                    <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                    <nil key="highlightedColor"/>
                                                                                </label>
                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="15" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="sLx-11-OBP">
                                                                                    <rect key="frame" x="0.0" y="24" width="221.5" height="24"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="height" constant="24" id="VFI-xm-h9N"/>
                                                                                    </constraints>
                                                                                    <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                                    <color key="textColor" name="TextNotSelected"/>
                                                                                    <nil key="highlightedColor"/>
                                                                                </label>
                                                                            </subviews>
                                                                        </stackView>
                                                                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="bottom" translatesAutoresizingMaskIntoConstraints="NO" id="Fgc-uu-a6c">
                                                                            <rect key="frame" x="221.5" y="0.0" width="119.5" height="48"/>
                                                                            <subviews>
                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Status" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="e7j-kp-Y8j">
                                                                                    <rect key="frame" x="73" y="0.0" width="46.5" height="24"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="height" constant="24" id="IuU-9H-iBP"/>
                                                                                    </constraints>
                                                                                    <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="15"/>
                                                                                    <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                    <nil key="highlightedColor"/>
                                                                                </label>
                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="In Progress" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="pf3-eG-mS9">
                                                                                    <rect key="frame" x="42" y="24" width="77.5" height="24"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="height" constant="24" id="M8g-wU-wb3"/>
                                                                                    </constraints>
                                                                                    <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                                    <color key="textColor" name="TextNotSelected"/>
                                                                                    <nil key="highlightedColor"/>
                                                                                </label>
                                                                            </subviews>
                                                                        </stackView>
                                                                    </subviews>
                                                                    <constraints>
                                                                        <constraint firstItem="Fgc-uu-a6c" firstAttribute="width" secondItem="9I8-mO-zBr" secondAttribute="width" multiplier="0.35" id="6br-v8-RVq"/>
                                                                    </constraints>
                                                                </stackView>
                                                                <stackView opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="YLM-5P-lOY">
                                                                    <rect key="frame" x="9" y="56" width="341" height="48"/>
                                                                    <subviews>
                                                                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="rka-32-n7H">
                                                                            <rect key="frame" x="0.0" y="0.0" width="221.5" height="48"/>
                                                                            <subviews>
                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Date" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="2YG-17-wPl">
                                                                                    <rect key="frame" x="0.0" y="0.0" width="221.5" height="24"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="height" constant="24" id="FCv-NK-qS0"/>
                                                                                    </constraints>
                                                                                    <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="15"/>
                                                                                    <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                    <nil key="highlightedColor"/>
                                                                                </label>
                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="20 Aug 2021" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="6Di-L6-EMn">
                                                                                    <rect key="frame" x="0.0" y="24" width="221.5" height="24"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="height" constant="24" id="uRP-TS-y2f"/>
                                                                                    </constraints>
                                                                                    <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                                    <color key="textColor" name="TextNotSelected"/>
                                                                                    <nil key="highlightedColor"/>
                                                                                </label>
                                                                            </subviews>
                                                                        </stackView>
                                                                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="bottom" translatesAutoresizingMaskIntoConstraints="NO" id="zaW-4w-107">
                                                                            <rect key="frame" x="221.5" y="0.0" width="119.5" height="48"/>
                                                                            <subviews>
                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Time" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="fnB-xU-g60">
                                                                                    <rect key="frame" x="84" y="0.0" width="35.5" height="24"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="height" constant="24" id="PHF-Ex-fja"/>
                                                                                    </constraints>
                                                                                    <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="15"/>
                                                                                    <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                    <nil key="highlightedColor"/>
                                                                                </label>
                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="08:00 AM" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="zzD-b1-aNx">
                                                                                    <rect key="frame" x="51.5" y="24" width="68" height="24"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="height" constant="24" id="0oY-DG-lew"/>
                                                                                    </constraints>
                                                                                    <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                                    <color key="textColor" name="TextNotSelected"/>
                                                                                    <nil key="highlightedColor"/>
                                                                                </label>
                                                                            </subviews>
                                                                        </stackView>
                                                                    </subviews>
                                                                    <constraints>
                                                                        <constraint firstItem="zaW-4w-107" firstAttribute="width" secondItem="YLM-5P-lOY" secondAttribute="width" multiplier="0.35" id="7al-Mo-gEM"/>
                                                                    </constraints>
                                                                </stackView>
                                                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="wRk-X9-jrQ">
                                                                    <rect key="frame" x="0.0" y="112" width="359" height="70"/>
                                                                    <subviews>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="  Details" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="IB3-b6-4bj">
                                                                            <rect key="frame" x="0.0" y="0.0" width="359" height="24"/>
                                                                            <constraints>
                                                                                <constraint firstAttribute="height" constant="24" id="lWt-N5-XhT"/>
                                                                            </constraints>
                                                                            <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="15"/>
                                                                            <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="mCW-Xc-1v4">
                                                                            <rect key="frame" x="0.0" y="24" width="359" height="46"/>
                                                                            <subviews>
                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" textAlignment="natural" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="CbH-xx-e5O">
                                                                                    <rect key="frame" x="8" y="8" width="343" height="30"/>
                                                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                    <string key="text">Many desktop publishing packages and web page editors now use Lorem Ipsum as their default model text, and a search for 'lorem ipsum' will uncover many web sites still in their infancy</string>
                                                                                    <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                                    <color key="textColor" red="0.15294117647058825" green="0.14117647058823529" blue="0.14117647058823529" alpha="0.5" colorSpace="custom" customColorSpace="sRGB"/>
                                                                                    <nil key="highlightedColor"/>
                                                                                </label>
                                                                            </subviews>
                                                                            <color key="backgroundColor" red="0.99215686274509807" green="0.76470588235294112" blue="0.31372549019607843" alpha="0.20000000000000001" colorSpace="custom" customColorSpace="sRGB"/>
                                                                            <constraints>
                                                                                <constraint firstItem="CbH-xx-e5O" firstAttribute="leading" secondItem="mCW-Xc-1v4" secondAttribute="leading" constant="8" id="6NZ-1S-D9T"/>
                                                                                <constraint firstItem="CbH-xx-e5O" firstAttribute="top" secondItem="mCW-Xc-1v4" secondAttribute="top" constant="8" id="O8C-nt-Tp8"/>
                                                                                <constraint firstAttribute="trailing" secondItem="CbH-xx-e5O" secondAttribute="trailing" constant="8" id="OpC-Ih-g5c"/>
                                                                                <constraint firstAttribute="bottom" secondItem="CbH-xx-e5O" secondAttribute="bottom" constant="8" id="eLr-GP-cXR"/>
                                                                            </constraints>
                                                                        </view>
                                                                    </subviews>
                                                                </stackView>
                                                            </subviews>
                                                            <constraints>
                                                                <constraint firstItem="wRk-X9-jrQ" firstAttribute="width" secondItem="3jA-6k-OAp" secondAttribute="width" id="3D9-OQ-JeC"/>
                                                                <constraint firstItem="YLM-5P-lOY" firstAttribute="width" secondItem="3jA-6k-OAp" secondAttribute="width" multiplier="0.95" id="kHv-fd-wLc"/>
                                                                <constraint firstItem="9I8-mO-zBr" firstAttribute="width" secondItem="3jA-6k-OAp" secondAttribute="width" multiplier="0.95" id="zTG-iP-Kgb"/>
                                                            </constraints>
                                                        </stackView>
                                                    </subviews>
                                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <constraints>
                                                        <constraint firstItem="3jA-6k-OAp" firstAttribute="leading" secondItem="p1c-vo-jEs" secondAttribute="leading" id="EKr-4q-oCn"/>
                                                        <constraint firstAttribute="trailing" secondItem="3jA-6k-OAp" secondAttribute="trailing" id="F6O-oD-Lb6"/>
                                                        <constraint firstItem="3jA-6k-OAp" firstAttribute="top" secondItem="wob-7Z-YWt" secondAttribute="bottom" constant="8" id="FtD-I4-p81"/>
                                                        <constraint firstItem="wob-7Z-YWt" firstAttribute="top" secondItem="p1c-vo-jEs" secondAttribute="top" id="Jf4-LY-ARY"/>
                                                        <constraint firstAttribute="trailing" secondItem="wob-7Z-YWt" secondAttribute="trailing" id="RfA-Aw-9NT"/>
                                                        <constraint firstAttribute="bottom" secondItem="3jA-6k-OAp" secondAttribute="bottom" id="XFe-vg-SIF"/>
                                                        <constraint firstItem="wob-7Z-YWt" firstAttribute="leading" secondItem="p1c-vo-jEs" secondAttribute="leading" id="cHv-Af-SF5"/>
                                                    </constraints>
                                                </view>
                                            </subviews>
                                            <constraints>
                                                <constraint firstAttribute="bottom" secondItem="p1c-vo-jEs" secondAttribute="bottom" constant="8" id="Ezz-46-heG"/>
                                                <constraint firstAttribute="trailing" secondItem="p1c-vo-jEs" secondAttribute="trailing" constant="8" id="PW8-oV-HxC"/>
                                                <constraint firstItem="p1c-vo-jEs" firstAttribute="top" secondItem="8Us-El-l5f" secondAttribute="top" constant="8" id="feO-me-tzK"/>
                                                <constraint firstItem="p1c-vo-jEs" firstAttribute="leading" secondItem="8Us-El-l5f" secondAttribute="leading" constant="8" id="vQS-tn-kOd"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <connections>
                                            <outlet property="lblComplainType" destination="wob-7Z-YWt" id="Pdy-9d-j2J"/>
                                            <outlet property="lblDate" destination="6Di-L6-EMn" id="Yda-D3-Wyt"/>
                                            <outlet property="lblDetails" destination="CbH-xx-e5O" id="gEk-yf-FBv"/>
                                            <outlet property="lblStatus" destination="pf3-eG-mS9" id="jKh-yL-HOc"/>
                                            <outlet property="lblTime" destination="zzD-b1-aNx" id="hGz-1N-fc1"/>
                                            <outlet property="lblTransactionID" destination="sLx-11-OBP" id="bKE-pd-KKv"/>
                                            <outlet property="viewMain" destination="p1c-vo-jEs" id="ISq-WT-gkW"/>
                                        </connections>
                                    </tableViewCell>
                                </prototypes>
                            </tableView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="55B-Kx-N4n">
                                <rect key="frame" x="0.0" y="44" width="375" height="547"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="UtT-NJ-0hr">
                                        <rect key="frame" x="106.5" y="214.5" width="162" height="118"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_no_data" translatesAutoresizingMaskIntoConstraints="NO" id="iyS-n2-Jpi">
                                                <rect key="frame" x="6" y="0.0" width="150" height="100"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="100" id="PAu-C9-9KB"/>
                                                    <constraint firstAttribute="width" constant="150" id="jFw-UG-qGN"/>
                                                </constraints>
                                            </imageView>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="No complaints available" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ief-aG-erZ">
                                                <rect key="frame" x="0.0" y="100" width="162" height="18"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                <color key="textColor" white="0.0" alpha="0.69999999999999996" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="UtT-NJ-0hr" firstAttribute="centerX" secondItem="55B-Kx-N4n" secondAttribute="centerX" id="9Iy-oL-dqe"/>
                                    <constraint firstItem="UtT-NJ-0hr" firstAttribute="centerY" secondItem="55B-Kx-N4n" secondAttribute="centerY" id="fgc-iN-TtT"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ob9-gm-4gp">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="SWQ-Mt-y0U">
                                        <rect key="frame" x="0.0" y="388" width="375" height="279"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Filter By" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="308-oF-99A">
                                                <rect key="frame" x="12" y="8" width="58" height="34"/>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="34" id="7ud-Wa-DJb"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" weight="medium" pointSize="15"/>
                                                <color key="textColor" name="SecondaryGrayText"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="GjO-tY-ayL">
                                                <rect key="frame" x="337" y="8" width="34" height="34"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="34" id="J8Q-u8-YaT"/>
                                                    <constraint firstAttribute="height" constant="34" id="knV-uh-p5S"/>
                                                </constraints>
                                                <inset key="imageEdgeInsets" minX="10" minY="10" maxX="10" maxY="10"/>
                                                <state key="normal" image="ic_cancel"/>
                                                <connections>
                                                    <action selector="filterCloseAction:" destination="QZG-JB-lyq" eventType="touchUpInside" id="H66-aZ-cYG"/>
                                                </connections>
                                            </button>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="GrF-mJ-ss1">
                                                <rect key="frame" x="19" y="50" width="337.5" height="1"/>
                                                <color key="backgroundColor" name="PrimaryText"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="1" id="JWV-V6-xVS"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Status" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="HdU-Hz-I25">
                                                <rect key="frame" x="12" y="59" width="41.5" height="20"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="20" id="8XC-o6-3tj"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                <color key="textColor" name="Primary"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Select Date" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="hJC-RA-hnx">
                                                <rect key="frame" x="12" y="131" width="75" height="20"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="20" id="P3Y-rS-Yhm"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                <color key="textColor" name="Primary"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <stackView opaque="NO" contentMode="scaleToFill" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="Lvv-43-gPS">
                                                <rect key="frame" x="12" y="91" width="351" height="32"/>
                                                <subviews>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="TrS-Wg-moV">
                                                        <rect key="frame" x="0.0" y="0.0" width="40.5" height="32"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="All" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="D57-JW-7ie">
                                                                <rect key="frame" x="4" y="4" width="32.5" height="24"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="24" id="RhI-Lz-2qg"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                <color key="textColor" name="SecondaryGrayText"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstItem="D57-JW-7ie" firstAttribute="leading" secondItem="TrS-Wg-moV" secondAttribute="leading" constant="4" id="9FD-1X-Zyf"/>
                                                            <constraint firstAttribute="trailing" secondItem="D57-JW-7ie" secondAttribute="trailing" constant="4" id="9jo-tb-q2G"/>
                                                            <constraint firstAttribute="bottom" secondItem="D57-JW-7ie" secondAttribute="bottom" constant="4" id="eRF-yv-j0n"/>
                                                            <constraint firstItem="D57-JW-7ie" firstAttribute="top" secondItem="TrS-Wg-moV" secondAttribute="top" constant="4" id="mHS-pQ-Leh"/>
                                                        </constraints>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Ktu-fV-mPq">
                                                        <rect key="frame" x="44.5" y="0.0" width="88" height="32"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Pending" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="KIr-eW-d1X">
                                                                <rect key="frame" x="4" y="4" width="80" height="24"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="24" id="6Xv-fl-kwl"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                <color key="textColor" name="SecondaryGrayText"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstItem="KIr-eW-d1X" firstAttribute="leading" secondItem="Ktu-fV-mPq" secondAttribute="leading" constant="4" id="8wL-Ej-Ftr"/>
                                                            <constraint firstAttribute="bottom" secondItem="KIr-eW-d1X" secondAttribute="bottom" constant="4" id="Tah-nM-JxH"/>
                                                            <constraint firstAttribute="trailing" secondItem="KIr-eW-d1X" secondAttribute="trailing" constant="4" id="bJe-Av-g0r"/>
                                                            <constraint firstItem="KIr-eW-d1X" firstAttribute="top" secondItem="Ktu-fV-mPq" secondAttribute="top" constant="4" id="yrA-Q4-ZKC"/>
                                                        </constraints>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="jPF-A9-Vv0">
                                                        <rect key="frame" x="136.5" y="0.0" width="105" height="32"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="InProgress" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="1nR-0l-fvi">
                                                                <rect key="frame" x="4" y="4" width="97" height="24"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="24" id="cgr-UJ-uy2"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                <color key="textColor" name="SecondaryGrayText"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstAttribute="trailing" secondItem="1nR-0l-fvi" secondAttribute="trailing" constant="4" id="27r-wW-MrM"/>
                                                            <constraint firstItem="1nR-0l-fvi" firstAttribute="top" secondItem="jPF-A9-Vv0" secondAttribute="top" constant="4" id="9Id-nE-JK5"/>
                                                            <constraint firstAttribute="bottom" secondItem="1nR-0l-fvi" secondAttribute="bottom" constant="4" id="HW1-sp-Gdt"/>
                                                            <constraint firstItem="1nR-0l-fvi" firstAttribute="leading" secondItem="jPF-A9-Vv0" secondAttribute="leading" constant="4" id="XG2-PQ-w48"/>
                                                        </constraints>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="r5z-Pf-tmE">
                                                        <rect key="frame" x="245.5" y="0.0" width="105.5" height="32"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Resolved" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ecA-QL-YjI">
                                                                <rect key="frame" x="4" y="4" width="97.5" height="24"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="24" id="d8e-hA-Hrr"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                <color key="textColor" name="SecondaryGrayText"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstItem="ecA-QL-YjI" firstAttribute="leading" secondItem="r5z-Pf-tmE" secondAttribute="leading" constant="4" id="1x8-DJ-Jif"/>
                                                            <constraint firstAttribute="trailing" secondItem="ecA-QL-YjI" secondAttribute="trailing" constant="4" id="jKI-sR-01w"/>
                                                            <constraint firstAttribute="bottom" secondItem="ecA-QL-YjI" secondAttribute="bottom" constant="4" id="lzI-oK-0o1"/>
                                                            <constraint firstItem="ecA-QL-YjI" firstAttribute="top" secondItem="r5z-Pf-tmE" secondAttribute="top" constant="4" id="qO2-Xm-lRk"/>
                                                        </constraints>
                                                    </view>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstItem="Ktu-fV-mPq" firstAttribute="width" secondItem="Lvv-43-gPS" secondAttribute="width" multiplier="0.25" id="8HC-GT-w6p"/>
                                                    <constraint firstItem="r5z-Pf-tmE" firstAttribute="width" secondItem="Lvv-43-gPS" secondAttribute="width" multiplier="0.3" id="lve-aM-ePa"/>
                                                    <constraint firstItem="jPF-A9-Vv0" firstAttribute="width" secondItem="Lvv-43-gPS" secondAttribute="width" multiplier="0.3" id="ywQ-4f-be8"/>
                                                </constraints>
                                            </stackView>
                                            <stackView opaque="NO" contentMode="scaleToFill" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="inX-nO-cvh">
                                                <rect key="frame" x="16" y="159" width="343" height="44"/>
                                                <subviews>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="I0F-KB-M6L">
                                                        <rect key="frame" x="0.0" y="0.0" width="137" height="44"/>
                                                        <subviews>
                                                            <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="lmh-x5-OZZ">
                                                                <rect key="frame" x="8" y="4" width="121" height="36"/>
                                                                <subviews>
                                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_calendar" translatesAutoresizingMaskIntoConstraints="NO" id="7nM-XM-5wB">
                                                                        <rect key="frame" x="0.0" y="10.5" width="15" height="15"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="width" constant="15" id="05w-ho-edu"/>
                                                                            <constraint firstAttribute="height" constant="15" id="hdA-KH-FfX"/>
                                                                        </constraints>
                                                                    </imageView>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="q1o-jM-EGK">
                                                                        <rect key="frame" x="27" y="0.0" width="94" height="36"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                                <constraints>
                                                                    <constraint firstItem="q1o-jM-EGK" firstAttribute="height" secondItem="lmh-x5-OZZ" secondAttribute="height" id="YPp-87-vLY"/>
                                                                </constraints>
                                                            </stackView>
                                                        </subviews>
                                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="44" id="2aG-2Y-uAg"/>
                                                            <constraint firstAttribute="trailing" secondItem="lmh-x5-OZZ" secondAttribute="trailing" constant="8" id="JpQ-TE-e1H"/>
                                                            <constraint firstItem="lmh-x5-OZZ" firstAttribute="leading" secondItem="I0F-KB-M6L" secondAttribute="leading" constant="8" id="Ob0-dw-jcc"/>
                                                            <constraint firstItem="lmh-x5-OZZ" firstAttribute="top" secondItem="I0F-KB-M6L" secondAttribute="top" constant="4" id="QQ5-7T-pQz"/>
                                                            <constraint firstAttribute="bottom" secondItem="lmh-x5-OZZ" secondAttribute="bottom" constant="4" id="fBg-9N-qdi"/>
                                                        </constraints>
                                                    </view>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="249" verticalHuggingPriority="251" text="To" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="gEU-oY-n6I">
                                                        <rect key="frame" x="145" y="0.0" width="53" height="44"/>
                                                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                        <nil key="textColor"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="00h-K7-dXy">
                                                        <rect key="frame" x="206" y="0.0" width="137" height="44"/>
                                                        <subviews>
                                                            <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="BDf-7U-2S1">
                                                                <rect key="frame" x="8" y="4" width="121" height="36"/>
                                                                <subviews>
                                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_calendar" translatesAutoresizingMaskIntoConstraints="NO" id="RI2-jn-f5v">
                                                                        <rect key="frame" x="0.0" y="10.5" width="15" height="15"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="width" constant="15" id="fiK-Gx-4S5"/>
                                                                            <constraint firstAttribute="height" constant="15" id="vyO-Xo-YiZ"/>
                                                                        </constraints>
                                                                    </imageView>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="SI2-mk-G7H">
                                                                        <rect key="frame" x="27" y="0.0" width="94" height="36"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                                <constraints>
                                                                    <constraint firstItem="SI2-mk-G7H" firstAttribute="height" secondItem="BDf-7U-2S1" secondAttribute="height" id="UjH-Q6-uul"/>
                                                                </constraints>
                                                            </stackView>
                                                        </subviews>
                                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstAttribute="trailing" secondItem="BDf-7U-2S1" secondAttribute="trailing" constant="8" id="9Dc-V2-in3"/>
                                                            <constraint firstAttribute="bottom" secondItem="BDf-7U-2S1" secondAttribute="bottom" constant="4" id="cMP-1a-fXO"/>
                                                            <constraint firstAttribute="height" constant="44" id="dcQ-m9-fuu"/>
                                                            <constraint firstItem="BDf-7U-2S1" firstAttribute="top" secondItem="00h-K7-dXy" secondAttribute="top" constant="4" id="kL5-jR-8lO"/>
                                                            <constraint firstItem="BDf-7U-2S1" firstAttribute="leading" secondItem="00h-K7-dXy" secondAttribute="leading" constant="8" id="xl2-Ls-toU"/>
                                                        </constraints>
                                                    </view>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstItem="I0F-KB-M6L" firstAttribute="width" secondItem="inX-nO-cvh" secondAttribute="width" multiplier="0.4" id="HRF-uH-5QP"/>
                                                    <constraint firstItem="00h-K7-dXy" firstAttribute="width" secondItem="inX-nO-cvh" secondAttribute="width" multiplier="0.4" id="T5g-7E-MLR"/>
                                                </constraints>
                                            </stackView>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="VvD-Tz-U4q">
                                                <rect key="frame" x="19" y="219" width="337.5" height="44"/>
                                                <color key="backgroundColor" name="Primary"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="44" id="TOV-e4-YSy"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                                <state key="normal" title="Apply Filter">
                                                    <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                </state>
                                                <connections>
                                                    <action selector="applyFilterAction:" destination="QZG-JB-lyq" eventType="touchUpInside" id="Usd-Mm-bUq"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" tag="101" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="SfP-AO-2OU">
                                                <rect key="frame" x="12" y="91" width="40.5" height="32"/>
                                                <color key="tintColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <state key="normal" title="Button"/>
                                                <buttonConfiguration key="configuration" style="plain" title=""/>
                                                <connections>
                                                    <action selector="complainStatusAction:" destination="QZG-JB-lyq" eventType="touchUpInside" id="de0-RI-uym"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" tag="102" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="6dd-ac-DIg">
                                                <rect key="frame" x="56.5" y="91" width="88" height="32"/>
                                                <color key="tintColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <state key="normal" title="Button"/>
                                                <buttonConfiguration key="configuration" style="plain"/>
                                                <connections>
                                                    <action selector="complainStatusAction:" destination="QZG-JB-lyq" eventType="touchUpInside" id="asM-mh-xkh"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" tag="103" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="yaR-UP-laa">
                                                <rect key="frame" x="148.5" y="91" width="105" height="32"/>
                                                <color key="tintColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <state key="normal" title="Button"/>
                                                <buttonConfiguration key="configuration" style="plain"/>
                                                <connections>
                                                    <action selector="complainStatusAction:" destination="QZG-JB-lyq" eventType="touchUpInside" id="WYL-5q-kOC"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" tag="104" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ADV-89-uVB">
                                                <rect key="frame" x="257.5" y="91" width="105.5" height="32"/>
                                                <color key="tintColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <state key="normal" title="Button"/>
                                                <buttonConfiguration key="configuration" style="plain"/>
                                                <connections>
                                                    <action selector="complainStatusAction:" destination="QZG-JB-lyq" eventType="touchUpInside" id="Que-T1-1t9"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="lR8-rB-tUo">
                                                <rect key="frame" x="16" y="159" width="137" height="44"/>
                                                <color key="tintColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <state key="normal" title="Button"/>
                                                <buttonConfiguration key="configuration" style="plain"/>
                                                <connections>
                                                    <action selector="fromDateAction:" destination="QZG-JB-lyq" eventType="touchUpInside" id="ocx-ud-mTJ"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="tcU-PP-Gg2">
                                                <rect key="frame" x="222" y="159" width="137" height="44"/>
                                                <color key="tintColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <state key="normal" title="Button"/>
                                                <buttonConfiguration key="configuration" style="plain"/>
                                                <connections>
                                                    <action selector="toDateAction:" destination="QZG-JB-lyq" eventType="touchUpInside" id="dqU-GU-9um"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="HdU-Hz-I25" firstAttribute="top" secondItem="GrF-mJ-ss1" secondAttribute="bottom" constant="8" id="01a-uS-fIM"/>
                                            <constraint firstItem="lR8-rB-tUo" firstAttribute="height" secondItem="I0F-KB-M6L" secondAttribute="height" id="1yB-Cc-pfm"/>
                                            <constraint firstItem="SfP-AO-2OU" firstAttribute="width" secondItem="TrS-Wg-moV" secondAttribute="width" id="4PZ-K5-8Yc"/>
                                            <constraint firstItem="VvD-Tz-U4q" firstAttribute="centerX" secondItem="SWQ-Mt-y0U" secondAttribute="centerX" id="6W3-m0-2ws"/>
                                            <constraint firstItem="Lvv-43-gPS" firstAttribute="leading" secondItem="SWQ-Mt-y0U" secondAttribute="leading" constant="12" id="6ox-21-Nhk"/>
                                            <constraint firstItem="inX-nO-cvh" firstAttribute="top" secondItem="hJC-RA-hnx" secondAttribute="bottom" constant="8" id="7aZ-Ee-lpY"/>
                                            <constraint firstItem="308-oF-99A" firstAttribute="leading" secondItem="SWQ-Mt-y0U" secondAttribute="leading" constant="12" id="9lN-7l-fHb"/>
                                            <constraint firstItem="tcU-PP-Gg2" firstAttribute="centerX" secondItem="00h-K7-dXy" secondAttribute="centerX" id="9n5-bu-E3A"/>
                                            <constraint firstItem="lR8-rB-tUo" firstAttribute="centerX" secondItem="I0F-KB-M6L" secondAttribute="centerX" id="BpQ-J3-Kef"/>
                                            <constraint firstItem="GjO-tY-ayL" firstAttribute="top" secondItem="SWQ-Mt-y0U" secondAttribute="top" constant="8" id="DAU-xE-Per"/>
                                            <constraint firstItem="tcU-PP-Gg2" firstAttribute="width" secondItem="00h-K7-dXy" secondAttribute="width" id="DID-Pf-Bg4"/>
                                            <constraint firstItem="hJC-RA-hnx" firstAttribute="top" secondItem="Lvv-43-gPS" secondAttribute="bottom" constant="8" id="EBC-8i-O0I"/>
                                            <constraint firstAttribute="bottom" secondItem="VvD-Tz-U4q" secondAttribute="bottom" constant="16" id="Eng-lc-zwA"/>
                                            <constraint firstItem="GrF-mJ-ss1" firstAttribute="width" secondItem="SWQ-Mt-y0U" secondAttribute="width" multiplier="0.9" id="GEG-yG-xwZ"/>
                                            <constraint firstItem="6dd-ac-DIg" firstAttribute="width" secondItem="Ktu-fV-mPq" secondAttribute="width" id="GUx-uN-rP9"/>
                                            <constraint firstItem="lR8-rB-tUo" firstAttribute="centerY" secondItem="I0F-KB-M6L" secondAttribute="centerY" id="HN3-xi-Ifj"/>
                                            <constraint firstItem="yaR-UP-laa" firstAttribute="centerX" secondItem="jPF-A9-Vv0" secondAttribute="centerX" id="IAB-N3-8v7"/>
                                            <constraint firstItem="Lvv-43-gPS" firstAttribute="top" secondItem="HdU-Hz-I25" secondAttribute="bottom" constant="12" id="Jfh-ed-RSb"/>
                                            <constraint firstItem="yaR-UP-laa" firstAttribute="height" secondItem="jPF-A9-Vv0" secondAttribute="height" id="Kwm-qI-MCo"/>
                                            <constraint firstItem="ADV-89-uVB" firstAttribute="height" secondItem="r5z-Pf-tmE" secondAttribute="height" id="ONT-J4-yLF"/>
                                            <constraint firstItem="ADV-89-uVB" firstAttribute="width" secondItem="r5z-Pf-tmE" secondAttribute="width" id="RSH-EY-vtm"/>
                                            <constraint firstItem="VvD-Tz-U4q" firstAttribute="top" secondItem="inX-nO-cvh" secondAttribute="bottom" constant="16" id="Rbn-uk-ug1"/>
                                            <constraint firstItem="SfP-AO-2OU" firstAttribute="height" secondItem="TrS-Wg-moV" secondAttribute="height" id="Sf4-3n-3k7"/>
                                            <constraint firstItem="hJC-RA-hnx" firstAttribute="leading" secondItem="SWQ-Mt-y0U" secondAttribute="leading" constant="12" id="W0h-s1-p4E"/>
                                            <constraint firstAttribute="trailing" secondItem="GjO-tY-ayL" secondAttribute="trailing" constant="4" id="WfF-JN-Vrd"/>
                                            <constraint firstItem="yaR-UP-laa" firstAttribute="width" secondItem="jPF-A9-Vv0" secondAttribute="width" id="ZAx-XS-s8S"/>
                                            <constraint firstItem="lR8-rB-tUo" firstAttribute="width" secondItem="I0F-KB-M6L" secondAttribute="width" id="bc9-Ob-yoC"/>
                                            <constraint firstItem="GrF-mJ-ss1" firstAttribute="centerX" secondItem="SWQ-Mt-y0U" secondAttribute="centerX" id="dAm-KB-bgR"/>
                                            <constraint firstItem="yaR-UP-laa" firstAttribute="centerY" secondItem="jPF-A9-Vv0" secondAttribute="centerY" id="dXJ-uq-3Fr"/>
                                            <constraint firstAttribute="trailing" secondItem="inX-nO-cvh" secondAttribute="trailing" constant="16" id="e6R-Rs-IGA"/>
                                            <constraint firstItem="6dd-ac-DIg" firstAttribute="height" secondItem="Ktu-fV-mPq" secondAttribute="height" id="ffU-Jg-hXH"/>
                                            <constraint firstItem="ADV-89-uVB" firstAttribute="centerY" secondItem="r5z-Pf-tmE" secondAttribute="centerY" id="gAT-UD-3de"/>
                                            <constraint firstItem="inX-nO-cvh" firstAttribute="leading" secondItem="SWQ-Mt-y0U" secondAttribute="leading" constant="16" id="gkx-Mi-dso"/>
                                            <constraint firstAttribute="trailing" secondItem="Lvv-43-gPS" secondAttribute="trailing" constant="12" id="hNp-xb-IdO"/>
                                            <constraint firstItem="tcU-PP-Gg2" firstAttribute="height" secondItem="00h-K7-dXy" secondAttribute="height" id="kMW-iD-jcq"/>
                                            <constraint firstItem="tcU-PP-Gg2" firstAttribute="centerY" secondItem="00h-K7-dXy" secondAttribute="centerY" id="lU4-5e-aOe"/>
                                            <constraint firstItem="6dd-ac-DIg" firstAttribute="centerX" secondItem="Ktu-fV-mPq" secondAttribute="centerX" id="mw9-9M-D7u"/>
                                            <constraint firstItem="SfP-AO-2OU" firstAttribute="centerY" secondItem="TrS-Wg-moV" secondAttribute="centerY" id="pYM-O3-qbi"/>
                                            <constraint firstItem="HdU-Hz-I25" firstAttribute="leading" secondItem="SWQ-Mt-y0U" secondAttribute="leading" constant="12" id="sCz-L6-l0G"/>
                                            <constraint firstItem="VvD-Tz-U4q" firstAttribute="width" secondItem="SWQ-Mt-y0U" secondAttribute="width" multiplier="0.9" id="vWq-U0-4f4"/>
                                            <constraint firstItem="6dd-ac-DIg" firstAttribute="centerY" secondItem="Ktu-fV-mPq" secondAttribute="centerY" id="wSf-hs-ZGb"/>
                                            <constraint firstItem="308-oF-99A" firstAttribute="top" secondItem="SWQ-Mt-y0U" secondAttribute="top" constant="8" id="waV-5v-tSw"/>
                                            <constraint firstItem="GrF-mJ-ss1" firstAttribute="top" secondItem="308-oF-99A" secondAttribute="bottom" constant="8" id="yGJ-EN-wVb"/>
                                            <constraint firstItem="SfP-AO-2OU" firstAttribute="centerX" secondItem="TrS-Wg-moV" secondAttribute="centerX" id="z9c-C7-Msd"/>
                                            <constraint firstItem="ADV-89-uVB" firstAttribute="centerX" secondItem="r5z-Pf-tmE" secondAttribute="centerX" id="zyE-6E-Pnu"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.75" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="bottom" secondItem="SWQ-Mt-y0U" secondAttribute="bottom" id="Abk-Xe-MiE"/>
                                    <constraint firstItem="SWQ-Mt-y0U" firstAttribute="centerX" secondItem="ob9-gm-4gp" secondAttribute="centerX" id="apc-G7-DaY"/>
                                    <constraint firstItem="SWQ-Mt-y0U" firstAttribute="width" secondItem="ob9-gm-4gp" secondAttribute="width" id="zx1-dq-r4o"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="wIZ-tY-nif">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="3Xr-iu-9YA">
                                        <rect key="frame" x="28" y="192" width="319" height="283.5"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Select Date" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="pSZ-F1-2eO">
                                                <rect key="frame" x="111" y="16" width="97" height="21.5"/>
                                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="18"/>
                                                <color key="textColor" name="Primary"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Qzo-tq-GqO">
                                                <rect key="frame" x="277" y="8" width="34" height="34"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="34" id="HNq-Fm-vdL"/>
                                                    <constraint firstAttribute="width" constant="34" id="Rrl-CD-9yp"/>
                                                </constraints>
                                                <inset key="imageEdgeInsets" minX="10" minY="10" maxX="10" maxY="10"/>
                                                <state key="normal" image="ic_cancel"/>
                                                <connections>
                                                    <action selector="cancelDateAction:" destination="QZG-JB-lyq" eventType="touchUpInside" id="mJg-7R-Ko7"/>
                                                </connections>
                                            </button>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="20" translatesAutoresizingMaskIntoConstraints="NO" id="bXr-nW-0jz">
                                                <rect key="frame" x="8" y="53.5" width="303" height="214"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" alignment="center" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="T4I-xK-4wm">
                                                        <rect key="frame" x="0.0" y="0.0" width="303" height="150"/>
                                                        <subviews>
                                                            <datePicker contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" datePickerMode="date" style="wheels" translatesAutoresizingMaskIntoConstraints="NO" id="XAy-k0-4EK">
                                                                <rect key="frame" x="0.0" y="0.0" width="303" height="150"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="150" id="9Ui-HI-8Iw"/>
                                                                </constraints>
                                                            </datePicker>
                                                        </subviews>
                                                    </stackView>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="24" translatesAutoresizingMaskIntoConstraints="NO" id="x3O-nc-KBa">
                                                        <rect key="frame" x="15.5" y="170" width="272" height="44"/>
                                                        <subviews>
                                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="4oN-L4-uT7">
                                                                <rect key="frame" x="0.0" y="0.0" width="272" height="44"/>
                                                                <subviews>
                                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="zWB-Bl-bQP">
                                                                        <rect key="frame" x="0.0" y="0.0" width="272" height="44"/>
                                                                        <color key="backgroundColor" name="Primary"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="44" id="aCD-Fg-Bn9"/>
                                                                        </constraints>
                                                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                                                        <state key="normal" title="OK">
                                                                            <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        </state>
                                                                        <connections>
                                                                            <action selector="okDateAction:" destination="QZG-JB-lyq" eventType="touchUpInside" id="o0j-Js-eZj"/>
                                                                        </connections>
                                                                    </button>
                                                                </subviews>
                                                            </stackView>
                                                        </subviews>
                                                    </stackView>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstItem="T4I-xK-4wm" firstAttribute="width" secondItem="bXr-nW-0jz" secondAttribute="width" id="7HF-cB-g9H"/>
                                                    <constraint firstItem="x3O-nc-KBa" firstAttribute="width" secondItem="bXr-nW-0jz" secondAttribute="width" multiplier="0.9" id="fTE-ij-F72"/>
                                                </constraints>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstItem="pSZ-F1-2eO" firstAttribute="centerX" secondItem="3Xr-iu-9YA" secondAttribute="centerX" id="1w1-3M-Wao"/>
                                            <constraint firstAttribute="trailing" secondItem="bXr-nW-0jz" secondAttribute="trailing" constant="8" id="C2m-eZ-etR"/>
                                            <constraint firstItem="bXr-nW-0jz" firstAttribute="leading" secondItem="3Xr-iu-9YA" secondAttribute="leading" constant="8" id="Rwy-80-ugx"/>
                                            <constraint firstAttribute="trailing" secondItem="Qzo-tq-GqO" secondAttribute="trailing" constant="8" id="UcM-Iq-Ewd"/>
                                            <constraint firstItem="Qzo-tq-GqO" firstAttribute="top" secondItem="3Xr-iu-9YA" secondAttribute="top" constant="8" id="ZbG-BG-nbC"/>
                                            <constraint firstItem="bXr-nW-0jz" firstAttribute="top" secondItem="pSZ-F1-2eO" secondAttribute="bottom" constant="16" id="fes-nh-Ri4"/>
                                            <constraint firstAttribute="bottom" secondItem="bXr-nW-0jz" secondAttribute="bottom" constant="16" id="oQA-9N-b3o"/>
                                            <constraint firstItem="pSZ-F1-2eO" firstAttribute="top" secondItem="3Xr-iu-9YA" secondAttribute="top" constant="16" id="q45-aF-Oxf"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.75" colorSpace="custom" customColorSpace="sRGB"/>
                                <constraints>
                                    <constraint firstItem="3Xr-iu-9YA" firstAttribute="centerX" secondItem="wIZ-tY-nif" secondAttribute="centerX" id="EI5-V2-nwK"/>
                                    <constraint firstItem="3Xr-iu-9YA" firstAttribute="centerY" secondItem="wIZ-tY-nif" secondAttribute="centerY" id="j7g-ks-lBg"/>
                                    <constraint firstItem="3Xr-iu-9YA" firstAttribute="width" secondItem="wIZ-tY-nif" secondAttribute="width" multiplier="0.85" id="uPN-I8-yYG"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="DlJ-jr-TmW"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="DlJ-jr-TmW" firstAttribute="bottom" secondItem="2qI-8z-Rw8" secondAttribute="bottom" constant="16" id="1ga-NI-Uy4"/>
                            <constraint firstItem="2qI-8z-Rw8" firstAttribute="top" secondItem="55B-Kx-N4n" secondAttribute="bottom" constant="16" id="26l-CT-isi"/>
                            <constraint firstItem="55B-Kx-N4n" firstAttribute="leading" secondItem="DlJ-jr-TmW" secondAttribute="leading" id="2mK-bJ-U0s"/>
                            <constraint firstItem="DlJ-jr-TmW" firstAttribute="trailing" secondItem="FHE-1f-H2t" secondAttribute="trailing" id="4bv-E2-WhS"/>
                            <constraint firstItem="ob9-gm-4gp" firstAttribute="leading" secondItem="DlJ-jr-TmW" secondAttribute="leading" id="590-Fu-wmn"/>
                            <constraint firstItem="2qI-8z-Rw8" firstAttribute="top" secondItem="9um-ao-WIb" secondAttribute="bottom" constant="16" id="B9N-Ss-SXz"/>
                            <constraint firstItem="yjV-hk-rD8" firstAttribute="top" secondItem="FHE-1f-H2t" secondAttribute="bottom" id="CPH-QE-kSm"/>
                            <constraint firstItem="ob9-gm-4gp" firstAttribute="top" secondItem="DlJ-jr-TmW" secondAttribute="top" id="Csd-pD-5jS"/>
                            <constraint firstItem="DlJ-jr-TmW" firstAttribute="trailing" secondItem="yjV-hk-rD8" secondAttribute="trailing" id="GmR-ai-7xT"/>
                            <constraint firstItem="FHE-1f-H2t" firstAttribute="leading" secondItem="DlJ-jr-TmW" secondAttribute="leading" id="NTC-63-CNJ"/>
                            <constraint firstItem="FHE-1f-H2t" firstAttribute="top" secondItem="DlJ-jr-TmW" secondAttribute="top" id="Qy3-Ld-JEx"/>
                            <constraint firstItem="DlJ-jr-TmW" firstAttribute="bottom" secondItem="ob9-gm-4gp" secondAttribute="bottom" id="Rjx-ks-2ee"/>
                            <constraint firstItem="DlJ-jr-TmW" firstAttribute="trailing" secondItem="55B-Kx-N4n" secondAttribute="trailing" id="Rta-0j-iuL"/>
                            <constraint firstItem="DlJ-jr-TmW" firstAttribute="bottom" secondItem="wIZ-tY-nif" secondAttribute="bottom" id="VjG-Lt-ifz"/>
                            <constraint firstItem="wIZ-tY-nif" firstAttribute="top" secondItem="DlJ-jr-TmW" secondAttribute="top" id="Xzu-LX-PQY"/>
                            <constraint firstItem="2qI-8z-Rw8" firstAttribute="centerX" secondItem="YDN-ki-R6f" secondAttribute="centerX" id="cip-cB-A2h"/>
                            <constraint firstItem="wIZ-tY-nif" firstAttribute="leading" secondItem="DlJ-jr-TmW" secondAttribute="leading" id="gzF-vU-r4b"/>
                            <constraint firstItem="55B-Kx-N4n" firstAttribute="top" secondItem="FHE-1f-H2t" secondAttribute="bottom" id="hCV-G1-xfO"/>
                            <constraint firstItem="DlJ-jr-TmW" firstAttribute="trailing" secondItem="ob9-gm-4gp" secondAttribute="trailing" id="htV-TZ-mcF"/>
                            <constraint firstItem="2qI-8z-Rw8" firstAttribute="width" secondItem="DlJ-jr-TmW" secondAttribute="width" multiplier="0.9" id="lff-AW-bTr"/>
                            <constraint firstItem="yjV-hk-rD8" firstAttribute="leading" secondItem="DlJ-jr-TmW" secondAttribute="leading" id="lie-Dw-ilZ"/>
                            <constraint firstItem="DlJ-jr-TmW" firstAttribute="trailing" secondItem="9um-ao-WIb" secondAttribute="trailing" id="nXw-Sz-yoz"/>
                            <constraint firstItem="9um-ao-WIb" firstAttribute="top" secondItem="FHE-1f-H2t" secondAttribute="bottom" id="sfN-jq-MWO"/>
                            <constraint firstItem="2qI-8z-Rw8" firstAttribute="top" secondItem="yjV-hk-rD8" secondAttribute="bottom" constant="16" id="vrm-06-RNX"/>
                            <constraint firstItem="DlJ-jr-TmW" firstAttribute="trailing" secondItem="wIZ-tY-nif" secondAttribute="trailing" id="yCS-uT-G9d"/>
                            <constraint firstItem="9um-ao-WIb" firstAttribute="leading" secondItem="DlJ-jr-TmW" secondAttribute="leading" id="zGt-Ix-sh3"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="btnAddComplain" destination="2qI-8z-Rw8" id="Ypj-MG-T9j"/>
                        <outlet property="btnApplyFilter" destination="VvD-Tz-U4q" id="nEZ-C5-CH8"/>
                        <outlet property="btnBack" destination="4yD-QG-s9E" id="kW0-GO-xuE"/>
                        <outlet property="btnFilter" destination="Ay3-nm-BxT" id="s4d-I7-18a"/>
                        <outlet property="btnOKDate" destination="zWB-Bl-bQP" id="GCs-um-AQy"/>
                        <outlet property="datePicker" destination="XAy-k0-4EK" id="mGN-dw-ufd"/>
                        <outlet property="lblAll" destination="D57-JW-7ie" id="COv-G1-hLJ"/>
                        <outlet property="lblFromDate" destination="q1o-jM-EGK" id="rRe-eT-RVI"/>
                        <outlet property="lblInProgress" destination="1nR-0l-fvi" id="rMi-t6-NPx"/>
                        <outlet property="lblPending" destination="KIr-eW-d1X" id="KhF-fb-V4q"/>
                        <outlet property="lblResolved" destination="ecA-QL-YjI" id="CfP-9U-17k"/>
                        <outlet property="lblToDate" destination="SI2-mk-G7H" id="dcU-49-UmL"/>
                        <outlet property="tableList" destination="9um-ao-WIb" id="bed-vu-Uub"/>
                        <outlet property="viewAll" destination="TrS-Wg-moV" id="duE-DP-cFx"/>
                        <outlet property="viewBgDate" destination="wIZ-tY-nif" id="oP0-t3-avD"/>
                        <outlet property="viewBgFilter" destination="ob9-gm-4gp" id="jfT-aT-zvd"/>
                        <outlet property="viewFromDate" destination="I0F-KB-M6L" id="dmR-Cw-KVL"/>
                        <outlet property="viewInProgress" destination="jPF-A9-Vv0" id="bs1-VL-PtE"/>
                        <outlet property="viewMainDate" destination="3Xr-iu-9YA" id="wWB-Z3-jvh"/>
                        <outlet property="viewMainFilter" destination="SWQ-Mt-y0U" id="uY0-mq-vve"/>
                        <outlet property="viewNoData" destination="55B-Kx-N4n" id="oMZ-cO-xdy"/>
                        <outlet property="viewPending" destination="Ktu-fV-mPq" id="Joh-0O-BtF"/>
                        <outlet property="viewResolved" destination="r5z-Pf-tmE" id="BLU-YP-hHO"/>
                        <outlet property="viewToDate" destination="00h-K7-dXy" id="kqh-k0-Khl"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="UF1-H8-3sP" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="718" y="70"/>
        </scene>
    </scenes>
    <resources>
        <image name="ic_back_arrow" width="64" height="64"/>
        <image name="ic_calendar" width="15" height="16"/>
        <image name="ic_cancel" width="64" height="64"/>
        <image name="ic_dropdown" width="64" height="64"/>
        <image name="ic_filter" width="500" height="501"/>
        <image name="ic_no_data" width="275" height="183"/>
        <namedColor name="Primary">
            <color red="0.016000000759959221" green="0.49399998784065247" blue="0.42699998617172241" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="PrimarySelection">
            <color red="0.59999999999999998" green="0.59999999999999998" blue="0.59999999999999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="PrimaryText">
            <color red="0.72500002384185791" green="0.72899997234344482" blue="0.7839999794960022" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="SecondaryGrayText">
            <color red="0.36470588235294116" green="0.36470588235294116" blue="0.36470588235294116" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="TextNotSelected">
            <color red="0.59999999999999998" green="0.59999999999999998" blue="0.59999999999999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="labelColor">
            <color white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>

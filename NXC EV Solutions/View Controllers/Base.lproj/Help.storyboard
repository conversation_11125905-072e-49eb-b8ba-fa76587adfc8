<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="19162" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="19144"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="collection view cell content view" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--HelpVC-->
        <scene sceneID="s0d-6b-0kx">
            <objects>
                <viewController storyboardIdentifier="HelpVC" id="Y6W-OH-hqX" customClass="HelpVC" customModule="NXC_EV_Solutions" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="5EZ-qb-Rvc">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="g99-nG-rtd">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="XcB-mJ-yKc">
                                        <rect key="frame" x="12" y="5" width="34" height="34"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="34" id="Kr6-GF-6WR"/>
                                            <constraint firstAttribute="height" constant="34" id="MU0-X1-k1Y"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="8" minY="6" maxX="8" maxY="6"/>
                                        <state key="normal" image="ic_back_arrow"/>
                                        <connections>
                                            <action selector="backButtonTapped:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="3kX-fr-iRn"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Help" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="SU7-u4-EDp">
                                        <rect key="frame" x="169" y="12" width="37" height="20.5"/>
                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                        <color key="textColor" red="0.070588235289999995" green="0.070588235289999995" blue="0.070588235289999995" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="SU7-u4-EDp" firstAttribute="centerY" secondItem="g99-nG-rtd" secondAttribute="centerY" id="5Gb-n6-0fG"/>
                                    <constraint firstItem="XcB-mJ-yKc" firstAttribute="centerY" secondItem="g99-nG-rtd" secondAttribute="centerY" id="Iwv-90-WTG"/>
                                    <constraint firstAttribute="height" constant="44" id="cDb-fF-RyW"/>
                                    <constraint firstItem="SU7-u4-EDp" firstAttribute="centerX" secondItem="g99-nG-rtd" secondAttribute="centerX" id="p7Q-tN-Yg5"/>
                                    <constraint firstItem="XcB-mJ-yKc" firstAttribute="leading" secondItem="g99-nG-rtd" secondAttribute="leading" constant="12" id="qOy-A2-wi8"/>
                                </constraints>
                            </view>
                            <wkWebView contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="osF-fa-sx0">
                                <rect key="frame" x="0.0" y="44" width="375" height="623"/>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <wkWebViewConfiguration key="configuration">
                                    <audiovisualMediaTypes key="mediaTypesRequiringUserActionForPlayback" none="YES"/>
                                    <wkPreferences key="preferences"/>
                                </wkWebViewConfiguration>
                            </wkWebView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="vDu-zF-Fre"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="g99-nG-rtd" firstAttribute="leading" secondItem="vDu-zF-Fre" secondAttribute="leading" id="3Uw-Ph-HY8"/>
                            <constraint firstItem="vDu-zF-Fre" firstAttribute="bottom" secondItem="osF-fa-sx0" secondAttribute="bottom" id="UBM-H9-QmJ"/>
                            <constraint firstItem="vDu-zF-Fre" firstAttribute="trailing" secondItem="osF-fa-sx0" secondAttribute="trailing" id="YEg-a2-5dn"/>
                            <constraint firstItem="osF-fa-sx0" firstAttribute="leading" secondItem="vDu-zF-Fre" secondAttribute="leading" id="fQl-6M-uWY"/>
                            <constraint firstItem="vDu-zF-Fre" firstAttribute="trailing" secondItem="g99-nG-rtd" secondAttribute="trailing" id="fdZ-lR-0ic"/>
                            <constraint firstItem="g99-nG-rtd" firstAttribute="top" secondItem="vDu-zF-Fre" secondAttribute="top" id="wrm-HV-edq"/>
                            <constraint firstItem="osF-fa-sx0" firstAttribute="top" secondItem="g99-nG-rtd" secondAttribute="bottom" id="zSt-dO-uYl"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="backButton" destination="XcB-mJ-yKc" id="c6s-hw-TO9"/>
                        <outlet property="webDetails" destination="osF-fa-sx0" id="mGU-RH-fOL"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Ief-a0-LHa" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="122.40000000000001" y="69.715142428785612"/>
        </scene>
        <!--About UsVC-->
        <scene sceneID="LWN-2o-sdw">
            <objects>
                <viewController storyboardIdentifier="AboutUsVC" id="K3e-ha-sat" customClass="AboutUsVC" customModule="NXC_EV_Solutions" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="1Qf-Ra-yNQ">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="vGG-eM-mxX">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="S0U-fi-0rK">
                                        <rect key="frame" x="12" y="5" width="34" height="34"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="34" id="Kdk-V6-6Ft"/>
                                            <constraint firstAttribute="height" constant="34" id="TUC-pI-RbN"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="8" minY="6" maxX="8" maxY="6"/>
                                        <state key="normal" image="ic_back_arrow"/>
                                        <connections>
                                            <action selector="backButtonTapped:" destination="K3e-ha-sat" eventType="touchUpInside" id="YVZ-xF-qRh"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="About Us" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="rE1-F8-Zd1">
                                        <rect key="frame" x="150.5" y="12" width="74" height="20.5"/>
                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                        <color key="textColor" red="0.070588235289999995" green="0.070588235289999995" blue="0.070588235289999995" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="rE1-F8-Zd1" firstAttribute="centerX" secondItem="vGG-eM-mxX" secondAttribute="centerX" id="PfG-O1-3DS"/>
                                    <constraint firstItem="S0U-fi-0rK" firstAttribute="centerY" secondItem="vGG-eM-mxX" secondAttribute="centerY" id="Zqz-b2-3oJ"/>
                                    <constraint firstAttribute="height" constant="44" id="ept-Wz-mNd"/>
                                    <constraint firstItem="S0U-fi-0rK" firstAttribute="leading" secondItem="vGG-eM-mxX" secondAttribute="leading" constant="12" id="gcP-Uv-5g0"/>
                                    <constraint firstItem="rE1-F8-Zd1" firstAttribute="centerY" secondItem="vGG-eM-mxX" secondAttribute="centerY" id="jwS-LJ-4EE"/>
                                </constraints>
                            </view>
                            <wkWebView contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="n3A-p9-N9l">
                                <rect key="frame" x="0.0" y="44" width="375" height="623"/>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <wkWebViewConfiguration key="configuration">
                                    <audiovisualMediaTypes key="mediaTypesRequiringUserActionForPlayback" none="YES"/>
                                    <wkPreferences key="preferences"/>
                                </wkWebViewConfiguration>
                            </wkWebView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="MWm-Bb-EIt"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="n3A-p9-N9l" firstAttribute="leading" secondItem="MWm-Bb-EIt" secondAttribute="leading" id="KLV-hs-EpR"/>
                            <constraint firstItem="vGG-eM-mxX" firstAttribute="top" secondItem="MWm-Bb-EIt" secondAttribute="top" id="ZY0-LJ-NIK"/>
                            <constraint firstItem="MWm-Bb-EIt" firstAttribute="trailing" secondItem="vGG-eM-mxX" secondAttribute="trailing" id="Zec-ch-HUL"/>
                            <constraint firstItem="MWm-Bb-EIt" firstAttribute="bottom" secondItem="n3A-p9-N9l" secondAttribute="bottom" id="eQe-KQ-qYs"/>
                            <constraint firstItem="vGG-eM-mxX" firstAttribute="leading" secondItem="MWm-Bb-EIt" secondAttribute="leading" id="jz0-ER-hRz"/>
                            <constraint firstItem="n3A-p9-N9l" firstAttribute="trailing" secondItem="MWm-Bb-EIt" secondAttribute="trailing" id="m0S-zn-ekW"/>
                            <constraint firstItem="n3A-p9-N9l" firstAttribute="top" secondItem="vGG-eM-mxX" secondAttribute="bottom" id="oeO-I9-sIn"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="backButton" destination="S0U-fi-0rK" id="CHn-wt-e7z"/>
                        <outlet property="webDetails" destination="n3A-p9-N9l" id="8oB-pm-02a"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="f0d-lI-PYA" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="868" y="69.715142428785612"/>
        </scene>
        <!--Game ListVC-->
        <scene sceneID="KND-6z-qOl">
            <objects>
                <viewController storyboardIdentifier="GameListVC" id="4MG-F4-4qJ" customClass="GameListVC" customModule="NXC_EV_Solutions" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="cPi-4b-iJU">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="5Ia-Re-Acl">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="BFY-lE-R6s">
                                        <rect key="frame" x="12" y="5" width="34" height="34"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="34" id="VXv-3X-fQ4"/>
                                            <constraint firstAttribute="height" constant="34" id="ruV-Yr-xTi"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="8" minY="6" maxX="8" maxY="6"/>
                                        <state key="normal" image="ic_back_arrow"/>
                                        <connections>
                                            <action selector="backTapped:" destination="4MG-F4-4qJ" eventType="touchUpInside" id="Eeg-ss-s1f"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Games" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="LKg-xJ-eQy">
                                        <rect key="frame" x="160" y="12" width="55.5" height="20.5"/>
                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                        <color key="textColor" red="0.070588235289999995" green="0.070588235289999995" blue="0.070588235289999995" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="LKg-xJ-eQy" firstAttribute="centerX" secondItem="5Ia-Re-Acl" secondAttribute="centerX" id="0R4-ow-QvU"/>
                                    <constraint firstItem="BFY-lE-R6s" firstAttribute="centerY" secondItem="5Ia-Re-Acl" secondAttribute="centerY" id="L4H-Ll-0UU"/>
                                    <constraint firstItem="BFY-lE-R6s" firstAttribute="leading" secondItem="5Ia-Re-Acl" secondAttribute="leading" constant="12" id="Nn9-DC-yyh"/>
                                    <constraint firstAttribute="height" constant="44" id="THK-dj-sKv"/>
                                    <constraint firstItem="LKg-xJ-eQy" firstAttribute="centerY" secondItem="5Ia-Re-Acl" secondAttribute="centerY" id="tEf-3k-OvP"/>
                                </constraints>
                            </view>
                            <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" dataMode="prototypes" translatesAutoresizingMaskIntoConstraints="NO" id="rlX-NG-iVT">
                                <rect key="frame" x="8" y="56" width="359" height="603"/>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <collectionViewFlowLayout key="collectionViewLayout" automaticEstimatedItemSize="YES" minimumLineSpacing="8" minimumInteritemSpacing="8" id="cYh-JS-g0p">
                                    <size key="itemSize" width="150" height="128"/>
                                    <size key="headerReferenceSize" width="0.0" height="0.0"/>
                                    <size key="footerReferenceSize" width="0.0" height="0.0"/>
                                    <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                                </collectionViewFlowLayout>
                                <cells>
                                    <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" reuseIdentifier="cell" id="5Nc-pu-aY0" customClass="GameListCell" customModule="NXC_EV_Solutions" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="0.0" width="150" height="128"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <collectionViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" id="Pw6-tF-cbV">
                                            <rect key="frame" x="0.0" y="0.0" width="150" height="128"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="gsH-Im-ggc">
                                                    <rect key="frame" x="0.0" y="0.0" width="150" height="128"/>
                                                    <subviews>
                                                        <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="Vck-qM-leO">
                                                            <rect key="frame" x="-20" y="-20" width="190" height="168"/>
                                                        </imageView>
                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="Fw7-bV-poz">
                                                            <rect key="frame" x="8" y="8" width="75" height="75"/>
                                                            <constraints>
                                                                <constraint firstAttribute="width" constant="75" id="OoM-pv-0sY"/>
                                                                <constraint firstAttribute="height" constant="75" id="jtO-ju-Tw4"/>
                                                            </constraints>
                                                        </imageView>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Game Name" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="QHY-w0-TTk">
                                                            <rect key="frame" x="16" y="98" width="88.5" height="18"/>
                                                            <fontDescription key="fontDescription" type="boldSystem" pointSize="15"/>
                                                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_right_arrow-1" translatesAutoresizingMaskIntoConstraints="NO" id="IFF-sL-4Yn">
                                                            <rect key="frame" x="116" y="98" width="18" height="18"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="18" id="TD0-8a-x9L"/>
                                                                <constraint firstAttribute="width" constant="18" id="pYG-ck-ZZ6"/>
                                                            </constraints>
                                                        </imageView>
                                                    </subviews>
                                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <constraints>
                                                        <constraint firstAttribute="trailing" secondItem="Vck-qM-leO" secondAttribute="trailing" constant="-20" id="1k5-Cr-gzm"/>
                                                        <constraint firstAttribute="bottom" secondItem="QHY-w0-TTk" secondAttribute="bottom" constant="12" id="E1Q-mK-fo3"/>
                                                        <constraint firstItem="Fw7-bV-poz" firstAttribute="top" secondItem="gsH-Im-ggc" secondAttribute="top" constant="8" id="EcT-LK-8Tj"/>
                                                        <constraint firstItem="Vck-qM-leO" firstAttribute="leading" secondItem="gsH-Im-ggc" secondAttribute="leading" constant="-20" id="OjY-F6-v6b"/>
                                                        <constraint firstAttribute="trailing" secondItem="IFF-sL-4Yn" secondAttribute="trailing" constant="16" id="R10-US-h3T"/>
                                                        <constraint firstItem="Vck-qM-leO" firstAttribute="top" secondItem="gsH-Im-ggc" secondAttribute="top" constant="-20" id="YIg-64-i2E"/>
                                                        <constraint firstItem="QHY-w0-TTk" firstAttribute="leading" secondItem="gsH-Im-ggc" secondAttribute="leading" constant="16" id="olG-cx-krC"/>
                                                        <constraint firstAttribute="bottom" secondItem="Vck-qM-leO" secondAttribute="bottom" constant="-20" id="qVM-Gv-FvC"/>
                                                        <constraint firstAttribute="bottom" secondItem="IFF-sL-4Yn" secondAttribute="bottom" constant="12" id="yC1-sK-aMK"/>
                                                        <constraint firstItem="Fw7-bV-poz" firstAttribute="leading" secondItem="gsH-Im-ggc" secondAttribute="leading" constant="8" id="ygh-8u-OEB"/>
                                                    </constraints>
                                                </view>
                                            </subviews>
                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstAttribute="trailing" secondItem="gsH-Im-ggc" secondAttribute="trailing" id="K8Y-MG-cuT"/>
                                                <constraint firstAttribute="bottom" secondItem="gsH-Im-ggc" secondAttribute="bottom" id="Mi7-ww-vld"/>
                                                <constraint firstItem="gsH-Im-ggc" firstAttribute="top" secondItem="Pw6-tF-cbV" secondAttribute="top" id="WUo-cS-yu7"/>
                                                <constraint firstItem="gsH-Im-ggc" firstAttribute="leading" secondItem="Pw6-tF-cbV" secondAttribute="leading" id="qpm-BH-LfT"/>
                                            </constraints>
                                        </collectionViewCellContentView>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <size key="customSize" width="150" height="128"/>
                                        <connections>
                                            <outlet property="imgBg" destination="Vck-qM-leO" id="goC-OP-LM0"/>
                                            <outlet property="imgGameName" destination="Fw7-bV-poz" id="VIa-Pu-tpW"/>
                                            <outlet property="lblGameName" destination="QHY-w0-TTk" id="fmO-Mt-8ca"/>
                                            <outlet property="viewMain" destination="gsH-Im-ggc" id="kNY-8o-uag"/>
                                        </connections>
                                    </collectionViewCell>
                                </cells>
                            </collectionView>
                            <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="GL5-Wd-Zcx">
                                <rect key="frame" x="8" y="56" width="359" height="603"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="No Games Found" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="GL5-Wd-Zcy">
                                        <rect key="frame" x="20" y="291" width="319" height="21"/>
                                        <fontDescription key="fontDescription" type="system" weight="medium" pointSize="17"/>
                                        <color key="textColor" red="0.070588235289999995" green="0.070588235289999995" blue="0.070588235289999995" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="GL5-Wd-Zcy" firstAttribute="leading" secondItem="GL5-Wd-Zcx" secondAttribute="leading" constant="20" id="GL5-Wd-Zc1"/>
                                    <constraint firstAttribute="trailing" secondItem="GL5-Wd-Zcy" secondAttribute="trailing" constant="20" id="GL5-Wd-Zc2"/>
                                    <constraint firstItem="GL5-Wd-Zcy" firstAttribute="centerY" secondItem="GL5-Wd-Zcx" secondAttribute="centerY" id="GL5-Wd-Zc3"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="a81-Si-6uD"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="5Ia-Re-Acl" firstAttribute="top" secondItem="a81-Si-6uD" secondAttribute="top" id="6ES-oW-Spk"/>
                            <constraint firstItem="a81-Si-6uD" firstAttribute="trailing" secondItem="5Ia-Re-Acl" secondAttribute="trailing" id="8iI-Nb-zkt"/>
                            <constraint firstItem="a81-Si-6uD" firstAttribute="bottom" secondItem="rlX-NG-iVT" secondAttribute="bottom" constant="8" id="9Ib-iy-hqO"/>
                            <constraint firstItem="5Ia-Re-Acl" firstAttribute="leading" secondItem="a81-Si-6uD" secondAttribute="leading" id="G8G-0h-6ak"/>
                            <constraint firstItem="rlX-NG-iVT" firstAttribute="leading" secondItem="a81-Si-6uD" secondAttribute="leading" constant="8" id="GSY-Kj-9ue"/>
                            <constraint firstItem="a81-Si-6uD" firstAttribute="trailing" secondItem="rlX-NG-iVT" secondAttribute="trailing" constant="8" id="NjQ-TZ-X34"/>
                            <constraint firstItem="rlX-NG-iVT" firstAttribute="top" secondItem="5Ia-Re-Acl" secondAttribute="bottom" constant="12" id="sN4-y8-fdg"/>
                            <constraint firstItem="GL5-Wd-Zcx" firstAttribute="top" secondItem="5Ia-Re-Acl" secondAttribute="bottom" constant="12" id="GL5-Wd-Zc4"/>
                            <constraint firstItem="GL5-Wd-Zcx" firstAttribute="leading" secondItem="a81-Si-6uD" secondAttribute="leading" constant="8" id="GL5-Wd-Zc5"/>
                            <constraint firstItem="a81-Si-6uD" firstAttribute="trailing" secondItem="GL5-Wd-Zcx" secondAttribute="trailing" constant="8" id="GL5-Wd-Zc6"/>
                            <constraint firstItem="a81-Si-6uD" firstAttribute="bottom" secondItem="GL5-Wd-Zcx" secondAttribute="bottom" constant="8" id="GL5-Wd-Zc7"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="btnBack" destination="BFY-lE-R6s" id="2Ge-mW-9kk"/>
                        <outlet property="collectionList" destination="rlX-NG-iVT" id="DFt-ag-l18"/>
                        <outlet property="viewNoData" destination="GL5-Wd-Zcx" id="GL5-Wd-Zc8"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="eaG-Ug-y7P" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1542" y="70"/>
        </scene>
        <!--Game DetailVC-->
        <scene sceneID="rnO-Iu-E96">
            <objects>
                <viewController storyboardIdentifier="GameDetailVC" id="hHM-W9-XuK" customClass="GameDetailVC" customModule="NXC_EV_Solutions" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="REW-je-T4v">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="V8b-dY-IUK">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="EDF-FT-TZc">
                                        <rect key="frame" x="12" y="5" width="34" height="34"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="34" id="9Tb-AX-PXe"/>
                                            <constraint firstAttribute="height" constant="34" id="PBK-it-Hmo"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="8" minY="6" maxX="8" maxY="6"/>
                                        <state key="normal" image="ic_back_arrow"/>
                                        <connections>
                                            <action selector="backTapped:" destination="hHM-W9-XuK" eventType="touchUpInside" id="osL-Re-ovm"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Games" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="uh0-rQ-beB">
                                        <rect key="frame" x="160" y="12" width="55.5" height="20.5"/>
                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                        <color key="textColor" red="0.070588235289999995" green="0.070588235289999995" blue="0.070588235289999995" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="uh0-rQ-beB" firstAttribute="centerX" secondItem="V8b-dY-IUK" secondAttribute="centerX" id="5NR-C8-YhO"/>
                                    <constraint firstItem="EDF-FT-TZc" firstAttribute="leading" secondItem="V8b-dY-IUK" secondAttribute="leading" constant="12" id="OI3-Ns-t0p"/>
                                    <constraint firstAttribute="height" constant="44" id="YIr-uz-QzA"/>
                                    <constraint firstItem="EDF-FT-TZc" firstAttribute="centerY" secondItem="V8b-dY-IUK" secondAttribute="centerY" id="hx4-eo-b9n"/>
                                    <constraint firstItem="uh0-rQ-beB" firstAttribute="centerY" secondItem="V8b-dY-IUK" secondAttribute="centerY" id="mOL-ep-YBu"/>
                                </constraints>
                            </view>
                            <wkWebView contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="kK7-dl-y6i">
                                <rect key="frame" x="0.0" y="44" width="375" height="623"/>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <wkWebViewConfiguration key="configuration">
                                    <audiovisualMediaTypes key="mediaTypesRequiringUserActionForPlayback" none="YES"/>
                                    <wkPreferences key="preferences"/>
                                </wkWebViewConfiguration>
                            </wkWebView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="etH-fe-bdE"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="etH-fe-bdE" firstAttribute="trailing" secondItem="V8b-dY-IUK" secondAttribute="trailing" id="8tj-fB-PPw"/>
                            <constraint firstItem="kK7-dl-y6i" firstAttribute="top" secondItem="V8b-dY-IUK" secondAttribute="bottom" id="Jpk-0q-tT1"/>
                            <constraint firstItem="V8b-dY-IUK" firstAttribute="leading" secondItem="etH-fe-bdE" secondAttribute="leading" id="atd-uW-mmG"/>
                            <constraint firstItem="etH-fe-bdE" firstAttribute="bottom" secondItem="kK7-dl-y6i" secondAttribute="bottom" id="b6P-9W-5oy"/>
                            <constraint firstItem="kK7-dl-y6i" firstAttribute="leading" secondItem="etH-fe-bdE" secondAttribute="leading" id="fv5-Tb-crT"/>
                            <constraint firstItem="V8b-dY-IUK" firstAttribute="top" secondItem="etH-fe-bdE" secondAttribute="top" id="wIX-Aa-cp2"/>
                            <constraint firstItem="kK7-dl-y6i" firstAttribute="trailing" secondItem="etH-fe-bdE" secondAttribute="trailing" id="wnk-bN-kwE"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="btnBack" destination="EDF-FT-TZc" id="gXe-Pj-ckK"/>
                        <outlet property="lblTitle" destination="uh0-rQ-beB" id="plY-Hz-dRj"/>
                        <outlet property="webDetails" destination="kK7-dl-y6i" id="pLa-U6-F2L"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="vqH-l6-TSK" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="2199" y="70"/>
        </scene>
        <!--TermsVC-->
        <scene sceneID="MJb-mL-6qU">
            <objects>
                <viewController storyboardIdentifier="TermsVC" id="KuM-EG-OcU" customClass="TermsVC" customModule="NXC_EV_Solutions" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="GiE-dQ-j05">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="RFs-sZ-ghl">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="sBr-Hd-yDV">
                                        <rect key="frame" x="12" y="5" width="34" height="34"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="34" id="xfs-8u-ycQ"/>
                                            <constraint firstAttribute="width" constant="34" id="yzR-iO-Lt8"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="8" minY="6" maxX="8" maxY="6"/>
                                        <state key="normal" image="ic_back_arrow"/>
                                        <connections>
                                            <action selector="backTapped:" destination="KuM-EG-OcU" eventType="touchUpInside" id="AjA-aA-Bju"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Terms and Conditions" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="kZF-Bk-eW4">
                                        <rect key="frame" x="101" y="12" width="173.5" height="20.5"/>
                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                        <color key="textColor" red="0.070588235289999995" green="0.070588235289999995" blue="0.070588235289999995" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="sBr-Hd-yDV" firstAttribute="centerY" secondItem="RFs-sZ-ghl" secondAttribute="centerY" id="4YN-pg-CDj"/>
                                    <constraint firstItem="kZF-Bk-eW4" firstAttribute="centerX" secondItem="RFs-sZ-ghl" secondAttribute="centerX" id="G1v-MP-SiK"/>
                                    <constraint firstItem="sBr-Hd-yDV" firstAttribute="leading" secondItem="RFs-sZ-ghl" secondAttribute="leading" constant="12" id="NuI-Cl-tGV"/>
                                    <constraint firstAttribute="height" constant="44" id="SUa-xO-sIu"/>
                                    <constraint firstItem="kZF-Bk-eW4" firstAttribute="centerY" secondItem="RFs-sZ-ghl" secondAttribute="centerY" id="hXr-9t-bhM"/>
                                </constraints>
                            </view>
                            <wkWebView contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="9Gm-Mc-jWQ">
                                <rect key="frame" x="0.0" y="44" width="375" height="623"/>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <wkWebViewConfiguration key="configuration">
                                    <audiovisualMediaTypes key="mediaTypesRequiringUserActionForPlayback" none="YES"/>
                                    <wkPreferences key="preferences"/>
                                </wkWebViewConfiguration>
                            </wkWebView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="6k9-Wj-VVv"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="6k9-Wj-VVv" firstAttribute="bottom" secondItem="9Gm-Mc-jWQ" secondAttribute="bottom" id="HLr-WO-DVY"/>
                            <constraint firstItem="9Gm-Mc-jWQ" firstAttribute="trailing" secondItem="6k9-Wj-VVv" secondAttribute="trailing" id="KRb-8d-kgG"/>
                            <constraint firstItem="9Gm-Mc-jWQ" firstAttribute="leading" secondItem="6k9-Wj-VVv" secondAttribute="leading" id="Ufi-s1-6M9"/>
                            <constraint firstItem="RFs-sZ-ghl" firstAttribute="top" secondItem="6k9-Wj-VVv" secondAttribute="top" id="Ygq-HM-BOY"/>
                            <constraint firstItem="6k9-Wj-VVv" firstAttribute="trailing" secondItem="RFs-sZ-ghl" secondAttribute="trailing" id="Z9y-Je-dWa"/>
                            <constraint firstItem="RFs-sZ-ghl" firstAttribute="leading" secondItem="6k9-Wj-VVv" secondAttribute="leading" id="aaM-be-laM"/>
                            <constraint firstItem="9Gm-Mc-jWQ" firstAttribute="top" secondItem="RFs-sZ-ghl" secondAttribute="bottom" id="lrD-SN-WRw"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="btnBack" destination="sBr-Hd-yDV" id="X5o-Fb-JhD"/>
                        <outlet property="webDetails" destination="9Gm-Mc-jWQ" id="OrM-Mt-u0R"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="dle-YY-Pen" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="2849" y="70"/>
        </scene>
        <!--NewsVC-->
        <scene sceneID="kv6-CC-Lrr">
            <objects>
                <viewController storyboardIdentifier="NewsVC" id="AOn-gU-AZL" customClass="NewsVC" customModule="NXC_EV_Solutions" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="lm8-8N-kKV">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="EO5-tk-rPM">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="2Ao-d2-bCB">
                                        <rect key="frame" x="12" y="5" width="34" height="34"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="34" id="4Co-uK-gox"/>
                                            <constraint firstAttribute="height" constant="34" id="ivD-o2-nxn"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="8" minY="6" maxX="8" maxY="6"/>
                                        <state key="normal" image="ic_back_arrow"/>
                                        <connections>
                                            <action selector="backTapped:" destination="AOn-gU-AZL" eventType="touchUpInside" id="3Dg-9I-Tah"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="News" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="2oJ-UJ-eUd">
                                        <rect key="frame" x="165.5" y="12" width="44.5" height="20.5"/>
                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                        <color key="textColor" red="0.070588235289999995" green="0.070588235289999995" blue="0.070588235289999995" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="2Ao-d2-bCB" firstAttribute="centerY" secondItem="EO5-tk-rPM" secondAttribute="centerY" id="Cai-Gx-2ST"/>
                                    <constraint firstItem="2Ao-d2-bCB" firstAttribute="leading" secondItem="EO5-tk-rPM" secondAttribute="leading" constant="12" id="FVM-DS-Bsj"/>
                                    <constraint firstItem="2oJ-UJ-eUd" firstAttribute="centerX" secondItem="EO5-tk-rPM" secondAttribute="centerX" id="K9E-90-3KK"/>
                                    <constraint firstItem="2oJ-UJ-eUd" firstAttribute="centerY" secondItem="EO5-tk-rPM" secondAttribute="centerY" id="obT-PF-lZV"/>
                                    <constraint firstAttribute="height" constant="44" id="vBI-5t-HjN"/>
                                </constraints>
                            </view>
                            <wkWebView contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="jbK-FK-j4u">
                                <rect key="frame" x="0.0" y="44" width="375" height="623"/>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <wkWebViewConfiguration key="configuration">
                                    <audiovisualMediaTypes key="mediaTypesRequiringUserActionForPlayback" none="YES"/>
                                    <wkPreferences key="preferences"/>
                                </wkWebViewConfiguration>
                            </wkWebView>
                            <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Nh5-Wd-Zcx">
                                <rect key="frame" x="0.0" y="44" width="375" height="623"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="No News Found" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Nh5-Wd-Zcy">
                                        <rect key="frame" x="20" y="301" width="335" height="21"/>
                                        <fontDescription key="fontDescription" type="system" weight="medium" pointSize="17"/>
                                        <color key="textColor" red="0.070588235289999995" green="0.070588235289999995" blue="0.070588235289999995" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="Nh5-Wd-Zcy" firstAttribute="leading" secondItem="Nh5-Wd-Zcx" secondAttribute="leading" constant="20" id="Nh5-Wd-Zc1"/>
                                    <constraint firstAttribute="trailing" secondItem="Nh5-Wd-Zcy" secondAttribute="trailing" constant="20" id="Nh5-Wd-Zc2"/>
                                    <constraint firstItem="Nh5-Wd-Zcy" firstAttribute="centerY" secondItem="Nh5-Wd-Zcx" secondAttribute="centerY" id="Nh5-Wd-Zc3"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="ISq-NS-fPP"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="ISq-NS-fPP" firstAttribute="trailing" secondItem="EO5-tk-rPM" secondAttribute="trailing" id="7Vc-hN-MKG"/>
                            <constraint firstItem="jbK-FK-j4u" firstAttribute="top" secondItem="EO5-tk-rPM" secondAttribute="bottom" id="MRD-am-8lE"/>
                            <constraint firstItem="jbK-FK-j4u" firstAttribute="leading" secondItem="ISq-NS-fPP" secondAttribute="leading" id="e3K-el-SCI"/>
                            <constraint firstItem="EO5-tk-rPM" firstAttribute="leading" secondItem="ISq-NS-fPP" secondAttribute="leading" id="ixE-GP-uXw"/>
                            <constraint firstItem="ISq-NS-fPP" firstAttribute="bottom" secondItem="jbK-FK-j4u" secondAttribute="bottom" id="qif-bn-pMN"/>
                            <constraint firstItem="EO5-tk-rPM" firstAttribute="top" secondItem="ISq-NS-fPP" secondAttribute="top" id="sFi-6Q-Rm1"/>
                            <constraint firstItem="jbK-FK-j4u" firstAttribute="trailing" secondItem="ISq-NS-fPP" secondAttribute="trailing" id="tTe-0z-1Mn"/>
                            <constraint firstItem="Nh5-Wd-Zcx" firstAttribute="top" secondItem="EO5-tk-rPM" secondAttribute="bottom" id="Nh5-Wd-Zc4"/>
                            <constraint firstItem="Nh5-Wd-Zcx" firstAttribute="leading" secondItem="ISq-NS-fPP" secondAttribute="leading" id="Nh5-Wd-Zc5"/>
                            <constraint firstItem="Nh5-Wd-Zcx" firstAttribute="trailing" secondItem="ISq-NS-fPP" secondAttribute="trailing" id="Nh5-Wd-Zc6"/>
                            <constraint firstItem="ISq-NS-fPP" firstAttribute="bottom" secondItem="Nh5-Wd-Zcx" secondAttribute="bottom" id="Nh5-Wd-Zc7"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="btnBack" destination="2Ao-d2-bCB" id="s4j-ip-tbR"/>
                        <outlet property="viewNoData" destination="Nh5-Wd-Zcx" id="Nh5-Wd-Zc8"/>
                        <outlet property="webDetails" destination="jbK-FK-j4u" id="1zd-1B-ipg"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="6Ma-1N-x3T" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="3516" y="70"/>
        </scene>
        <!--Buy ChargerVC-->
        <scene sceneID="bbl-Sb-Z9d">
            <objects>
                <viewController storyboardIdentifier="BuyChargerVC" id="Kyj-HY-1Mr" customClass="BuyChargerVC" customModule="NXC_EV_Solutions" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="uVf-uf-0dG">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="XIu-JU-X9E">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="B0d-To-fSs">
                                        <rect key="frame" x="12" y="5" width="34" height="34"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="34" id="JLy-9B-PtU"/>
                                            <constraint firstAttribute="width" constant="34" id="twV-li-CSk"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="8" minY="6" maxX="8" maxY="6"/>
                                        <state key="normal" image="ic_back_arrow"/>
                                        <connections>
                                            <action selector="backTapped:" destination="Kyj-HY-1Mr" eventType="touchUpInside" id="Pmg-1a-5ZY"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Buy Charger" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="WWr-8W-vdW">
                                        <rect key="frame" x="138" y="12" width="99" height="20.5"/>
                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                        <color key="textColor" red="0.070588235289999995" green="0.070588235289999995" blue="0.070588235289999995" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="B0d-To-fSs" firstAttribute="leading" secondItem="XIu-JU-X9E" secondAttribute="leading" constant="12" id="AVJ-1x-h34"/>
                                    <constraint firstItem="WWr-8W-vdW" firstAttribute="centerY" secondItem="XIu-JU-X9E" secondAttribute="centerY" id="JBb-l0-lTl"/>
                                    <constraint firstItem="WWr-8W-vdW" firstAttribute="centerX" secondItem="XIu-JU-X9E" secondAttribute="centerX" id="dUg-vI-NiI"/>
                                    <constraint firstItem="B0d-To-fSs" firstAttribute="centerY" secondItem="XIu-JU-X9E" secondAttribute="centerY" id="fCa-hP-2yZ"/>
                                    <constraint firstAttribute="height" constant="44" id="ryK-ds-7F0"/>
                                </constraints>
                            </view>
                            <wkWebView contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Lgv-S9-rZG">
                                <rect key="frame" x="0.0" y="44" width="375" height="623"/>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <wkWebViewConfiguration key="configuration">
                                    <audiovisualMediaTypes key="mediaTypesRequiringUserActionForPlayback" none="YES"/>
                                    <wkPreferences key="preferences"/>
                                </wkWebViewConfiguration>
                            </wkWebView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="9vm-6Y-g3f"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="XIu-JU-X9E" firstAttribute="leading" secondItem="9vm-6Y-g3f" secondAttribute="leading" id="7Q4-nh-Fc4"/>
                            <constraint firstItem="Lgv-S9-rZG" firstAttribute="trailing" secondItem="9vm-6Y-g3f" secondAttribute="trailing" id="HLU-8P-wac"/>
                            <constraint firstItem="9vm-6Y-g3f" firstAttribute="trailing" secondItem="XIu-JU-X9E" secondAttribute="trailing" id="Lw1-Vs-ZEd"/>
                            <constraint firstItem="XIu-JU-X9E" firstAttribute="top" secondItem="9vm-6Y-g3f" secondAttribute="top" id="SuO-5P-mUw"/>
                            <constraint firstItem="Lgv-S9-rZG" firstAttribute="top" secondItem="XIu-JU-X9E" secondAttribute="bottom" id="XOD-XU-Eml"/>
                            <constraint firstItem="Lgv-S9-rZG" firstAttribute="leading" secondItem="9vm-6Y-g3f" secondAttribute="leading" id="i3e-Fb-0er"/>
                            <constraint firstItem="9vm-6Y-g3f" firstAttribute="bottom" secondItem="Lgv-S9-rZG" secondAttribute="bottom" id="lUY-nk-eZP"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="btnBack" destination="B0d-To-fSs" id="Y13-rq-Vrj"/>
                        <outlet property="webDetails" destination="Lgv-S9-rZG" id="wtE-eT-Cys"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="B0d-PL-0Mx" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="4188" y="70"/>
        </scene>
    </scenes>
    <resources>
        <image name="ic_back_arrow" width="64" height="64"/>
        <image name="ic_right_arrow-1" width="22" height="15"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>

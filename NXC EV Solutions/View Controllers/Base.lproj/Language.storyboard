<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="19162" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="19144"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--LanguageVC-->
        <scene sceneID="s0d-6b-0kx">
            <objects>
                <viewController storyboardIdentifier="LanguageVC" id="Y6W-OH-hqX" customClass="LanguageVC" customModule="NXC_EV_Solutions" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="5EZ-qb-Rvc">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="gzE-DJ-PrE">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="0Ap-6B-X9O">
                                        <rect key="frame" x="12" y="5" width="34" height="34"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="34" id="H9b-tO-EUP"/>
                                            <constraint firstAttribute="width" constant="34" id="ly6-Da-6hA"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="8" minY="6" maxX="8" maxY="6"/>
                                        <state key="normal" image="ic_back_arrow"/>
                                        <connections>
                                            <action selector="backAction:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="1Yg-iE-KZG"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Language" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="r3n-1A-mAS">
                                        <rect key="frame" x="148.5" y="12" width="78.5" height="20.5"/>
                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                        <color key="textColor" red="0.070588235289999995" green="0.070588235289999995" blue="0.070588235289999995" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="r3n-1A-mAS" firstAttribute="centerX" secondItem="gzE-DJ-PrE" secondAttribute="centerX" id="3Gk-0a-TMT"/>
                                    <constraint firstItem="r3n-1A-mAS" firstAttribute="centerY" secondItem="gzE-DJ-PrE" secondAttribute="centerY" id="CW9-aL-jST"/>
                                    <constraint firstAttribute="height" constant="44" id="J5k-FR-Gui"/>
                                    <constraint firstItem="0Ap-6B-X9O" firstAttribute="leading" secondItem="gzE-DJ-PrE" secondAttribute="leading" constant="12" id="hqt-vr-IgP"/>
                                    <constraint firstItem="0Ap-6B-X9O" firstAttribute="centerY" secondItem="gzE-DJ-PrE" secondAttribute="centerY" id="vZb-BK-o14"/>
                                </constraints>
                            </view>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="kEL-IR-H6L">
                                <rect key="frame" x="19" y="60" width="337.5" height="182"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="YNK-Jp-ETw">
                                        <rect key="frame" x="0.0" y="0.0" width="337.5" height="50"/>
                                        <subviews>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="zAo-Sj-TMg">
                                                <rect key="frame" x="0.0" y="0.0" width="337.5" height="50"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="English" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="QPy-ek-CnR">
                                                        <rect key="frame" x="6" y="8" width="49.5" height="34"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="34" id="KWS-Rz-xgj"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_uncheck" highlightedImage="ic_checked" translatesAutoresizingMaskIntoConstraints="NO" id="iKP-Dn-Jma">
                                                        <rect key="frame" x="305.5" y="13" width="24" height="24"/>
                                                        <color key="tintColor" name="PrimaryTextColorLight"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="24" id="WdE-Tv-fme"/>
                                                            <constraint firstAttribute="width" constant="24" id="Xoz-xh-0Fb"/>
                                                        </constraints>
                                                    </imageView>
                                                </subviews>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <constraints>
                                                    <constraint firstItem="QPy-ek-CnR" firstAttribute="leading" secondItem="zAo-Sj-TMg" secondAttribute="leading" constant="6" id="6mU-ua-eNS"/>
                                                    <constraint firstItem="QPy-ek-CnR" firstAttribute="centerY" secondItem="zAo-Sj-TMg" secondAttribute="centerY" id="OVq-bR-Y3Q"/>
                                                    <constraint firstAttribute="height" constant="50" id="Rke-1r-YuN"/>
                                                    <constraint firstItem="iKP-Dn-Jma" firstAttribute="centerY" secondItem="zAo-Sj-TMg" secondAttribute="centerY" id="ZrO-tu-9aj"/>
                                                    <constraint firstAttribute="trailing" secondItem="iKP-Dn-Jma" secondAttribute="trailing" constant="8" id="z0v-83-P6P"/>
                                                </constraints>
                                            </view>
                                        </subviews>
                                    </stackView>
                                    <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="rL9-IZ-aZz">
                                        <rect key="frame" x="0.0" y="66" width="337.5" height="50"/>
                                        <subviews>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="3DW-ei-y0G">
                                                <rect key="frame" x="0.0" y="0.0" width="337.5" height="50"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="ગુજરાતી" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="MdA-dt-6Vz">
                                                        <rect key="frame" x="6" y="8" width="44.5" height="34"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="34" id="4py-zf-uuo"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_uncheck" highlightedImage="ic_checked" translatesAutoresizingMaskIntoConstraints="NO" id="Wx5-n0-ytB">
                                                        <rect key="frame" x="305.5" y="13" width="24" height="24"/>
                                                        <color key="tintColor" name="PrimaryTextColorLight"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="24" id="pra-9D-AQv"/>
                                                            <constraint firstAttribute="width" constant="24" id="sst-aS-Jqe"/>
                                                        </constraints>
                                                    </imageView>
                                                </subviews>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <constraints>
                                                    <constraint firstAttribute="trailing" secondItem="Wx5-n0-ytB" secondAttribute="trailing" constant="8" id="8PB-aN-8Sk"/>
                                                    <constraint firstItem="Wx5-n0-ytB" firstAttribute="centerY" secondItem="3DW-ei-y0G" secondAttribute="centerY" id="PHz-Ks-9yy"/>
                                                    <constraint firstAttribute="height" constant="50" id="Q5M-MK-JfI"/>
                                                    <constraint firstItem="MdA-dt-6Vz" firstAttribute="leading" secondItem="3DW-ei-y0G" secondAttribute="leading" constant="6" id="Wlq-22-dKF"/>
                                                    <constraint firstItem="MdA-dt-6Vz" firstAttribute="centerY" secondItem="3DW-ei-y0G" secondAttribute="centerY" id="jPL-x8-X7d"/>
                                                </constraints>
                                            </view>
                                        </subviews>
                                    </stackView>
                                    <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="wE3-5h-kz6">
                                        <rect key="frame" x="0.0" y="132" width="337.5" height="50"/>
                                        <subviews>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="oBu-x0-Y3k">
                                                <rect key="frame" x="0.0" y="0.0" width="337.5" height="50"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="हिन्दी" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="2fC-q9-FIx">
                                                        <rect key="frame" x="6" y="8" width="28" height="34"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="34" id="VE7-Zn-GSu"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_uncheck" highlightedImage="ic_checked" translatesAutoresizingMaskIntoConstraints="NO" id="ivo-WS-Lig">
                                                        <rect key="frame" x="305.5" y="13" width="24" height="24"/>
                                                        <color key="tintColor" name="PrimaryTextColorLight"/>
                                                        <constraints>
                                                            <constraint firstAttribute="width" constant="24" id="r2D-Nj-e4V"/>
                                                            <constraint firstAttribute="height" constant="24" id="sLf-vv-qJb"/>
                                                        </constraints>
                                                    </imageView>
                                                </subviews>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <constraints>
                                                    <constraint firstItem="2fC-q9-FIx" firstAttribute="centerY" secondItem="oBu-x0-Y3k" secondAttribute="centerY" id="00T-nh-awK"/>
                                                    <constraint firstAttribute="trailing" secondItem="ivo-WS-Lig" secondAttribute="trailing" constant="8" id="S3Q-NV-PzY"/>
                                                    <constraint firstItem="ivo-WS-Lig" firstAttribute="centerY" secondItem="oBu-x0-Y3k" secondAttribute="centerY" id="Zg1-TF-QD2"/>
                                                    <constraint firstItem="2fC-q9-FIx" firstAttribute="leading" secondItem="oBu-x0-Y3k" secondAttribute="leading" constant="6" id="fbr-ld-CLM"/>
                                                    <constraint firstAttribute="height" constant="50" id="ih8-O0-BxV"/>
                                                </constraints>
                                            </view>
                                        </subviews>
                                    </stackView>
                                </subviews>
                            </stackView>
                            <button opaque="NO" tag="101" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="OFV-GB-pQt">
                                <rect key="frame" x="19" y="60" width="337.5" height="50"/>
                                <color key="tintColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="plain"/>
                                <connections>
                                    <action selector="languageAction:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="oQa-Uk-hYb"/>
                                </connections>
                            </button>
                            <button opaque="NO" tag="102" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Jl4-v6-Frc">
                                <rect key="frame" x="19" y="126" width="337.5" height="50"/>
                                <color key="tintColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="plain"/>
                                <connections>
                                    <action selector="languageAction:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="5OJ-ea-WVc"/>
                                </connections>
                            </button>
                            <button opaque="NO" tag="103" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="58d-Lw-dc3">
                                <rect key="frame" x="19" y="192" width="337.5" height="50"/>
                                <color key="tintColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="plain"/>
                                <connections>
                                    <action selector="languageAction:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="DJ5-Zk-h7S"/>
                                </connections>
                            </button>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="aYu-7g-ERb">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="wBP-HV-VmQ">
                                        <rect key="frame" x="19" y="214.5" width="337.5" height="238.5"/>
                                        <subviews>
                                            <button opaque="NO" alpha="0.0" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="nwU-tP-xqV">
                                                <rect key="frame" x="293.5" y="0.0" width="44" height="44"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="44" id="1Dy-Ty-dqB"/>
                                                    <constraint firstAttribute="width" constant="44" id="pIA-bj-tLH"/>
                                                </constraints>
                                                <inset key="imageEdgeInsets" minX="14" minY="14" maxX="14" maxY="14"/>
                                                <state key="normal" image="ic_cancel"/>
                                            </button>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="30" baselineRelativeArrangement="YES" translatesAutoresizingMaskIntoConstraints="NO" id="xAI-TG-oO7">
                                                <rect key="frame" x="16.5" y="16" width="304" height="146.5"/>
                                                <subviews>
                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_thumb_up" translatesAutoresizingMaskIntoConstraints="NO" id="AIi-8R-Iym">
                                                        <rect key="frame" x="119.5" y="0.0" width="65" height="65"/>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstAttribute="width" constant="65" id="9tm-US-6yf"/>
                                                            <constraint firstAttribute="height" constant="65" id="BQJ-gk-To6"/>
                                                        </constraints>
                                                    </imageView>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Language Changed" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="wsG-kK-hgm">
                                                        <rect key="frame" x="75" y="79" width="154.5" height="20.5"/>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="You will receive your RFID Card in 2-3 days." textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Bmq-3n-Pxu">
                                                        <rect key="frame" x="45.5" y="110.5" width="213" height="36"/>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                        <color key="textColor" white="0.0" alpha="0.5" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstItem="Bmq-3n-Pxu" firstAttribute="width" secondItem="xAI-TG-oO7" secondAttribute="width" multiplier="0.7" id="DnL-bW-RkA"/>
                                                </constraints>
                                            </stackView>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="20" translatesAutoresizingMaskIntoConstraints="NO" id="Mxu-hn-WNX">
                                                <rect key="frame" x="16" y="178.5" width="305.5" height="44"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="24" translatesAutoresizingMaskIntoConstraints="NO" id="r3z-0g-fM3">
                                                        <rect key="frame" x="15" y="0.0" width="275" height="44"/>
                                                        <subviews>
                                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="WPx-Yh-OF5">
                                                                <rect key="frame" x="0.0" y="0.0" width="275" height="44"/>
                                                                <color key="backgroundColor" name="Primary"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="44" id="Lv4-OZ-PLE"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                                                <state key="normal" title="OK">
                                                                    <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                </state>
                                                                <connections>
                                                                    <action selector="okAlertTapped:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="o0W-yV-Xdh"/>
                                                                </connections>
                                                            </button>
                                                        </subviews>
                                                    </stackView>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstItem="r3z-0g-fM3" firstAttribute="width" secondItem="Mxu-hn-WNX" secondAttribute="width" multiplier="0.9" id="AbM-Y9-Aky"/>
                                                </constraints>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstItem="xAI-TG-oO7" firstAttribute="top" secondItem="wBP-HV-VmQ" secondAttribute="top" constant="16" id="6MM-g0-tL2"/>
                                            <constraint firstItem="Mxu-hn-WNX" firstAttribute="top" secondItem="xAI-TG-oO7" secondAttribute="bottom" constant="16" id="BlK-Na-BEl"/>
                                            <constraint firstItem="nwU-tP-xqV" firstAttribute="top" secondItem="wBP-HV-VmQ" secondAttribute="top" id="Hmg-m7-2dH"/>
                                            <constraint firstAttribute="trailing" secondItem="nwU-tP-xqV" secondAttribute="trailing" id="Of9-5s-RBM"/>
                                            <constraint firstItem="xAI-TG-oO7" firstAttribute="centerX" secondItem="wBP-HV-VmQ" secondAttribute="centerX" id="Q17-QQ-nSG"/>
                                            <constraint firstItem="xAI-TG-oO7" firstAttribute="width" secondItem="wBP-HV-VmQ" secondAttribute="width" multiplier="0.9" id="TTO-9p-RXr"/>
                                            <constraint firstItem="Mxu-hn-WNX" firstAttribute="width" secondItem="wBP-HV-VmQ" secondAttribute="width" multiplier="0.905185" id="UC3-gD-5wA"/>
                                            <constraint firstAttribute="bottom" secondItem="Mxu-hn-WNX" secondAttribute="bottom" constant="16" id="XF6-iX-KWo"/>
                                            <constraint firstItem="Mxu-hn-WNX" firstAttribute="centerX" secondItem="wBP-HV-VmQ" secondAttribute="centerX" id="hsG-zV-LRJ"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.75" colorSpace="custom" customColorSpace="sRGB"/>
                                <constraints>
                                    <constraint firstItem="wBP-HV-VmQ" firstAttribute="centerX" secondItem="aYu-7g-ERb" secondAttribute="centerX" id="ABQ-w7-aSW"/>
                                    <constraint firstItem="wBP-HV-VmQ" firstAttribute="centerY" secondItem="aYu-7g-ERb" secondAttribute="centerY" id="COQ-ft-zS9"/>
                                    <constraint firstItem="wBP-HV-VmQ" firstAttribute="width" secondItem="aYu-7g-ERb" secondAttribute="width" multiplier="0.9" id="DEc-CE-UKt"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="vDu-zF-Fre"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="Jl4-v6-Frc" firstAttribute="centerX" secondItem="rL9-IZ-aZz" secondAttribute="centerX" id="2tk-oE-qJ9"/>
                            <constraint firstItem="OFV-GB-pQt" firstAttribute="centerY" secondItem="YNK-Jp-ETw" secondAttribute="centerY" id="75T-kv-U30"/>
                            <constraint firstItem="Jl4-v6-Frc" firstAttribute="centerY" secondItem="rL9-IZ-aZz" secondAttribute="centerY" id="7xV-hK-fzU"/>
                            <constraint firstItem="Jl4-v6-Frc" firstAttribute="height" secondItem="rL9-IZ-aZz" secondAttribute="height" id="DmF-4n-jer"/>
                            <constraint firstItem="aYu-7g-ERb" firstAttribute="leading" secondItem="vDu-zF-Fre" secondAttribute="leading" id="FnF-x7-wXH"/>
                            <constraint firstItem="kEL-IR-H6L" firstAttribute="width" secondItem="vDu-zF-Fre" secondAttribute="width" multiplier="0.9" id="HDi-Qk-WWk"/>
                            <constraint firstItem="kEL-IR-H6L" firstAttribute="top" secondItem="gzE-DJ-PrE" secondAttribute="bottom" constant="16" id="J3E-s9-dp9"/>
                            <constraint firstItem="aYu-7g-ERb" firstAttribute="top" secondItem="vDu-zF-Fre" secondAttribute="top" id="Lpx-iA-Ddm"/>
                            <constraint firstItem="OFV-GB-pQt" firstAttribute="height" secondItem="YNK-Jp-ETw" secondAttribute="height" id="Nyw-tU-1aT"/>
                            <constraint firstItem="OFV-GB-pQt" firstAttribute="centerX" secondItem="YNK-Jp-ETw" secondAttribute="centerX" id="P6L-P7-aug"/>
                            <constraint firstItem="gzE-DJ-PrE" firstAttribute="leading" secondItem="vDu-zF-Fre" secondAttribute="leading" id="Pge-xY-cOn"/>
                            <constraint firstItem="58d-Lw-dc3" firstAttribute="height" secondItem="wE3-5h-kz6" secondAttribute="height" id="Pth-cl-FUo"/>
                            <constraint firstItem="vDu-zF-Fre" firstAttribute="trailing" secondItem="aYu-7g-ERb" secondAttribute="trailing" id="WfN-f0-prd"/>
                            <constraint firstItem="58d-Lw-dc3" firstAttribute="centerX" secondItem="wE3-5h-kz6" secondAttribute="centerX" id="avB-La-EYt"/>
                            <constraint firstItem="kEL-IR-H6L" firstAttribute="centerX" secondItem="5EZ-qb-Rvc" secondAttribute="centerX" id="en1-95-HR2"/>
                            <constraint firstItem="Jl4-v6-Frc" firstAttribute="width" secondItem="rL9-IZ-aZz" secondAttribute="width" id="fNA-Qk-rc3"/>
                            <constraint firstItem="OFV-GB-pQt" firstAttribute="width" secondItem="YNK-Jp-ETw" secondAttribute="width" id="lVA-Tm-cCH"/>
                            <constraint firstItem="58d-Lw-dc3" firstAttribute="width" secondItem="wE3-5h-kz6" secondAttribute="width" id="nd2-Za-eoB"/>
                            <constraint firstItem="vDu-zF-Fre" firstAttribute="bottom" secondItem="aYu-7g-ERb" secondAttribute="bottom" id="otz-OF-g8m"/>
                            <constraint firstItem="58d-Lw-dc3" firstAttribute="centerY" secondItem="wE3-5h-kz6" secondAttribute="centerY" id="uG9-FB-kaV"/>
                            <constraint firstItem="vDu-zF-Fre" firstAttribute="trailing" secondItem="gzE-DJ-PrE" secondAttribute="trailing" id="vYi-DG-h1f"/>
                            <constraint firstItem="gzE-DJ-PrE" firstAttribute="top" secondItem="vDu-zF-Fre" secondAttribute="top" id="yFP-GZ-15r"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="btnBack" destination="0Ap-6B-X9O" id="rq6-Cq-VBO"/>
                        <outlet property="btnOKAlert" destination="WPx-Yh-OF5" id="mt3-EE-kOo"/>
                        <outlet property="imgEnglish" destination="iKP-Dn-Jma" id="lgB-Qz-Mbo"/>
                        <outlet property="imgGujarati" destination="Wx5-n0-ytB" id="kbh-nF-bw6"/>
                        <outlet property="imgHindi" destination="ivo-WS-Lig" id="Ye8-si-aAc"/>
                        <outlet property="lblAlertText" destination="Bmq-3n-Pxu" id="5ad-GU-g9L"/>
                        <outlet property="lblAlertTitle" destination="wsG-kK-hgm" id="YA2-mR-fbJ"/>
                        <outlet property="viewBgAlert" destination="aYu-7g-ERb" id="QLc-lO-2Yx"/>
                        <outlet property="viewEnglish" destination="zAo-Sj-TMg" id="KFi-UC-voX"/>
                        <outlet property="viewGujarati" destination="3DW-ei-y0G" id="YTi-3l-UjR"/>
                        <outlet property="viewHindi" destination="oBu-x0-Y3k" id="Mg3-MW-qkr"/>
                        <outlet property="viewMainAlert" destination="wBP-HV-VmQ" id="wql-qD-cWf"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Ief-a0-LHa" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="52" y="69.715142428785612"/>
        </scene>
    </scenes>
    <resources>
        <image name="ic_back_arrow" width="64" height="64"/>
        <image name="ic_cancel" width="64" height="64"/>
        <image name="ic_checked" width="500" height="500"/>
        <image name="ic_thumb_up" width="300" height="300"/>
        <image name="ic_uncheck" width="500" height="500"/>
        <namedColor name="Primary">
            <color red="0.016000000759959221" green="0.49399998784065247" blue="0.42699998617172241" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="PrimaryTextColorLight">
            <color red="0.32899999618530273" green="0.32400000095367432" blue="0.4779999852180481" alpha="0.33000001311302185" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>

<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="19162" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="N6r-V5-ODj">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="19144"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--SplashVC-->
        <scene sceneID="aFV-vt-FtJ">
            <objects>
                <viewController storyboardIdentifier="SplashVC" id="8gY-v7-TEh" customClass="SplashVC" customModule="NXC_EV_Solutions" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="eOP-He-svh">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_new_nxc_logo" translatesAutoresizingMaskIntoConstraints="NO" id="oLq-3c-lJ7">
                                <rect key="frame" x="75" y="40" width="225" height="90"/>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="90" id="XTh-uJ-9zr"/>
                                </constraints>
                            </imageView>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" translatesAutoresizingMaskIntoConstraints="NO" id="pEy-A8-42Q">
                                <rect key="frame" x="37.5" y="199.5" width="300" height="68"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" usesAttributedText="YES" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="g3B-pg-cpk">
                                        <rect key="frame" x="0.0" y="0.0" width="300" height="34"/>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="34" id="Rc8-Jt-px3"/>
                                        </constraints>
                                        <attributedString key="attributedText">
                                            <fragment content="Think ">
                                                <attributes>
                                                    <color key="NSColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <font key="NSFont" size="24" name="Arial-BoldMT"/>
                                                    <paragraphStyle key="NSParagraphStyle" alignment="center" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0"/>
                                                </attributes>
                                            </fragment>
                                            <fragment content="Electric">
                                                <attributes>
                                                    <color key="NSColor" red="0.40000000000000002" green="0.66274509803921566" blue="0.27058823529411763" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <font key="NSFont" size="24" name="Arial-BoldMT"/>
                                                    <paragraphStyle key="NSParagraphStyle" alignment="center" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0"/>
                                                </attributes>
                                            </fragment>
                                        </attributedString>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" usesAttributedText="YES" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ogb-xX-1AG">
                                        <rect key="frame" x="0.0" y="34" width="300" height="34"/>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="34" id="qsv-sL-iPq"/>
                                        </constraints>
                                        <attributedString key="attributedText">
                                            <fragment content="Think ">
                                                <attributes>
                                                    <color key="NSColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <font key="NSFont" size="24" name="Arial-BoldMT"/>
                                                    <paragraphStyle key="NSParagraphStyle" alignment="center" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0"/>
                                                </attributes>
                                            </fragment>
                                            <fragment content="NXC">
                                                <attributes>
                                                    <color key="NSColor" red="0.40000000000000002" green="0.66274509803921566" blue="0.27058823529411763" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <font key="NSFont" size="24" name="Arial-BoldMT"/>
                                                    <paragraphStyle key="NSParagraphStyle" alignment="center" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0"/>
                                                </attributes>
                                            </fragment>
                                        </attributedString>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                            </stackView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="piv-cy-Iwn">
                                <rect key="frame" x="0.0" y="467" width="375" height="200"/>
                                <subviews>
                                    <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_splash_img" translatesAutoresizingMaskIntoConstraints="NO" id="ulG-fT-AYl">
                                        <rect key="frame" x="0.0" y="-100" width="375" height="240"/>
                                    </imageView>
                                </subviews>
                                <color key="backgroundColor" name="PrimaryDarkBGColor"/>
                                <constraints>
                                    <constraint firstItem="ulG-fT-AYl" firstAttribute="height" secondItem="piv-cy-Iwn" secondAttribute="height" multiplier="1.2" id="EWG-aN-lp3"/>
                                    <constraint firstItem="ulG-fT-AYl" firstAttribute="top" secondItem="piv-cy-Iwn" secondAttribute="top" constant="-100" id="GjM-yL-Auf"/>
                                    <constraint firstItem="ulG-fT-AYl" firstAttribute="width" secondItem="piv-cy-Iwn" secondAttribute="width" id="Riq-3v-JKu"/>
                                    <constraint firstItem="ulG-fT-AYl" firstAttribute="centerX" secondItem="piv-cy-Iwn" secondAttribute="centerX" id="qW7-tL-YHo"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="3hS-F3-cGh"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="piv-cy-Iwn" secondAttribute="trailing" id="1PX-rR-ljg"/>
                            <constraint firstItem="pEy-A8-42Q" firstAttribute="centerY" secondItem="eOP-He-svh" secondAttribute="centerY" multiplier="0.7" id="4CZ-GF-jNh"/>
                            <constraint firstItem="oLq-3c-lJ7" firstAttribute="top" secondItem="3hS-F3-cGh" secondAttribute="top" constant="40" id="4M4-63-hsm"/>
                            <constraint firstAttribute="bottom" secondItem="piv-cy-Iwn" secondAttribute="bottom" id="AqV-4k-VLx"/>
                            <constraint firstItem="oLq-3c-lJ7" firstAttribute="width" secondItem="3hS-F3-cGh" secondAttribute="width" multiplier="0.6" id="Caa-DR-5uk"/>
                            <constraint firstItem="piv-cy-Iwn" firstAttribute="height" secondItem="eOP-He-svh" secondAttribute="height" multiplier="0.3" id="L4F-go-IHP"/>
                            <constraint firstItem="pEy-A8-42Q" firstAttribute="width" secondItem="3hS-F3-cGh" secondAttribute="width" multiplier="0.8" id="Qew-r7-h1h"/>
                            <constraint firstItem="piv-cy-Iwn" firstAttribute="leading" secondItem="eOP-He-svh" secondAttribute="leading" id="Xra-2v-SBz"/>
                            <constraint firstItem="oLq-3c-lJ7" firstAttribute="centerX" secondItem="eOP-He-svh" secondAttribute="centerX" id="eSa-5K-gbp"/>
                            <constraint firstItem="pEy-A8-42Q" firstAttribute="centerX" secondItem="eOP-He-svh" secondAttribute="centerX" id="urW-lM-Vtr"/>
                            <constraint firstItem="piv-cy-Iwn" firstAttribute="bottom" secondItem="eOP-He-svh" secondAttribute="bottom" id="wYZ-EU-sZo"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="FSy-JZ-Oe2"/>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="G6R-Hu-Z1e" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="119" y="-45"/>
        </scene>
        <!--Navigation Controller-->
        <scene sceneID="uX0-MP-e4F">
            <objects>
                <navigationController automaticallyAdjustsScrollViewInsets="NO" navigationBarHidden="YES" id="N6r-V5-ODj" sceneMemberID="viewController">
                    <toolbarItems/>
                    <navigationBar key="navigationBar" contentMode="scaleToFill" id="owu-fZ-irt">
                        <autoresizingMask key="autoresizingMask"/>
                    </navigationBar>
                    <nil name="viewControllers"/>
                    <connections>
                        <segue destination="8gY-v7-TEh" kind="relationship" relationship="rootViewController" id="zS3-s2-ZQj"/>
                    </connections>
                </navigationController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Uvh-Fb-xPf" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-665.21739130434787" y="-44.866071428571423"/>
        </scene>
        <!--OnboardingVC-->
        <scene sceneID="5YA-D7-kDr">
            <objects>
                <viewController storyboardIdentifier="OnboardingVC" id="J1o-AV-l8j" customClass="OnboardingVC" customModule="NXC_EV_Solutions" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="8I7-5I-F6o">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <viewLayoutGuide key="safeArea" id="LHf-Dc-32J"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="qUj-3C-pX4" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="822" y="-45"/>
        </scene>
        <!--LoginVC-->
        <scene sceneID="G9v-Ho-hNU">
            <objects>
                <viewController storyboardIdentifier="LoginVC" id="3Pw-30-ghx" customClass="LoginVC" customModule="NXC_EV_Solutions" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="7TO-Xd-JaS">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="yjY-MQ-up1">
                                <rect key="frame" x="19" y="421" width="337.5" height="50"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="vdg-bf-tfR">
                                        <rect key="frame" x="0.0" y="0.0" width="337.5" height="50"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_smartphone" translatesAutoresizingMaskIntoConstraints="NO" id="RDN-g6-gC0">
                                                <rect key="frame" x="8" y="10" width="30" height="30"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="30" id="DJN-Ra-XGa"/>
                                                    <constraint firstAttribute="width" secondItem="RDN-g6-gC0" secondAttribute="height" multiplier="1:1" id="ozQ-3j-vzv"/>
                                                </constraints>
                                            </imageView>
                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Phone Number " textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="jqG-zh-kbC">
                                                <rect key="frame" x="50" y="0.0" width="275.5" height="50"/>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <color key="tintColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="50" id="o0W-XM-GTV"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                <textInputTraits key="textInputTraits" keyboardType="numberPad"/>
                                            </textField>
                                        </subviews>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="bottom" secondItem="jqG-zh-kbC" secondAttribute="bottom" id="693-S1-czY"/>
                                            <constraint firstItem="RDN-g6-gC0" firstAttribute="leading" secondItem="vdg-bf-tfR" secondAttribute="leading" constant="8" id="CSn-9x-zTk"/>
                                            <constraint firstItem="jqG-zh-kbC" firstAttribute="leading" secondItem="RDN-g6-gC0" secondAttribute="trailing" constant="12" id="NnI-3y-Owx"/>
                                            <constraint firstAttribute="trailing" secondItem="jqG-zh-kbC" secondAttribute="trailing" constant="12" id="SCD-FL-g7a"/>
                                            <constraint firstItem="RDN-g6-gC0" firstAttribute="centerY" secondItem="vdg-bf-tfR" secondAttribute="centerY" id="cty-dy-isp"/>
                                            <constraint firstAttribute="height" constant="50" id="f6e-ta-w6s"/>
                                            <constraint firstItem="jqG-zh-kbC" firstAttribute="top" secondItem="vdg-bf-tfR" secondAttribute="top" id="jYb-td-Xgb"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </stackView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_login_Img" translatesAutoresizingMaskIntoConstraints="NO" id="VIb-9d-WGZ">
                                <rect key="frame" x="19" y="150" width="337.5" height="233.5"/>
                            </imageView>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="g8V-HB-wuh">
                                <rect key="frame" x="19" y="491" width="337.5" height="44"/>
                                <color key="backgroundColor" name="PrimaryColor"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="44" id="6v0-sg-Wxx"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                <state key="normal" title="Login">
                                    <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                </state>
                                <connections>
                                    <action selector="loginAction:" destination="3Pw-30-ghx" eventType="touchUpInside" id="cyJ-f3-Vz8"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="zZ4-Ww-glx">
                                <rect key="frame" x="65" y="623" width="245" height="44"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="44" id="vPc-14-r2v"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" weight="medium" pointSize="16"/>
                                <state key="normal" title="A NXC Controls Pvt. Ltd. Product">
                                    <color key="titleColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </state>
                                <connections>
                                    <action selector="productByAction:" destination="3Pw-30-ghx" eventType="touchUpInside" id="p0b-YE-Z5R"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="characterWrap" hasAttributedTitle="YES" translatesAutoresizingMaskIntoConstraints="NO" id="tpc-N9-11H">
                                <rect key="frame" x="19" y="547" width="337.5" height="44"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="44" id="wbs-Kw-dcY"/>
                                </constraints>
                                <state key="normal">
                                    <attributedString key="attributedTitle">
                                        <fragment content="By logging in, you agree to our ">
                                            <attributes>
                                                <color key="NSColor" name="textColor" catalog="System" colorSpace="catalog"/>
                                                <font key="NSFont" metaFont="system" size="15"/>
                                                <paragraphStyle key="NSParagraphStyle" alignment="natural" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0"/>
                                            </attributes>
                                        </fragment>
                                        <fragment content="Terms and Conditions">
                                            <attributes>
                                                <color key="NSColor" name="textColor" catalog="System" colorSpace="catalog"/>
                                                <font key="NSFont" metaFont="systemBold" size="15"/>
                                                <font key="NSOriginalFont" metaFont="systemBold" size="15"/>
                                                <paragraphStyle key="NSParagraphStyle" alignment="natural" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0"/>
                                                <integer key="NSUnderline" value="1"/>
                                            </attributes>
                                        </fragment>
                                    </attributedString>
                                </state>
                                <connections>
                                    <action selector="termsAndConditionAction:" destination="3Pw-30-ghx" eventType="touchUpInside" id="CgT-TH-UGk"/>
                                </connections>
                            </button>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_new_nxc_logo" translatesAutoresizingMaskIntoConstraints="NO" id="YcM-oe-jH3">
                                <rect key="frame" x="75" y="40" width="225" height="90"/>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="90" id="62a-JU-FgJ"/>
                                </constraints>
                            </imageView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="48e-IE-cFP"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="g8V-HB-wuh" firstAttribute="centerX" secondItem="7TO-Xd-JaS" secondAttribute="centerX" id="2pO-1y-cVX"/>
                            <constraint firstItem="VIb-9d-WGZ" firstAttribute="centerX" secondItem="7TO-Xd-JaS" secondAttribute="centerX" id="I2L-vv-nDG"/>
                            <constraint firstItem="tpc-N9-11H" firstAttribute="width" secondItem="48e-IE-cFP" secondAttribute="width" multiplier="0.9" id="WQg-hR-SeJ"/>
                            <constraint firstItem="g8V-HB-wuh" firstAttribute="top" secondItem="yjY-MQ-up1" secondAttribute="bottom" constant="20" id="Wgu-Tk-YBu"/>
                            <constraint firstItem="tpc-N9-11H" firstAttribute="centerX" secondItem="7TO-Xd-JaS" secondAttribute="centerX" id="ZA3-rI-8uB"/>
                            <constraint firstItem="YcM-oe-jH3" firstAttribute="width" secondItem="48e-IE-cFP" secondAttribute="width" multiplier="0.6" id="a4M-sV-GMq"/>
                            <constraint firstItem="g8V-HB-wuh" firstAttribute="width" secondItem="48e-IE-cFP" secondAttribute="width" multiplier="0.9" id="bUS-yK-pcJ"/>
                            <constraint firstItem="yjY-MQ-up1" firstAttribute="centerX" secondItem="7TO-Xd-JaS" secondAttribute="centerX" id="duR-uh-2Ow"/>
                            <constraint firstItem="zZ4-Ww-glx" firstAttribute="top" secondItem="tpc-N9-11H" secondAttribute="bottom" constant="32" id="fTI-qx-RyW"/>
                            <constraint firstItem="VIb-9d-WGZ" firstAttribute="height" secondItem="48e-IE-cFP" secondAttribute="height" multiplier="0.35" id="gSP-aD-MiL"/>
                            <constraint firstItem="yjY-MQ-up1" firstAttribute="width" secondItem="48e-IE-cFP" secondAttribute="width" multiplier="0.9" id="hal-Dt-a8x"/>
                            <constraint firstItem="48e-IE-cFP" firstAttribute="bottom" secondItem="zZ4-Ww-glx" secondAttribute="bottom" id="mT8-xE-Qu2"/>
                            <constraint firstItem="zZ4-Ww-glx" firstAttribute="centerX" secondItem="7TO-Xd-JaS" secondAttribute="centerX" id="pYj-Xc-diF"/>
                            <constraint firstItem="YcM-oe-jH3" firstAttribute="top" secondItem="48e-IE-cFP" secondAttribute="top" constant="40" id="vEt-1K-FtW"/>
                            <constraint firstItem="tpc-N9-11H" firstAttribute="top" secondItem="g8V-HB-wuh" secondAttribute="bottom" constant="12" id="vfn-8Y-suq"/>
                            <constraint firstItem="YcM-oe-jH3" firstAttribute="centerX" secondItem="7TO-Xd-JaS" secondAttribute="centerX" id="w4F-9k-Lq3"/>
                            <constraint firstItem="VIb-9d-WGZ" firstAttribute="width" secondItem="48e-IE-cFP" secondAttribute="width" multiplier="0.9" id="wKh-Lc-Vz6"/>
                            <constraint firstItem="VIb-9d-WGZ" firstAttribute="top" secondItem="YcM-oe-jH3" secondAttribute="bottom" constant="20" id="zmj-uT-fRf"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="btnLogin" destination="g8V-HB-wuh" id="Z7F-zw-GqH"/>
                        <outlet property="btnTC" destination="tpc-N9-11H" id="0X1-ON-fhv"/>
                        <outlet property="txtPhone" destination="jqG-zh-kbC" id="fGr-yQ-6tP"/>
                        <outlet property="viewPhone" destination="vdg-bf-tfR" id="ftu-1f-Gdc"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="jYZ-sq-5pr" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1521" y="-45"/>
        </scene>
        <!--MenuVC-->
        <scene sceneID="ix5-Wn-SWX">
            <objects>
                <viewController storyboardIdentifier="MenuVC" id="Fyq-yB-34n" customClass="MenuVC" customModule="NXC_EV_Solutions" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="2CO-cx-KQb">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="52b-Ip-Wcc">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="lV0-TS-pcm">
                                        <rect key="frame" x="0.0" y="0.0" width="300" height="667"/>
                                        <subviews>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="mju-OZ-aIN">
                                                <rect key="frame" x="12" y="593" width="276" height="44"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="LaB-qw-09Q">
                                                        <rect key="frame" x="8" y="0.0" width="264" height="44"/>
                                                        <subviews>
                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_logout" translatesAutoresizingMaskIntoConstraints="NO" id="bae-Lh-m4u">
                                                                <rect key="frame" x="0.0" y="10" width="24" height="24"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="24" id="lhb-bT-MvN"/>
                                                                    <constraint firstAttribute="height" constant="24" id="sC5-qu-u8g"/>
                                                                </constraints>
                                                            </imageView>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Logout" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Fyy-4U-D0Q">
                                                                <rect key="frame" x="32" y="13.5" width="232" height="17"/>
                                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                <nil key="textColor"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                    </stackView>
                                                </subviews>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="44" id="50A-Bf-1zQ"/>
                                                    <constraint firstItem="LaB-qw-09Q" firstAttribute="leading" secondItem="mju-OZ-aIN" secondAttribute="leading" constant="8" id="DkX-Wb-sze"/>
                                                    <constraint firstAttribute="trailing" secondItem="LaB-qw-09Q" secondAttribute="trailing" constant="4" id="O7S-EX-OP0"/>
                                                    <constraint firstItem="LaB-qw-09Q" firstAttribute="top" secondItem="mju-OZ-aIN" secondAttribute="top" id="YJt-HN-sIS"/>
                                                    <constraint firstAttribute="bottom" secondItem="LaB-qw-09Q" secondAttribute="bottom" id="jxF-Ed-4MN"/>
                                                </constraints>
                                            </view>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="kdc-ln-OKW">
                                                <rect key="frame" x="12" y="8" width="276" height="86.5"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="2" translatesAutoresizingMaskIntoConstraints="NO" id="jre-qj-6iT">
                                                        <rect key="frame" x="12" y="12" width="252" height="62.5"/>
                                                        <subviews>
                                                            <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="uYH-M2-uWu">
                                                                <rect key="frame" x="0.0" y="0.0" width="252" height="62.5"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="J" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="7T0-Fe-8VI">
                                                                        <rect key="frame" x="0.0" y="9.5" width="44" height="44"/>
                                                                        <color key="backgroundColor" name="Primary"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="width" constant="44" id="OZI-9H-Ba4"/>
                                                                            <constraint firstAttribute="height" constant="44" id="ax6-P6-dSc"/>
                                                                        </constraints>
                                                                        <fontDescription key="fontDescription" type="boldSystem" pointSize="22"/>
                                                                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="FY9-LC-FLp">
                                                                        <rect key="frame" x="56" y="10.5" width="196" height="41.5"/>
                                                                        <subviews>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Jobin Macwan" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="lIr-l2-sBZ">
                                                                                <rect key="frame" x="0.0" y="0.0" width="196" height="20.5"/>
                                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                                <nil key="textColor"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Avail Balance : ₹ 25000" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="HPZ-Y9-QEy">
                                                                                <rect key="frame" x="0.0" y="24.5" width="196" height="17"/>
                                                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                                <color key="textColor" name="Primary"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                        </subviews>
                                                                    </stackView>
                                                                </subviews>
                                                            </stackView>
                                                        </subviews>
                                                    </stackView>
                                                </subviews>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstItem="jre-qj-6iT" firstAttribute="leading" secondItem="kdc-ln-OKW" secondAttribute="leading" constant="12" id="5hP-3Y-GPQ"/>
                                                    <constraint firstAttribute="bottom" secondItem="jre-qj-6iT" secondAttribute="bottom" constant="12" id="9sV-Tc-NRU"/>
                                                    <constraint firstAttribute="trailing" secondItem="jre-qj-6iT" secondAttribute="trailing" constant="12" id="kKa-Bs-Baq"/>
                                                    <constraint firstItem="jre-qj-6iT" firstAttribute="top" secondItem="kdc-ln-OKW" secondAttribute="top" constant="12" id="knt-4F-TpO"/>
                                                </constraints>
                                            </view>
                                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="-1" estimatedSectionHeaderHeight="-1" sectionFooterHeight="-1" estimatedSectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="NIc-LA-mwg">
                                                <rect key="frame" x="12" y="106.5" width="276" height="128"/>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="128" id="R4K-9g-KeB"/>
                                                </constraints>
                                                <prototypes>
                                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="none" indentationWidth="10" reuseIdentifier="cell" rowHeight="50" id="eHj-OD-vkp" customClass="MenuCell" customModule="NXC_EV_Solutions" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="44.5" width="276" height="50"/>
                                                        <autoresizingMask key="autoresizingMask"/>
                                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="eHj-OD-vkp" id="O0x-QB-Imu">
                                                            <rect key="frame" x="0.0" y="0.0" width="276" height="50"/>
                                                            <autoresizingMask key="autoresizingMask"/>
                                                            <subviews>
                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="HL5-XD-Mra">
                                                                    <rect key="frame" x="8" y="6" width="260" height="38"/>
                                                                    <subviews>
                                                                        <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="5TC-fy-5mm">
                                                                            <rect key="frame" x="12" y="2" width="242" height="34"/>
                                                                            <subviews>
                                                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="3Jh-Mt-ymE">
                                                                                    <rect key="frame" x="0.0" y="8" width="18" height="18"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="height" constant="18" id="ZsY-6L-l6X"/>
                                                                                        <constraint firstAttribute="width" constant="18" id="gBO-yJ-O2P"/>
                                                                                    </constraints>
                                                                                </imageView>
                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Profile" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="1PD-VY-fcg">
                                                                                    <rect key="frame" x="34" y="0.0" width="208" height="34"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="height" constant="44" id="NRv-YF-rZa"/>
                                                                                    </constraints>
                                                                                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="14"/>
                                                                                    <nil key="textColor"/>
                                                                                    <nil key="highlightedColor"/>
                                                                                </label>
                                                                            </subviews>
                                                                        </stackView>
                                                                    </subviews>
                                                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                    <constraints>
                                                                        <constraint firstItem="5TC-fy-5mm" firstAttribute="leading" secondItem="HL5-XD-Mra" secondAttribute="leading" constant="12" id="6pM-2m-Dit"/>
                                                                        <constraint firstAttribute="trailing" secondItem="5TC-fy-5mm" secondAttribute="trailing" constant="6" id="G2x-1Q-mgk"/>
                                                                        <constraint firstItem="5TC-fy-5mm" firstAttribute="top" secondItem="HL5-XD-Mra" secondAttribute="top" constant="2" id="OQs-49-Gp2"/>
                                                                        <constraint firstAttribute="bottom" secondItem="5TC-fy-5mm" secondAttribute="bottom" constant="2" id="hHJ-RJ-Vme"/>
                                                                    </constraints>
                                                                </view>
                                                            </subviews>
                                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstItem="HL5-XD-Mra" firstAttribute="leading" secondItem="O0x-QB-Imu" secondAttribute="leading" constant="8" id="855-Gf-t2A"/>
                                                                <constraint firstItem="HL5-XD-Mra" firstAttribute="top" secondItem="O0x-QB-Imu" secondAttribute="top" constant="6" id="BoN-Jo-lkW"/>
                                                                <constraint firstAttribute="bottom" secondItem="HL5-XD-Mra" secondAttribute="bottom" constant="6" id="NTt-zZ-Aqk"/>
                                                                <constraint firstAttribute="trailing" secondItem="HL5-XD-Mra" secondAttribute="trailing" constant="8" id="gZR-j6-sKe"/>
                                                            </constraints>
                                                        </tableViewCellContentView>
                                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <connections>
                                                            <outlet property="imgMenu" destination="3Jh-Mt-ymE" id="aYf-Fb-OQ9"/>
                                                            <outlet property="lblMenu" destination="1PD-VY-fcg" id="dX1-7M-Ppf"/>
                                                            <outlet property="viewMain" destination="HL5-XD-Mra" id="Yag-Xy-xvW"/>
                                                        </connections>
                                                    </tableViewCell>
                                                </prototypes>
                                            </tableView>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="LZS-YW-sBZ">
                                                <rect key="frame" x="12" y="593" width="276" height="44"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="44" id="VeF-jV-O07"/>
                                                </constraints>
                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                <connections>
                                                    <action selector="logoutTapped:" destination="Fyq-yB-34n" eventType="touchUpInside" id="OcF-MU-uvl"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="kdc-ln-OKW" firstAttribute="leading" secondItem="lV0-TS-pcm" secondAttribute="leading" constant="12" id="1IQ-9E-qT1"/>
                                            <constraint firstItem="mju-OZ-aIN" firstAttribute="leading" secondItem="lV0-TS-pcm" secondAttribute="leading" constant="12" id="2mJ-Fq-Eyj"/>
                                            <constraint firstItem="LZS-YW-sBZ" firstAttribute="centerY" secondItem="mju-OZ-aIN" secondAttribute="centerY" id="6OJ-tW-iHo"/>
                                            <constraint firstItem="kdc-ln-OKW" firstAttribute="top" secondItem="lV0-TS-pcm" secondAttribute="top" constant="8" id="DLD-P8-N6D"/>
                                            <constraint firstItem="NIc-LA-mwg" firstAttribute="top" secondItem="kdc-ln-OKW" secondAttribute="bottom" constant="12" id="R2k-JI-jk6"/>
                                            <constraint firstItem="LZS-YW-sBZ" firstAttribute="width" secondItem="mju-OZ-aIN" secondAttribute="width" id="TBP-y5-ABI"/>
                                            <constraint firstItem="LZS-YW-sBZ" firstAttribute="centerX" secondItem="mju-OZ-aIN" secondAttribute="centerX" id="UgE-MF-8N7"/>
                                            <constraint firstAttribute="trailing" secondItem="mju-OZ-aIN" secondAttribute="trailing" constant="12" id="ZCb-UJ-tca"/>
                                            <constraint firstAttribute="bottom" secondItem="mju-OZ-aIN" secondAttribute="bottom" constant="30" id="eEU-mz-hgY"/>
                                            <constraint firstAttribute="trailing" secondItem="NIc-LA-mwg" secondAttribute="trailing" constant="12" id="oRD-ip-zzS"/>
                                            <constraint firstItem="kdc-ln-OKW" firstAttribute="height" secondItem="lV0-TS-pcm" secondAttribute="height" multiplier="0.13" id="oVy-ZB-cnf"/>
                                            <constraint firstItem="NIc-LA-mwg" firstAttribute="leading" secondItem="lV0-TS-pcm" secondAttribute="leading" constant="12" id="rmg-ty-awu"/>
                                            <constraint firstAttribute="trailing" secondItem="kdc-ln-OKW" secondAttribute="trailing" constant="12" id="zv9-g4-bCF"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.75" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="lV0-TS-pcm" firstAttribute="width" secondItem="52b-Ip-Wcc" secondAttribute="width" multiplier="0.8" id="dlZ-Hm-2Og"/>
                                    <constraint firstItem="lV0-TS-pcm" firstAttribute="leading" secondItem="52b-Ip-Wcc" secondAttribute="leading" id="oaz-wH-3R9"/>
                                    <constraint firstAttribute="bottom" secondItem="lV0-TS-pcm" secondAttribute="bottom" id="pEm-At-R5k"/>
                                    <constraint firstItem="lV0-TS-pcm" firstAttribute="top" secondItem="52b-Ip-Wcc" secondAttribute="top" id="uvJ-eG-BPD"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="oSw-YM-a40"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="oSw-YM-a40" firstAttribute="bottom" secondItem="52b-Ip-Wcc" secondAttribute="bottom" id="g1I-ti-jUi"/>
                            <constraint firstItem="oSw-YM-a40" firstAttribute="trailing" secondItem="52b-Ip-Wcc" secondAttribute="trailing" id="hrK-Nw-PEx"/>
                            <constraint firstItem="52b-Ip-Wcc" firstAttribute="top" secondItem="oSw-YM-a40" secondAttribute="top" id="keu-cb-haz"/>
                            <constraint firstItem="52b-Ip-Wcc" firstAttribute="leading" secondItem="oSw-YM-a40" secondAttribute="leading" id="syb-tA-RrH"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="lblBalance" destination="HPZ-Y9-QEy" id="nu0-DY-8wi"/>
                        <outlet property="lblInitial" destination="7T0-Fe-8VI" id="HLL-I7-bGo"/>
                        <outlet property="lblUserName" destination="lIr-l2-sBZ" id="dCF-OA-JZs"/>
                        <outlet property="tableHeight" destination="R4K-9g-KeB" id="hIO-3s-e0i"/>
                        <outlet property="tableMenu" destination="NIc-LA-mwg" id="KMq-qu-cCV"/>
                        <outlet property="viewBgMenu" destination="52b-Ip-Wcc" id="0kQ-MU-Ues"/>
                        <outlet property="viewLogout" destination="mju-OZ-aIN" id="TXy-nU-eYa"/>
                        <outlet property="viewMainMenu" destination="lV0-TS-pcm" id="4om-7m-dz8"/>
                        <outlet property="viewProfile" destination="kdc-ln-OKW" id="Ogw-H3-eiP"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="DMc-za-TGX" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1520.8" y="691.304347826087"/>
        </scene>
        <!--VerificationVC-->
        <scene sceneID="Obg-vs-0tD">
            <objects>
                <viewController storyboardIdentifier="VerificationVC" id="KbX-dC-Lg7" customClass="VerificationVC" customModule="NXC_EV_Solutions" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="OF8-0G-JzO">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="g7K-8b-ECe">
                                <rect key="frame" x="19" y="563" width="337.5" height="44"/>
                                <color key="backgroundColor" name="PrimaryColor"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="44" id="JSd-P4-MFx"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                <state key="normal" title="VERIFY">
                                    <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </state>
                                <connections>
                                    <action selector="verifyAction:" destination="KbX-dC-Lg7" eventType="touchUpInside" id="CRj-XF-fD3"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" hasAttributedTitle="YES" translatesAutoresizingMaskIntoConstraints="NO" id="mVt-U9-VGT">
                                <rect key="frame" x="55.5" y="607" width="264" height="44"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="44" id="SCn-ev-Me1"/>
                                </constraints>
                                <state key="normal">
                                    <attributedString key="attributedTitle">
                                        <fragment content="Didn't receive the code?">
                                            <attributes>
                                                <color key="NSColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                <font key="NSFont" size="15" name="HelveticaNeue"/>
                                                <paragraphStyle key="NSParagraphStyle" alignment="left" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0" allowsDefaultTighteningForTruncation="NO"/>
                                            </attributes>
                                        </fragment>
                                        <fragment content=" ">
                                            <attributes>
                                                <color key="NSColor" red="0.0" green="0.47843000000000002" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                <font key="NSFont" size="15" name="HelveticaNeue"/>
                                                <paragraphStyle key="NSParagraphStyle" alignment="left" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0" allowsDefaultTighteningForTruncation="NO"/>
                                            </attributes>
                                        </fragment>
                                        <fragment content="Resend Code">
                                            <attributes>
                                                <color key="NSColor" red="0.015689999999999999" green="0.49412" blue="0.42745" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                <font key="NSFont" size="15" name="HelveticaNeue-Bold"/>
                                                <paragraphStyle key="NSParagraphStyle" alignment="left" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0" allowsDefaultTighteningForTruncation="NO"/>
                                                <integer key="NSUnderline" value="1"/>
                                                <color key="NSUnderlineColor" red="0.015689999999999999" green="0.49412" blue="0.42745" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            </attributes>
                                        </fragment>
                                    </attributedString>
                                </state>
                                <connections>
                                    <action selector="productByAction:" destination="3Pw-30-ghx" eventType="touchUpInside" id="LRi-th-zde"/>
                                    <action selector="resendAction:" destination="KbX-dC-Lg7" eventType="touchUpInside" id="xxY-Jq-Mcg"/>
                                </connections>
                            </button>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="00:00" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="PkP-Sx-oqw">
                                <rect key="frame" x="164" y="503" width="47.5" height="44"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="44" id="rEE-Bu-b4x"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" weight="medium" pointSize="17"/>
                                <color key="textColor" name="PrimaryDarkTextColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="oOl-B2-MqF">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="p9R-Xm-C45">
                                        <rect key="frame" x="12" y="5" width="34" height="34"/>
                                        <constraints>
                                            <constraint firstAttribute="width" secondItem="p9R-Xm-C45" secondAttribute="height" multiplier="1:1" id="bPl-DJ-Mx6"/>
                                            <constraint firstAttribute="height" constant="34" id="vUa-hX-bd5"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="8" minY="6" maxX="8" maxY="6"/>
                                        <state key="normal" image="ic_back_arrow"/>
                                        <connections>
                                            <action selector="backAction:" destination="KbX-dC-Lg7" eventType="touchUpInside" id="7ap-hM-2kh"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="JX2-re-hcm">
                                        <rect key="frame" x="187.5" y="22" width="0.0" height="0.0"/>
                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="44" id="WWA-nZ-bWS"/>
                                    <constraint firstItem="JX2-re-hcm" firstAttribute="centerY" secondItem="oOl-B2-MqF" secondAttribute="centerY" id="fYb-5C-2DJ"/>
                                    <constraint firstItem="p9R-Xm-C45" firstAttribute="leading" secondItem="oOl-B2-MqF" secondAttribute="leading" constant="12" id="gGI-aY-cYa"/>
                                    <constraint firstItem="p9R-Xm-C45" firstAttribute="centerY" secondItem="oOl-B2-MqF" secondAttribute="centerY" id="oy4-cs-o0g"/>
                                    <constraint firstItem="JX2-re-hcm" firstAttribute="centerX" secondItem="oOl-B2-MqF" secondAttribute="centerX" id="pPf-hw-buu"/>
                                </constraints>
                            </view>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_otp" translatesAutoresizingMaskIntoConstraints="NO" id="0r4-fr-kHZ">
                                <rect key="frame" x="0.0" y="126" width="375" height="167"/>
                            </imageView>
                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="6" translatesAutoresizingMaskIntoConstraints="NO" id="UAw-vd-DpI">
                                <rect key="frame" x="22.5" y="429" width="330" height="50"/>
                                <subviews>
                                    <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" textAlignment="center" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="FR1-MY-NNI">
                                        <rect key="frame" x="0.0" y="0.0" width="50" height="50"/>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <color key="tintColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="50" id="KZd-8U-ySI"/>
                                            <constraint firstAttribute="width" constant="50" id="mAC-gB-tvA"/>
                                        </constraints>
                                        <color key="textColor" name="PrimaryDarkTextColor"/>
                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="20"/>
                                        <textInputTraits key="textInputTraits" keyboardType="numberPad"/>
                                    </textField>
                                    <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" textAlignment="center" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="ehW-G1-3Bw">
                                        <rect key="frame" x="56" y="0.0" width="50" height="50"/>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <color key="tintColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <color key="textColor" name="PrimaryDarkTextColor"/>
                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="20"/>
                                        <textInputTraits key="textInputTraits" keyboardType="numberPad"/>
                                    </textField>
                                    <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" textAlignment="center" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="dWy-9A-Xe4">
                                        <rect key="frame" x="112" y="0.0" width="50" height="50"/>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <color key="tintColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <color key="textColor" name="PrimaryDarkTextColor"/>
                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="20"/>
                                        <textInputTraits key="textInputTraits" keyboardType="numberPad"/>
                                    </textField>
                                    <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" textAlignment="center" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="QKb-ri-wWA">
                                        <rect key="frame" x="168" y="0.0" width="50" height="50"/>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <color key="tintColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <color key="textColor" name="PrimaryDarkTextColor"/>
                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="20"/>
                                        <textInputTraits key="textInputTraits" keyboardType="numberPad"/>
                                    </textField>
                                    <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" textAlignment="center" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="Fas-Kh-NnY">
                                        <rect key="frame" x="224" y="0.0" width="50" height="50"/>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <color key="tintColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <color key="textColor" name="PrimaryDarkTextColor"/>
                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="20"/>
                                        <textInputTraits key="textInputTraits" keyboardType="numberPad"/>
                                    </textField>
                                    <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" textAlignment="center" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="yKv-VB-xZW">
                                        <rect key="frame" x="280" y="0.0" width="50" height="50"/>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <color key="tintColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <color key="textColor" name="PrimaryDarkTextColor"/>
                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="20"/>
                                        <textInputTraits key="textInputTraits" keyboardType="numberPad"/>
                                    </textField>
                                </subviews>
                            </stackView>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="fta-wp-XfG">
                                <rect key="frame" x="19" y="52" width="337.5" height="58"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Phone Verification" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Fwq-EM-eB0">
                                        <rect key="frame" x="82.5" y="0.0" width="172" height="34"/>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="34" id="Un5-xC-jVt"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="boldSystem" pointSize="20"/>
                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Enter your 6-digit OTP code" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="fXi-iP-hVx">
                                        <rect key="frame" x="76" y="34" width="185.5" height="24"/>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="24" id="KvZ-RQ-Gap"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" weight="medium" pointSize="14"/>
                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                            </stackView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="LET-kX-4wF"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="LET-kX-4wF" firstAttribute="bottom" secondItem="mVt-U9-VGT" secondAttribute="bottom" constant="16" id="1bU-3x-Ocf"/>
                            <constraint firstItem="0r4-fr-kHZ" firstAttribute="top" secondItem="fta-wp-XfG" secondAttribute="bottom" constant="16" id="69i-EB-O9g"/>
                            <constraint firstItem="mVt-U9-VGT" firstAttribute="top" secondItem="g7K-8b-ECe" secondAttribute="bottom" id="9Aq-Kp-xGC"/>
                            <constraint firstItem="0r4-fr-kHZ" firstAttribute="width" secondItem="LET-kX-4wF" secondAttribute="width" id="9rE-EK-Jbt"/>
                            <constraint firstItem="PkP-Sx-oqw" firstAttribute="centerX" secondItem="OF8-0G-JzO" secondAttribute="centerX" id="H9d-I5-TEs"/>
                            <constraint firstItem="0r4-fr-kHZ" firstAttribute="height" secondItem="LET-kX-4wF" secondAttribute="height" multiplier="0.25" id="JJT-0r-Njk"/>
                            <constraint firstItem="PkP-Sx-oqw" firstAttribute="top" secondItem="UAw-vd-DpI" secondAttribute="bottom" constant="24" id="KDu-Jt-waf"/>
                            <constraint firstItem="oOl-B2-MqF" firstAttribute="leading" secondItem="LET-kX-4wF" secondAttribute="leading" id="LeN-hx-7jD"/>
                            <constraint firstItem="UAw-vd-DpI" firstAttribute="centerX" secondItem="OF8-0G-JzO" secondAttribute="centerX" id="MK9-OS-4N7"/>
                            <constraint firstItem="g7K-8b-ECe" firstAttribute="top" secondItem="PkP-Sx-oqw" secondAttribute="bottom" constant="16" id="O1F-ds-puw"/>
                            <constraint firstItem="fta-wp-XfG" firstAttribute="top" secondItem="oOl-B2-MqF" secondAttribute="bottom" constant="8" id="OWH-EL-IAx"/>
                            <constraint firstItem="g7K-8b-ECe" firstAttribute="centerX" secondItem="OF8-0G-JzO" secondAttribute="centerX" id="QMM-wh-TgJ"/>
                            <constraint firstItem="oOl-B2-MqF" firstAttribute="trailing" secondItem="LET-kX-4wF" secondAttribute="trailing" id="XGa-L0-f8F"/>
                            <constraint firstItem="fta-wp-XfG" firstAttribute="centerX" secondItem="OF8-0G-JzO" secondAttribute="centerX" id="aeD-uc-A7c"/>
                            <constraint firstItem="0r4-fr-kHZ" firstAttribute="centerX" secondItem="OF8-0G-JzO" secondAttribute="centerX" id="c0o-uw-8JJ"/>
                            <constraint firstItem="g7K-8b-ECe" firstAttribute="width" secondItem="LET-kX-4wF" secondAttribute="width" multiplier="0.9" id="kIK-yu-jF8"/>
                            <constraint firstItem="oOl-B2-MqF" firstAttribute="top" secondItem="LET-kX-4wF" secondAttribute="top" id="lDt-Fr-q4P"/>
                            <constraint firstItem="mVt-U9-VGT" firstAttribute="centerX" secondItem="OF8-0G-JzO" secondAttribute="centerX" id="suP-xB-n8W"/>
                            <constraint firstItem="fta-wp-XfG" firstAttribute="width" secondItem="LET-kX-4wF" secondAttribute="width" multiplier="0.9" id="tzH-6z-MH2"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="btnBack" destination="p9R-Xm-C45" id="ieh-Vn-vN3"/>
                        <outlet property="btnResendOTP" destination="mVt-U9-VGT" id="zCo-SG-U7b"/>
                        <outlet property="btnVerify" destination="g7K-8b-ECe" id="T98-ap-br8"/>
                        <outlet property="lblTimer" destination="PkP-Sx-oqw" id="okr-tD-uzU"/>
                        <outlet property="txtOTP1" destination="FR1-MY-NNI" id="fn8-aA-lQV"/>
                        <outlet property="txtOTP2" destination="ehW-G1-3Bw" id="cgx-NI-pzj"/>
                        <outlet property="txtOTP3" destination="dWy-9A-Xe4" id="oFV-iU-IW5"/>
                        <outlet property="txtOTP4" destination="QKb-ri-wWA" id="vyP-ue-cbP"/>
                        <outlet property="txtOTP5" destination="Fas-Kh-NnY" id="MgS-5j-bVo"/>
                        <outlet property="txtOTP6" destination="yKv-VB-xZW" id="1dF-Av-XRS"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="uq0-Ff-8Ws" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="2210" y="-45"/>
        </scene>
        <!--Rear View Controller-->
        <scene sceneID="I7X-3n-g3C">
            <objects>
                <viewController storyboardIdentifier="RearViewController" id="TO3-WI-z9E" customClass="RearViewController" customModule="NXC_EV_Solutions" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="THS-5X-K2M">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Cg1-9s-ul6">
                                <rect key="frame" x="0.0" y="0.0" width="300" height="667"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="txq-sA-ieU">
                                        <rect key="frame" x="10" y="20" width="280" height="30"/>
                                        <color key="backgroundColor" red="1" green="0.050980392159999999" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <state key="normal" title="Open View 1"/>
                                        <connections>
                                            <action selector="openViewBtnAction:" destination="TO3-WI-z9E" eventType="touchUpInside" id="oCN-MS-SsU"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="3aj-Xj-sEk">
                                        <rect key="frame" x="8" y="65" width="282" height="30"/>
                                        <color key="backgroundColor" red="0.99942404029999998" green="0.98555368190000003" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <state key="normal" title="Open View 2"/>
                                        <connections>
                                            <action selector="openViewTwoBtnAction:" destination="TO3-WI-z9E" eventType="touchUpInside" id="8ct-zU-b0a"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="txq-sA-ieU" firstAttribute="top" secondItem="Cg1-9s-ul6" secondAttribute="top" constant="20" id="1Pn-jw-rxG"/>
                                    <constraint firstItem="txq-sA-ieU" firstAttribute="leading" secondItem="Cg1-9s-ul6" secondAttribute="leading" constant="10" id="JDJ-uH-gTI"/>
                                    <constraint firstAttribute="trailing" secondItem="3aj-Xj-sEk" secondAttribute="trailing" constant="10" id="OcL-4Z-h25"/>
                                    <constraint firstItem="3aj-Xj-sEk" firstAttribute="top" secondItem="txq-sA-ieU" secondAttribute="bottom" constant="15" id="Yby-Ta-lqf"/>
                                    <constraint firstItem="3aj-Xj-sEk" firstAttribute="leading" secondItem="Cg1-9s-ul6" secondAttribute="leading" constant="8" id="cRT-29-DBz"/>
                                    <constraint firstAttribute="trailing" secondItem="txq-sA-ieU" secondAttribute="trailing" constant="10" id="rOO-v7-y9i"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="sET-zs-j5o"/>
                        <color key="backgroundColor" red="0.99999600649999998" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstItem="sET-zs-j5o" firstAttribute="bottom" secondItem="Cg1-9s-ul6" secondAttribute="bottom" id="3cW-c8-sYY"/>
                            <constraint firstItem="Cg1-9s-ul6" firstAttribute="leading" secondItem="sET-zs-j5o" secondAttribute="leading" id="BbH-UY-ofC"/>
                            <constraint firstItem="Cg1-9s-ul6" firstAttribute="top" secondItem="sET-zs-j5o" secondAttribute="top" id="Nvs-6l-udY"/>
                            <constraint firstItem="Cg1-9s-ul6" firstAttribute="width" secondItem="THS-5X-K2M" secondAttribute="width" multiplier="0.8" id="Ydc-Wg-1zs"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="5Mu-qE-0Sr" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="822" y="693"/>
        </scene>
        <!--View Controller-->
        <scene sceneID="YcI-hF-QgG">
            <objects>
                <viewController storyboardIdentifier="ViewController" id="Nhx-xe-p1N" customClass="ViewController" customModule="NXC_EV_Solutions" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="fp6-Mr-vAY">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="teH-Lc-wP6">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="3HE-BP-oTK">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                <color key="backgroundColor" red="0.99999600649999998" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="Mqv-B6-Qik"/>
                        <color key="backgroundColor" red="0.83741801979999997" green="0.83743780850000005" blue="0.83742713930000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstAttribute="bottom" secondItem="teH-Lc-wP6" secondAttribute="bottom" id="1RK-Dy-w2f"/>
                            <constraint firstItem="teH-Lc-wP6" firstAttribute="leading" secondItem="fp6-Mr-vAY" secondAttribute="leading" id="3ph-uY-20D"/>
                            <constraint firstItem="3HE-BP-oTK" firstAttribute="top" secondItem="fp6-Mr-vAY" secondAttribute="top" id="7BM-62-vmZ"/>
                            <constraint firstAttribute="trailing" secondItem="teH-Lc-wP6" secondAttribute="trailing" id="Aox-Pp-Q5y"/>
                            <constraint firstItem="3HE-BP-oTK" firstAttribute="leading" secondItem="fp6-Mr-vAY" secondAttribute="leading" id="PYw-WY-RCV"/>
                            <constraint firstItem="teH-Lc-wP6" firstAttribute="top" secondItem="fp6-Mr-vAY" secondAttribute="top" id="TeO-gZ-xTU"/>
                            <constraint firstAttribute="bottom" secondItem="3HE-BP-oTK" secondAttribute="bottom" id="Y7S-YL-eZc"/>
                            <constraint firstAttribute="trailing" secondItem="3HE-BP-oTK" secondAttribute="trailing" id="Yvp-Mv-hgr"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="dgS-Hr-X3v"/>
                    <connections>
                        <outlet property="sideMenuView" destination="teH-Lc-wP6" id="OH0-vz-7FU"/>
                        <outlet property="tabbarContainerView" destination="3HE-BP-oTK" id="lNv-Qa-uWV"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="od5-mr-6fx" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-666" y="719"/>
        </scene>
    </scenes>
    <resources>
        <image name="ic_back_arrow" width="64" height="64"/>
        <image name="ic_login_Img" width="500" height="365"/>
        <image name="ic_logout" width="24" height="24"/>
        <image name="ic_new_nxc_logo" width="300" height="161"/>
        <image name="ic_otp" width="508" height="505"/>
        <image name="ic_smartphone" width="400" height="400"/>
        <image name="ic_splash_img" width="500" height="321"/>
        <namedColor name="Primary">
            <color red="0.016000000759959221" green="0.49399998784065247" blue="0.42699998617172241" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="PrimaryColor">
            <color red="0.015686274509803921" green="0.49411764705882355" blue="0.42745098039215684" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="PrimaryDarkBGColor">
            <color red="0.90196078431372551" green="0.94901960784313721" blue="0.94117647058823528" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="PrimaryDarkTextColor">
            <color red="0.0080000003799796104" green="0.24699999392032623" blue="0.21600000560283661" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>

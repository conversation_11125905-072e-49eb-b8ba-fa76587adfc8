<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="19455" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="19454"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--User InfoVC-->
        <scene sceneID="vN5-d1-ifU">
            <objects>
                <viewController storyboardIdentifier="UserInfoVC" id="Xhu-Eh-62f" customClass="UserInfoVC" customModule="NXC_EV_Solutions" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="R32-p3-274">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" scrollEnabled="NO" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" translatesAutoresizingMaskIntoConstraints="NO" id="bEv-8v-oBp">
                                <rect key="frame" x="0.0" y="53.5" width="375" height="613.5"/>
                                <subviews>
                                    <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="hj6-ZG-wQG">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="600.5"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Personal Details (1/2)" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="jgh-Kc-Mq2">
                                                <rect key="frame" x="16" y="8" width="173.5" height="34"/>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="34" id="hcZ-fx-s9l"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="boldSystem" pointSize="17"/>
                                                <color key="textColor" name="PrimaryColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="Qdh-Bn-X9g">
                                                <rect key="frame" x="19" y="54" width="337.5" height="57"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="1" translatesAutoresizingMaskIntoConstraints="NO" id="VbW-KR-XXt">
                                                        <rect key="frame" x="0.0" y="0.0" width="164.5" height="57"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="First Name" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="kpu-WF-PPy">
                                                                <rect key="frame" x="0.0" y="0.0" width="164.5" height="20"/>
                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="20" id="cpX-qr-vaM"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                <color key="textColor" name="GrayPlaceholder"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="RYC-mZ-Q5i">
                                                                <rect key="frame" x="0.0" y="21" width="164.5" height="34"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="34" id="ZaX-db-Rgx"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                <textInputTraits key="textInputTraits" autocapitalizationType="words"/>
                                                            </textField>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Ljv-Ur-qW1">
                                                                <rect key="frame" x="0.0" y="56" width="164.5" height="1"/>
                                                                <color key="backgroundColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="1" id="vVo-dZ-9Ga"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                <color key="textColor" white="0.33333333333333331" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                    </stackView>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="1" translatesAutoresizingMaskIntoConstraints="NO" id="2tN-jK-Tgs">
                                                        <rect key="frame" x="172.5" y="0.0" width="165" height="57"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Last Name" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="60l-Xb-2NW">
                                                                <rect key="frame" x="0.0" y="0.0" width="165" height="20"/>
                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="20" id="gaD-5z-bC3"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                <color key="textColor" name="GrayPlaceholder"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="ws3-Qa-Jn4">
                                                                <rect key="frame" x="0.0" y="21" width="165" height="34"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="34" id="WIG-nC-WQn"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                <textInputTraits key="textInputTraits" autocapitalizationType="words"/>
                                                            </textField>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="aZa-eE-Xbh">
                                                                <rect key="frame" x="0.0" y="56" width="165" height="1"/>
                                                                <color key="backgroundColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="1" id="kAS-Db-P80"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                <color key="textColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                    </stackView>
                                                </subviews>
                                            </stackView>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="*Optional" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="xjD-CF-4Ej">
                                                <rect key="frame" x="16" y="123" width="62.5" height="24"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="24" id="scE-E1-Zu5"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" weight="medium" pointSize="14"/>
                                                <color key="textColor" name="PrimaryColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" alignment="top" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="TIJ-B9-yez">
                                                <rect key="frame" x="19" y="159" width="337.5" height="264"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="1" translatesAutoresizingMaskIntoConstraints="NO" id="1rA-Xt-JLR">
                                                        <rect key="frame" x="0.0" y="0.0" width="337.5" height="57"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Email" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="gWq-yy-aqC">
                                                                <rect key="frame" x="0.0" y="0.0" width="337.5" height="20"/>
                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="20" id="AWg-ck-bhu"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                <color key="textColor" name="GrayPlaceholder"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="0fm-ly-b8b">
                                                                <rect key="frame" x="0.0" y="21" width="337.5" height="34"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="34" id="zRn-p6-r5p"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                <textInputTraits key="textInputTraits" keyboardType="emailAddress"/>
                                                            </textField>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Dhf-7L-ivn">
                                                                <rect key="frame" x="0.0" y="56" width="337.5" height="1"/>
                                                                <color key="backgroundColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="1" id="U6a-sg-iRV"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                <color key="textColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                    </stackView>
                                                    <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="24" translatesAutoresizingMaskIntoConstraints="NO" id="eZG-H1-cDe">
                                                        <rect key="frame" x="0.0" y="69" width="179" height="57"/>
                                                        <subviews>
                                                            <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="RNX-e2-neQ">
                                                                <rect key="frame" x="0.0" y="0.0" width="77.5" height="57"/>
                                                                <subviews>
                                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" tag="104" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_uncheck" highlightedImage="ic_checked" translatesAutoresizingMaskIntoConstraints="NO" id="dv7-Mj-yj1">
                                                                        <rect key="frame" x="0.0" y="16.5" width="24" height="24"/>
                                                                        <color key="tintColor" name="PrimaryTextColorLight"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="width" constant="24" id="04K-N5-P0J"/>
                                                                            <constraint firstAttribute="height" constant="24" id="z4t-aL-f9w"/>
                                                                        </constraints>
                                                                    </imageView>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Male" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="kzb-TE-kQK">
                                                                        <rect key="frame" x="28" y="11.5" width="49.5" height="34"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="34" id="dhs-R4-ano"/>
                                                                        </constraints>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                            <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="xa7-Ky-1So">
                                                                <rect key="frame" x="101.5" y="0.0" width="77.5" height="57"/>
                                                                <subviews>
                                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" tag="104" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_uncheck" highlightedImage="ic_checked" translatesAutoresizingMaskIntoConstraints="NO" id="6dH-lx-adv">
                                                                        <rect key="frame" x="0.0" y="16.5" width="24" height="24"/>
                                                                        <color key="tintColor" name="PrimaryTextColorLight"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="width" constant="24" id="glO-U0-zHc"/>
                                                                            <constraint firstAttribute="height" constant="24" id="kpf-o7-3wj"/>
                                                                        </constraints>
                                                                    </imageView>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Female" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="xp7-JD-Lyl">
                                                                        <rect key="frame" x="28" y="11.5" width="49.5" height="34"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="34" id="CgW-SZ-FdO"/>
                                                                        </constraints>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                        </subviews>
                                                    </stackView>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="1" translatesAutoresizingMaskIntoConstraints="NO" id="z30-4m-494">
                                                        <rect key="frame" x="0.0" y="138" width="337.5" height="57"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Date of Birth" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="l3T-kB-x6A">
                                                                <rect key="frame" x="0.0" y="0.0" width="337.5" height="20"/>
                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="20" id="AQF-gk-mDs"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                <color key="textColor" name="GrayPlaceholder"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="vQF-2M-A6g">
                                                                <rect key="frame" x="0.0" y="21" width="337.5" height="34"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="34" id="Mmc-vY-Idt"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                <textInputTraits key="textInputTraits"/>
                                                            </textField>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="9ne-Mq-U1s">
                                                                <rect key="frame" x="0.0" y="56" width="337.5" height="1"/>
                                                                <color key="backgroundColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="1" id="tn2-fW-IUI"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                <color key="textColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                    </stackView>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="1" translatesAutoresizingMaskIntoConstraints="NO" id="xvi-83-vxp">
                                                        <rect key="frame" x="0.0" y="207" width="337.5" height="57"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="GST No." textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="szO-yi-sLB">
                                                                <rect key="frame" x="0.0" y="0.0" width="337.5" height="20"/>
                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="20" id="sSg-G4-rmN"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                <color key="textColor" name="GrayPlaceholder"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="7g8-Yd-wZr">
                                                                <rect key="frame" x="0.0" y="21" width="337.5" height="34"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="34" id="eAA-lT-Eec"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                <textInputTraits key="textInputTraits"/>
                                                            </textField>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="QPX-hj-tB2">
                                                                <rect key="frame" x="0.0" y="56" width="337.5" height="1"/>
                                                                <color key="backgroundColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="1" id="1iW-jB-6Bk"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                <color key="textColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                    </stackView>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstItem="xvi-83-vxp" firstAttribute="width" secondItem="TIJ-B9-yez" secondAttribute="width" id="Cwl-Iy-e2P"/>
                                                    <constraint firstItem="z30-4m-494" firstAttribute="width" secondItem="TIJ-B9-yez" secondAttribute="width" id="bCg-eF-5fs"/>
                                                    <constraint firstItem="1rA-Xt-JLR" firstAttribute="width" secondItem="TIJ-B9-yez" secondAttribute="width" id="xSk-hf-9UV"/>
                                                </constraints>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="jgh-Kc-Mq2" firstAttribute="leading" secondItem="hj6-ZG-wQG" secondAttribute="leading" constant="16" id="4fk-j4-Xer"/>
                                            <constraint firstItem="xjD-CF-4Ej" firstAttribute="leading" secondItem="hj6-ZG-wQG" secondAttribute="leading" constant="16" id="7bu-8m-4gK"/>
                                            <constraint firstItem="Qdh-Bn-X9g" firstAttribute="centerX" secondItem="hj6-ZG-wQG" secondAttribute="centerX" id="AeP-mP-s2O"/>
                                            <constraint firstItem="Qdh-Bn-X9g" firstAttribute="top" secondItem="jgh-Kc-Mq2" secondAttribute="bottom" constant="12" id="BDs-HJ-nka"/>
                                            <constraint firstItem="jgh-Kc-Mq2" firstAttribute="top" secondItem="hj6-ZG-wQG" secondAttribute="top" constant="8" id="VKF-xU-MLu"/>
                                            <constraint firstItem="TIJ-B9-yez" firstAttribute="centerX" secondItem="hj6-ZG-wQG" secondAttribute="centerX" id="Vs0-iY-NVS"/>
                                            <constraint firstAttribute="height" constant="600.5" id="hA9-2o-3kQ"/>
                                            <constraint firstItem="TIJ-B9-yez" firstAttribute="top" secondItem="xjD-CF-4Ej" secondAttribute="bottom" constant="12" id="rmR-Oo-7Vq"/>
                                            <constraint firstItem="Qdh-Bn-X9g" firstAttribute="width" secondItem="hj6-ZG-wQG" secondAttribute="width" multiplier="0.9" id="sGz-kA-al9"/>
                                            <constraint firstItem="TIJ-B9-yez" firstAttribute="width" secondItem="hj6-ZG-wQG" secondAttribute="width" multiplier="0.9" id="zI8-gt-x3K"/>
                                            <constraint firstItem="xjD-CF-4Ej" firstAttribute="top" secondItem="Qdh-Bn-X9g" secondAttribute="bottom" constant="12" id="zdK-ns-Nch"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="hj6-ZG-wQG" firstAttribute="leading" secondItem="uim-SE-Oej" secondAttribute="leading" id="7IE-0l-xMe"/>
                                    <constraint firstItem="hj6-ZG-wQG" firstAttribute="top" secondItem="uim-SE-Oej" secondAttribute="top" id="Iqf-UY-mzy"/>
                                    <constraint firstAttribute="trailing" secondItem="hj6-ZG-wQG" secondAttribute="trailing" id="PMM-Gk-Y1H"/>
                                    <constraint firstAttribute="bottom" secondItem="hj6-ZG-wQG" secondAttribute="bottom" id="Y2z-d0-a3j"/>
                                    <constraint firstItem="hj6-ZG-wQG" firstAttribute="centerX" secondItem="bEv-8v-oBp" secondAttribute="centerX" id="al2-Fo-kds"/>
                                </constraints>
                                <viewLayoutGuide key="contentLayoutGuide" id="E0m-Y1-MAw"/>
                                <viewLayoutGuide key="frameLayoutGuide" id="uim-SE-Oej"/>
                            </scrollView>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="x3d-4q-R1Q">
                                <rect key="frame" x="0.0" y="3" width="44" height="44"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="44" id="rB6-MP-s9z"/>
                                    <constraint firstAttribute="width" constant="44" id="sf6-gj-S7a"/>
                                </constraints>
                                <inset key="imageEdgeInsets" minX="12" minY="12" maxX="12" maxY="12"/>
                                <state key="normal" image="ic_backarrow"/>
                                <connections>
                                    <action selector="backAction:" destination="Xhu-Eh-62f" eventType="touchUpInside" id="CfF-S2-bUi"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="AcT-rR-aa7">
                                <rect key="frame" x="19" y="350.5" width="337.5" height="57"/>
                                <inset key="imageEdgeInsets" minX="12" minY="12" maxX="12" maxY="12"/>
                                <connections>
                                    <action selector="dobTapped:" destination="Xhu-Eh-62f" eventType="touchUpInside" id="3dD-Ou-iqK"/>
                                </connections>
                            </button>
                            <button opaque="NO" tag="101" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="d3D-Fk-Agk">
                                <rect key="frame" x="19" y="281.5" width="77.5" height="57"/>
                                <inset key="imageEdgeInsets" minX="12" minY="12" maxX="12" maxY="12"/>
                                <connections>
                                    <action selector="genderTapped:" destination="Xhu-Eh-62f" eventType="touchUpInside" id="aOw-tw-DYd"/>
                                </connections>
                            </button>
                            <button opaque="NO" tag="102" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="VHu-W4-0T7">
                                <rect key="frame" x="120.5" y="281.5" width="77.5" height="57"/>
                                <inset key="imageEdgeInsets" minX="12" minY="12" maxX="12" maxY="12"/>
                                <connections>
                                    <action selector="genderTapped:" destination="Xhu-Eh-62f" eventType="touchUpInside" id="YVl-Mh-eGG"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="zf8-xd-SfU">
                                <rect key="frame" x="307" y="599" width="44" height="44"/>
                                <color key="backgroundColor" name="PrimaryColor"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="44" id="18I-Zk-eAR"/>
                                    <constraint firstAttribute="height" constant="44" id="BaT-Jc-rav"/>
                                </constraints>
                                <inset key="imageEdgeInsets" minX="11" minY="11" maxX="11" maxY="11"/>
                                <state key="normal" image="ic_next"/>
                                <connections>
                                    <action selector="nextAction:" destination="Xhu-Eh-62f" eventType="touchUpInside" id="gdd-xh-cRW"/>
                                </connections>
                            </button>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="aUx-KS-AcW">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="nOV-Rl-KxT">
                                        <rect key="frame" x="19" y="192" width="337.5" height="283.5"/>
                                        <subviews>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="cUs-o0-s9S">
                                                <rect key="frame" x="295.5" y="8" width="34" height="34"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="34" id="ZjN-ET-cPU"/>
                                                    <constraint firstAttribute="height" constant="34" id="fAK-1Q-xrK"/>
                                                </constraints>
                                                <inset key="imageEdgeInsets" minX="10" minY="10" maxX="10" maxY="10"/>
                                                <state key="normal" image="ic_cancel"/>
                                                <connections>
                                                    <action selector="cancelAction:" destination="Xhu-Eh-62f" eventType="touchUpInside" id="6pS-hl-2Xl"/>
                                                    <action selector="cancelRefundAction:" destination="NTg-DK-shC" eventType="touchUpInside" id="Rjt-g7-t4B"/>
                                                </connections>
                                            </button>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Select Date Of Birth" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="NUF-Tq-vT7">
                                                <rect key="frame" x="85.5" y="16" width="166.5" height="21.5"/>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="18"/>
                                                <color key="textColor" name="PrimaryColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="20" translatesAutoresizingMaskIntoConstraints="NO" id="NyJ-yW-KuO">
                                                <rect key="frame" x="16" y="53.5" width="305.5" height="214"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" alignment="center" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="JjB-3w-lnt">
                                                        <rect key="frame" x="0.0" y="0.0" width="305" height="150"/>
                                                        <subviews>
                                                            <datePicker contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" datePickerMode="date" style="wheels" translatesAutoresizingMaskIntoConstraints="NO" id="FPH-a8-BkH">
                                                                <rect key="frame" x="0.0" y="0.0" width="305" height="150"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="150" id="Olc-m4-dDX"/>
                                                                </constraints>
                                                            </datePicker>
                                                        </subviews>
                                                    </stackView>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="24" translatesAutoresizingMaskIntoConstraints="NO" id="t6f-8r-8hK">
                                                        <rect key="frame" x="15" y="170" width="275" height="44"/>
                                                        <subviews>
                                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="bhh-ek-gIt">
                                                                <rect key="frame" x="0.0" y="0.0" width="275" height="44"/>
                                                                <subviews>
                                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="1X7-WE-zP1">
                                                                        <rect key="frame" x="0.0" y="0.0" width="275" height="44"/>
                                                                        <color key="backgroundColor" name="PrimaryColor"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="44" id="JBn-U2-Ql1"/>
                                                                        </constraints>
                                                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                                                        <state key="normal" title="OK">
                                                                            <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        </state>
                                                                        <connections>
                                                                            <action selector="OKAction:" destination="Xhu-Eh-62f" eventType="touchUpInside" id="yPm-bg-YDA"/>
                                                                        </connections>
                                                                    </button>
                                                                </subviews>
                                                            </stackView>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstItem="bhh-ek-gIt" firstAttribute="width" secondItem="t6f-8r-8hK" secondAttribute="width" id="dog-kT-Zg5"/>
                                                        </constraints>
                                                    </stackView>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstItem="JjB-3w-lnt" firstAttribute="width" secondItem="NyJ-yW-KuO" secondAttribute="width" multiplier="0.99844" id="Vma-6j-8wX"/>
                                                    <constraint firstItem="t6f-8r-8hK" firstAttribute="width" secondItem="NyJ-yW-KuO" secondAttribute="width" multiplier="0.9" id="yIc-bv-NiG"/>
                                                </constraints>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstItem="cUs-o0-s9S" firstAttribute="top" secondItem="nOV-Rl-KxT" secondAttribute="top" constant="8" id="87D-R0-riL"/>
                                            <constraint firstAttribute="bottom" secondItem="NyJ-yW-KuO" secondAttribute="bottom" constant="16" id="8Lc-fA-uWR"/>
                                            <constraint firstItem="NyJ-yW-KuO" firstAttribute="top" secondItem="NUF-Tq-vT7" secondAttribute="bottom" constant="16" id="9Zs-dD-rkw"/>
                                            <constraint firstItem="NyJ-yW-KuO" firstAttribute="width" secondItem="nOV-Rl-KxT" secondAttribute="width" multiplier="0.905185" id="AuB-z2-rQ8"/>
                                            <constraint firstItem="NyJ-yW-KuO" firstAttribute="centerX" secondItem="nOV-Rl-KxT" secondAttribute="centerX" id="ENv-JI-Gfk"/>
                                            <constraint firstItem="NUF-Tq-vT7" firstAttribute="centerX" secondItem="nOV-Rl-KxT" secondAttribute="centerX" id="FGi-Ac-7N0"/>
                                            <constraint firstItem="NUF-Tq-vT7" firstAttribute="top" secondItem="nOV-Rl-KxT" secondAttribute="top" constant="16" id="FgP-1D-ao2"/>
                                            <constraint firstAttribute="trailing" secondItem="cUs-o0-s9S" secondAttribute="trailing" constant="8" id="gGm-A8-VY0"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.75" colorSpace="custom" customColorSpace="sRGB"/>
                                <constraints>
                                    <constraint firstItem="nOV-Rl-KxT" firstAttribute="width" secondItem="aUx-KS-AcW" secondAttribute="width" multiplier="0.9" id="HcO-Xb-Y6f"/>
                                    <constraint firstItem="nOV-Rl-KxT" firstAttribute="centerY" secondItem="aUx-KS-AcW" secondAttribute="centerY" id="Ifo-Z1-dV4"/>
                                    <constraint firstItem="nOV-Rl-KxT" firstAttribute="centerX" secondItem="aUx-KS-AcW" secondAttribute="centerX" id="U6f-9N-AOR"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="s4Y-6h-3EL"/>
                        <color key="backgroundColor" name="PrimaryColor"/>
                        <constraints>
                            <constraint firstItem="d3D-Fk-Agk" firstAttribute="width" secondItem="RNX-e2-neQ" secondAttribute="width" id="2BT-Y0-fSo"/>
                            <constraint firstItem="AcT-rR-aa7" firstAttribute="width" secondItem="z30-4m-494" secondAttribute="width" id="3Q7-7b-q6Q"/>
                            <constraint firstItem="aUx-KS-AcW" firstAttribute="trailing" secondItem="s4Y-6h-3EL" secondAttribute="trailing" id="4aX-MA-Zce"/>
                            <constraint firstItem="s4Y-6h-3EL" firstAttribute="trailing" secondItem="zf8-xd-SfU" secondAttribute="trailing" constant="24" id="7PW-fs-2LL"/>
                            <constraint firstItem="VHu-W4-0T7" firstAttribute="height" secondItem="xa7-Ky-1So" secondAttribute="height" id="DJz-iF-DRa"/>
                            <constraint firstItem="d3D-Fk-Agk" firstAttribute="height" secondItem="RNX-e2-neQ" secondAttribute="height" id="DpD-D0-e8K"/>
                            <constraint firstItem="s4Y-6h-3EL" firstAttribute="trailing" secondItem="bEv-8v-oBp" secondAttribute="trailing" id="Io4-4s-kRt"/>
                            <constraint firstItem="d3D-Fk-Agk" firstAttribute="centerX" secondItem="RNX-e2-neQ" secondAttribute="centerX" id="NqH-GA-gQG"/>
                            <constraint firstItem="d3D-Fk-Agk" firstAttribute="centerY" secondItem="RNX-e2-neQ" secondAttribute="centerY" id="OL7-js-tss"/>
                            <constraint firstItem="aUx-KS-AcW" firstAttribute="leading" secondItem="s4Y-6h-3EL" secondAttribute="leading" id="P3A-zC-VQQ"/>
                            <constraint firstItem="AcT-rR-aa7" firstAttribute="height" secondItem="z30-4m-494" secondAttribute="height" id="Qa6-YQ-R8W"/>
                            <constraint firstItem="x3d-4q-R1Q" firstAttribute="top" secondItem="s4Y-6h-3EL" secondAttribute="top" constant="3" id="U33-3u-3mo"/>
                            <constraint firstItem="VHu-W4-0T7" firstAttribute="centerY" secondItem="xa7-Ky-1So" secondAttribute="centerY" id="Xm6-cF-re5"/>
                            <constraint firstItem="x3d-4q-R1Q" firstAttribute="leading" secondItem="s4Y-6h-3EL" secondAttribute="leading" id="fov-dv-rK0"/>
                            <constraint firstItem="bEv-8v-oBp" firstAttribute="leading" secondItem="s4Y-6h-3EL" secondAttribute="leading" id="hgA-4w-lCh"/>
                            <constraint firstItem="AcT-rR-aa7" firstAttribute="centerY" secondItem="z30-4m-494" secondAttribute="centerY" id="hhe-Pl-Qd0"/>
                            <constraint firstItem="aUx-KS-AcW" firstAttribute="bottom" secondItem="s4Y-6h-3EL" secondAttribute="bottom" id="iwe-1k-QQe"/>
                            <constraint firstItem="bEv-8v-oBp" firstAttribute="height" secondItem="s4Y-6h-3EL" secondAttribute="height" multiplier="0.92" id="j75-fV-PAW"/>
                            <constraint firstItem="AcT-rR-aa7" firstAttribute="centerX" secondItem="z30-4m-494" secondAttribute="centerX" id="jdg-tT-dSL"/>
                            <constraint firstItem="s4Y-6h-3EL" firstAttribute="bottom" secondItem="zf8-xd-SfU" secondAttribute="bottom" constant="24" id="n0U-lg-6LI"/>
                            <constraint firstItem="VHu-W4-0T7" firstAttribute="centerX" secondItem="xa7-Ky-1So" secondAttribute="centerX" id="nYe-4S-pGU"/>
                            <constraint firstItem="s4Y-6h-3EL" firstAttribute="bottom" secondItem="bEv-8v-oBp" secondAttribute="bottom" id="qNK-py-xLs"/>
                            <constraint firstItem="aUx-KS-AcW" firstAttribute="top" secondItem="s4Y-6h-3EL" secondAttribute="top" id="sfW-8V-3Zx"/>
                            <constraint firstItem="VHu-W4-0T7" firstAttribute="width" secondItem="xa7-Ky-1So" secondAttribute="width" id="wwt-kg-1zn"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="DOBButton" destination="AcT-rR-aa7" id="ioc-Vy-Q85"/>
                        <outlet property="btnNext" destination="zf8-xd-SfU" id="ypm-SF-fqu"/>
                        <outlet property="btnOK" destination="1X7-WE-zP1" id="Nmg-fw-suz"/>
                        <outlet property="datePicker" destination="FPH-a8-BkH" id="Qod-Tu-z5k"/>
                        <outlet property="imgFemale" destination="6dH-lx-adv" id="fvC-ZI-YQ1"/>
                        <outlet property="imgMale" destination="dv7-Mj-yj1" id="PA8-mI-3gu"/>
                        <outlet property="scrollDetails" destination="bEv-8v-oBp" id="aS2-4N-XSY"/>
                        <outlet property="txtDOB" destination="vQF-2M-A6g" id="1g7-lj-Euc"/>
                        <outlet property="txtEmail" destination="0fm-ly-b8b" id="Qn6-ln-D3q"/>
                        <outlet property="txtFirstName" destination="RYC-mZ-Q5i" id="SqZ-vO-olw"/>
                        <outlet property="txtGST" destination="7g8-Yd-wZr" id="dw4-fy-pcS"/>
                        <outlet property="txtLastName" destination="ws3-Qa-Jn4" id="iRq-R5-0Km"/>
                        <outlet property="viewBg" destination="aUx-KS-AcW" id="67f-fH-ehX"/>
                        <outlet property="viewMain" destination="nOV-Rl-KxT" id="cfO-3w-ZkE"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="tZw-ZO-1xg" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-385" y="133"/>
        </scene>
        <!--Profile DetailsVC-->
        <scene sceneID="FVo-iE-2yI">
            <objects>
                <viewController storyboardIdentifier="ProfileDetailsVC" id="NTg-DK-shC" customClass="ProfileDetailsVC" customModule="NXC_EV_Solutions" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="mrL-F8-iu0">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="cOA-dh-Avk">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="MQi-On-GEr">
                                        <rect key="frame" x="12" y="5" width="34" height="34"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="34" id="PXd-LJ-1Oi"/>
                                            <constraint firstAttribute="height" constant="34" id="ubH-95-3Vc"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="8" minY="6" maxX="8" maxY="6"/>
                                        <state key="normal" image="ic_back_arrow"/>
                                        <connections>
                                            <action selector="backAction:" destination="NTg-DK-shC" eventType="touchUpInside" id="DdZ-hL-DpO"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="User Profile" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="cO8-Wg-nIN">
                                        <rect key="frame" x="141" y="12" width="93.5" height="20.5"/>
                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                        <color key="textColor" red="0.070588235289999995" green="0.070588235289999995" blue="0.070588235289999995" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="MQi-On-GEr" firstAttribute="centerY" secondItem="cOA-dh-Avk" secondAttribute="centerY" id="01d-PY-dMP"/>
                                    <constraint firstAttribute="height" constant="44" id="Bq2-GN-1WX"/>
                                    <constraint firstItem="cO8-Wg-nIN" firstAttribute="centerX" secondItem="cOA-dh-Avk" secondAttribute="centerX" id="JLN-eB-9If"/>
                                    <constraint firstItem="MQi-On-GEr" firstAttribute="leading" secondItem="cOA-dh-Avk" secondAttribute="leading" constant="12" id="hQT-Ps-HjJ"/>
                                    <constraint firstItem="cO8-Wg-nIN" firstAttribute="centerY" secondItem="cOA-dh-Avk" secondAttribute="centerY" id="qkA-8r-YNN"/>
                                </constraints>
                            </view>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="3Ij-yZ-US7">
                                <rect key="frame" x="9.5" y="52" width="356" height="611"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="Akq-41-aem">
                                        <rect key="frame" x="0.0" y="0.0" width="356" height="40"/>
                                        <subviews>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="eCG-XP-Nan">
                                                <rect key="frame" x="0.0" y="0.0" width="40" height="40"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="J" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="g7P-qe-LkA">
                                                        <rect key="frame" x="4" y="4" width="32" height="32"/>
                                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="22"/>
                                                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                                <color key="backgroundColor" name="Primary"/>
                                                <constraints>
                                                    <constraint firstItem="g7P-qe-LkA" firstAttribute="top" secondItem="eCG-XP-Nan" secondAttribute="top" constant="4" id="1qf-FK-ZyN"/>
                                                    <constraint firstAttribute="width" constant="40" id="AuF-dq-ijT"/>
                                                    <constraint firstAttribute="height" constant="40" id="MN2-bY-Tzo"/>
                                                    <constraint firstAttribute="trailing" secondItem="g7P-qe-LkA" secondAttribute="trailing" constant="4" id="O1J-Zw-Q3f"/>
                                                    <constraint firstItem="g7P-qe-LkA" firstAttribute="leading" secondItem="eCG-XP-Nan" secondAttribute="leading" constant="4" id="Pli-Mr-BUS"/>
                                                    <constraint firstAttribute="bottom" secondItem="g7P-qe-LkA" secondAttribute="bottom" constant="4" id="jBk-ju-7NF"/>
                                                </constraints>
                                            </view>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="nxX-8I-fzL">
                                                <rect key="frame" x="52" y="1.5" width="274" height="37.5"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" text="Jobin Macwan" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="KGi-wD-SdF">
                                                        <rect key="frame" x="0.0" y="0.0" width="274" height="20.5"/>
                                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                        <nil key="textColor"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="<EMAIL>" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="AAm-gk-PvO">
                                                        <rect key="frame" x="0.0" y="20.5" width="274" height="17"/>
                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                        <color key="textColor" name="SecondaryGrayText"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                            </stackView>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_edit" translatesAutoresizingMaskIntoConstraints="NO" id="8oh-jp-apK">
                                                <rect key="frame" x="338" y="11" width="18" height="18"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="18" id="W3R-d7-j5P"/>
                                                    <constraint firstAttribute="width" constant="18" id="sq3-DE-gk7"/>
                                                </constraints>
                                            </imageView>
                                        </subviews>
                                    </stackView>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="wOZ-sv-B7x">
                                        <rect key="frame" x="0.0" y="48" width="356" height="202"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Personal Details" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="IgF-qW-Ty1">
                                                <rect key="frame" x="0.0" y="0.0" width="356" height="30"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="30" id="wms-aG-ePY"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="16"/>
                                                <color key="textColor" red="0.070588235289999995" green="0.070588235289999995" blue="0.070588235289999995" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="O1v-jM-1u6">
                                                <rect key="frame" x="0.0" y="38" width="356" height="30"/>
                                                <subviews>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Cm8-3y-fdr">
                                                        <rect key="frame" x="0.0" y="0.0" width="30" height="30"/>
                                                        <subviews>
                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_phone" translatesAutoresizingMaskIntoConstraints="NO" id="JH8-9e-xhK">
                                                                <rect key="frame" x="6" y="6" width="18" height="18"/>
                                                            </imageView>
                                                        </subviews>
                                                        <color key="backgroundColor" name="UserProfileBG"/>
                                                        <constraints>
                                                            <constraint firstItem="JH8-9e-xhK" firstAttribute="top" secondItem="Cm8-3y-fdr" secondAttribute="top" constant="6" id="08g-dw-cGi"/>
                                                            <constraint firstAttribute="trailing" secondItem="JH8-9e-xhK" secondAttribute="trailing" constant="6" id="9p9-ga-dfm"/>
                                                            <constraint firstAttribute="height" constant="30" id="G0e-yb-UYE"/>
                                                            <constraint firstItem="JH8-9e-xhK" firstAttribute="leading" secondItem="Cm8-3y-fdr" secondAttribute="leading" constant="6" id="OEL-Mk-HYL"/>
                                                            <constraint firstAttribute="bottom" secondItem="JH8-9e-xhK" secondAttribute="bottom" constant="6" id="ULc-Fx-rjS"/>
                                                            <constraint firstAttribute="width" constant="30" id="qQ5-ul-MKT"/>
                                                        </constraints>
                                                    </view>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Moa-6A-lAN">
                                                        <rect key="frame" x="38" y="0.0" width="318" height="30"/>
                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                        <color key="textColor" name="SecondaryGrayText"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="30" id="iUJ-IY-HSY"/>
                                                </constraints>
                                            </stackView>
                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="giz-n7-DVl">
                                                <rect key="frame" x="0.0" y="76" width="356" height="30"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="0gI-w9-egd">
                                                        <rect key="frame" x="0.0" y="0.0" width="174" height="30"/>
                                                        <subviews>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Afm-dK-WnL">
                                                                <rect key="frame" x="0.0" y="0.0" width="30" height="30"/>
                                                                <subviews>
                                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_dob" translatesAutoresizingMaskIntoConstraints="NO" id="KXN-lo-5Cb">
                                                                        <rect key="frame" x="6" y="6" width="18" height="18"/>
                                                                    </imageView>
                                                                </subviews>
                                                                <color key="backgroundColor" name="UserProfileBG"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="30" id="5fD-5I-Gh4"/>
                                                                    <constraint firstItem="KXN-lo-5Cb" firstAttribute="top" secondItem="Afm-dK-WnL" secondAttribute="top" constant="6" id="9Wn-qT-ezr"/>
                                                                    <constraint firstAttribute="trailing" secondItem="KXN-lo-5Cb" secondAttribute="trailing" constant="6" id="KUj-Ta-sK5"/>
                                                                    <constraint firstAttribute="height" constant="30" id="aaa-fY-sB9"/>
                                                                    <constraint firstAttribute="bottom" secondItem="KXN-lo-5Cb" secondAttribute="bottom" constant="6" id="bh8-Of-Ixs"/>
                                                                    <constraint firstItem="KXN-lo-5Cb" firstAttribute="leading" secondItem="Afm-dK-WnL" secondAttribute="leading" constant="6" id="wJX-5Z-zRR"/>
                                                                </constraints>
                                                            </view>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="rXp-bv-4Wo">
                                                                <rect key="frame" x="38" y="0.0" width="136" height="30"/>
                                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                <color key="textColor" name="SecondaryGrayText"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                    </stackView>
                                                    <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="FmI-xB-JsB">
                                                        <rect key="frame" x="182" y="0.0" width="174" height="30"/>
                                                        <subviews>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="iNd-KO-a0n">
                                                                <rect key="frame" x="0.0" y="0.0" width="30" height="30"/>
                                                                <subviews>
                                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_gender" translatesAutoresizingMaskIntoConstraints="NO" id="wLh-Zn-mw7">
                                                                        <rect key="frame" x="6" y="6" width="18" height="18"/>
                                                                    </imageView>
                                                                </subviews>
                                                                <color key="backgroundColor" name="UserProfileBG"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="30" id="2VC-4A-ZBf"/>
                                                                    <constraint firstAttribute="width" constant="30" id="H2W-V7-1BF"/>
                                                                    <constraint firstItem="wLh-Zn-mw7" firstAttribute="top" secondItem="iNd-KO-a0n" secondAttribute="top" constant="6" id="TMI-Ue-Yln"/>
                                                                    <constraint firstItem="wLh-Zn-mw7" firstAttribute="leading" secondItem="iNd-KO-a0n" secondAttribute="leading" constant="6" id="am4-cW-JBQ"/>
                                                                    <constraint firstAttribute="trailing" secondItem="wLh-Zn-mw7" secondAttribute="trailing" constant="6" id="iHw-kR-N3a"/>
                                                                    <constraint firstAttribute="bottom" secondItem="wLh-Zn-mw7" secondAttribute="bottom" constant="6" id="oD0-XE-S6J"/>
                                                                </constraints>
                                                            </view>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="mtE-gu-dGh">
                                                                <rect key="frame" x="38" y="0.0" width="136" height="30"/>
                                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                <color key="textColor" name="SecondaryGrayText"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                    </stackView>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="30" id="1cf-IJ-ARa"/>
                                                </constraints>
                                            </stackView>
                                            <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="fXA-z8-Nm4">
                                                <rect key="frame" x="0.0" y="114" width="356" height="50"/>
                                                <subviews>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="CY4-7Q-tdJ">
                                                        <rect key="frame" x="0.0" y="10" width="30" height="30"/>
                                                        <subviews>
                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_address" translatesAutoresizingMaskIntoConstraints="NO" id="0aD-g2-sU3">
                                                                <rect key="frame" x="6" y="6" width="18" height="18"/>
                                                            </imageView>
                                                        </subviews>
                                                        <color key="backgroundColor" name="UserProfileBG"/>
                                                        <constraints>
                                                            <constraint firstAttribute="width" constant="30" id="5DS-Ro-35T"/>
                                                            <constraint firstItem="0aD-g2-sU3" firstAttribute="leading" secondItem="CY4-7Q-tdJ" secondAttribute="leading" constant="6" id="PRy-pV-cQ0"/>
                                                            <constraint firstAttribute="trailing" secondItem="0aD-g2-sU3" secondAttribute="trailing" constant="6" id="dAM-Lr-kai"/>
                                                            <constraint firstItem="0aD-g2-sU3" firstAttribute="top" secondItem="CY4-7Q-tdJ" secondAttribute="top" constant="6" id="u8L-Yi-FDj"/>
                                                            <constraint firstAttribute="height" constant="30" id="xrb-FE-LV3"/>
                                                            <constraint firstAttribute="bottom" secondItem="0aD-g2-sU3" secondAttribute="bottom" constant="6" id="ytW-er-gto"/>
                                                        </constraints>
                                                    </view>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="yJw-Gy-z45">
                                                        <rect key="frame" x="38" y="25" width="318" height="0.0"/>
                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                        <color key="textColor" name="SecondaryGrayText"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="50" id="jYa-fg-lrm"/>
                                                </constraints>
                                            </stackView>
                                            <stackView opaque="NO" contentMode="scaleToFill" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="Dqn-Ke-MLj">
                                                <rect key="frame" x="0.0" y="172" width="356" height="30"/>
                                                <subviews>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="AlY-5v-CGN">
                                                        <rect key="frame" x="0.0" y="0.0" width="30" height="30"/>
                                                        <subviews>
                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_user_address" translatesAutoresizingMaskIntoConstraints="NO" id="vZh-ym-tJH">
                                                                <rect key="frame" x="6" y="6" width="18" height="18"/>
                                                            </imageView>
                                                        </subviews>
                                                        <color key="backgroundColor" name="UserProfileBG"/>
                                                        <constraints>
                                                            <constraint firstItem="vZh-ym-tJH" firstAttribute="top" secondItem="AlY-5v-CGN" secondAttribute="top" constant="6" id="0o8-6d-gGo"/>
                                                            <constraint firstAttribute="bottom" secondItem="vZh-ym-tJH" secondAttribute="bottom" constant="6" id="NdH-6t-VxF"/>
                                                            <constraint firstAttribute="trailing" secondItem="vZh-ym-tJH" secondAttribute="trailing" constant="6" id="UlM-54-yad"/>
                                                            <constraint firstAttribute="height" constant="30" id="cfn-20-v0B"/>
                                                            <constraint firstAttribute="width" constant="30" id="oyr-at-5TX"/>
                                                            <constraint firstItem="vZh-ym-tJH" firstAttribute="leading" secondItem="AlY-5v-CGN" secondAttribute="leading" constant="6" id="teh-7F-CYi"/>
                                                        </constraints>
                                                    </view>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="j5X-aF-XeX">
                                                        <rect key="frame" x="38" y="0.0" width="318" height="30"/>
                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                        <color key="textColor" name="SecondaryGrayText"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="30" id="aYS-LC-Npd"/>
                                                </constraints>
                                            </stackView>
                                        </subviews>
                                    </stackView>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="kN7-Bt-2bM">
                                        <rect key="frame" x="0.0" y="258" width="356" height="40"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="siC-6U-a6s">
                                                <rect key="frame" x="0.0" y="8" width="348" height="32"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" verticalHuggingPriority="251" text="Car Profile" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="7xN-bt-Evp">
                                                        <rect key="frame" x="0.0" y="6.5" width="224" height="19.5"/>
                                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="16"/>
                                                        <color key="textColor" red="0.070588235289999995" green="0.070588235289999995" blue="0.070588235289999995" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="NMW-8B-AWt">
                                                        <rect key="frame" x="236" y="0.0" width="80" height="32"/>
                                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="16"/>
                                                        <color key="textColor" red="0.070588235289999995" green="0.070588235289999995" blue="0.070588235289999995" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_add" translatesAutoresizingMaskIntoConstraints="NO" id="qyP-t6-NcD">
                                                        <rect key="frame" x="328" y="6" width="20" height="20"/>
                                                        <constraints>
                                                            <constraint firstAttribute="width" constant="20" id="90U-ph-Rda"/>
                                                            <constraint firstAttribute="height" constant="20" id="NAN-3v-gjc"/>
                                                        </constraints>
                                                    </imageView>
                                                </subviews>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="siC-6U-a6s" secondAttribute="trailing" constant="8" id="ICj-aZ-BhR"/>
                                            <constraint firstItem="siC-6U-a6s" firstAttribute="top" secondItem="kN7-Bt-2bM" secondAttribute="top" constant="8" id="K7s-6c-DQA"/>
                                            <constraint firstItem="siC-6U-a6s" firstAttribute="leading" secondItem="kN7-Bt-2bM" secondAttribute="leading" id="KZa-hM-o6N"/>
                                            <constraint firstAttribute="height" constant="40" id="XSS-rz-mX6"/>
                                            <constraint firstAttribute="bottom" secondItem="siC-6U-a6s" secondAttribute="bottom" id="dgO-jZ-gCd"/>
                                        </constraints>
                                    </view>
                                    <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="none" allowsSelection="NO" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="csK-vc-83f">
                                        <rect key="frame" x="0.0" y="306" width="356" height="253"/>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <prototypes>
                                            <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" reuseIdentifier="cell" id="8bl-jf-xp5" customClass="ProfileDetailsCell" customModule="NXC_EV_Solutions" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="44.5" width="356" height="132"/>
                                                <autoresizingMask key="autoresizingMask"/>
                                                <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="8bl-jf-xp5" id="E6C-Bh-lXv">
                                                    <rect key="frame" x="0.0" y="0.0" width="356" height="132"/>
                                                    <autoresizingMask key="autoresizingMask"/>
                                                    <subviews>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="w9o-sM-sd5">
                                                            <rect key="frame" x="4" y="4" width="348" height="124"/>
                                                            <subviews>
                                                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="bottom" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="2Fp-U7-gwL">
                                                                    <rect key="frame" x="4" y="4" width="340" height="114"/>
                                                                    <subviews>
                                                                        <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="QNX-mi-A8M">
                                                                            <rect key="frame" x="7" y="0.0" width="333" height="24"/>
                                                                            <subviews>
                                                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_mfd" translatesAutoresizingMaskIntoConstraints="NO" id="WxG-51-l2w">
                                                                                    <rect key="frame" x="0.0" y="2" width="20" height="20"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="height" constant="20" id="J57-V9-7dU"/>
                                                                                        <constraint firstAttribute="width" constant="20" id="Zlf-Ix-v8d"/>
                                                                                    </constraints>
                                                                                </imageView>
                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Manufacturer" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="XGJ-2t-7RN">
                                                                                    <rect key="frame" x="28" y="2" width="100" height="20"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="height" constant="20" id="3Ia-3p-GQB"/>
                                                                                    </constraints>
                                                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                                    <color key="textColor" name="PrimarySelection"/>
                                                                                    <nil key="highlightedColor"/>
                                                                                </label>
                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="MG" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="cOx-hT-dLL">
                                                                                    <rect key="frame" x="136" y="0.0" width="197" height="24"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="height" constant="24" id="RgR-jW-yoZ"/>
                                                                                    </constraints>
                                                                                    <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                                    <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                    <nil key="highlightedColor"/>
                                                                                </label>
                                                                            </subviews>
                                                                            <constraints>
                                                                                <constraint firstItem="XGJ-2t-7RN" firstAttribute="width" secondItem="QNX-mi-A8M" secondAttribute="width" multiplier="0.3" id="xff-Oy-rRT"/>
                                                                            </constraints>
                                                                        </stackView>
                                                                        <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="hsf-kO-jEQ">
                                                                            <rect key="frame" x="7" y="28" width="333" height="24"/>
                                                                            <subviews>
                                                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_model" translatesAutoresizingMaskIntoConstraints="NO" id="ai1-cF-LbH">
                                                                                    <rect key="frame" x="0.0" y="0.0" width="24" height="24"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="width" constant="24" id="YcC-eh-32a"/>
                                                                                        <constraint firstAttribute="height" constant="24" id="xRK-Bc-a4w"/>
                                                                                    </constraints>
                                                                                </imageView>
                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Model" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="MLg-BC-IlN">
                                                                                    <rect key="frame" x="32" y="2" width="66.5" height="20"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="height" constant="20" id="btd-AQ-8uf"/>
                                                                                    </constraints>
                                                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                                    <color key="textColor" name="PrimarySelection"/>
                                                                                    <nil key="highlightedColor"/>
                                                                                </label>
                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Seltos" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="nrV-e1-Hf2">
                                                                                    <rect key="frame" x="106.5" y="0.0" width="226.5" height="24"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="height" constant="24" id="9wn-IY-rLU"/>
                                                                                    </constraints>
                                                                                    <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                                    <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                    <nil key="highlightedColor"/>
                                                                                </label>
                                                                            </subviews>
                                                                            <constraints>
                                                                                <constraint firstItem="MLg-BC-IlN" firstAttribute="width" secondItem="hsf-kO-jEQ" secondAttribute="width" multiplier="0.2" id="yh7-GV-sr9"/>
                                                                            </constraints>
                                                                        </stackView>
                                                                        <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="evh-Wv-gzk">
                                                                            <rect key="frame" x="7" y="56" width="333" height="24"/>
                                                                            <subviews>
                                                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_registration" translatesAutoresizingMaskIntoConstraints="NO" id="RFk-pO-ZKl">
                                                                                    <rect key="frame" x="0.0" y="0.0" width="24" height="24"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="width" constant="24" id="3TR-p6-Uf0"/>
                                                                                        <constraint firstAttribute="height" constant="24" id="x9y-mW-P96"/>
                                                                                    </constraints>
                                                                                </imageView>
                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Register No." textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="MsP-kJ-jQZ">
                                                                                    <rect key="frame" x="32" y="2" width="100" height="20"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="height" constant="20" id="tLf-SN-NSb"/>
                                                                                    </constraints>
                                                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                                    <color key="textColor" name="PrimarySelection"/>
                                                                                    <nil key="highlightedColor"/>
                                                                                </label>
                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Reg123" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Zk5-bF-2bT">
                                                                                    <rect key="frame" x="140" y="0.0" width="193" height="24"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="height" constant="24" id="276-FK-esL"/>
                                                                                    </constraints>
                                                                                    <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                                    <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                    <nil key="highlightedColor"/>
                                                                                </label>
                                                                            </subviews>
                                                                            <constraints>
                                                                                <constraint firstItem="MsP-kJ-jQZ" firstAttribute="width" secondItem="evh-Wv-gzk" secondAttribute="width" multiplier="0.3" id="ofj-R4-2sg"/>
                                                                            </constraints>
                                                                        </stackView>
                                                                        <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="JwB-2h-ki8">
                                                                            <rect key="frame" x="244" y="84" width="96" height="30"/>
                                                                            <subviews>
                                                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" hasAttributedTitle="YES" translatesAutoresizingMaskIntoConstraints="NO" id="6Z8-XB-C1W">
                                                                                    <rect key="frame" x="0.0" y="0.0" width="44" height="30"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="width" constant="44" id="8jR-43-EqO"/>
                                                                                        <constraint firstAttribute="height" constant="30" id="ynz-ha-UAw"/>
                                                                                    </constraints>
                                                                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                                    <state key="normal">
                                                                                        <attributedString key="attributedTitle">
                                                                                            <fragment content="Edit">
                                                                                                <attributes>
                                                                                                    <color key="NSColor" red="0.015686274509803921" green="0.49411764705882355" blue="0.42745098039215684" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                                                    <font key="NSFont" metaFont="system" size="14"/>
                                                                                                    <integer key="NSUnderline" value="1"/>
                                                                                                </attributes>
                                                                                            </fragment>
                                                                                        </attributedString>
                                                                                    </state>
                                                                                </button>
                                                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" hasAttributedTitle="YES" translatesAutoresizingMaskIntoConstraints="NO" id="LJC-sa-ElL">
                                                                                    <rect key="frame" x="52" y="0.0" width="44" height="30"/>
                                                                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                                    <state key="normal">
                                                                                        <attributedString key="attributedTitle">
                                                                                            <fragment content="Delete">
                                                                                                <attributes>
                                                                                                    <color key="NSColor" red="1" green="0.14913141730000001" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                                                    <font key="NSFont" metaFont="system" size="14"/>
                                                                                                    <integer key="NSUnderline" value="1"/>
                                                                                                </attributes>
                                                                                            </fragment>
                                                                                        </attributedString>
                                                                                    </state>
                                                                                </button>
                                                                            </subviews>
                                                                        </stackView>
                                                                    </subviews>
                                                                    <constraints>
                                                                        <constraint firstItem="QNX-mi-A8M" firstAttribute="width" secondItem="2Fp-U7-gwL" secondAttribute="width" multiplier="0.98" id="A7L-97-0ci"/>
                                                                        <constraint firstItem="evh-Wv-gzk" firstAttribute="width" secondItem="2Fp-U7-gwL" secondAttribute="width" multiplier="0.98" id="OwY-Cj-Kgt"/>
                                                                        <constraint firstItem="hsf-kO-jEQ" firstAttribute="width" secondItem="2Fp-U7-gwL" secondAttribute="width" multiplier="0.98" id="Vrc-Zd-MDW"/>
                                                                    </constraints>
                                                                </stackView>
                                                            </subviews>
                                                            <color key="backgroundColor" name="UserProfileBG"/>
                                                            <constraints>
                                                                <constraint firstAttribute="trailing" secondItem="2Fp-U7-gwL" secondAttribute="trailing" constant="4" id="Tl3-8x-aeZ"/>
                                                                <constraint firstAttribute="bottom" secondItem="2Fp-U7-gwL" secondAttribute="bottom" constant="6" id="abe-r1-Ugp"/>
                                                                <constraint firstItem="2Fp-U7-gwL" firstAttribute="leading" secondItem="w9o-sM-sd5" secondAttribute="leading" constant="4" id="enH-4j-XuK"/>
                                                                <constraint firstItem="2Fp-U7-gwL" firstAttribute="top" secondItem="w9o-sM-sd5" secondAttribute="top" constant="4" id="wQE-KU-0xl"/>
                                                            </constraints>
                                                        </view>
                                                    </subviews>
                                                    <constraints>
                                                        <constraint firstAttribute="trailing" secondItem="w9o-sM-sd5" secondAttribute="trailing" constant="4" id="Het-ge-972"/>
                                                        <constraint firstItem="w9o-sM-sd5" firstAttribute="top" secondItem="E6C-Bh-lXv" secondAttribute="top" constant="4" id="Jm0-we-bvs"/>
                                                        <constraint firstAttribute="bottom" secondItem="w9o-sM-sd5" secondAttribute="bottom" constant="4" id="lTS-Eh-ACe"/>
                                                        <constraint firstItem="w9o-sM-sd5" firstAttribute="leading" secondItem="E6C-Bh-lXv" secondAttribute="leading" constant="4" id="mEV-bk-sfm"/>
                                                    </constraints>
                                                </tableViewCellContentView>
                                                <connections>
                                                    <outlet property="btnDelete" destination="LJC-sa-ElL" id="RmK-NA-p94"/>
                                                    <outlet property="btnEdit" destination="6Z8-XB-C1W" id="znv-8i-ZIq"/>
                                                    <outlet property="lblMfd" destination="cOx-hT-dLL" id="UBe-YE-lBX"/>
                                                    <outlet property="lblModel" destination="nrV-e1-Hf2" id="BkI-as-bJJ"/>
                                                    <outlet property="lblRegNo" destination="Zk5-bF-2bT" id="KSY-76-Qbj"/>
                                                    <outlet property="viewMain" destination="w9o-sM-sd5" id="jgu-92-Sza"/>
                                                </connections>
                                            </tableViewCell>
                                        </prototypes>
                                    </tableView>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="yLf-gL-Ur0">
                                        <rect key="frame" x="0.0" y="567" width="356" height="44"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="44" id="1Qp-AT-vm2"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="16"/>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                        <state key="normal" title="Delete Account">
                                            <color key="titleColor" red="0.*****************" green="0.**********" blue="0.027450980390000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        </state>
                                        <connections>
                                            <action selector="deleteAccountAction:" destination="NTg-DK-shC" eventType="touchUpInside" id="bS6-7V-MZI"/>
                                        </connections>
                                    </button>
                                </subviews>
                            </stackView>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="5Fg-X6-UeU">
                                <rect key="frame" x="327.5" y="314" width="40" height="40"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="PDf-IK-3Ja"/>
                                    <constraint firstAttribute="width" constant="40" id="SdZ-9o-bHZ"/>
                                </constraints>
                                <color key="tintColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <connections>
                                    <action selector="addVehicleAction:" destination="NTg-DK-shC" eventType="touchUpInside" id="TEC-FZ-FcT"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="YTP-Gj-QMA">
                                <rect key="frame" x="336.5" y="52" width="40" height="40"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="40" id="5I6-hz-qoT"/>
                                    <constraint firstAttribute="height" constant="40" id="Hh7-Bs-TvS"/>
                                </constraints>
                                <color key="tintColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <connections>
                                    <action selector="editProfileAction:" destination="NTg-DK-shC" eventType="touchUpInside" id="xyj-P8-0Qw"/>
                                </connections>
                            </button>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Iu5-Kc-Ca9">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="WMB-SG-LEE">
                                        <rect key="frame" x="19" y="204" width="337.5" height="259.5"/>
                                        <subviews>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="JGX-Ft-ZHg">
                                                <rect key="frame" x="293.5" y="0.0" width="44" height="44"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="44" id="FWo-Nu-fQZ"/>
                                                    <constraint firstAttribute="width" constant="44" id="Hpp-Ds-pgA"/>
                                                </constraints>
                                                <inset key="imageEdgeInsets" minX="14" minY="14" maxX="14" maxY="14"/>
                                                <state key="normal" image="ic_cancel"/>
                                                <connections>
                                                    <action selector="cancelAlertAction:" destination="NTg-DK-shC" eventType="touchUpInside" id="LXg-gZ-xMT"/>
                                                </connections>
                                            </button>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="30" baselineRelativeArrangement="YES" translatesAutoresizingMaskIntoConstraints="NO" id="xaP-Mz-Y9W">
                                                <rect key="frame" x="16.5" y="16" width="304" height="167.5"/>
                                                <subviews>
                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_deleteAccount" translatesAutoresizingMaskIntoConstraints="NO" id="0h9-Hp-LwN">
                                                        <rect key="frame" x="119.5" y="0.0" width="65" height="65"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="65" id="9fy-Ge-zfQ"/>
                                                            <constraint firstAttribute="width" constant="65" id="dHQ-yR-AYy"/>
                                                        </constraints>
                                                    </imageView>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Are you sure you want to delete your account?" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="mn3-4G-Myj">
                                                        <rect key="frame" x="25" y="79" width="254" height="41"/>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="You will lose all your data and your account will be permanently deleted." textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="u03-Tm-DiW">
                                                        <rect key="frame" x="3" y="131.5" width="298.5" height="36"/>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                        <color key="textColor" white="0.0" alpha="0.5" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                            </stackView>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="20" translatesAutoresizingMaskIntoConstraints="NO" id="OD3-3Z-oKp">
                                                <rect key="frame" x="16" y="199.5" width="305.5" height="44"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="24" translatesAutoresizingMaskIntoConstraints="NO" id="6uw-ID-XXp">
                                                        <rect key="frame" x="15" y="0.0" width="275" height="44"/>
                                                        <subviews>
                                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="sHY-ZQ-ESQ">
                                                                <rect key="frame" x="0.0" y="0.0" width="275" height="44"/>
                                                                <color key="backgroundColor" red="0.*****************" green="0.**********" blue="0.027450980390000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="44" id="93z-ln-DMg"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                                                <state key="normal" title="DELETE">
                                                                    <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                </state>
                                                                <connections>
                                                                    <action selector="OKAction:" destination="Xhu-Eh-62f" eventType="touchUpInside" id="cfS-Y5-1mN"/>
                                                                    <action selector="deleteAlertAction:" destination="NTg-DK-shC" eventType="touchUpInside" id="Gsw-yf-bZw"/>
                                                                </connections>
                                                            </button>
                                                        </subviews>
                                                    </stackView>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstItem="6uw-ID-XXp" firstAttribute="width" secondItem="OD3-3Z-oKp" secondAttribute="width" multiplier="0.9" id="bTV-NR-jYU"/>
                                                </constraints>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstItem="JGX-Ft-ZHg" firstAttribute="top" secondItem="WMB-SG-LEE" secondAttribute="top" id="89t-w0-lHr"/>
                                            <constraint firstAttribute="trailing" secondItem="JGX-Ft-ZHg" secondAttribute="trailing" id="Drk-3p-uY7"/>
                                            <constraint firstItem="xaP-Mz-Y9W" firstAttribute="centerX" secondItem="WMB-SG-LEE" secondAttribute="centerX" id="NOn-QD-VqT"/>
                                            <constraint firstItem="OD3-3Z-oKp" firstAttribute="top" secondItem="xaP-Mz-Y9W" secondAttribute="bottom" constant="16" id="bLk-Fj-bV4"/>
                                            <constraint firstItem="OD3-3Z-oKp" firstAttribute="width" secondItem="WMB-SG-LEE" secondAttribute="width" multiplier="0.905185" id="e5f-9N-T2v"/>
                                            <constraint firstItem="OD3-3Z-oKp" firstAttribute="centerX" secondItem="WMB-SG-LEE" secondAttribute="centerX" id="hiu-KG-bRQ"/>
                                            <constraint firstAttribute="bottom" secondItem="OD3-3Z-oKp" secondAttribute="bottom" constant="16" id="tuq-e9-JFe"/>
                                            <constraint firstItem="xaP-Mz-Y9W" firstAttribute="width" secondItem="WMB-SG-LEE" secondAttribute="width" multiplier="0.9" id="wfR-96-xeN"/>
                                            <constraint firstItem="xaP-Mz-Y9W" firstAttribute="top" secondItem="WMB-SG-LEE" secondAttribute="top" constant="16" id="yl5-PD-iYi"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.75" colorSpace="custom" customColorSpace="sRGB"/>
                                <constraints>
                                    <constraint firstItem="WMB-SG-LEE" firstAttribute="centerX" secondItem="Iu5-Kc-Ca9" secondAttribute="centerX" id="Edu-lc-xgo"/>
                                    <constraint firstItem="WMB-SG-LEE" firstAttribute="width" secondItem="Iu5-Kc-Ca9" secondAttribute="width" multiplier="0.9" id="LSn-ab-RYu"/>
                                    <constraint firstItem="WMB-SG-LEE" firstAttribute="centerY" secondItem="Iu5-Kc-Ca9" secondAttribute="centerY" id="e2I-Aq-T7u"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Axb-xk-NxY">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="eXG-85-M9u">
                                        <rect key="frame" x="19" y="126.5" width="337.5" height="414.5"/>
                                        <subviews>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="SFq-ha-1iG">
                                                <rect key="frame" x="293.5" y="0.0" width="44" height="44"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="44" id="0QW-4m-rQ7"/>
                                                    <constraint firstAttribute="width" constant="44" id="dOi-mK-qlU"/>
                                                </constraints>
                                                <inset key="imageEdgeInsets" minX="14" minY="14" maxX="14" maxY="14"/>
                                                <state key="normal" image="ic_cancel"/>
                                                <connections>
                                                    <action selector="cancelRefundAction:" destination="NTg-DK-shC" eventType="touchUpInside" id="dOX-Xq-XVq"/>
                                                </connections>
                                            </button>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="xq4-Da-34X">
                                                <rect key="frame" x="16.5" y="16" width="304" height="322.5"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Refund Your Money" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="NAT-ry-JDK">
                                                        <rect key="frame" x="74.5" y="0.0" width="155" height="20.5"/>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Please enter bank details to enter money in your wallet" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Ofm-hQ-dlQ">
                                                        <rect key="frame" x="2.5" y="32.5" width="299.5" height="36"/>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                        <color key="textColor" white="0.0" alpha="0.5" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Your account will be permanently deleted" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="IJP-o3-aa1">
                                                        <rect key="frame" x="10.5" y="80.5" width="283" height="18"/>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                        <color key="textColor" white="0.0" alpha="0.5" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" borderStyle="roundedRect" placeholder="Bank Name" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="WIb-I2-NWu">
                                                        <rect key="frame" x="0.0" y="110.5" width="304" height="44"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="44" id="abT-wh-Dhs"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                        <textInputTraits key="textInputTraits"/>
                                                    </textField>
                                                    <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" borderStyle="roundedRect" placeholder="Branch Name" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="cJJ-ah-aHD">
                                                        <rect key="frame" x="0.0" y="166.5" width="304" height="44"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="44" id="brg-4O-Oy3"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                        <textInputTraits key="textInputTraits"/>
                                                    </textField>
                                                    <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" borderStyle="roundedRect" placeholder="Account Number" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="qno-vD-nRw">
                                                        <rect key="frame" x="0.0" y="222.5" width="304" height="44"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="44" id="ETD-34-oUh"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                        <textInputTraits key="textInputTraits"/>
                                                    </textField>
                                                    <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" borderStyle="roundedRect" placeholder="IFSC Code" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="Ka2-pd-935">
                                                        <rect key="frame" x="0.0" y="278.5" width="304" height="44"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="44" id="s3y-qC-paZ"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                        <textInputTraits key="textInputTraits"/>
                                                    </textField>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstItem="cJJ-ah-aHD" firstAttribute="width" secondItem="xq4-Da-34X" secondAttribute="width" id="Ngx-Me-M6d"/>
                                                    <constraint firstItem="Ka2-pd-935" firstAttribute="width" secondItem="xq4-Da-34X" secondAttribute="width" id="g3E-sg-BoP"/>
                                                    <constraint firstItem="WIb-I2-NWu" firstAttribute="width" secondItem="xq4-Da-34X" secondAttribute="width" id="sWB-Np-4QD"/>
                                                    <constraint firstItem="qno-vD-nRw" firstAttribute="width" secondItem="xq4-Da-34X" secondAttribute="width" id="uaU-g8-Qad"/>
                                                </constraints>
                                            </stackView>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="20" translatesAutoresizingMaskIntoConstraints="NO" id="EsF-JD-hoF">
                                                <rect key="frame" x="16" y="354.5" width="305.5" height="44"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="24" translatesAutoresizingMaskIntoConstraints="NO" id="8pw-fu-uB7">
                                                        <rect key="frame" x="0.0" y="0.0" width="305.5" height="44"/>
                                                        <subviews>
                                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="XlU-DD-2xK">
                                                                <rect key="frame" x="0.0" y="0.0" width="305.5" height="44"/>
                                                                <subviews>
                                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Kud-V2-tEq">
                                                                        <rect key="frame" x="0.0" y="0.0" width="305.5" height="44"/>
                                                                        <color key="backgroundColor" name="Primary"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="44" id="FjP-Gl-IRx"/>
                                                                        </constraints>
                                                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                                                        <state key="normal" title="Submit">
                                                                            <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        </state>
                                                                        <connections>
                                                                            <action selector="submitRefundAction:" destination="NTg-DK-shC" eventType="touchUpInside" id="TTs-yD-IVj"/>
                                                                        </connections>
                                                                    </button>
                                                                </subviews>
                                                            </stackView>
                                                        </subviews>
                                                    </stackView>
                                                </subviews>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstItem="EsF-JD-hoF" firstAttribute="leading" secondItem="eXG-85-M9u" secondAttribute="leading" constant="16" id="Ad1-gF-ciW"/>
                                            <constraint firstAttribute="bottom" secondItem="EsF-JD-hoF" secondAttribute="bottom" constant="16" id="Bd3-WZ-A9P"/>
                                            <constraint firstItem="SFq-ha-1iG" firstAttribute="top" secondItem="eXG-85-M9u" secondAttribute="top" id="f4F-fV-hiF"/>
                                            <constraint firstAttribute="trailing" secondItem="EsF-JD-hoF" secondAttribute="trailing" constant="16" id="fRx-ma-CbM"/>
                                            <constraint firstItem="EsF-JD-hoF" firstAttribute="top" secondItem="xq4-Da-34X" secondAttribute="bottom" constant="16" id="fV8-WI-Gz7"/>
                                            <constraint firstItem="xq4-Da-34X" firstAttribute="centerX" secondItem="eXG-85-M9u" secondAttribute="centerX" id="gcA-11-v1C"/>
                                            <constraint firstItem="xq4-Da-34X" firstAttribute="width" secondItem="eXG-85-M9u" secondAttribute="width" multiplier="0.9" id="qBL-cC-XN4"/>
                                            <constraint firstAttribute="trailing" secondItem="SFq-ha-1iG" secondAttribute="trailing" id="qwX-YW-l0B"/>
                                            <constraint firstItem="xq4-Da-34X" firstAttribute="top" secondItem="eXG-85-M9u" secondAttribute="top" constant="16" id="sFY-SD-pmV"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.75" colorSpace="custom" customColorSpace="sRGB"/>
                                <constraints>
                                    <constraint firstItem="eXG-85-M9u" firstAttribute="centerY" secondItem="Axb-xk-NxY" secondAttribute="centerY" id="AWD-zq-PPZ"/>
                                    <constraint firstItem="eXG-85-M9u" firstAttribute="width" secondItem="Axb-xk-NxY" secondAttribute="width" multiplier="0.9" id="fkl-bg-y1t"/>
                                    <constraint firstItem="eXG-85-M9u" firstAttribute="centerX" secondItem="Axb-xk-NxY" secondAttribute="centerX" id="kGu-JO-x6d"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="dmX-R8-477"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="dmX-R8-477" firstAttribute="bottom" secondItem="3Ij-yZ-US7" secondAttribute="bottom" constant="4" id="07F-qn-FM6"/>
                            <constraint firstItem="dmX-R8-477" firstAttribute="trailing" secondItem="cOA-dh-Avk" secondAttribute="trailing" id="3sI-hd-vPM"/>
                            <constraint firstItem="Axb-xk-NxY" firstAttribute="top" secondItem="dmX-R8-477" secondAttribute="top" id="Awu-md-v9G"/>
                            <constraint firstItem="Iu5-Kc-Ca9" firstAttribute="leading" secondItem="dmX-R8-477" secondAttribute="leading" id="BCv-A6-QRr"/>
                            <constraint firstItem="Iu5-Kc-Ca9" firstAttribute="top" secondItem="dmX-R8-477" secondAttribute="top" id="Bbh-zY-3Lb"/>
                            <constraint firstItem="Axb-xk-NxY" firstAttribute="bottom" secondItem="dmX-R8-477" secondAttribute="bottom" id="C8e-26-spJ"/>
                            <constraint firstItem="3Ij-yZ-US7" firstAttribute="width" secondItem="dmX-R8-477" secondAttribute="width" multiplier="0.95" id="Gdc-Fs-wNN"/>
                            <constraint firstItem="3Ij-yZ-US7" firstAttribute="centerX" secondItem="mrL-F8-iu0" secondAttribute="centerX" id="J4r-0b-dEN"/>
                            <constraint firstItem="Axb-xk-NxY" firstAttribute="leading" secondItem="dmX-R8-477" secondAttribute="leading" id="JLC-mv-3ki"/>
                            <constraint firstItem="dmX-R8-477" firstAttribute="trailing" secondItem="Axb-xk-NxY" secondAttribute="trailing" id="N66-1F-1kb"/>
                            <constraint firstItem="Iu5-Kc-Ca9" firstAttribute="trailing" secondItem="dmX-R8-477" secondAttribute="trailing" id="POw-dq-Hua"/>
                            <constraint firstItem="YTP-Gj-QMA" firstAttribute="centerX" secondItem="8oh-jp-apK" secondAttribute="centerX" id="Yhb-3G-Xyh"/>
                            <constraint firstItem="5Fg-X6-UeU" firstAttribute="centerY" secondItem="qyP-t6-NcD" secondAttribute="centerY" id="bN0-8y-4D0"/>
                            <constraint firstItem="Iu5-Kc-Ca9" firstAttribute="bottom" secondItem="dmX-R8-477" secondAttribute="bottom" id="d7H-KV-iMR"/>
                            <constraint firstItem="3Ij-yZ-US7" firstAttribute="top" secondItem="cOA-dh-Avk" secondAttribute="bottom" constant="8" id="d7f-hG-amd"/>
                            <constraint firstItem="5Fg-X6-UeU" firstAttribute="centerX" secondItem="qyP-t6-NcD" secondAttribute="centerX" id="dCw-Sd-glq"/>
                            <constraint firstItem="cOA-dh-Avk" firstAttribute="leading" secondItem="dmX-R8-477" secondAttribute="leading" id="evq-Bx-GP6"/>
                            <constraint firstItem="YTP-Gj-QMA" firstAttribute="centerY" secondItem="8oh-jp-apK" secondAttribute="centerY" id="q6t-UH-ccp"/>
                            <constraint firstItem="cOA-dh-Avk" firstAttribute="top" secondItem="dmX-R8-477" secondAttribute="top" id="v2z-mL-GUW"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="btnBack" destination="MQi-On-GEr" id="gQi-FC-iri"/>
                        <outlet property="btnDeleteAlert" destination="sHY-ZQ-ESQ" id="q1t-t6-vgr"/>
                        <outlet property="btnSubmitRefund" destination="Kud-V2-tEq" id="JJW-Lt-f1L"/>
                        <outlet property="lblAddress" destination="yJw-Gy-z45" id="pDg-dN-PQh"/>
                        <outlet property="lblDOB" destination="rXp-bv-4Wo" id="yGw-ee-Iwd"/>
                        <outlet property="lblEmail" destination="AAm-gk-PvO" id="Wmu-Wr-eDM"/>
                        <outlet property="lblGender" destination="mtE-gu-dGh" id="BTk-1m-hNc"/>
                        <outlet property="lblInitial" destination="g7P-qe-LkA" id="NSb-rd-aEv"/>
                        <outlet property="lblLocation" destination="j5X-aF-XeX" id="Sj4-FB-9ka"/>
                        <outlet property="lblName" destination="KGi-wD-SdF" id="B4F-BD-8mO"/>
                        <outlet property="lblPhone" destination="Moa-6A-lAN" id="pKZ-Sg-xrI"/>
                        <outlet property="stackAddress" destination="fXA-z8-Nm4" id="LMK-2e-YOy"/>
                        <outlet property="stackCity" destination="Dqn-Ke-MLj" id="nzs-6z-ph0"/>
                        <outlet property="stackDOB" destination="0gI-w9-egd" id="JQj-JT-BSU"/>
                        <outlet property="stackDOBGender" destination="giz-n7-DVl" id="MD8-Nr-qoz"/>
                        <outlet property="stackGender" destination="FmI-xB-JsB" id="cNO-d2-ycP"/>
                        <outlet property="stackPhone" destination="O1v-jM-1u6" id="weC-w9-dZ9"/>
                        <outlet property="tableVehicle" destination="csK-vc-83f" id="CrB-9A-tff"/>
                        <outlet property="txtAccountNo" destination="qno-vD-nRw" id="JQk-QZ-UGz"/>
                        <outlet property="txtBankName" destination="WIb-I2-NWu" id="FGL-Ud-TLd"/>
                        <outlet property="txtBranchName" destination="cJJ-ah-aHD" id="7hu-wf-jSj"/>
                        <outlet property="txtIFSC" destination="Ka2-pd-935" id="8pA-K4-sU0"/>
                        <outlet property="viewAddress" destination="CY4-7Q-tdJ" id="oDm-Hw-KW9"/>
                        <outlet property="viewBgAlert" destination="Iu5-Kc-Ca9" id="Ggb-mi-6n2"/>
                        <outlet property="viewBgRefund" destination="Axb-xk-NxY" id="GUe-1G-IFb"/>
                        <outlet property="viewDOB" destination="Afm-dK-WnL" id="bG4-Ur-fkv"/>
                        <outlet property="viewGender" destination="iNd-KO-a0n" id="wyP-Fl-Yap"/>
                        <outlet property="viewLocation" destination="AlY-5v-CGN" id="mTc-6E-m2M"/>
                        <outlet property="viewMainAlert" destination="WMB-SG-LEE" id="m5x-ki-Mpr"/>
                        <outlet property="viewMainRefund" destination="eXG-85-M9u" id="HOq-w5-dwn"/>
                        <outlet property="viewPhone" destination="Cm8-3y-fdr" id="vfS-tx-C6v"/>
                        <outlet property="viewProfileInitial" destination="eCG-XP-Nan" id="C80-SM-Oms"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="qDR-ks-GY3" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1009" y="133"/>
        </scene>
        <!--AddressVC-->
        <scene sceneID="Z6p-gz-1Zu">
            <objects>
                <viewController storyboardIdentifier="AddressVC" id="NvH-Qk-MPf" customClass="AddressVC" customModule="NXC_EV_Solutions" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="QCW-xx-CP5">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" scrollEnabled="NO" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" translatesAutoresizingMaskIntoConstraints="NO" id="f5J-B4-sMA">
                                <rect key="frame" x="0.0" y="53.5" width="375" height="613.5"/>
                                <subviews>
                                    <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="AKZ-5D-JbL">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="600.5"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Your Address (2/2)" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="I2V-0f-tDq">
                                                <rect key="frame" x="16" y="8" width="153" height="34"/>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="34" id="ZPT-yI-X59"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="boldSystem" pointSize="17"/>
                                                <color key="textColor" name="PrimaryColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="A3F-OR-SJs">
                                                <rect key="frame" x="19" y="54" width="337.5" height="402"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="1" translatesAutoresizingMaskIntoConstraints="NO" id="Gfd-Pz-A1b">
                                                        <rect key="frame" x="0.0" y="0.0" width="337.5" height="57"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Flat, House No. , Building" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="7XQ-df-Kdp">
                                                                <rect key="frame" x="0.0" y="0.0" width="337.5" height="20"/>
                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="20" id="03H-aF-1PH"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                <color key="textColor" name="GrayPlaceholder"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="D4R-LB-Zkp">
                                                                <rect key="frame" x="0.0" y="21" width="337.5" height="34"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="34" id="cXW-Nq-rYu"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                <textInputTraits key="textInputTraits" autocapitalizationType="words"/>
                                                            </textField>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="YIr-NY-17V">
                                                                <rect key="frame" x="0.0" y="56" width="337.5" height="1"/>
                                                                <color key="backgroundColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="1" id="6lM-eU-ntm"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                <color key="textColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                    </stackView>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="1" translatesAutoresizingMaskIntoConstraints="NO" id="SAo-Sf-wrp">
                                                        <rect key="frame" x="0.0" y="69" width="337.5" height="57"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Area Street" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="PeG-dC-4PH">
                                                                <rect key="frame" x="0.0" y="0.0" width="337.5" height="20"/>
                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="20" id="ubE-94-IYy"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                <color key="textColor" name="GrayPlaceholder"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="EI2-SX-tjx">
                                                                <rect key="frame" x="0.0" y="21" width="337.5" height="34"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="34" id="U4i-te-NLD"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                <textInputTraits key="textInputTraits" autocapitalizationType="words"/>
                                                            </textField>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="1Bb-QI-Gy8">
                                                                <rect key="frame" x="0.0" y="56" width="337.5" height="1"/>
                                                                <color key="backgroundColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="1" id="mvH-dI-dJn"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                <color key="textColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                    </stackView>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="1" translatesAutoresizingMaskIntoConstraints="NO" id="pZc-GQ-HtI">
                                                        <rect key="frame" x="0.0" y="138" width="337.5" height="57"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Landmark" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="3fe-ok-Qyw">
                                                                <rect key="frame" x="0.0" y="0.0" width="337.5" height="20"/>
                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="20" id="MJo-aL-jxp"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                <color key="textColor" name="GrayPlaceholder"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="OhD-WZ-hrT">
                                                                <rect key="frame" x="0.0" y="21" width="337.5" height="34"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="34" id="R1j-LU-Ejx"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                <textInputTraits key="textInputTraits" autocapitalizationType="words"/>
                                                            </textField>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="vaG-Ve-q0P">
                                                                <rect key="frame" x="0.0" y="56" width="337.5" height="1"/>
                                                                <color key="backgroundColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="1" id="ij0-gn-1qm"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                <color key="textColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                    </stackView>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="1" translatesAutoresizingMaskIntoConstraints="NO" id="dog-a7-pst">
                                                        <rect key="frame" x="0.0" y="207" width="337.5" height="57"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Pincode" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="1Jc-Ne-HRR">
                                                                <rect key="frame" x="0.0" y="0.0" width="337.5" height="20"/>
                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="20" id="SUL-zy-j1I"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                <color key="textColor" name="GrayPlaceholder"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="5tu-rF-AQa">
                                                                <rect key="frame" x="0.0" y="21" width="337.5" height="34"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="34" id="Moi-Yl-hVs"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                <textInputTraits key="textInputTraits" keyboardType="numberPad"/>
                                                            </textField>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Cqt-Gs-XTf">
                                                                <rect key="frame" x="0.0" y="56" width="337.5" height="1"/>
                                                                <color key="backgroundColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="1" id="YyN-7k-0Oh"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                <color key="textColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                    </stackView>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="1" translatesAutoresizingMaskIntoConstraints="NO" id="W3h-T8-cfx">
                                                        <rect key="frame" x="0.0" y="276" width="337.5" height="57"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="State" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="tUF-yS-Ofs">
                                                                <rect key="frame" x="0.0" y="0.0" width="337.5" height="20"/>
                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="20" id="0PW-Ou-soe"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                <color key="textColor" name="GrayPlaceholder"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="y1R-1G-BGh">
                                                                <rect key="frame" x="0.0" y="21" width="337.5" height="34"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="mNO-ME-Rge">
                                                                        <rect key="frame" x="4" y="4" width="306.5" height="26"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_dropdown" translatesAutoresizingMaskIntoConstraints="NO" id="1EK-AX-WtK">
                                                                        <rect key="frame" x="314.5" y="9.5" width="15" height="15"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="width" constant="15" id="18m-oc-XM9"/>
                                                                            <constraint firstAttribute="height" constant="15" id="rrv-NG-PO9"/>
                                                                        </constraints>
                                                                    </imageView>
                                                                </subviews>
                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstItem="mNO-ME-Rge" firstAttribute="top" secondItem="y1R-1G-BGh" secondAttribute="top" constant="4" id="FD2-1m-b0t"/>
                                                                    <constraint firstAttribute="trailing" secondItem="1EK-AX-WtK" secondAttribute="trailing" constant="8" id="P5t-4q-0Uz"/>
                                                                    <constraint firstItem="mNO-ME-Rge" firstAttribute="leading" secondItem="y1R-1G-BGh" secondAttribute="leading" constant="4" id="aXd-GE-9iQ"/>
                                                                    <constraint firstAttribute="height" constant="34" id="eJY-9L-9HS"/>
                                                                    <constraint firstItem="1EK-AX-WtK" firstAttribute="centerY" secondItem="y1R-1G-BGh" secondAttribute="centerY" id="gT7-FV-AMw"/>
                                                                    <constraint firstItem="1EK-AX-WtK" firstAttribute="leading" secondItem="mNO-ME-Rge" secondAttribute="trailing" constant="4" id="iiQ-bf-rlc"/>
                                                                    <constraint firstAttribute="bottom" secondItem="mNO-ME-Rge" secondAttribute="bottom" constant="4" id="oWN-He-aa8"/>
                                                                </constraints>
                                                            </view>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="UNM-7w-chR">
                                                                <rect key="frame" x="0.0" y="56" width="337.5" height="1"/>
                                                                <color key="backgroundColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="1" id="COl-Lm-bm5"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                <color key="textColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                    </stackView>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="1" translatesAutoresizingMaskIntoConstraints="NO" id="Z2i-Nd-RDS">
                                                        <rect key="frame" x="0.0" y="345" width="337.5" height="57"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Town/City" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="EMB-pd-7go">
                                                                <rect key="frame" x="0.0" y="0.0" width="337.5" height="20"/>
                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="20" id="A4R-bT-c8I"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                <color key="textColor" name="GrayPlaceholder"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="fiM-5W-ygZ">
                                                                <rect key="frame" x="0.0" y="21" width="337.5" height="34"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="fsY-J0-MmM">
                                                                        <rect key="frame" x="4" y="4" width="306.5" height="26"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_dropdown" translatesAutoresizingMaskIntoConstraints="NO" id="RBZ-ef-CfB">
                                                                        <rect key="frame" x="314.5" y="9.5" width="15" height="15"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="15" id="01g-zq-gLk"/>
                                                                            <constraint firstAttribute="width" constant="15" id="ypK-Pe-4zb"/>
                                                                        </constraints>
                                                                    </imageView>
                                                                </subviews>
                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstItem="fsY-J0-MmM" firstAttribute="leading" secondItem="fiM-5W-ygZ" secondAttribute="leading" constant="4" id="2O9-3e-gwW"/>
                                                                    <constraint firstAttribute="trailing" secondItem="RBZ-ef-CfB" secondAttribute="trailing" constant="8" id="2wf-mZ-xpb"/>
                                                                    <constraint firstItem="fsY-J0-MmM" firstAttribute="top" secondItem="fiM-5W-ygZ" secondAttribute="top" constant="4" id="HAm-VF-4f7"/>
                                                                    <constraint firstItem="RBZ-ef-CfB" firstAttribute="leading" secondItem="fsY-J0-MmM" secondAttribute="trailing" constant="4" id="YeQ-Df-sIT"/>
                                                                    <constraint firstItem="RBZ-ef-CfB" firstAttribute="centerY" secondItem="fiM-5W-ygZ" secondAttribute="centerY" id="fjG-MB-QB5"/>
                                                                    <constraint firstAttribute="height" constant="34" id="nyU-he-2ei"/>
                                                                    <constraint firstAttribute="bottom" secondItem="fsY-J0-MmM" secondAttribute="bottom" constant="4" id="peZ-0N-sgc"/>
                                                                </constraints>
                                                            </view>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="rAG-K8-oOU">
                                                                <rect key="frame" x="0.0" y="56" width="337.5" height="1"/>
                                                                <color key="backgroundColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="1" id="rB4-U0-pr0"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                <color key="textColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                    </stackView>
                                                </subviews>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="I2V-0f-tDq" firstAttribute="leading" secondItem="AKZ-5D-JbL" secondAttribute="leading" constant="16" id="7i6-Ay-6a9"/>
                                            <constraint firstItem="A3F-OR-SJs" firstAttribute="width" secondItem="AKZ-5D-JbL" secondAttribute="width" multiplier="0.9" id="IYH-aL-mDV"/>
                                            <constraint firstItem="A3F-OR-SJs" firstAttribute="top" secondItem="I2V-0f-tDq" secondAttribute="bottom" constant="12" id="WZr-jX-9hy"/>
                                            <constraint firstAttribute="height" constant="600.5" id="arY-N1-3mb"/>
                                            <constraint firstItem="I2V-0f-tDq" firstAttribute="top" secondItem="AKZ-5D-JbL" secondAttribute="top" constant="8" id="esi-U3-uF9"/>
                                            <constraint firstItem="A3F-OR-SJs" firstAttribute="centerX" secondItem="AKZ-5D-JbL" secondAttribute="centerX" id="n4u-Dy-Xch"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="AKZ-5D-JbL" firstAttribute="centerX" secondItem="f5J-B4-sMA" secondAttribute="centerX" id="E0V-Hw-nSr"/>
                                    <constraint firstItem="AKZ-5D-JbL" firstAttribute="leading" secondItem="xRA-ia-x1g" secondAttribute="leading" id="GJl-cy-hmt"/>
                                    <constraint firstItem="AKZ-5D-JbL" firstAttribute="top" secondItem="xRA-ia-x1g" secondAttribute="top" id="gcF-nK-EGV"/>
                                    <constraint firstAttribute="trailing" secondItem="AKZ-5D-JbL" secondAttribute="trailing" id="qgc-Pd-PzR"/>
                                    <constraint firstAttribute="bottom" secondItem="AKZ-5D-JbL" secondAttribute="bottom" id="t2o-os-yDc"/>
                                </constraints>
                                <viewLayoutGuide key="contentLayoutGuide" id="FVQ-SW-4gx"/>
                                <viewLayoutGuide key="frameLayoutGuide" id="xRA-ia-x1g"/>
                            </scrollView>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="b9F-Hp-fvl">
                                <rect key="frame" x="0.0" y="3" width="44" height="44"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="44" id="aEk-rz-Ktz"/>
                                    <constraint firstAttribute="width" constant="44" id="zjr-Wm-56b"/>
                                </constraints>
                                <inset key="imageEdgeInsets" minX="12" minY="12" maxX="12" maxY="12"/>
                                <state key="normal" image="ic_backarrow"/>
                                <connections>
                                    <action selector="backTapped:" destination="NvH-Qk-MPf" eventType="touchUpInside" id="WDA-c1-9qb"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="xIN-V9-PLN">
                                <rect key="frame" x="19" y="383.5" width="337.5" height="57"/>
                                <inset key="imageEdgeInsets" minX="12" minY="12" maxX="12" maxY="12"/>
                                <connections>
                                    <action selector="stateTapped:" destination="NvH-Qk-MPf" eventType="touchUpInside" id="iY4-3i-X2e"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="5Fg-w3-Mrg">
                                <rect key="frame" x="19" y="452.5" width="337.5" height="57"/>
                                <inset key="imageEdgeInsets" minX="12" minY="12" maxX="12" maxY="12"/>
                                <connections>
                                    <action selector="cityTapped:" destination="NvH-Qk-MPf" eventType="touchUpInside" id="VU9-gF-QTI"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="tFT-Ii-jRe">
                                <rect key="frame" x="307" y="599" width="44" height="44"/>
                                <color key="backgroundColor" name="PrimaryColor"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="44" id="Km6-sA-VTg"/>
                                    <constraint firstAttribute="height" constant="44" id="d25-Ex-QEt"/>
                                </constraints>
                                <inset key="imageEdgeInsets" minX="11" minY="11" maxX="11" maxY="11"/>
                                <state key="normal" image="ic_next"/>
                                <connections>
                                    <action selector="addProfileTapped:" destination="NvH-Qk-MPf" eventType="touchUpInside" id="yrb-VU-wsT"/>
                                </connections>
                            </button>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="qef-qo-rKB"/>
                        <color key="backgroundColor" name="PrimaryColor"/>
                        <constraints>
                            <constraint firstItem="b9F-Hp-fvl" firstAttribute="leading" secondItem="qef-qo-rKB" secondAttribute="leading" id="0Ej-kt-wC1"/>
                            <constraint firstItem="5Fg-w3-Mrg" firstAttribute="centerX" secondItem="Z2i-Nd-RDS" secondAttribute="centerX" id="0eG-qO-uiu"/>
                            <constraint firstItem="xIN-V9-PLN" firstAttribute="centerY" secondItem="W3h-T8-cfx" secondAttribute="centerY" id="2ZT-Tz-MBN"/>
                            <constraint firstItem="5Fg-w3-Mrg" firstAttribute="width" secondItem="Z2i-Nd-RDS" secondAttribute="width" id="EA5-Ot-HMm"/>
                            <constraint firstItem="qef-qo-rKB" firstAttribute="trailing" secondItem="tFT-Ii-jRe" secondAttribute="trailing" constant="24" id="NrB-zx-A6E"/>
                            <constraint firstItem="5Fg-w3-Mrg" firstAttribute="height" secondItem="Z2i-Nd-RDS" secondAttribute="height" id="PtY-bb-3LI"/>
                            <constraint firstItem="5Fg-w3-Mrg" firstAttribute="centerY" secondItem="Z2i-Nd-RDS" secondAttribute="centerY" id="RTt-Fa-4ia"/>
                            <constraint firstItem="b9F-Hp-fvl" firstAttribute="top" secondItem="qef-qo-rKB" secondAttribute="top" constant="3" id="XHG-hv-4Nm"/>
                            <constraint firstItem="f5J-B4-sMA" firstAttribute="leading" secondItem="qef-qo-rKB" secondAttribute="leading" id="YZj-eB-3qi"/>
                            <constraint firstItem="xIN-V9-PLN" firstAttribute="width" secondItem="W3h-T8-cfx" secondAttribute="width" id="aCS-rn-Qlp"/>
                            <constraint firstItem="xIN-V9-PLN" firstAttribute="centerX" secondItem="W3h-T8-cfx" secondAttribute="centerX" id="krB-jd-9iv"/>
                            <constraint firstItem="xIN-V9-PLN" firstAttribute="height" secondItem="W3h-T8-cfx" secondAttribute="height" id="qEE-mv-PbU"/>
                            <constraint firstItem="f5J-B4-sMA" firstAttribute="height" secondItem="qef-qo-rKB" secondAttribute="height" multiplier="0.92" id="vH6-hg-z10"/>
                            <constraint firstItem="qef-qo-rKB" firstAttribute="trailing" secondItem="f5J-B4-sMA" secondAttribute="trailing" id="wdx-Sz-f2R"/>
                            <constraint firstItem="qef-qo-rKB" firstAttribute="bottom" secondItem="f5J-B4-sMA" secondAttribute="bottom" id="xsK-Wb-uZ7"/>
                            <constraint firstItem="qef-qo-rKB" firstAttribute="bottom" secondItem="tFT-Ii-jRe" secondAttribute="bottom" constant="24" id="yOH-Sj-kxa"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="addProfileButton" destination="tFT-Ii-jRe" id="6D6-W5-HQm"/>
                        <outlet property="areaTextfield" destination="EI2-SX-tjx" id="xhB-VT-dqL"/>
                        <outlet property="cityButton" destination="5Fg-w3-Mrg" id="hvk-R4-71W"/>
                        <outlet property="cityLabel" destination="fsY-J0-MmM" id="Ll3-y8-Jbi"/>
                        <outlet property="flatTextField" destination="D4R-LB-Zkp" id="FbR-2u-bOa"/>
                        <outlet property="landmarkTextField" destination="OhD-WZ-hrT" id="SAM-Lv-LVS"/>
                        <outlet property="pincodeTextField" destination="5tu-rF-AQa" id="QYU-5Z-4pi"/>
                        <outlet property="scrollDetails" destination="f5J-B4-sMA" id="fNB-b9-egW"/>
                        <outlet property="stateButton" destination="xIN-V9-PLN" id="jBh-1I-wut"/>
                        <outlet property="stateLabel" destination="mNO-ME-Rge" id="z8c-xI-qlb"/>
                        <outlet property="viewMain" destination="AKZ-5D-JbL" id="ZTv-Es-DGp"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="NGg-ua-MjT" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="330" y="133"/>
        </scene>
    </scenes>
    <resources>
        <image name="ic_add" width="128" height="128"/>
        <image name="ic_address" width="500" height="425"/>
        <image name="ic_back_arrow" width="64" height="64"/>
        <image name="ic_backarrow" width="64" height="64"/>
        <image name="ic_cancel" width="64" height="64"/>
        <image name="ic_checked" width="500" height="500"/>
        <image name="ic_deleteAccount" width="80" height="80"/>
        <image name="ic_dob" width="500" height="500"/>
        <image name="ic_dropdown" width="64" height="64"/>
        <image name="ic_edit" width="32" height="32"/>
        <image name="ic_gender" width="500" height="500"/>
        <image name="ic_mfd" width="500" height="500"/>
        <image name="ic_model" width="500" height="500"/>
        <image name="ic_next" width="64" height="64"/>
        <image name="ic_phone" width="500" height="500"/>
        <image name="ic_registration" width="500" height="500"/>
        <image name="ic_uncheck" width="500" height="500"/>
        <image name="ic_user_address" width="500" height="500"/>
        <namedColor name="GrayPlaceholder">
            <color red="0.0" green="0.0" blue="0.0" alpha="0.5" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Primary">
            <color red="0.016000000759959221" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="PrimaryColor">
            <color red="0.015686274509803921" green="0.49411764705882355" blue="0.42745098039215684" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="PrimarySelection">
            <color red="0.59999999999999998" green="0.59999999999999998" blue="0.59999999999999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="PrimaryTextColorLight">
            <color red="0.32899999618530273" green="0.32400000095367432" blue="0.4779999852180481" alpha="0.33000001311302185" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="SecondaryGrayText">
            <color red="0.36470588235294116" green="0.36470588235294116" blue="0.36470588235294116" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="UserProfileBG">
            <color red="0.97647058823529409" green="0.97647058823529409" blue="0.97647058823529409" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>

<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="19455" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="19454"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--Order CardVC-->
        <scene sceneID="s0d-6b-0kx">
            <objects>
                <viewController storyboardIdentifier="OrderCardVC" id="Y6W-OH-hqX" customClass="OrderCardVC" customModule="NXC_EV_Solutions" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="5EZ-qb-Rvc">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="vy5-xE-BoY">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="P3F-b5-H42">
                                        <rect key="frame" x="12" y="5" width="34" height="34"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="34" id="1X2-6Z-D9Y"/>
                                            <constraint firstAttribute="width" constant="34" id="xia-4F-AOk"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="8" minY="6" maxX="8" maxY="6"/>
                                        <state key="normal" image="ic_back_arrow"/>
                                        <connections>
                                            <action selector="backAction:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="qJZ-8Z-WTO"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Order RFID" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="fX8-ri-gBr">
                                        <rect key="frame" x="144" y="12" width="87.5" height="20.5"/>
                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                        <color key="textColor" red="0.070588235289999995" green="0.070588235289999995" blue="0.070588235289999995" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="xsX-yO-S3k">
                                        <rect key="frame" x="329" y="5" width="34" height="34"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="34" id="6db-wS-cZ9"/>
                                            <constraint firstAttribute="width" constant="34" id="8da-0G-jyp"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="8" minY="8" maxX="8" maxY="8"/>
                                        <state key="normal" image="ic_about"/>
                                        <connections>
                                            <action selector="infoTapped:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="6Ow-xQ-Ucj"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="xsX-yO-S3k" secondAttribute="trailing" constant="12" id="1se-Dk-pqP"/>
                                    <constraint firstItem="P3F-b5-H42" firstAttribute="leading" secondItem="vy5-xE-BoY" secondAttribute="leading" constant="12" id="2dq-zJ-rd2"/>
                                    <constraint firstItem="xsX-yO-S3k" firstAttribute="centerY" secondItem="vy5-xE-BoY" secondAttribute="centerY" id="92C-IJ-bok"/>
                                    <constraint firstAttribute="height" constant="44" id="Cxh-uy-OQ0"/>
                                    <constraint firstItem="fX8-ri-gBr" firstAttribute="centerY" secondItem="vy5-xE-BoY" secondAttribute="centerY" id="J2V-wV-Tt7"/>
                                    <constraint firstItem="fX8-ri-gBr" firstAttribute="centerX" secondItem="vy5-xE-BoY" secondAttribute="centerX" id="KFZ-g8-9Mb"/>
                                    <constraint firstItem="P3F-b5-H42" firstAttribute="centerY" secondItem="vy5-xE-BoY" secondAttribute="centerY" id="eBD-SQ-2tI"/>
                                </constraints>
                            </view>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Pbi-ki-u2P">
                                <rect key="frame" x="19" y="607" width="337.5" height="44"/>
                                <color key="backgroundColor" name="Primary"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="44" id="2Ph-IG-EJZ"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                <state key="normal" title="Order RFID">
                                    <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                </state>
                                <connections>
                                    <action selector="OrderRFIDAction:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="ndf-WT-C50"/>
                                </connections>
                            </button>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="V8j-Cx-0GX">
                                <rect key="frame" x="0.0" y="52" width="375" height="547"/>
                                <subviews>
                                    <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="k6s-9b-Fpa">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="610"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="r2Q-Yh-yhz">
                                                <rect key="frame" x="0.0" y="12" width="375" height="586"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="1" translatesAutoresizingMaskIntoConstraints="NO" id="J9p-e3-aqv">
                                                        <rect key="frame" x="19" y="0.0" width="337.5" height="57"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Full Name" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="BjZ-AU-T3u">
                                                                <rect key="frame" x="0.0" y="0.0" width="337.5" height="20"/>
                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="20" id="xUL-BO-oT6"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                <color key="textColor" systemColor="systemGrayColor"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="3VL-CS-21K">
                                                                <rect key="frame" x="0.0" y="21" width="337.5" height="34"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="34" id="wzq-in-H9r"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                <textInputTraits key="textInputTraits"/>
                                                            </textField>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="1KX-X4-1HX">
                                                                <rect key="frame" x="0.0" y="56" width="337.5" height="1"/>
                                                                <color key="backgroundColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="1" id="dpc-gi-rta"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                <color key="textColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                    </stackView>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="1" translatesAutoresizingMaskIntoConstraints="NO" id="gV1-Dg-rOm">
                                                        <rect key="frame" x="19" y="69" width="337.5" height="57"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Phone Number" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="gd0-PD-HH2">
                                                                <rect key="frame" x="0.0" y="0.0" width="337.5" height="20"/>
                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="20" id="eJl-98-BQZ"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                <color key="textColor" systemColor="systemGrayColor"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="381-pQ-cxB">
                                                                <rect key="frame" x="0.0" y="21" width="337.5" height="34"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="34" id="cz4-eI-F7V"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                <textInputTraits key="textInputTraits" keyboardType="phonePad"/>
                                                            </textField>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="pT5-tW-djA">
                                                                <rect key="frame" x="0.0" y="56" width="337.5" height="1"/>
                                                                <color key="backgroundColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="1" id="Gnw-DM-jDy"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                <color key="textColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                    </stackView>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="1" translatesAutoresizingMaskIntoConstraints="NO" id="aQ8-5f-n5q">
                                                        <rect key="frame" x="19" y="138" width="337.5" height="57"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Flat, House No. , Building" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="b6f-8b-ZBs">
                                                                <rect key="frame" x="0.0" y="0.0" width="337.5" height="20"/>
                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="20" id="Xiq-Xr-Uuf"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                <color key="textColor" systemColor="systemGrayColor"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="g1g-hL-gio">
                                                                <rect key="frame" x="0.0" y="21" width="337.5" height="34"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="34" id="PNy-zy-8bo"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                <textInputTraits key="textInputTraits"/>
                                                            </textField>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ju1-lX-BBH">
                                                                <rect key="frame" x="0.0" y="56" width="337.5" height="1"/>
                                                                <color key="backgroundColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="1" id="LFB-Px-KXv"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                <color key="textColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                    </stackView>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="1" translatesAutoresizingMaskIntoConstraints="NO" id="LMO-Xd-Vby">
                                                        <rect key="frame" x="19" y="207" width="337.5" height="57"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Area Street" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Fcj-DH-9Z1">
                                                                <rect key="frame" x="0.0" y="0.0" width="337.5" height="20"/>
                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="20" id="ZeV-Vg-yHu"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                <color key="textColor" systemColor="systemGrayColor"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="Keo-81-06R">
                                                                <rect key="frame" x="0.0" y="21" width="337.5" height="34"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="34" id="cy3-LK-ywt"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                <textInputTraits key="textInputTraits"/>
                                                            </textField>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="4Mh-HO-UOA">
                                                                <rect key="frame" x="0.0" y="56" width="337.5" height="1"/>
                                                                <color key="backgroundColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="1" id="zpo-yM-N6A"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                <color key="textColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                    </stackView>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="1" translatesAutoresizingMaskIntoConstraints="NO" id="sf8-zo-IhR">
                                                        <rect key="frame" x="19" y="276" width="337.5" height="57"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Landmark" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="NKt-Cx-Rvf">
                                                                <rect key="frame" x="0.0" y="0.0" width="337.5" height="20"/>
                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="20" id="Ra8-Go-Iij"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                <color key="textColor" systemColor="systemGrayColor"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="9Lx-vF-rsd">
                                                                <rect key="frame" x="0.0" y="21" width="337.5" height="34"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="34" id="S24-cX-cZx"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                <textInputTraits key="textInputTraits"/>
                                                            </textField>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="6NN-tI-8hn">
                                                                <rect key="frame" x="0.0" y="56" width="337.5" height="1"/>
                                                                <color key="backgroundColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="1" id="xtM-2G-hPR"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                <color key="textColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                    </stackView>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="1" translatesAutoresizingMaskIntoConstraints="NO" id="Ook-TJ-Qxx">
                                                        <rect key="frame" x="19" y="345" width="337.5" height="57"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Pincode" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="FQw-8X-MVE">
                                                                <rect key="frame" x="0.0" y="0.0" width="337.5" height="20"/>
                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="20" id="fA1-nv-FXw"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                <color key="textColor" systemColor="systemGrayColor"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="Ojk-eZ-ReT">
                                                                <rect key="frame" x="0.0" y="21" width="337.5" height="34"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="34" id="axW-yl-L1f"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                <textInputTraits key="textInputTraits" keyboardType="numberPad"/>
                                                            </textField>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="e1V-ix-BXn">
                                                                <rect key="frame" x="0.0" y="56" width="337.5" height="1"/>
                                                                <color key="backgroundColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="1" id="CQZ-QY-dWg"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                <color key="textColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                    </stackView>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="1" translatesAutoresizingMaskIntoConstraints="NO" id="aLz-LD-Rhb">
                                                        <rect key="frame" x="19" y="414" width="337.5" height="57"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="State" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="7gc-rw-bog">
                                                                <rect key="frame" x="0.0" y="0.0" width="337.5" height="20"/>
                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="20" id="tgt-0w-tJW"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                <color key="textColor" name="GrayPlaceholder"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="WK9-gW-9R1">
                                                                <rect key="frame" x="0.0" y="21" width="337.5" height="34"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="JqY-bu-LIw">
                                                                        <rect key="frame" x="4" y="4" width="306.5" height="26"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_dropdown" translatesAutoresizingMaskIntoConstraints="NO" id="uow-qX-zJx">
                                                                        <rect key="frame" x="314.5" y="9.5" width="15" height="15"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="width" constant="15" id="Kls-fn-euz"/>
                                                                            <constraint firstAttribute="height" constant="15" id="qIS-Ql-Vrf"/>
                                                                        </constraints>
                                                                    </imageView>
                                                                </subviews>
                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstItem="uow-qX-zJx" firstAttribute="centerY" secondItem="WK9-gW-9R1" secondAttribute="centerY" id="8DT-3q-qX7"/>
                                                                    <constraint firstItem="JqY-bu-LIw" firstAttribute="top" secondItem="WK9-gW-9R1" secondAttribute="top" constant="4" id="NN4-OW-qhJ"/>
                                                                    <constraint firstItem="uow-qX-zJx" firstAttribute="leading" secondItem="JqY-bu-LIw" secondAttribute="trailing" constant="4" id="T0d-ai-k84"/>
                                                                    <constraint firstAttribute="trailing" secondItem="uow-qX-zJx" secondAttribute="trailing" constant="8" id="dJZ-EX-GRk"/>
                                                                    <constraint firstItem="JqY-bu-LIw" firstAttribute="leading" secondItem="WK9-gW-9R1" secondAttribute="leading" constant="4" id="iCs-yX-Mnh"/>
                                                                    <constraint firstAttribute="bottom" secondItem="JqY-bu-LIw" secondAttribute="bottom" constant="4" id="kMG-a1-Fgw"/>
                                                                    <constraint firstAttribute="height" constant="34" id="w3P-HF-PFd"/>
                                                                </constraints>
                                                            </view>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="STd-kd-uDo">
                                                                <rect key="frame" x="0.0" y="56" width="337.5" height="1"/>
                                                                <color key="backgroundColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="1" id="Y1E-q3-cWz"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                <color key="textColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                    </stackView>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="1" translatesAutoresizingMaskIntoConstraints="NO" id="tss-S1-M6K">
                                                        <rect key="frame" x="19" y="483" width="337.5" height="57"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Town/City" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="jLo-cf-YMF">
                                                                <rect key="frame" x="0.0" y="0.0" width="337.5" height="20"/>
                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="20" id="mn7-1D-sfh"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                <color key="textColor" name="GrayPlaceholder"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="BcE-HB-UKY">
                                                                <rect key="frame" x="0.0" y="21" width="337.5" height="34"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="jIb-9B-AwR">
                                                                        <rect key="frame" x="4" y="4" width="306.5" height="26"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_dropdown" translatesAutoresizingMaskIntoConstraints="NO" id="pUx-xs-qUR">
                                                                        <rect key="frame" x="314.5" y="9.5" width="15" height="15"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="15" id="jdl-Uw-3qb"/>
                                                                            <constraint firstAttribute="width" constant="15" id="vUL-cw-14W"/>
                                                                        </constraints>
                                                                    </imageView>
                                                                </subviews>
                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstItem="pUx-xs-qUR" firstAttribute="leading" secondItem="jIb-9B-AwR" secondAttribute="trailing" constant="4" id="1rq-TK-pfy"/>
                                                                    <constraint firstItem="jIb-9B-AwR" firstAttribute="leading" secondItem="BcE-HB-UKY" secondAttribute="leading" constant="4" id="LJP-NN-baQ"/>
                                                                    <constraint firstAttribute="trailing" secondItem="pUx-xs-qUR" secondAttribute="trailing" constant="8" id="NI3-mk-hcr"/>
                                                                    <constraint firstItem="jIb-9B-AwR" firstAttribute="top" secondItem="BcE-HB-UKY" secondAttribute="top" constant="4" id="PWd-AA-bMO"/>
                                                                    <constraint firstItem="pUx-xs-qUR" firstAttribute="centerY" secondItem="BcE-HB-UKY" secondAttribute="centerY" id="Wq4-Jb-3qI"/>
                                                                    <constraint firstAttribute="bottom" secondItem="jIb-9B-AwR" secondAttribute="bottom" constant="4" id="aQG-yD-Z7A"/>
                                                                    <constraint firstAttribute="height" constant="34" id="xjg-8w-3hB"/>
                                                                </constraints>
                                                            </view>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="dyo-pV-SRb">
                                                                <rect key="frame" x="0.0" y="56" width="337.5" height="1"/>
                                                                <color key="backgroundColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="1" id="Snp-Qf-cgD"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                <color key="textColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                    </stackView>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="H59-Th-m1f">
                                                        <rect key="frame" x="0.0" y="552" width="375" height="34"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="RFID Card Charges : ₹250" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="gNF-Gy-3h4">
                                                                <rect key="frame" x="103" y="8.5" width="169" height="17"/>
                                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                <nil key="textColor"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                        <color key="backgroundColor" name="PaymentNote"/>
                                                        <constraints>
                                                            <constraint firstItem="gNF-Gy-3h4" firstAttribute="centerY" secondItem="H59-Th-m1f" secondAttribute="centerY" id="PAq-Q4-3Nh"/>
                                                            <constraint firstItem="gNF-Gy-3h4" firstAttribute="centerX" secondItem="H59-Th-m1f" secondAttribute="centerX" id="PR0-kx-TmE"/>
                                                            <constraint firstAttribute="height" constant="34" id="yIX-3a-bbl"/>
                                                        </constraints>
                                                    </view>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstItem="tss-S1-M6K" firstAttribute="width" secondItem="r2Q-Yh-yhz" secondAttribute="width" multiplier="0.9" id="3mq-Kx-7y6"/>
                                                    <constraint firstItem="H59-Th-m1f" firstAttribute="width" secondItem="r2Q-Yh-yhz" secondAttribute="width" id="X1Y-oa-DeN"/>
                                                    <constraint firstItem="gV1-Dg-rOm" firstAttribute="width" secondItem="r2Q-Yh-yhz" secondAttribute="width" multiplier="0.9" id="dt6-hi-c4H"/>
                                                    <constraint firstItem="aQ8-5f-n5q" firstAttribute="width" secondItem="r2Q-Yh-yhz" secondAttribute="width" multiplier="0.9" id="m2q-cB-rOH"/>
                                                    <constraint firstItem="sf8-zo-IhR" firstAttribute="width" secondItem="r2Q-Yh-yhz" secondAttribute="width" multiplier="0.9" id="pdg-7f-OhX"/>
                                                    <constraint firstItem="aLz-LD-Rhb" firstAttribute="width" secondItem="r2Q-Yh-yhz" secondAttribute="width" multiplier="0.9" id="qGf-wA-Ftr"/>
                                                    <constraint firstItem="J9p-e3-aqv" firstAttribute="width" secondItem="r2Q-Yh-yhz" secondAttribute="width" multiplier="0.9" id="tf3-WO-mxx"/>
                                                    <constraint firstItem="Ook-TJ-Qxx" firstAttribute="width" secondItem="r2Q-Yh-yhz" secondAttribute="width" multiplier="0.9" id="uwm-1l-5aZ"/>
                                                    <constraint firstItem="LMO-Xd-Vby" firstAttribute="width" secondItem="r2Q-Yh-yhz" secondAttribute="width" multiplier="0.9" id="xVO-LT-95g"/>
                                                </constraints>
                                            </stackView>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ZB4-NH-0bE">
                                                <rect key="frame" x="19" y="426" width="337.5" height="57"/>
                                                <inset key="imageEdgeInsets" minX="12" minY="12" maxX="12" maxY="12"/>
                                                <connections>
                                                    <action selector="stateTapped:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="csE-4L-fIi"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="pnv-E2-9l3">
                                                <rect key="frame" x="19" y="495" width="337.5" height="57"/>
                                                <inset key="imageEdgeInsets" minX="12" minY="12" maxX="12" maxY="12"/>
                                                <connections>
                                                    <action selector="cityTapped:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="RyW-M7-Yc8"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="ZB4-NH-0bE" firstAttribute="width" secondItem="aLz-LD-Rhb" secondAttribute="width" id="0qD-Xf-hJW"/>
                                            <constraint firstItem="ZB4-NH-0bE" firstAttribute="centerY" secondItem="aLz-LD-Rhb" secondAttribute="centerY" id="0rS-o1-cid"/>
                                            <constraint firstItem="pnv-E2-9l3" firstAttribute="centerX" secondItem="tss-S1-M6K" secondAttribute="centerX" id="2Gt-iY-zyE"/>
                                            <constraint firstItem="pnv-E2-9l3" firstAttribute="height" secondItem="tss-S1-M6K" secondAttribute="height" id="DYV-Ec-QOs"/>
                                            <constraint firstAttribute="trailing" secondItem="r2Q-Yh-yhz" secondAttribute="trailing" id="Pzz-QL-xGY"/>
                                            <constraint firstItem="r2Q-Yh-yhz" firstAttribute="width" secondItem="k6s-9b-Fpa" secondAttribute="width" id="VIn-FC-4k3"/>
                                            <constraint firstItem="r2Q-Yh-yhz" firstAttribute="top" secondItem="k6s-9b-Fpa" secondAttribute="top" constant="12" id="XPu-Gx-ekp"/>
                                            <constraint firstItem="ZB4-NH-0bE" firstAttribute="height" secondItem="aLz-LD-Rhb" secondAttribute="height" id="YRa-an-Mru"/>
                                            <constraint firstItem="pnv-E2-9l3" firstAttribute="width" secondItem="tss-S1-M6K" secondAttribute="width" id="d9B-X0-jWN"/>
                                            <constraint firstItem="ZB4-NH-0bE" firstAttribute="centerX" secondItem="aLz-LD-Rhb" secondAttribute="centerX" id="ekh-3D-9d8"/>
                                            <constraint firstItem="r2Q-Yh-yhz" firstAttribute="leading" secondItem="k6s-9b-Fpa" secondAttribute="leading" id="fyF-uY-hJl"/>
                                            <constraint firstItem="pnv-E2-9l3" firstAttribute="centerY" secondItem="tss-S1-M6K" secondAttribute="centerY" id="ruf-2v-mbI"/>
                                            <constraint firstItem="r2Q-Yh-yhz" firstAttribute="centerY" secondItem="k6s-9b-Fpa" secondAttribute="centerY" id="zqS-RJ-BIe"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="0QG-b7-PYg" firstAttribute="top" secondItem="k6s-9b-Fpa" secondAttribute="top" id="1MW-6G-Ubn"/>
                                    <constraint firstItem="0QG-b7-PYg" firstAttribute="leading" secondItem="k6s-9b-Fpa" secondAttribute="leading" id="aw1-9x-Ltl"/>
                                    <constraint firstItem="k6s-9b-Fpa" firstAttribute="centerX" secondItem="d9P-Mk-jIU" secondAttribute="centerX" id="iD9-dn-bFZ"/>
                                    <constraint firstItem="k6s-9b-Fpa" firstAttribute="bottom" secondItem="0QG-b7-PYg" secondAttribute="bottom" id="iUe-xT-dVh"/>
                                    <constraint firstItem="k6s-9b-Fpa" firstAttribute="trailing" secondItem="0QG-b7-PYg" secondAttribute="trailing" id="qcB-7A-nFi"/>
                                </constraints>
                                <viewLayoutGuide key="contentLayoutGuide" id="0QG-b7-PYg"/>
                                <viewLayoutGuide key="frameLayoutGuide" id="d9P-Mk-jIU"/>
                            </scrollView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="W1n-4G-ixh">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="TkV-f5-EWs">
                                        <rect key="frame" x="19" y="196.5" width="337.5" height="274.5"/>
                                        <subviews>
                                            <button opaque="NO" alpha="0.0" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="S6v-Pq-b5n">
                                                <rect key="frame" x="293.5" y="0.0" width="44" height="44"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="44" id="pyC-S1-PKa"/>
                                                    <constraint firstAttribute="height" constant="44" id="rfC-7q-UCP"/>
                                                </constraints>
                                                <inset key="imageEdgeInsets" minX="14" minY="14" maxX="14" maxY="14"/>
                                                <state key="normal" image="ic_cancel"/>
                                                <connections>
                                                    <action selector="cancelButtonTapped:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="CzZ-nN-QX6"/>
                                                </connections>
                                            </button>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="30" baselineRelativeArrangement="YES" translatesAutoresizingMaskIntoConstraints="NO" id="aDM-jf-GUS">
                                                <rect key="frame" x="16.5" y="16" width="304" height="182.5"/>
                                                <subviews>
                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_thumb_up" translatesAutoresizingMaskIntoConstraints="NO" id="0QT-tI-WBc">
                                                        <rect key="frame" x="119.5" y="0.0" width="65" height="65"/>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="65" id="O8I-Qm-qcx"/>
                                                            <constraint firstAttribute="width" constant="65" id="OXF-5x-gU5"/>
                                                        </constraints>
                                                    </imageView>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="RFID Card" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="22D-gS-XvE">
                                                        <rect key="frame" x="112" y="79" width="80" height="20.5"/>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="You will receive your RFID Card in 8-10 working days, you will get updates regarding delivery on your registered number." textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="82x-bn-63F">
                                                        <rect key="frame" x="15.5" y="110.5" width="273" height="72"/>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                        <color key="textColor" white="0.0" alpha="0.5" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstItem="82x-bn-63F" firstAttribute="width" secondItem="aDM-jf-GUS" secondAttribute="width" multiplier="0.9" id="vel-1p-Vr8"/>
                                                </constraints>
                                            </stackView>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="20" translatesAutoresizingMaskIntoConstraints="NO" id="UlU-bp-XfZ">
                                                <rect key="frame" x="16" y="214.5" width="305.5" height="44"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="24" translatesAutoresizingMaskIntoConstraints="NO" id="ajI-5i-2Bo">
                                                        <rect key="frame" x="15" y="0.0" width="275" height="44"/>
                                                        <subviews>
                                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="0h5-oS-Msm">
                                                                <rect key="frame" x="0.0" y="0.0" width="275" height="44"/>
                                                                <color key="backgroundColor" name="Primary"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="44" id="oby-iV-NYr"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                                                <state key="normal" title="OK">
                                                                    <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                </state>
                                                                <connections>
                                                                    <action selector="okButtonTapped:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="M9R-WL-5yq"/>
                                                                </connections>
                                                            </button>
                                                        </subviews>
                                                    </stackView>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstItem="ajI-5i-2Bo" firstAttribute="width" secondItem="UlU-bp-XfZ" secondAttribute="width" multiplier="0.9" id="mR0-R3-MFa"/>
                                                </constraints>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstItem="S6v-Pq-b5n" firstAttribute="top" secondItem="TkV-f5-EWs" secondAttribute="top" id="23g-mD-3Iv"/>
                                            <constraint firstItem="aDM-jf-GUS" firstAttribute="top" secondItem="TkV-f5-EWs" secondAttribute="top" constant="16" id="8t7-6f-sGu"/>
                                            <constraint firstAttribute="bottom" secondItem="UlU-bp-XfZ" secondAttribute="bottom" constant="16" id="LQL-dv-mau"/>
                                            <constraint firstItem="UlU-bp-XfZ" firstAttribute="width" secondItem="TkV-f5-EWs" secondAttribute="width" multiplier="0.905185" id="Zr8-PK-cRN"/>
                                            <constraint firstItem="UlU-bp-XfZ" firstAttribute="centerX" secondItem="TkV-f5-EWs" secondAttribute="centerX" id="fzC-0b-6sg"/>
                                            <constraint firstItem="aDM-jf-GUS" firstAttribute="centerX" secondItem="TkV-f5-EWs" secondAttribute="centerX" id="m64-cY-ibc"/>
                                            <constraint firstAttribute="trailing" secondItem="S6v-Pq-b5n" secondAttribute="trailing" id="mGa-hy-RQf"/>
                                            <constraint firstItem="aDM-jf-GUS" firstAttribute="width" secondItem="TkV-f5-EWs" secondAttribute="width" multiplier="0.9" id="sva-Ed-RfF"/>
                                            <constraint firstItem="UlU-bp-XfZ" firstAttribute="top" secondItem="aDM-jf-GUS" secondAttribute="bottom" constant="16" id="uom-ai-HJP"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="WXC-wL-Scb">
                                        <rect key="frame" x="19" y="214" width="337.5" height="239"/>
                                        <subviews>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="5t5-hJ-zrV">
                                                <rect key="frame" x="293.5" y="0.0" width="44" height="44"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="44" id="57r-V4-z0m"/>
                                                    <constraint firstAttribute="width" constant="44" id="vio-Dc-4Jn"/>
                                                </constraints>
                                                <inset key="imageEdgeInsets" minX="14" minY="14" maxX="14" maxY="14"/>
                                                <state key="normal" image="ic_cancel"/>
                                                <connections>
                                                    <action selector="cancelButtonTapped:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="GUQ-ai-Yhz"/>
                                                    <action selector="cancelPopTapped:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="KV7-Zq-ui6"/>
                                                </connections>
                                            </button>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="30" baselineRelativeArrangement="YES" translatesAutoresizingMaskIntoConstraints="NO" id="WFg-bD-VdN">
                                                <rect key="frame" x="16.5" y="16" width="304" height="147"/>
                                                <subviews>
                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_thumb_up" translatesAutoresizingMaskIntoConstraints="NO" id="1QZ-QF-MPE">
                                                        <rect key="frame" x="119.5" y="0.0" width="65" height="65"/>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="65" id="PVn-kR-S0H"/>
                                                            <constraint firstAttribute="width" constant="65" id="Xaz-FU-5Ne"/>
                                                        </constraints>
                                                    </imageView>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="RFID Card Order" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="tKN-rc-zXN">
                                                        <rect key="frame" x="87" y="79" width="130" height="20.5"/>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Please verify your address details. $50 will be charged from your wallet. " textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="S9C-3J-UxS">
                                                        <rect key="frame" x="15.5" y="111" width="273" height="36"/>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                        <color key="textColor" white="0.0" alpha="0.5" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstItem="S9C-3J-UxS" firstAttribute="width" secondItem="WFg-bD-VdN" secondAttribute="width" multiplier="0.9" id="eII-5Y-6DA"/>
                                                </constraints>
                                            </stackView>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="20" translatesAutoresizingMaskIntoConstraints="NO" id="Dbr-7l-n1R">
                                                <rect key="frame" x="16" y="179" width="305.5" height="44"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="24" translatesAutoresizingMaskIntoConstraints="NO" id="G5o-gU-TLQ">
                                                        <rect key="frame" x="15" y="0.0" width="275" height="44"/>
                                                        <subviews>
                                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="bIx-Mm-evw">
                                                                <rect key="frame" x="0.0" y="0.0" width="275" height="44"/>
                                                                <color key="backgroundColor" name="Primary"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="44" id="lVR-eh-Km4"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                                                <state key="normal" title="Order Now">
                                                                    <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                </state>
                                                                <connections>
                                                                    <action selector="orderNowTapped:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="iVN-Ol-Zac"/>
                                                                </connections>
                                                            </button>
                                                        </subviews>
                                                    </stackView>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstItem="G5o-gU-TLQ" firstAttribute="width" secondItem="Dbr-7l-n1R" secondAttribute="width" multiplier="0.9" id="djF-lP-VJV"/>
                                                </constraints>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstItem="WFg-bD-VdN" firstAttribute="width" secondItem="WXC-wL-Scb" secondAttribute="width" multiplier="0.9" id="736-m8-aIO"/>
                                            <constraint firstAttribute="bottom" secondItem="Dbr-7l-n1R" secondAttribute="bottom" constant="16" id="7lk-85-eH7"/>
                                            <constraint firstItem="WFg-bD-VdN" firstAttribute="centerX" secondItem="WXC-wL-Scb" secondAttribute="centerX" id="7rz-HU-Um0"/>
                                            <constraint firstItem="WFg-bD-VdN" firstAttribute="top" secondItem="WXC-wL-Scb" secondAttribute="top" constant="16" id="MAC-TC-N6j"/>
                                            <constraint firstItem="5t5-hJ-zrV" firstAttribute="top" secondItem="WXC-wL-Scb" secondAttribute="top" id="XCI-AK-Tft"/>
                                            <constraint firstItem="Dbr-7l-n1R" firstAttribute="width" secondItem="WXC-wL-Scb" secondAttribute="width" multiplier="0.905185" id="crt-Cx-pEO"/>
                                            <constraint firstItem="Dbr-7l-n1R" firstAttribute="centerX" secondItem="WXC-wL-Scb" secondAttribute="centerX" id="f9e-vP-F0K"/>
                                            <constraint firstAttribute="trailing" secondItem="5t5-hJ-zrV" secondAttribute="trailing" id="mkX-rL-dHB"/>
                                            <constraint firstItem="Dbr-7l-n1R" firstAttribute="top" secondItem="WFg-bD-VdN" secondAttribute="bottom" constant="16" id="rjl-3y-wqS"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.75" colorSpace="custom" customColorSpace="sRGB"/>
                                <constraints>
                                    <constraint firstItem="WXC-wL-Scb" firstAttribute="centerX" secondItem="W1n-4G-ixh" secondAttribute="centerX" id="2Zh-Pr-41K"/>
                                    <constraint firstItem="TkV-f5-EWs" firstAttribute="centerX" secondItem="W1n-4G-ixh" secondAttribute="centerX" id="4Pa-sl-gfB"/>
                                    <constraint firstItem="TkV-f5-EWs" firstAttribute="width" secondItem="W1n-4G-ixh" secondAttribute="width" multiplier="0.9" id="5rX-aS-IPW"/>
                                    <constraint firstItem="WXC-wL-Scb" firstAttribute="centerY" secondItem="W1n-4G-ixh" secondAttribute="centerY" id="70W-zk-chN"/>
                                    <constraint firstItem="WXC-wL-Scb" firstAttribute="width" secondItem="W1n-4G-ixh" secondAttribute="width" multiplier="0.9" id="865-gq-Vcc"/>
                                    <constraint firstItem="TkV-f5-EWs" firstAttribute="centerY" secondItem="W1n-4G-ixh" secondAttribute="centerY" id="hTZ-vk-eRp"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="vDu-zF-Fre"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="V8j-Cx-0GX" firstAttribute="centerX" secondItem="5EZ-qb-Rvc" secondAttribute="centerX" id="1ji-nA-VEe"/>
                            <constraint firstItem="vDu-zF-Fre" firstAttribute="trailing" secondItem="vy5-xE-BoY" secondAttribute="trailing" id="54v-bk-BVc"/>
                            <constraint firstItem="vDu-zF-Fre" firstAttribute="bottom" secondItem="Pbi-ki-u2P" secondAttribute="bottom" constant="16" id="87w-Kx-YlP"/>
                            <constraint firstItem="Pbi-ki-u2P" firstAttribute="centerX" secondItem="5EZ-qb-Rvc" secondAttribute="centerX" id="BdX-nb-HFD"/>
                            <constraint firstItem="V8j-Cx-0GX" firstAttribute="width" secondItem="vDu-zF-Fre" secondAttribute="width" id="G5h-xg-qed"/>
                            <constraint firstItem="W1n-4G-ixh" firstAttribute="leading" secondItem="vDu-zF-Fre" secondAttribute="leading" id="SGp-3H-ZNU"/>
                            <constraint firstItem="Pbi-ki-u2P" firstAttribute="width" secondItem="vDu-zF-Fre" secondAttribute="width" multiplier="0.9" id="UIy-0N-uA8"/>
                            <constraint firstItem="W1n-4G-ixh" firstAttribute="top" secondItem="vDu-zF-Fre" secondAttribute="top" id="VdB-wu-wip"/>
                            <constraint firstItem="vy5-xE-BoY" firstAttribute="leading" secondItem="vDu-zF-Fre" secondAttribute="leading" id="YAU-Ol-mZS"/>
                            <constraint firstItem="W1n-4G-ixh" firstAttribute="trailing" secondItem="vDu-zF-Fre" secondAttribute="trailing" id="cyh-eF-7yi"/>
                            <constraint firstItem="W1n-4G-ixh" firstAttribute="bottom" secondItem="vDu-zF-Fre" secondAttribute="bottom" id="mCm-m3-nnW"/>
                            <constraint firstItem="V8j-Cx-0GX" firstAttribute="top" secondItem="vy5-xE-BoY" secondAttribute="bottom" constant="8" id="nq9-nO-asM"/>
                            <constraint firstItem="vy5-xE-BoY" firstAttribute="top" secondItem="vDu-zF-Fre" secondAttribute="top" id="pAJ-7F-mg0"/>
                            <constraint firstItem="Pbi-ki-u2P" firstAttribute="top" secondItem="V8j-Cx-0GX" secondAttribute="bottom" constant="8" id="v2j-4U-piT"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="areaTextField" destination="Keo-81-06R" id="NNV-Mu-FhR"/>
                        <outlet property="btnBack" destination="P3F-b5-H42" id="LG1-es-Rqq"/>
                        <outlet property="btnInfo" destination="xsX-yO-S3k" id="AKR-FM-2eV"/>
                        <outlet property="btnOK" destination="0h5-oS-Msm" id="7Id-6z-6qy"/>
                        <outlet property="btnOKPop" destination="bIx-Mm-evw" id="Q8V-3z-zcM"/>
                        <outlet property="btnOrder" destination="Pbi-ki-u2P" id="K9y-Hw-Deg"/>
                        <outlet property="cityButton" destination="pnv-E2-9l3" id="SMJ-th-TZM"/>
                        <outlet property="cityLabel" destination="jIb-9B-AwR" id="gpR-Mo-iPH"/>
                        <outlet property="flatTextField" destination="g1g-hL-gio" id="ign-1S-hpN"/>
                        <outlet property="landmarkTextField" destination="9Lx-vF-rsd" id="yCy-Yt-CoH"/>
                        <outlet property="lblMsgPop" destination="S9C-3J-UxS" id="T5U-sb-3A1"/>
                        <outlet property="lblRFIDCharges" destination="gNF-Gy-3h4" id="cg9-4v-tPp"/>
                        <outlet property="lblTitlePop" destination="tKN-rc-zXN" id="GnE-Bl-xxg"/>
                        <outlet property="nameTextField" destination="3VL-CS-21K" id="fKC-eI-Dsb"/>
                        <outlet property="phoneTextField" destination="381-pQ-cxB" id="ubn-Mk-zFm"/>
                        <outlet property="pincodeTextField" destination="Ojk-eZ-ReT" id="MPt-no-VXK"/>
                        <outlet property="scrollDetails" destination="V8j-Cx-0GX" id="vt4-OT-aA0"/>
                        <outlet property="stateButton" destination="ZB4-NH-0bE" id="ql5-IN-czq"/>
                        <outlet property="stateLabel" destination="JqY-bu-LIw" id="xXF-j9-FAY"/>
                        <outlet property="viewBgAlert" destination="W1n-4G-ixh" id="zgf-FP-6Wt"/>
                        <outlet property="viewMainAlert" destination="TkV-f5-EWs" id="N67-bx-lh1"/>
                        <outlet property="viewMainPop" destination="WXC-wL-Scb" id="Cw0-2M-LMt"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Ief-a0-LHa" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="120.8" y="69.715142428785612"/>
        </scene>
        <!--Track ListVC-->
        <scene sceneID="WDN-uE-mEd">
            <objects>
                <viewController storyboardIdentifier="RFIDTrackListVC" id="cGG-rl-LsG" customClass="RFIDTrackListVC" customModule="NXC_EV_Solutions" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="3ch-uX-2F5">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="87F-Go-MXO">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Zvf-p8-cGv">
                                        <rect key="frame" x="12" y="5" width="34" height="34"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="34" id="iUV-EE-arx"/>
                                            <constraint firstAttribute="width" constant="34" id="k2S-Il-aoZ"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="8" minY="6" maxX="8" maxY="6"/>
                                        <state key="normal" image="ic_back_arrow"/>
                                        <connections>
                                            <action selector="backButtonTapped:" destination="cGG-rl-LsG" eventType="touchUpInside" id="SUt-75-NCg"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Your RFID Order" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="nMq-La-el7">
                                        <rect key="frame" x="123.5" y="12" width="128" height="20.5"/>
                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                        <color key="textColor" red="0.070588235289999995" green="0.070588235289999995" blue="0.070588235289999995" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="nMq-La-el7" firstAttribute="centerX" secondItem="87F-Go-MXO" secondAttribute="centerX" id="0iD-fe-Jhd"/>
                                    <constraint firstItem="Zvf-p8-cGv" firstAttribute="centerY" secondItem="87F-Go-MXO" secondAttribute="centerY" id="MTP-He-Z3S"/>
                                    <constraint firstItem="nMq-La-el7" firstAttribute="centerY" secondItem="87F-Go-MXO" secondAttribute="centerY" id="aAK-Dl-eDf"/>
                                    <constraint firstAttribute="height" constant="44" id="hau-6T-oQB"/>
                                    <constraint firstItem="Zvf-p8-cGv" firstAttribute="leading" secondItem="87F-Go-MXO" secondAttribute="leading" constant="12" id="l1Z-xM-1h9"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="OBF-vG-AYx">
                                <rect key="frame" x="8" y="52" width="359" height="607"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="4Xc-Wh-Ksg">
                                        <rect key="frame" x="104.5" y="214" width="150" height="118"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_no_data" translatesAutoresizingMaskIntoConstraints="NO" id="0r8-tK-zPz">
                                                <rect key="frame" x="0.0" y="0.0" width="150" height="100"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="100" id="awv-dp-cel"/>
                                                    <constraint firstAttribute="width" constant="150" id="lxp-7Z-nV4"/>
                                                </constraints>
                                            </imageView>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="No Records Found" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="kye-E8-0zd">
                                                <rect key="frame" x="11.5" y="100" width="127" height="18"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                <color key="textColor" white="0.0" alpha="0.69999999999999996" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="4Xc-Wh-Ksg" firstAttribute="centerX" secondItem="OBF-vG-AYx" secondAttribute="centerX" id="dTX-o3-ALp"/>
                                    <constraint firstItem="4Xc-Wh-Ksg" firstAttribute="centerY" secondItem="OBF-vG-AYx" secondAttribute="centerY" multiplier="0.9" id="wyy-Xe-sxU"/>
                                </constraints>
                            </view>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="03n-04-dX9">
                                <rect key="frame" x="8" y="52" width="359" height="607"/>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <prototypes>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="none" indentationWidth="10" reuseIdentifier="cell" id="71Y-vR-yJB" customClass="RFIDTrackListCell" customModule="NXC_EV_Solutions" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="44.5" width="359" height="58"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="71Y-vR-yJB" id="PAR-vn-cbd">
                                            <rect key="frame" x="0.0" y="0.0" width="359" height="58"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="N9y-44-DV9">
                                                    <rect key="frame" x="4" y="4" width="351" height="50"/>
                                                    <subviews>
                                                        <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="8W3-Fr-oph">
                                                            <rect key="frame" x="4" y="0.0" width="343" height="44"/>
                                                            <subviews>
                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="v6L-zK-C66">
                                                                    <rect key="frame" x="0.0" y="3" width="38" height="38"/>
                                                                    <subviews>
                                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_card_list" translatesAutoresizingMaskIntoConstraints="NO" id="4bZ-5E-ymj">
                                                                            <rect key="frame" x="8" y="8" width="22" height="22"/>
                                                                            <constraints>
                                                                                <constraint firstAttribute="height" constant="22" id="kEg-ck-bfA"/>
                                                                                <constraint firstAttribute="width" constant="22" id="qMb-y4-acf"/>
                                                                            </constraints>
                                                                        </imageView>
                                                                    </subviews>
                                                                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                                    <constraints>
                                                                        <constraint firstItem="4bZ-5E-ymj" firstAttribute="centerY" secondItem="v6L-zK-C66" secondAttribute="centerY" id="DbU-qM-rXB"/>
                                                                        <constraint firstAttribute="height" constant="38" id="iF0-nN-XVU"/>
                                                                        <constraint firstItem="4bZ-5E-ymj" firstAttribute="centerX" secondItem="v6L-zK-C66" secondAttribute="centerX" id="mPk-np-D8B"/>
                                                                        <constraint firstAttribute="width" constant="38" id="x3d-n8-zL4"/>
                                                                    </constraints>
                                                                </view>
                                                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="KWI-9u-Ych">
                                                                    <rect key="frame" x="46" y="0.0" width="277" height="44"/>
                                                                    <subviews>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Paid to EVC Charge" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="b2C-WX-sO0">
                                                                            <rect key="frame" x="0.0" y="0.0" width="277" height="24"/>
                                                                            <constraints>
                                                                                <constraint firstAttribute="height" constant="24" id="7Sw-3L-yxQ"/>
                                                                            </constraints>
                                                                            <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                            <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Delivered" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="5x9-Dv-lKn">
                                                                            <rect key="frame" x="0.0" y="24" width="277" height="20"/>
                                                                            <constraints>
                                                                                <constraint firstAttribute="height" constant="20" id="PTI-AH-aog"/>
                                                                            </constraints>
                                                                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                            <color key="textColor" name="PrimarySelection"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                    </subviews>
                                                                </stackView>
                                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_right_navigate" translatesAutoresizingMaskIntoConstraints="NO" id="q7W-Yv-1ku">
                                                                    <rect key="frame" x="331" y="16" width="12" height="12"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="height" constant="12" id="3hz-yD-jK6"/>
                                                                        <constraint firstAttribute="width" constant="12" id="Hcf-2K-gG4"/>
                                                                    </constraints>
                                                                </imageView>
                                                            </subviews>
                                                        </stackView>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="SA3-1Q-VvS">
                                                            <rect key="frame" x="0.0" y="49.5" width="351" height="0.5"/>
                                                            <color key="backgroundColor" name="PrimaryTextColor"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="0.5" id="FiP-xA-Hyh"/>
                                                            </constraints>
                                                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                            <nil key="textColor"/>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                    </subviews>
                                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <constraints>
                                                        <constraint firstAttribute="bottom" secondItem="8W3-Fr-oph" secondAttribute="bottom" constant="6" id="FF4-9b-aA0"/>
                                                        <constraint firstItem="8W3-Fr-oph" firstAttribute="top" secondItem="N9y-44-DV9" secondAttribute="top" id="GyJ-i4-4E2"/>
                                                        <constraint firstItem="SA3-1Q-VvS" firstAttribute="leading" secondItem="N9y-44-DV9" secondAttribute="leading" id="I5V-7B-tAQ"/>
                                                        <constraint firstAttribute="trailing" secondItem="8W3-Fr-oph" secondAttribute="trailing" constant="4" id="Mhc-l7-KEi"/>
                                                        <constraint firstAttribute="trailing" secondItem="SA3-1Q-VvS" secondAttribute="trailing" id="ZKZ-Ca-oJf"/>
                                                        <constraint firstItem="8W3-Fr-oph" firstAttribute="leading" secondItem="N9y-44-DV9" secondAttribute="leading" constant="4" id="nrz-HB-7UE"/>
                                                        <constraint firstAttribute="bottom" secondItem="SA3-1Q-VvS" secondAttribute="bottom" id="oaD-eP-DKP"/>
                                                    </constraints>
                                                </view>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="N9y-44-DV9" firstAttribute="top" secondItem="PAR-vn-cbd" secondAttribute="top" constant="4" id="2RC-bO-QgC"/>
                                                <constraint firstItem="N9y-44-DV9" firstAttribute="leading" secondItem="PAR-vn-cbd" secondAttribute="leading" constant="4" id="6k6-v9-JrU"/>
                                                <constraint firstAttribute="bottom" secondItem="N9y-44-DV9" secondAttribute="bottom" constant="4" id="DlF-jE-raG"/>
                                                <constraint firstAttribute="trailing" secondItem="N9y-44-DV9" secondAttribute="trailing" constant="4" id="GgQ-z8-ZNK"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <connections>
                                            <outlet property="nameLabel" destination="b2C-WX-sO0" id="KC9-0M-ZW7"/>
                                            <outlet property="timeLabel" destination="5x9-Dv-lKn" id="OzY-vc-bdP"/>
                                            <outlet property="viewIcon" destination="v6L-zK-C66" id="0tC-Tg-MGe"/>
                                            <outlet property="viewMain" destination="N9y-44-DV9" id="H6e-bf-Ank"/>
                                        </connections>
                                    </tableViewCell>
                                </prototypes>
                            </tableView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="hNu-JS-P7W"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="OBF-vG-AYx" firstAttribute="leading" secondItem="hNu-JS-P7W" secondAttribute="leading" constant="8" id="AMB-Pm-fqG"/>
                            <constraint firstItem="hNu-JS-P7W" firstAttribute="trailing" secondItem="OBF-vG-AYx" secondAttribute="trailing" constant="8" id="IM8-b5-ix7"/>
                            <constraint firstItem="03n-04-dX9" firstAttribute="leading" secondItem="hNu-JS-P7W" secondAttribute="leading" constant="8" id="Myn-If-CoB"/>
                            <constraint firstItem="hNu-JS-P7W" firstAttribute="bottom" secondItem="OBF-vG-AYx" secondAttribute="bottom" constant="8" id="OWj-KV-2gv"/>
                            <constraint firstItem="OBF-vG-AYx" firstAttribute="top" secondItem="87F-Go-MXO" secondAttribute="bottom" constant="8" id="Q03-N8-n6U"/>
                            <constraint firstItem="hNu-JS-P7W" firstAttribute="bottom" secondItem="03n-04-dX9" secondAttribute="bottom" constant="8" id="aCk-28-aJp"/>
                            <constraint firstItem="hNu-JS-P7W" firstAttribute="trailing" secondItem="03n-04-dX9" secondAttribute="trailing" constant="8" id="aqq-8H-XYv"/>
                            <constraint firstItem="87F-Go-MXO" firstAttribute="top" secondItem="hNu-JS-P7W" secondAttribute="top" id="fei-HX-IBb"/>
                            <constraint firstItem="87F-Go-MXO" firstAttribute="leading" secondItem="hNu-JS-P7W" secondAttribute="leading" id="sOa-t2-elC"/>
                            <constraint firstItem="03n-04-dX9" firstAttribute="top" secondItem="87F-Go-MXO" secondAttribute="bottom" constant="8" id="uhN-Y3-dVj"/>
                            <constraint firstItem="hNu-JS-P7W" firstAttribute="trailing" secondItem="87F-Go-MXO" secondAttribute="trailing" id="vZv-St-Xjc"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="btnBack" destination="Zvf-p8-cGv" id="O8j-99-AtJ"/>
                        <outlet property="tableTrackRFID" destination="03n-04-dX9" id="t6c-Ar-roo"/>
                        <outlet property="viewNoData" destination="OBF-vG-AYx" id="wcR-CE-jWy"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="IEB-JO-vNJ" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="773.60000000000002" y="69.715142428785612"/>
        </scene>
        <!--TrackRFID StatusVC-->
        <scene sceneID="vNN-c2-K1a">
            <objects>
                <viewController storyboardIdentifier="TrackRFIDStatusVC" id="DJT-cc-l5j" customClass="TrackRFIDStatusVC" customModule="NXC_EV_Solutions" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="bfL-zv-Zpb">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="DdI-83-0zB">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="K6l-L5-2Dm">
                                        <rect key="frame" x="12" y="5" width="34" height="34"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="34" id="2wT-bt-RjF"/>
                                            <constraint firstAttribute="height" constant="34" id="xl6-op-FJn"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="8" minY="6" maxX="8" maxY="6"/>
                                        <state key="normal" image="ic_back_arrow"/>
                                        <connections>
                                            <action selector="backButtonTapped:" destination="DJT-cc-l5j" eventType="touchUpInside" id="Lrc-1I-anI"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Track Order" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="1pX-8I-uRQ">
                                        <rect key="frame" x="140.5" y="12" width="94.5" height="20.5"/>
                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                        <color key="textColor" red="0.070588235289999995" green="0.070588235289999995" blue="0.070588235289999995" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="K6l-L5-2Dm" firstAttribute="centerY" secondItem="DdI-83-0zB" secondAttribute="centerY" id="0Y0-4E-0pM"/>
                                    <constraint firstItem="1pX-8I-uRQ" firstAttribute="centerY" secondItem="DdI-83-0zB" secondAttribute="centerY" id="Qhe-Ys-70U"/>
                                    <constraint firstItem="K6l-L5-2Dm" firstAttribute="leading" secondItem="DdI-83-0zB" secondAttribute="leading" constant="12" id="bYJ-qf-Gtn"/>
                                    <constraint firstAttribute="height" constant="44" id="nRT-4T-HZq"/>
                                    <constraint firstItem="1pX-8I-uRQ" firstAttribute="centerX" secondItem="DdI-83-0zB" secondAttribute="centerX" id="wjk-Ir-cM3"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Rt7-kg-ggb">
                                <rect key="frame" x="19" y="56" width="337.5" height="218"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Tracking Status" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Dw8-Xs-FKF">
                                        <rect key="frame" x="12" y="12" width="113" height="20"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="20" id="hva-BL-ZWO"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                        <color key="textColor" name="PrimarySelection"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" scrollEnabled="NO" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="-1" estimatedSectionHeaderHeight="-1" sectionFooterHeight="-1" estimatedSectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="ueW-DU-45G">
                                        <rect key="frame" x="12" y="48" width="313.5" height="146"/>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="146" id="1Yh-V1-nTL"/>
                                        </constraints>
                                        <prototypes>
                                            <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" reuseIdentifier="cell" id="xMh-0I-IWY" customClass="TrackRFIDStatusCell" customModule="NXC_EV_Solutions" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="44.5" width="313.5" height="138"/>
                                                <autoresizingMask key="autoresizingMask"/>
                                                <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="xMh-0I-IWY" id="q0U-b7-65V">
                                                    <rect key="frame" x="0.0" y="0.0" width="313.5" height="138"/>
                                                    <autoresizingMask key="autoresizingMask"/>
                                                    <subviews>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="0bo-6f-iy4">
                                                            <rect key="frame" x="0.0" y="0.0" width="313.5" height="138"/>
                                                            <subviews>
                                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_line" translatesAutoresizingMaskIntoConstraints="NO" id="DWM-tG-NBI">
                                                                    <rect key="frame" x="10" y="83" width="20" height="55"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="width" constant="20" id="ynw-L5-3jx"/>
                                                                    </constraints>
                                                                </imageView>
                                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_line" translatesAutoresizingMaskIntoConstraints="NO" id="KSj-p3-fVO">
                                                                    <rect key="frame" x="10" y="0.0" width="20" height="55"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="width" constant="20" id="LcM-zF-YvP"/>
                                                                    </constraints>
                                                                </imageView>
                                                                <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="zBk-H3-MoA">
                                                                    <rect key="frame" x="8" y="0.0" width="301.5" height="138"/>
                                                                    <subviews>
                                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_ticked" translatesAutoresizingMaskIntoConstraints="NO" id="cOe-05-GwI">
                                                                            <rect key="frame" x="0.0" y="57" width="24" height="24"/>
                                                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                            <constraints>
                                                                                <constraint firstAttribute="height" constant="24" id="4cQ-Dd-XSo"/>
                                                                                <constraint firstAttribute="width" constant="24" id="rAP-GX-n0f"/>
                                                                            </constraints>
                                                                        </imageView>
                                                                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="hm6-Bf-a0e">
                                                                            <rect key="frame" x="40" y="47" width="261.5" height="44"/>
                                                                            <subviews>
                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Approved" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="LHj-s8-Gsw">
                                                                                    <rect key="frame" x="0.0" y="0.0" width="261.5" height="24"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="24" id="ZfM-Z5-HM5"/>
                                                                                    </constraints>
                                                                                    <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                                    <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                    <nil key="highlightedColor"/>
                                                                                </label>
                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="20 Sep 2021, 10:20 AM" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="eUZ-Ww-Vcp">
                                                                                    <rect key="frame" x="0.0" y="24" width="261.5" height="20"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="height" constant="20" id="E71-J3-Ctg"/>
                                                                                    </constraints>
                                                                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                                    <color key="textColor" name="PrimarySelection"/>
                                                                                    <nil key="highlightedColor"/>
                                                                                </label>
                                                                            </subviews>
                                                                        </stackView>
                                                                    </subviews>
                                                                </stackView>
                                                            </subviews>
                                                            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                            <constraints>
                                                                <constraint firstItem="KSj-p3-fVO" firstAttribute="centerX" secondItem="cOe-05-GwI" secondAttribute="centerX" id="0Ua-RC-k10"/>
                                                                <constraint firstItem="KSj-p3-fVO" firstAttribute="top" secondItem="0bo-6f-iy4" secondAttribute="top" id="5yN-2X-jk5"/>
                                                                <constraint firstItem="cOe-05-GwI" firstAttribute="top" secondItem="KSj-p3-fVO" secondAttribute="bottom" constant="2" id="EBJ-SQ-ZC9"/>
                                                                <constraint firstAttribute="bottom" secondItem="zBk-H3-MoA" secondAttribute="bottom" id="Kkg-PV-cFw"/>
                                                                <constraint firstItem="zBk-H3-MoA" firstAttribute="leading" secondItem="0bo-6f-iy4" secondAttribute="leading" constant="8" id="Xm1-Jx-4tL"/>
                                                                <constraint firstAttribute="trailing" secondItem="zBk-H3-MoA" secondAttribute="trailing" constant="4" id="ZXm-zH-IJb"/>
                                                                <constraint firstAttribute="bottom" secondItem="DWM-tG-NBI" secondAttribute="bottom" id="lAO-gh-ktf"/>
                                                                <constraint firstItem="DWM-tG-NBI" firstAttribute="top" secondItem="cOe-05-GwI" secondAttribute="bottom" constant="2" id="lnK-4F-r7Q"/>
                                                                <constraint firstItem="zBk-H3-MoA" firstAttribute="top" secondItem="0bo-6f-iy4" secondAttribute="top" id="p7H-tT-jwi"/>
                                                                <constraint firstItem="DWM-tG-NBI" firstAttribute="centerX" secondItem="cOe-05-GwI" secondAttribute="centerX" id="yuz-5P-aiV"/>
                                                            </constraints>
                                                        </view>
                                                    </subviews>
                                                    <constraints>
                                                        <constraint firstAttribute="trailing" secondItem="0bo-6f-iy4" secondAttribute="trailing" id="7hH-CV-wax"/>
                                                        <constraint firstAttribute="bottom" secondItem="0bo-6f-iy4" secondAttribute="bottom" id="CoU-pe-qBF"/>
                                                        <constraint firstItem="0bo-6f-iy4" firstAttribute="top" secondItem="q0U-b7-65V" secondAttribute="top" id="KGL-ma-bOW"/>
                                                        <constraint firstItem="0bo-6f-iy4" firstAttribute="leading" secondItem="q0U-b7-65V" secondAttribute="leading" id="rUC-rb-6PC"/>
                                                    </constraints>
                                                </tableViewCellContentView>
                                                <connections>
                                                    <outlet property="imgLineBottom" destination="DWM-tG-NBI" id="Cpe-kl-hJd"/>
                                                    <outlet property="imgLineTop" destination="KSj-p3-fVO" id="du7-9H-xmK"/>
                                                    <outlet property="imgTick" destination="cOe-05-GwI" id="72L-4t-lhG"/>
                                                    <outlet property="lblStatus" destination="LHj-s8-Gsw" id="HrU-A2-avC"/>
                                                    <outlet property="lblTime" destination="eUZ-Ww-Vcp" id="VAr-A9-d9W"/>
                                                    <outlet property="viewMain" destination="0bo-6f-iy4" id="EXq-NW-kh1"/>
                                                </connections>
                                            </tableViewCell>
                                        </prototypes>
                                    </tableView>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="Dw8-Xs-FKF" firstAttribute="leading" secondItem="Rt7-kg-ggb" secondAttribute="leading" constant="12" id="0AN-IZ-cV3"/>
                                    <constraint firstAttribute="bottom" secondItem="ueW-DU-45G" secondAttribute="bottom" constant="24" id="CXX-R1-hmQ"/>
                                    <constraint firstItem="Dw8-Xs-FKF" firstAttribute="top" secondItem="Rt7-kg-ggb" secondAttribute="top" constant="12" id="ICU-S5-05O"/>
                                    <constraint firstItem="ueW-DU-45G" firstAttribute="top" secondItem="Dw8-Xs-FKF" secondAttribute="bottom" constant="16" id="N3w-ue-mYc"/>
                                    <constraint firstAttribute="trailing" secondItem="ueW-DU-45G" secondAttribute="trailing" constant="12" id="Zna-In-GBA"/>
                                    <constraint firstItem="ueW-DU-45G" firstAttribute="leading" secondItem="Rt7-kg-ggb" secondAttribute="leading" constant="12" id="od3-TY-5J4"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="hlR-wI-rHs"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="Rt7-kg-ggb" firstAttribute="top" secondItem="DdI-83-0zB" secondAttribute="bottom" constant="12" id="45C-7M-CGw"/>
                            <constraint firstItem="Rt7-kg-ggb" firstAttribute="centerX" secondItem="bfL-zv-Zpb" secondAttribute="centerX" id="7aS-SV-uW2"/>
                            <constraint firstItem="hlR-wI-rHs" firstAttribute="trailing" secondItem="DdI-83-0zB" secondAttribute="trailing" id="8BL-Nq-jkm"/>
                            <constraint firstItem="Rt7-kg-ggb" firstAttribute="width" secondItem="hlR-wI-rHs" secondAttribute="width" multiplier="0.9" id="ekU-by-asC"/>
                            <constraint firstItem="DdI-83-0zB" firstAttribute="top" secondItem="hlR-wI-rHs" secondAttribute="top" id="m57-ph-GJX"/>
                            <constraint firstItem="DdI-83-0zB" firstAttribute="leading" secondItem="hlR-wI-rHs" secondAttribute="leading" id="uui-82-ZlE"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="backButton" destination="K6l-L5-2Dm" id="8uc-T4-Q8g"/>
                        <outlet property="tableHeight" destination="1Yh-V1-nTL" id="EDc-0g-SHR"/>
                        <outlet property="tableTrackStatus" destination="ueW-DU-45G" id="FCY-Kb-BJP"/>
                        <outlet property="viewMain" destination="Rt7-kg-ggb" id="aS4-ac-Uk5"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="0Ia-vS-9zC" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1438" y="70"/>
        </scene>
        <!--Issue New CardVC-->
        <scene sceneID="ssU-G2-yHF">
            <objects>
                <viewController storyboardIdentifier="IssueNewCardVC" id="eat-Yf-PBu" customClass="IssueNewCardVC" customModule="NXC_EV_Solutions" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="V9T-w5-CHC">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Ace-fu-F17">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="y91-sA-caj">
                                        <rect key="frame" x="12" y="5" width="34" height="34"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="34" id="Zuy-rX-19z"/>
                                            <constraint firstAttribute="height" constant="34" id="kQ2-UU-Re5"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="8" minY="6" maxX="8" maxY="6"/>
                                        <state key="normal" image="ic_back_arrow"/>
                                        <connections>
                                            <action selector="backButtonTapped:" destination="eat-Yf-PBu" eventType="touchUpInside" id="EtL-N8-Tx7"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="RFID Card" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="jx0-yL-SQQ">
                                        <rect key="frame" x="147.5" y="12" width="80" height="20.5"/>
                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                        <color key="textColor" red="0.070588235289999995" green="0.070588235289999995" blue="0.070588235289999995" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="jx0-yL-SQQ" firstAttribute="centerY" secondItem="Ace-fu-F17" secondAttribute="centerY" id="1EF-gq-wgp"/>
                                    <constraint firstAttribute="height" constant="44" id="7QV-U8-OLD"/>
                                    <constraint firstItem="y91-sA-caj" firstAttribute="centerY" secondItem="Ace-fu-F17" secondAttribute="centerY" id="b2M-Bm-MxC"/>
                                    <constraint firstItem="jx0-yL-SQQ" firstAttribute="centerX" secondItem="Ace-fu-F17" secondAttribute="centerX" id="wUU-C8-3y0"/>
                                    <constraint firstItem="y91-sA-caj" firstAttribute="leading" secondItem="Ace-fu-F17" secondAttribute="leading" constant="12" id="xbg-W1-pO9"/>
                                </constraints>
                            </view>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_rect" translatesAutoresizingMaskIntoConstraints="NO" id="4zM-HG-Ceb">
                                <rect key="frame" x="103" y="313" width="169" height="44"/>
                            </imageView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_rect" translatesAutoresizingMaskIntoConstraints="NO" id="3ob-BV-VHI">
                                <rect key="frame" x="103" y="369" width="169" height="44"/>
                            </imageView>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="kKF-bp-Y5Z">
                                <rect key="frame" x="103" y="221" width="169" height="192"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_issue_card" translatesAutoresizingMaskIntoConstraints="NO" id="9hN-Nw-9mf">
                                        <rect key="frame" x="0.0" y="0.0" width="169" height="80"/>
                                    </imageView>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Issue New Card" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="yvx-xK-nH9">
                                        <rect key="frame" x="0.0" y="92" width="169" height="44"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="44" id="dSG-Li-Npx"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <color key="textColor" name="Primary"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Track Order" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="07C-bP-Iy5">
                                        <rect key="frame" x="0.0" y="148" width="169" height="44"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="44" id="xzO-8R-QgE"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <color key="textColor" name="Primary"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                            </stackView>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Lpb-o0-eLw">
                                <rect key="frame" x="103" y="313" width="169" height="44"/>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <connections>
                                    <action selector="issueButtonTapped:" destination="eat-Yf-PBu" eventType="touchUpInside" id="9f0-Yc-K86"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="O9A-mp-qJu">
                                <rect key="frame" x="103" y="369" width="169" height="44"/>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <connections>
                                    <action selector="trackButtonTapped:" destination="eat-Yf-PBu" eventType="touchUpInside" id="vuq-3d-vvO"/>
                                </connections>
                            </button>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="N6s-Bb-epQ">
                                <rect key="frame" x="0.0" y="44" width="375" height="559"/>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <prototypes>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="none" indentationWidth="10" reuseIdentifier="cell" rowHeight="200" id="K8N-3m-hO9" customClass="RFIDTrackListCell" customModule="NXC_EV_Solutions" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="44.5" width="375" height="200"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="K8N-3m-hO9" id="r3x-iB-HU8">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="200"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="sds-6H-Jb5">
                                                    <rect key="frame" x="16" y="8" width="343" height="184"/>
                                                    <subviews>
                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" alpha="0.0" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_cardBG" translatesAutoresizingMaskIntoConstraints="NO" id="WAk-Nd-A8o">
                                                            <rect key="frame" x="0.0" y="8" width="343" height="168"/>
                                                        </imageView>
                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_RFID_card" translatesAutoresizingMaskIntoConstraints="NO" id="5EK-Ug-gOR">
                                                            <rect key="frame" x="20" y="8" width="44" height="44"/>
                                                            <constraints>
                                                                <constraint firstAttribute="width" constant="44" id="OXL-Bb-uo0"/>
                                                                <constraint firstAttribute="height" constant="44" id="lm0-HD-8fS"/>
                                                            </constraints>
                                                        </imageView>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="NXC EV" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="H0B-lO-qOV">
                                                            <rect key="frame" x="258" y="18" width="65" height="24"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="24" id="ial-nD-lRa"/>
                                                            </constraints>
                                                            <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="18"/>
                                                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="cVP-kF-qa4">
                                                            <rect key="frame" x="175" y="117.5" width="148" height="52"/>
                                                            <subviews>
                                                                <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="pMN-bY-qbM">
                                                                    <rect key="frame" x="0.0" y="0.0" width="148" height="24"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="height" constant="24" id="5tS-wk-1LI"/>
                                                                    </constraints>
                                                                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="14"/>
                                                                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                    <nil key="highlightedColor"/>
                                                                </label>
                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Jobin Macwan" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="2jD-2H-eXg">
                                                                    <rect key="frame" x="0.0" y="28" width="148" height="24"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="height" constant="24" id="q4U-r9-oOf"/>
                                                                    </constraints>
                                                                    <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="18"/>
                                                                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                    <nil key="highlightedColor"/>
                                                                </label>
                                                            </subviews>
                                                        </stackView>
                                                        <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" alignment="center" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="TXS-FY-pOG">
                                                            <rect key="frame" x="20" y="131" width="114" height="37"/>
                                                            <subviews>
                                                                <stackView hidden="YES" opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="2" translatesAutoresizingMaskIntoConstraints="NO" id="xcS-gh-8Fc">
                                                                    <rect key="frame" x="0.0" y="0.0" width="53" height="37"/>
                                                                    <subviews>
                                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_replace_card" translatesAutoresizingMaskIntoConstraints="NO" id="in9-5m-s9W">
                                                                            <rect key="frame" x="17.5" y="0.0" width="18" height="18"/>
                                                                            <constraints>
                                                                                <constraint firstAttribute="width" constant="18" id="dz8-jp-oyL"/>
                                                                                <constraint firstAttribute="height" constant="18" id="orN-oe-V5y"/>
                                                                            </constraints>
                                                                        </imageView>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Replace" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="MMn-wk-Qb8">
                                                                            <rect key="frame" x="0.0" y="20" width="53" height="17"/>
                                                                            <fontDescription key="fontDescription" type="system" weight="medium" pointSize="14"/>
                                                                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                    </subviews>
                                                                </stackView>
                                                                <stackView hidden="YES" opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="2" translatesAutoresizingMaskIntoConstraints="NO" id="a2c-VG-CQg">
                                                                    <rect key="frame" x="57" y="0.0" width="18" height="37"/>
                                                                    <subviews>
                                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_track_order" translatesAutoresizingMaskIntoConstraints="NO" id="HYn-e1-emI">
                                                                            <rect key="frame" x="0.0" y="0.0" width="18" height="18"/>
                                                                            <constraints>
                                                                                <constraint firstAttribute="height" constant="18" id="1Ey-1A-23C"/>
                                                                                <constraint firstAttribute="width" constant="18" id="7yZ-Hx-3gO"/>
                                                                            </constraints>
                                                                        </imageView>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Track" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="fbp-C5-bnn">
                                                                            <rect key="frame" x="0.0" y="20" width="18" height="17"/>
                                                                            <fontDescription key="fontDescription" type="system" weight="medium" pointSize="14"/>
                                                                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                    </subviews>
                                                                </stackView>
                                                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="2" translatesAutoresizingMaskIntoConstraints="NO" id="BJS-ks-Mln">
                                                                    <rect key="frame" x="61" y="0.0" width="53" height="37"/>
                                                                    <subviews>
                                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_block_card-1" translatesAutoresizingMaskIntoConstraints="NO" id="9Du-6x-lMh">
                                                                            <rect key="frame" x="17.5" y="0.0" width="18" height="18"/>
                                                                            <constraints>
                                                                                <constraint firstAttribute="width" constant="18" id="13Y-3k-pVI"/>
                                                                                <constraint firstAttribute="height" constant="18" id="NR3-NY-uHA"/>
                                                                            </constraints>
                                                                        </imageView>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Block" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="jMp-Hx-L4V">
                                                                            <rect key="frame" x="8" y="20" width="37" height="17"/>
                                                                            <fontDescription key="fontDescription" type="system" weight="medium" pointSize="14"/>
                                                                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                    </subviews>
                                                                </stackView>
                                                            </subviews>
                                                        </stackView>
                                                        <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="VLI-xl-23g">
                                                            <rect key="frame" x="20" y="131" width="53" height="37"/>
                                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                        </button>
                                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="WXt-bJ-56g">
                                                            <rect key="frame" x="81" y="131" width="53" height="37"/>
                                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                        </button>
                                                    </subviews>
                                                    <color key="backgroundColor" red="0.016000000759959221" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <constraints>
                                                        <constraint firstItem="5EK-Ug-gOR" firstAttribute="leading" secondItem="sds-6H-Jb5" secondAttribute="leading" constant="20" id="3xR-6s-oLf"/>
                                                        <constraint firstItem="WXt-bJ-56g" firstAttribute="centerX" secondItem="BJS-ks-Mln" secondAttribute="centerX" id="4db-qf-fhP"/>
                                                        <constraint firstItem="WAk-Nd-A8o" firstAttribute="top" secondItem="sds-6H-Jb5" secondAttribute="top" constant="8" id="8M1-RS-e55"/>
                                                        <constraint firstAttribute="bottom" secondItem="WAk-Nd-A8o" secondAttribute="bottom" constant="8" id="92G-vJ-leW"/>
                                                        <constraint firstItem="WXt-bJ-56g" firstAttribute="width" secondItem="BJS-ks-Mln" secondAttribute="width" id="Gsz-48-H2F"/>
                                                        <constraint firstAttribute="bottom" secondItem="TXS-FY-pOG" secondAttribute="bottom" constant="16" id="Gvq-d6-VQh"/>
                                                        <constraint firstItem="VLI-xl-23g" firstAttribute="centerX" secondItem="xcS-gh-8Fc" secondAttribute="centerX" id="HSf-gy-s1n"/>
                                                        <constraint firstAttribute="trailing" secondItem="WAk-Nd-A8o" secondAttribute="trailing" id="Iac-sz-j83"/>
                                                        <constraint firstItem="TXS-FY-pOG" firstAttribute="leading" secondItem="sds-6H-Jb5" secondAttribute="leading" constant="20" id="IbI-LG-Jvf"/>
                                                        <constraint firstItem="VLI-xl-23g" firstAttribute="width" secondItem="xcS-gh-8Fc" secondAttribute="width" id="TrW-MU-q5I"/>
                                                        <constraint firstAttribute="trailing" secondItem="H0B-lO-qOV" secondAttribute="trailing" constant="20" id="Vpm-sG-ela"/>
                                                        <constraint firstItem="H0B-lO-qOV" firstAttribute="centerY" secondItem="5EK-Ug-gOR" secondAttribute="centerY" id="Y3l-Ck-hUL"/>
                                                        <constraint firstItem="5EK-Ug-gOR" firstAttribute="top" secondItem="sds-6H-Jb5" secondAttribute="top" constant="8" id="e5g-2u-V4o"/>
                                                        <constraint firstItem="cVP-kF-qa4" firstAttribute="bottom" secondItem="TXS-FY-pOG" secondAttribute="bottom" multiplier="1.01" id="fWO-gA-IbA"/>
                                                        <constraint firstItem="WXt-bJ-56g" firstAttribute="height" secondItem="BJS-ks-Mln" secondAttribute="height" id="n0v-7F-cUF"/>
                                                        <constraint firstItem="VLI-xl-23g" firstAttribute="centerY" secondItem="xcS-gh-8Fc" secondAttribute="centerY" id="pz0-5n-iKb"/>
                                                        <constraint firstItem="WAk-Nd-A8o" firstAttribute="leading" secondItem="sds-6H-Jb5" secondAttribute="leading" id="wLl-w1-IXj"/>
                                                        <constraint firstItem="WXt-bJ-56g" firstAttribute="centerY" secondItem="BJS-ks-Mln" secondAttribute="centerY" id="y8n-OF-rRs"/>
                                                        <constraint firstAttribute="trailing" secondItem="cVP-kF-qa4" secondAttribute="trailing" constant="20" id="yft-2p-4Df"/>
                                                        <constraint firstItem="VLI-xl-23g" firstAttribute="height" secondItem="xcS-gh-8Fc" secondAttribute="height" id="ylx-O9-q0x"/>
                                                    </constraints>
                                                </view>
                                            </subviews>
                                            <constraints>
                                                <constraint firstAttribute="bottom" secondItem="sds-6H-Jb5" secondAttribute="bottom" constant="8" id="FZB-Hn-5af"/>
                                                <constraint firstItem="sds-6H-Jb5" firstAttribute="leading" secondItem="r3x-iB-HU8" secondAttribute="leading" constant="16" id="XIp-sm-rdC"/>
                                                <constraint firstAttribute="trailing" secondItem="sds-6H-Jb5" secondAttribute="trailing" constant="16" id="jCS-21-9HT"/>
                                                <constraint firstItem="sds-6H-Jb5" firstAttribute="top" secondItem="r3x-iB-HU8" secondAttribute="top" constant="8" id="sxM-W0-FVo"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <connections>
                                            <outlet property="btnBlock" destination="WXt-bJ-56g" id="Mv7-uj-Z1J"/>
                                            <outlet property="btnReplace" destination="VLI-xl-23g" id="qhI-zE-aq6"/>
                                            <outlet property="dateLabel" destination="pMN-bY-qbM" id="IaG-Mh-Fzf"/>
                                            <outlet property="imgCard" destination="WAk-Nd-A8o" id="IRM-oW-8b2"/>
                                            <outlet property="nameLabel" destination="2jD-2H-eXg" id="u15-aW-Jjg"/>
                                            <outlet property="viewMain" destination="sds-6H-Jb5" id="TpY-bo-aRh"/>
                                        </connections>
                                    </tableViewCell>
                                </prototypes>
                            </tableView>
                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="bVy-HF-mU5">
                                <rect key="frame" x="19" y="611" width="337.5" height="44"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="FIZ-LJ-x7V">
                                        <rect key="frame" x="0.0" y="0.0" width="160.5" height="44"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="Lt5-aL-vKn">
                                                <rect key="frame" x="23.5" y="4" width="113" height="36"/>
                                                <subviews>
                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_track_card" translatesAutoresizingMaskIntoConstraints="NO" id="vVv-Hb-3Gl">
                                                        <rect key="frame" x="0.0" y="6" width="24" height="24"/>
                                                        <constraints>
                                                            <constraint firstAttribute="width" constant="24" id="7rt-Ox-COt"/>
                                                            <constraint firstAttribute="height" constant="24" id="YEG-5e-mxF"/>
                                                        </constraints>
                                                    </imageView>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Track Card" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="iW1-Ow-8U7">
                                                        <rect key="frame" x="32" y="8.5" width="81" height="19.5"/>
                                                        <fontDescription key="fontDescription" type="system" weight="medium" pointSize="16"/>
                                                        <color key="textColor" red="0.0" green="0.0" blue="0.0" alpha="0.5" colorSpace="custom" customColorSpace="sRGB"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="44" id="NFo-a5-EKq"/>
                                            <constraint firstItem="Lt5-aL-vKn" firstAttribute="centerX" secondItem="FIZ-LJ-x7V" secondAttribute="centerX" id="OM5-E1-Vty"/>
                                            <constraint firstItem="Lt5-aL-vKn" firstAttribute="top" secondItem="FIZ-LJ-x7V" secondAttribute="top" constant="4" id="Ygn-ds-usa"/>
                                            <constraint firstAttribute="bottom" secondItem="Lt5-aL-vKn" secondAttribute="bottom" constant="4" id="xsC-wI-eop"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="zId-Q8-w4X">
                                        <rect key="frame" x="176.5" y="0.0" width="161" height="44"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="26y-TU-5La">
                                                <rect key="frame" x="28" y="4" width="105" height="36"/>
                                                <subviews>
                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_add_card" translatesAutoresizingMaskIntoConstraints="NO" id="5NM-Nc-bLG">
                                                        <rect key="frame" x="0.0" y="6" width="24" height="24"/>
                                                        <constraints>
                                                            <constraint firstAttribute="width" constant="24" id="KTF-va-vMz"/>
                                                            <constraint firstAttribute="height" constant="24" id="SC0-ur-y7a"/>
                                                        </constraints>
                                                    </imageView>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="New Card" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="OqZ-dh-IZo">
                                                        <rect key="frame" x="32" y="8.5" width="73" height="19.5"/>
                                                        <fontDescription key="fontDescription" type="system" weight="medium" pointSize="16"/>
                                                        <color key="textColor" red="0.0" green="0.0" blue="0.0" alpha="0.5" colorSpace="custom" customColorSpace="sRGB"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="26y-TU-5La" firstAttribute="top" secondItem="zId-Q8-w4X" secondAttribute="top" constant="4" id="JYt-wF-2v5"/>
                                            <constraint firstItem="26y-TU-5La" firstAttribute="centerX" secondItem="zId-Q8-w4X" secondAttribute="centerX" id="m1w-cX-yzW"/>
                                            <constraint firstAttribute="bottom" secondItem="26y-TU-5La" secondAttribute="bottom" constant="4" id="zOx-ct-xod"/>
                                        </constraints>
                                    </view>
                                </subviews>
                            </stackView>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="xd3-w7-jLm">
                                <rect key="frame" x="19" y="611" width="160.5" height="44"/>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <connections>
                                    <action selector="trackCardTapped:" destination="eat-Yf-PBu" eventType="touchUpInside" id="D7y-Qh-Jpl"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="xFr-ll-3jG">
                                <rect key="frame" x="195.5" y="611" width="161" height="44"/>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <connections>
                                    <action selector="AddCardTapped:" destination="eat-Yf-PBu" eventType="touchUpInside" id="VSZ-s6-dme"/>
                                </connections>
                            </button>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="a4e-Va-Qhu">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="S3F-VW-UKq">
                                        <rect key="frame" x="19" y="214.5" width="337.5" height="238.5"/>
                                        <subviews>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="9QM-Rh-jI7">
                                                <rect key="frame" x="293.5" y="0.0" width="44" height="44"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="44" id="5f6-4b-ja3"/>
                                                    <constraint firstAttribute="width" constant="44" id="Hbd-7B-2j9"/>
                                                </constraints>
                                                <inset key="imageEdgeInsets" minX="14" minY="14" maxX="14" maxY="14"/>
                                                <state key="normal" image="ic_cancel"/>
                                                <connections>
                                                    <action selector="closeAlertTapped:" destination="eat-Yf-PBu" eventType="touchUpInside" id="Pmq-pu-INc"/>
                                                </connections>
                                            </button>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="30" baselineRelativeArrangement="YES" translatesAutoresizingMaskIntoConstraints="NO" id="848-BE-58x">
                                                <rect key="frame" x="16.5" y="16" width="304" height="146.5"/>
                                                <subviews>
                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_block_RFID" translatesAutoresizingMaskIntoConstraints="NO" id="wO8-8G-dcg">
                                                        <rect key="frame" x="119.5" y="0.0" width="65" height="65"/>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstAttribute="width" constant="65" id="Dv2-Jn-Ov1"/>
                                                            <constraint firstAttribute="height" constant="65" id="unu-DC-9DH"/>
                                                        </constraints>
                                                    </imageView>
                                                    <label opaque="NO" userInteractionEnabled="NO" tag="100" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Block Card" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="zWL-Rx-jaz">
                                                        <rect key="frame" x="109" y="79" width="86.5" height="20.5"/>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" tag="101" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Are you sure you want to block the card?" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="QNb-Pl-cjo">
                                                        <rect key="frame" x="45.5" y="110.5" width="213" height="36"/>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                        <color key="textColor" white="0.0" alpha="0.5" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstItem="QNb-Pl-cjo" firstAttribute="width" secondItem="848-BE-58x" secondAttribute="width" multiplier="0.7" id="fut-df-aA3"/>
                                                </constraints>
                                            </stackView>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="20" translatesAutoresizingMaskIntoConstraints="NO" id="zyK-MN-1lf">
                                                <rect key="frame" x="16" y="178.5" width="305.5" height="44"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="J12-Mq-J8s">
                                                        <rect key="frame" x="15" y="0.0" width="275" height="44"/>
                                                        <subviews>
                                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="KYc-0b-XjB">
                                                                <rect key="frame" x="0.0" y="0.0" width="129.5" height="44"/>
                                                                <color key="backgroundColor" name="Primary"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="44" id="yTe-Ye-x4d"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                                                <state key="normal" title="No">
                                                                    <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                </state>
                                                                <connections>
                                                                    <action selector="noBlockTapped:" destination="eat-Yf-PBu" eventType="touchUpInside" id="HjG-DE-cg5"/>
                                                                </connections>
                                                            </button>
                                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Ras-ol-fKH">
                                                                <rect key="frame" x="145.5" y="0.0" width="129.5" height="44"/>
                                                                <color key="backgroundColor" name="Primary"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="44" id="rWg-XT-57J"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                                                <state key="normal" title="Yes">
                                                                    <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                </state>
                                                                <connections>
                                                                    <action selector="yesBlockTapped:" destination="eat-Yf-PBu" eventType="touchUpInside" id="0BK-aS-bqx"/>
                                                                </connections>
                                                            </button>
                                                        </subviews>
                                                    </stackView>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstItem="J12-Mq-J8s" firstAttribute="width" secondItem="zyK-MN-1lf" secondAttribute="width" multiplier="0.9" id="hXo-BF-csq"/>
                                                </constraints>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstItem="848-BE-58x" firstAttribute="width" secondItem="S3F-VW-UKq" secondAttribute="width" multiplier="0.9" id="8rW-oP-jzm"/>
                                            <constraint firstAttribute="bottom" secondItem="zyK-MN-1lf" secondAttribute="bottom" constant="16" id="Czz-wx-D4I"/>
                                            <constraint firstItem="9QM-Rh-jI7" firstAttribute="top" secondItem="S3F-VW-UKq" secondAttribute="top" id="FKQ-xT-Cp2"/>
                                            <constraint firstItem="zyK-MN-1lf" firstAttribute="top" secondItem="848-BE-58x" secondAttribute="bottom" constant="16" id="Fxs-bB-v2u"/>
                                            <constraint firstItem="zyK-MN-1lf" firstAttribute="width" secondItem="S3F-VW-UKq" secondAttribute="width" multiplier="0.905185" id="Lik-Y0-4kL"/>
                                            <constraint firstItem="848-BE-58x" firstAttribute="centerX" secondItem="S3F-VW-UKq" secondAttribute="centerX" id="as9-5c-yju"/>
                                            <constraint firstItem="848-BE-58x" firstAttribute="top" secondItem="S3F-VW-UKq" secondAttribute="top" constant="16" id="cjo-4H-j1g"/>
                                            <constraint firstItem="zyK-MN-1lf" firstAttribute="centerX" secondItem="S3F-VW-UKq" secondAttribute="centerX" id="frC-kV-W3I"/>
                                            <constraint firstAttribute="trailing" secondItem="9QM-Rh-jI7" secondAttribute="trailing" id="gUr-KO-Mnh"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.75" colorSpace="custom" customColorSpace="sRGB"/>
                                <constraints>
                                    <constraint firstItem="S3F-VW-UKq" firstAttribute="width" secondItem="a4e-Va-Qhu" secondAttribute="width" multiplier="0.9" id="Jh3-dM-uE3"/>
                                    <constraint firstItem="S3F-VW-UKq" firstAttribute="centerX" secondItem="a4e-Va-Qhu" secondAttribute="centerX" id="NN5-EB-Wdz"/>
                                    <constraint firstItem="S3F-VW-UKq" firstAttribute="centerY" secondItem="a4e-Va-Qhu" secondAttribute="centerY" id="oOP-sR-EGY"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="h05-jk-EKn"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="xFr-ll-3jG" firstAttribute="centerX" secondItem="zId-Q8-w4X" secondAttribute="centerX" id="1gU-9I-GK4"/>
                            <constraint firstItem="h05-jk-EKn" firstAttribute="trailing" secondItem="N6s-Bb-epQ" secondAttribute="trailing" id="2fw-fR-O8Q"/>
                            <constraint firstItem="N6s-Bb-epQ" firstAttribute="leading" secondItem="h05-jk-EKn" secondAttribute="leading" id="360-3b-2uu"/>
                            <constraint firstItem="h05-jk-EKn" firstAttribute="bottom" secondItem="bVy-HF-mU5" secondAttribute="bottom" constant="12" id="3Df-Ae-XYQ"/>
                            <constraint firstItem="3ob-BV-VHI" firstAttribute="height" secondItem="07C-bP-Iy5" secondAttribute="height" id="3dt-Km-bZ4"/>
                            <constraint firstItem="N6s-Bb-epQ" firstAttribute="top" secondItem="Ace-fu-F17" secondAttribute="bottom" id="5Gc-xe-Rb7"/>
                            <constraint firstItem="a4e-Va-Qhu" firstAttribute="leading" secondItem="h05-jk-EKn" secondAttribute="leading" id="5QZ-EW-B4j"/>
                            <constraint firstItem="kKF-bp-Y5Z" firstAttribute="width" secondItem="h05-jk-EKn" secondAttribute="width" multiplier="0.45" id="5WK-js-fOA"/>
                            <constraint firstItem="Lpb-o0-eLw" firstAttribute="centerY" secondItem="yvx-xK-nH9" secondAttribute="centerY" id="8cu-Bi-9ik"/>
                            <constraint firstItem="4zM-HG-Ceb" firstAttribute="height" secondItem="yvx-xK-nH9" secondAttribute="height" id="9iq-pK-N2w"/>
                            <constraint firstItem="xFr-ll-3jG" firstAttribute="height" secondItem="zId-Q8-w4X" secondAttribute="height" id="9kG-Li-S19"/>
                            <constraint firstItem="xd3-w7-jLm" firstAttribute="height" secondItem="FIZ-LJ-x7V" secondAttribute="height" id="B8i-QU-NK8"/>
                            <constraint firstItem="3ob-BV-VHI" firstAttribute="centerY" secondItem="07C-bP-Iy5" secondAttribute="centerY" id="Cff-fl-b9S"/>
                            <constraint firstItem="xd3-w7-jLm" firstAttribute="centerX" secondItem="FIZ-LJ-x7V" secondAttribute="centerX" id="FSx-vw-PAh"/>
                            <constraint firstItem="bVy-HF-mU5" firstAttribute="width" secondItem="h05-jk-EKn" secondAttribute="width" multiplier="0.9" id="G2u-H4-z3w"/>
                            <constraint firstItem="Ace-fu-F17" firstAttribute="top" secondItem="h05-jk-EKn" secondAttribute="top" id="Gzz-KR-whB"/>
                            <constraint firstItem="Ace-fu-F17" firstAttribute="leading" secondItem="h05-jk-EKn" secondAttribute="leading" id="N1L-ah-2nO"/>
                            <constraint firstItem="Lpb-o0-eLw" firstAttribute="height" secondItem="yvx-xK-nH9" secondAttribute="height" id="QHm-3U-TF2"/>
                            <constraint firstItem="kKF-bp-Y5Z" firstAttribute="centerY" secondItem="V9T-w5-CHC" secondAttribute="centerY" multiplier="0.95" id="Qiv-pv-zIT"/>
                            <constraint firstItem="a4e-Va-Qhu" firstAttribute="bottom" secondItem="h05-jk-EKn" secondAttribute="bottom" id="SCL-fx-xy7"/>
                            <constraint firstItem="4zM-HG-Ceb" firstAttribute="centerY" secondItem="yvx-xK-nH9" secondAttribute="centerY" id="UcO-VB-5Cj"/>
                            <constraint firstItem="a4e-Va-Qhu" firstAttribute="trailing" secondItem="h05-jk-EKn" secondAttribute="trailing" id="UgN-hf-TAz"/>
                            <constraint firstItem="4zM-HG-Ceb" firstAttribute="centerX" secondItem="yvx-xK-nH9" secondAttribute="centerX" id="XO9-g8-60g"/>
                            <constraint firstItem="xFr-ll-3jG" firstAttribute="centerY" secondItem="zId-Q8-w4X" secondAttribute="centerY" id="Xyr-NR-6jY"/>
                            <constraint firstItem="xd3-w7-jLm" firstAttribute="centerY" secondItem="FIZ-LJ-x7V" secondAttribute="centerY" id="cJ1-Z5-Hpv"/>
                            <constraint firstItem="a4e-Va-Qhu" firstAttribute="top" secondItem="h05-jk-EKn" secondAttribute="top" id="egQ-ZF-gbX"/>
                            <constraint firstItem="O9A-mp-qJu" firstAttribute="centerY" secondItem="07C-bP-Iy5" secondAttribute="centerY" id="fHj-Md-bBV"/>
                            <constraint firstItem="Lpb-o0-eLw" firstAttribute="width" secondItem="yvx-xK-nH9" secondAttribute="width" id="g8Q-tM-uto"/>
                            <constraint firstItem="xd3-w7-jLm" firstAttribute="width" secondItem="FIZ-LJ-x7V" secondAttribute="width" id="gMV-h1-gwE"/>
                            <constraint firstItem="Lpb-o0-eLw" firstAttribute="centerX" secondItem="yvx-xK-nH9" secondAttribute="centerX" id="hpx-kg-VFz"/>
                            <constraint firstItem="O9A-mp-qJu" firstAttribute="centerX" secondItem="07C-bP-Iy5" secondAttribute="centerX" id="i07-mJ-wpd"/>
                            <constraint firstItem="bVy-HF-mU5" firstAttribute="top" secondItem="N6s-Bb-epQ" secondAttribute="bottom" constant="8" id="kEy-dp-vj6"/>
                            <constraint firstItem="bVy-HF-mU5" firstAttribute="centerX" secondItem="V9T-w5-CHC" secondAttribute="centerX" id="kZ4-4H-oxw"/>
                            <constraint firstItem="4zM-HG-Ceb" firstAttribute="width" secondItem="yvx-xK-nH9" secondAttribute="width" id="kwY-gT-Cva"/>
                            <constraint firstItem="3ob-BV-VHI" firstAttribute="width" secondItem="07C-bP-Iy5" secondAttribute="width" id="lbj-cA-Ozi"/>
                            <constraint firstItem="kKF-bp-Y5Z" firstAttribute="centerX" secondItem="V9T-w5-CHC" secondAttribute="centerX" id="mFg-hx-ubd"/>
                            <constraint firstItem="3ob-BV-VHI" firstAttribute="centerX" secondItem="07C-bP-Iy5" secondAttribute="centerX" id="q4n-9o-C7q"/>
                            <constraint firstItem="h05-jk-EKn" firstAttribute="trailing" secondItem="Ace-fu-F17" secondAttribute="trailing" id="qbX-34-rOa"/>
                            <constraint firstItem="O9A-mp-qJu" firstAttribute="width" secondItem="07C-bP-Iy5" secondAttribute="width" id="ubI-JS-jB2"/>
                            <constraint firstItem="O9A-mp-qJu" firstAttribute="height" secondItem="07C-bP-Iy5" secondAttribute="height" id="xWv-uR-L3z"/>
                            <constraint firstItem="xFr-ll-3jG" firstAttribute="width" secondItem="zId-Q8-w4X" secondAttribute="width" id="zCc-jR-92c"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="backButton" destination="y91-sA-caj" id="G9V-Zu-uQR"/>
                        <outlet property="lblIssueCard" destination="yvx-xK-nH9" id="2qe-sW-gs3"/>
                        <outlet property="lblTrackOrder" destination="07C-bP-Iy5" id="UAS-Ih-oPC"/>
                        <outlet property="noBlockButton" destination="KYc-0b-XjB" id="Nzr-Jg-MEJ"/>
                        <outlet property="tableList" destination="N6s-Bb-epQ" id="OwJ-8x-xoJ"/>
                        <outlet property="viewAddCard" destination="zId-Q8-w4X" id="YMx-Vh-5hw"/>
                        <outlet property="viewBgAlert" destination="a4e-Va-Qhu" id="7p7-NW-xyj"/>
                        <outlet property="viewMainAlert" destination="S3F-VW-UKq" id="LbK-uQ-HrM"/>
                        <outlet property="viewTrackCard" destination="FIZ-LJ-x7V" id="KVe-oR-6mx"/>
                        <outlet property="yesBlockButton" destination="Ras-ol-fKH" id="oQc-W8-MM0"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="YQC-cj-qDd" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="2109.5999999999999" y="69.715142428785612"/>
        </scene>
        <!--Replace CardVC-->
        <scene sceneID="akK-f4-cjR">
            <objects>
                <viewController storyboardIdentifier="ReplaceCardVC" id="hEx-oT-80V" customClass="ReplaceCardVC" customModule="NXC_EV_Solutions" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="sxQ-nK-Bi0">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="QUv-Dv-pKb">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="80W-tz-EaP">
                                        <rect key="frame" x="12" y="5" width="34" height="34"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="34" id="7UT-jm-ffo"/>
                                            <constraint firstAttribute="width" constant="34" id="a00-Ne-YYP"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="8" minY="6" maxX="8" maxY="6"/>
                                        <state key="normal" image="ic_back_arrow"/>
                                        <connections>
                                            <action selector="backButtonTapped:" destination="hEx-oT-80V" eventType="touchUpInside" id="rdg-wO-QYe"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Replace RFID Card" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="YdC-yj-eQs">
                                        <rect key="frame" x="114" y="12" width="147.5" height="20.5"/>
                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                        <color key="textColor" red="0.070588235289999995" green="0.070588235289999995" blue="0.070588235289999995" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="44" id="097-6M-Bgb"/>
                                    <constraint firstItem="YdC-yj-eQs" firstAttribute="centerX" secondItem="QUv-Dv-pKb" secondAttribute="centerX" id="AwD-Al-SRA"/>
                                    <constraint firstItem="80W-tz-EaP" firstAttribute="centerY" secondItem="QUv-Dv-pKb" secondAttribute="centerY" id="Cr1-BF-jSR"/>
                                    <constraint firstItem="80W-tz-EaP" firstAttribute="leading" secondItem="QUv-Dv-pKb" secondAttribute="leading" constant="12" id="OkR-gc-jxO"/>
                                    <constraint firstItem="YdC-yj-eQs" firstAttribute="centerY" secondItem="QUv-Dv-pKb" secondAttribute="centerY" id="Wwx-cb-pzv"/>
                                </constraints>
                            </view>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="1XV-uD-5zM">
                                <rect key="frame" x="19" y="56" width="337.5" height="245"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="Kvt-Sb-IiN">
                                        <rect key="frame" x="0.0" y="0.0" width="337.5" height="112"/>
                                        <subviews>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="fCg-Kr-JbH">
                                                <rect key="frame" x="0.0" y="0.0" width="337.5" height="50"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="64L-uH-vI5">
                                                        <rect key="frame" x="12" y="4" width="313.5" height="42"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Does not work properly" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="JPW-yJ-y6r">
                                                                <rect key="frame" x="0.0" y="12" width="279.5" height="18"/>
                                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                <nil key="textColor"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_uncheck" highlightedImage="ic_checked" translatesAutoresizingMaskIntoConstraints="NO" id="UGZ-AN-bcf">
                                                                <rect key="frame" x="291.5" y="10" width="22" height="22"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="22" id="3dc-fo-fHr"/>
                                                                    <constraint firstAttribute="width" constant="22" id="XHL-nK-exO"/>
                                                                </constraints>
                                                            </imageView>
                                                        </subviews>
                                                    </stackView>
                                                </subviews>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <constraints>
                                                    <constraint firstAttribute="bottom" secondItem="64L-uH-vI5" secondAttribute="bottom" constant="4" id="B2G-3v-aIs"/>
                                                    <constraint firstItem="64L-uH-vI5" firstAttribute="top" secondItem="fCg-Kr-JbH" secondAttribute="top" constant="4" id="TmR-xf-Ra2"/>
                                                    <constraint firstAttribute="height" constant="50" id="qFo-eb-PZk"/>
                                                    <constraint firstItem="64L-uH-vI5" firstAttribute="leading" secondItem="fCg-Kr-JbH" secondAttribute="leading" constant="12" id="qcL-5E-dyT"/>
                                                    <constraint firstAttribute="trailing" secondItem="64L-uH-vI5" secondAttribute="trailing" constant="12" id="qea-Gz-QRI"/>
                                                </constraints>
                                            </view>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="7ef-Hc-Ikz">
                                                <rect key="frame" x="0.0" y="62" width="337.5" height="50"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="LBy-1A-Pa2">
                                                        <rect key="frame" x="12" y="4" width="313.5" height="42"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Defective" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="2gt-I6-fa7">
                                                                <rect key="frame" x="0.0" y="12" width="279.5" height="18"/>
                                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                <nil key="textColor"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_uncheck" highlightedImage="ic_checked" translatesAutoresizingMaskIntoConstraints="NO" id="utq-Wm-EmE">
                                                                <rect key="frame" x="291.5" y="10" width="22" height="22"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="22" id="5EM-Ek-29w"/>
                                                                    <constraint firstAttribute="width" constant="22" id="hyd-5k-b08"/>
                                                                </constraints>
                                                            </imageView>
                                                        </subviews>
                                                    </stackView>
                                                </subviews>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <constraints>
                                                    <constraint firstItem="LBy-1A-Pa2" firstAttribute="top" secondItem="7ef-Hc-Ikz" secondAttribute="top" constant="4" id="2fi-xf-3wS"/>
                                                    <constraint firstItem="LBy-1A-Pa2" firstAttribute="leading" secondItem="7ef-Hc-Ikz" secondAttribute="leading" constant="12" id="7e6-ix-uxC"/>
                                                    <constraint firstAttribute="trailing" secondItem="LBy-1A-Pa2" secondAttribute="trailing" constant="12" id="8BB-uw-Acd"/>
                                                    <constraint firstAttribute="bottom" secondItem="LBy-1A-Pa2" secondAttribute="bottom" constant="4" id="HaP-lP-TOQ"/>
                                                </constraints>
                                            </view>
                                        </subviews>
                                    </stackView>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="vJt-RV-h8e">
                                        <rect key="frame" x="0.0" y="128" width="337.5" height="117"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Other Reason" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="jX2-vn-f4K">
                                                <rect key="frame" x="0.0" y="0.0" width="337.5" height="34"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="34" id="eG3-Gn-AbN"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" textAlignment="natural" translatesAutoresizingMaskIntoConstraints="NO" id="Xqb-ty-YFj">
                                                <rect key="frame" x="0.0" y="42" width="337.5" height="75"/>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="75" id="40L-Ay-so7"/>
                                                </constraints>
                                                <color key="textColor" systemColor="labelColor"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                                            </textView>
                                        </subviews>
                                    </stackView>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="vJt-RV-h8e" firstAttribute="width" secondItem="1XV-uD-5zM" secondAttribute="width" id="LbH-OH-3RE"/>
                                    <constraint firstItem="Kvt-Sb-IiN" firstAttribute="width" secondItem="1XV-uD-5zM" secondAttribute="width" id="xdB-bE-LHC"/>
                                </constraints>
                            </stackView>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="eD9-Lz-6id">
                                <rect key="frame" x="19" y="56" width="337.5" height="50"/>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <connections>
                                    <action selector="notWorkingTapped:" destination="hEx-oT-80V" eventType="touchUpInside" id="nn4-KW-ZeD"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ULL-eZ-gLl">
                                <rect key="frame" x="19" y="118" width="337.5" height="50"/>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <connections>
                                    <action selector="defectiveTapped:" destination="hEx-oT-80V" eventType="touchUpInside" id="Quh-aB-5yv"/>
                                </connections>
                            </button>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="WbB-P9-QXW">
                                <rect key="frame" x="0.0" y="325" width="375" height="40"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Replacement Charges : ₹250" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="WGS-Xh-d2V">
                                        <rect key="frame" x="93.5" y="11.5" width="188" height="17"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" name="PaymentNote"/>
                                <constraints>
                                    <constraint firstItem="WGS-Xh-d2V" firstAttribute="centerX" secondItem="WbB-P9-QXW" secondAttribute="centerX" id="57r-GY-9Iu"/>
                                    <constraint firstAttribute="height" constant="40" id="LyV-1b-fyg"/>
                                    <constraint firstItem="WGS-Xh-d2V" firstAttribute="centerY" secondItem="WbB-P9-QXW" secondAttribute="centerY" id="WTO-pa-aKZ"/>
                                </constraints>
                            </view>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="DPc-Fj-us9">
                                <rect key="frame" x="19" y="607" width="337.5" height="44"/>
                                <color key="backgroundColor" name="Primary"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="44" id="z6C-86-LpZ"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                <state key="normal" title="Replace Now">
                                    <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                </state>
                                <connections>
                                    <action selector="OrderRFIDAction:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="OpX-Vq-wbg"/>
                                    <action selector="replaceButtonTapped:" destination="hEx-oT-80V" eventType="touchUpInside" id="aJA-Wz-PCv"/>
                                </connections>
                            </button>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="Es0-9f-c7x"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="Es0-9f-c7x" firstAttribute="trailing" secondItem="QUv-Dv-pKb" secondAttribute="trailing" id="0rf-19-fLA"/>
                            <constraint firstItem="1XV-uD-5zM" firstAttribute="top" secondItem="QUv-Dv-pKb" secondAttribute="bottom" constant="12" id="4Tc-RL-RU8"/>
                            <constraint firstItem="WbB-P9-QXW" firstAttribute="width" secondItem="Es0-9f-c7x" secondAttribute="width" id="5P4-m8-lPB"/>
                            <constraint firstItem="eD9-Lz-6id" firstAttribute="centerY" secondItem="fCg-Kr-JbH" secondAttribute="centerY" id="80N-Vu-NXz"/>
                            <constraint firstItem="ULL-eZ-gLl" firstAttribute="height" secondItem="7ef-Hc-Ikz" secondAttribute="height" id="Jes-kL-NTe"/>
                            <constraint firstItem="ULL-eZ-gLl" firstAttribute="width" secondItem="7ef-Hc-Ikz" secondAttribute="width" id="PGq-by-pMi"/>
                            <constraint firstItem="WbB-P9-QXW" firstAttribute="centerX" secondItem="sxQ-nK-Bi0" secondAttribute="centerX" id="RIP-2J-wOK"/>
                            <constraint firstItem="Es0-9f-c7x" firstAttribute="bottom" secondItem="DPc-Fj-us9" secondAttribute="bottom" constant="16" id="VHa-6l-y10"/>
                            <constraint firstItem="WbB-P9-QXW" firstAttribute="top" secondItem="1XV-uD-5zM" secondAttribute="bottom" constant="24" id="ZkH-0u-lVc"/>
                            <constraint firstItem="eD9-Lz-6id" firstAttribute="width" secondItem="fCg-Kr-JbH" secondAttribute="width" id="dXI-Mu-QFw"/>
                            <constraint firstItem="ULL-eZ-gLl" firstAttribute="centerX" secondItem="7ef-Hc-Ikz" secondAttribute="centerX" id="i2O-Ey-aha"/>
                            <constraint firstItem="ULL-eZ-gLl" firstAttribute="centerY" secondItem="7ef-Hc-Ikz" secondAttribute="centerY" id="jJ5-6A-1k0"/>
                            <constraint firstItem="1XV-uD-5zM" firstAttribute="width" secondItem="Es0-9f-c7x" secondAttribute="width" multiplier="0.9" id="mDb-sn-sew"/>
                            <constraint firstItem="eD9-Lz-6id" firstAttribute="centerX" secondItem="fCg-Kr-JbH" secondAttribute="centerX" id="p4y-kG-esJ"/>
                            <constraint firstItem="eD9-Lz-6id" firstAttribute="height" secondItem="fCg-Kr-JbH" secondAttribute="height" id="vgY-N1-oOP"/>
                            <constraint firstItem="QUv-Dv-pKb" firstAttribute="top" secondItem="Es0-9f-c7x" secondAttribute="top" id="vgf-7W-xsh"/>
                            <constraint firstItem="QUv-Dv-pKb" firstAttribute="leading" secondItem="Es0-9f-c7x" secondAttribute="leading" id="vtS-DM-YkC"/>
                            <constraint firstItem="DPc-Fj-us9" firstAttribute="width" secondItem="Es0-9f-c7x" secondAttribute="width" multiplier="0.9" id="wBH-gY-DwY"/>
                            <constraint firstItem="DPc-Fj-us9" firstAttribute="centerX" secondItem="sxQ-nK-Bi0" secondAttribute="centerX" id="wlx-rl-Bxh"/>
                            <constraint firstItem="1XV-uD-5zM" firstAttribute="centerX" secondItem="sxQ-nK-Bi0" secondAttribute="centerX" id="zs0-vE-LgL"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="backButton" destination="80W-tz-EaP" id="zu2-n4-GOC"/>
                        <outlet property="btnReplaceNow" destination="DPc-Fj-us9" id="ZHY-U2-GGC"/>
                        <outlet property="imgDefective" destination="utq-Wm-EmE" id="LvP-ZG-3wQ"/>
                        <outlet property="imgNotWorking" destination="UGZ-AN-bcf" id="4BE-17-mll"/>
                        <outlet property="txtOtherReason" destination="Xqb-ty-YFj" id="47c-JP-TNe"/>
                        <outlet property="viewDefective" destination="7ef-Hc-Ikz" id="Mb8-eH-33o"/>
                        <outlet property="viewNotWorking" destination="fCg-Kr-JbH" id="3hv-fc-qcE"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="kQy-7o-6sa" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="2765.5999999999999" y="69.715142428785612"/>
        </scene>
    </scenes>
    <resources>
        <image name="ic_RFID_card" width="500" height="500"/>
        <image name="ic_about" width="500" height="500"/>
        <image name="ic_add_card" width="50" height="50"/>
        <image name="ic_back_arrow" width="64" height="64"/>
        <image name="ic_block_RFID" width="300" height="300"/>
        <image name="ic_block_card-1" width="500" height="522"/>
        <image name="ic_cancel" width="64" height="64"/>
        <image name="ic_cardBG" width="1000" height="536"/>
        <image name="ic_card_list" width="100" height="100"/>
        <image name="ic_checked" width="500" height="500"/>
        <image name="ic_dropdown" width="64" height="64"/>
        <image name="ic_issue_card" width="80" height="80"/>
        <image name="ic_line" width="2" height="55"/>
        <image name="ic_no_data" width="275" height="183"/>
        <image name="ic_rect" width="170" height="47"/>
        <image name="ic_replace_card" width="500" height="517"/>
        <image name="ic_right_navigate" width="64" height="64"/>
        <image name="ic_thumb_up" width="300" height="300"/>
        <image name="ic_ticked" width="64" height="64"/>
        <image name="ic_track_card" width="50" height="50"/>
        <image name="ic_track_order" width="500" height="500"/>
        <image name="ic_uncheck" width="500" height="500"/>
        <namedColor name="GrayPlaceholder">
            <color red="0.0" green="0.0" blue="0.0" alpha="0.5" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="PaymentNote">
            <color red="0.82352941176470584" green="0.*****************" blue="0.14117647058823529" alpha="0.15000000596046448" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Primary">
            <color red="0.016000000759959221" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="PrimarySelection">
            <color red="0.59999999999999998" green="0.59999999999999998" blue="0.59999999999999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="PrimaryTextColor">
            <color red="0.32899999618530273" green="0.32400000095367432" blue="0.4779999852180481" alpha="0.33000001311302185" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="labelColor">
            <color white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="systemGrayColor">
            <color red="0.55686274509803924" green="0.55686274509803924" blue="0.57647058823529407" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>

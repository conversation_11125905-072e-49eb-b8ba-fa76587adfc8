<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="19162" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="19144"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--VehicleVC-->
        <scene sceneID="nxD-iX-szb">
            <objects>
                <viewController storyboardIdentifier="VehicleVC" id="xdf-Lr-4wg" customClass="VehicleVC" customModule="NXC_EV_Solutions" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="bhj-U6-yKi">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" scrollEnabled="NO" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" translatesAutoresizingMaskIntoConstraints="NO" id="4ag-hk-2hR">
                                <rect key="frame" x="0.0" y="53.5" width="375" height="613.5"/>
                                <subviews>
                                    <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="N7v-yF-Ylo">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="600.5"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Vehicle Information" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="7se-g7-kpH">
                                                <rect key="frame" x="16" y="8" width="158.5" height="34"/>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="34" id="WzO-Br-xha"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="boldSystem" pointSize="17"/>
                                                <color key="textColor" name="PrimaryColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="1dW-ot-jno">
                                                <rect key="frame" x="19" y="54" width="337.5" height="347"/>
                                                <subviews>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="2wO-rG-epM">
                                                        <rect key="frame" x="0.0" y="0.0" width="337.5" height="46"/>
                                                        <subviews>
                                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="vjc-tz-DkC">
                                                                <rect key="frame" x="0.0" y="0.0" width="307.5" height="44"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="44" id="tI6-6P-9fS"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                <textInputTraits key="textInputTraits"/>
                                                            </textField>
                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_dropdown" translatesAutoresizingMaskIntoConstraints="NO" id="Bg1-fU-4zQ">
                                                                <rect key="frame" x="317.5" y="15" width="16" height="16"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="16" id="5Ld-Sh-os3"/>
                                                                    <constraint firstAttribute="height" constant="16" id="wgD-7v-Q19"/>
                                                                </constraints>
                                                            </imageView>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="5bl-ma-T4f">
                                                                <rect key="frame" x="0.0" y="45" width="337.5" height="1"/>
                                                                <color key="backgroundColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="1" id="1dy-rW-n07"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                <color key="textColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstAttribute="trailing" secondItem="Bg1-fU-4zQ" secondAttribute="trailing" constant="4" id="884-jU-rYn"/>
                                                            <constraint firstItem="5bl-ma-T4f" firstAttribute="leading" secondItem="2wO-rG-epM" secondAttribute="leading" id="9qu-1R-JpU"/>
                                                            <constraint firstItem="vjc-tz-DkC" firstAttribute="top" secondItem="2wO-rG-epM" secondAttribute="top" id="HkA-kD-5MQ"/>
                                                            <constraint firstItem="vjc-tz-DkC" firstAttribute="leading" secondItem="2wO-rG-epM" secondAttribute="leading" id="NUN-NJ-UqC"/>
                                                            <constraint firstAttribute="trailing" secondItem="5bl-ma-T4f" secondAttribute="trailing" id="cdT-ss-CoK"/>
                                                            <constraint firstItem="Bg1-fU-4zQ" firstAttribute="centerY" secondItem="2wO-rG-epM" secondAttribute="centerY" id="hwO-tC-ZBt"/>
                                                            <constraint firstAttribute="trailing" secondItem="vjc-tz-DkC" secondAttribute="trailing" constant="30" id="i0x-zP-zCd"/>
                                                            <constraint firstAttribute="bottom" secondItem="5bl-ma-T4f" secondAttribute="bottom" id="qy5-gW-KZo"/>
                                                            <constraint firstItem="5bl-ma-T4f" firstAttribute="top" secondItem="vjc-tz-DkC" secondAttribute="bottom" constant="1" id="t0S-cP-iBG"/>
                                                        </constraints>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="bKT-cA-uHp">
                                                        <rect key="frame" x="0.0" y="58" width="337.5" height="46"/>
                                                        <subviews>
                                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="5Gt-tX-ckh">
                                                                <rect key="frame" x="0.0" y="0.0" width="307.5" height="44"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="44" id="J6G-3P-uY6"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                <textInputTraits key="textInputTraits"/>
                                                            </textField>
                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" alpha="0.0" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_dropdown" translatesAutoresizingMaskIntoConstraints="NO" id="jic-EZ-yE6">
                                                                <rect key="frame" x="317.5" y="15" width="16" height="16"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="16" id="GVD-cc-Vud"/>
                                                                    <constraint firstAttribute="height" constant="16" id="mtY-x7-TfR"/>
                                                                </constraints>
                                                            </imageView>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="b05-He-1LZ">
                                                                <rect key="frame" x="0.0" y="45" width="337.5" height="1"/>
                                                                <color key="backgroundColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="1" id="7lP-Al-qWT"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                <color key="textColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstItem="b05-He-1LZ" firstAttribute="top" secondItem="5Gt-tX-ckh" secondAttribute="bottom" constant="1" id="2t2-7m-eTU"/>
                                                            <constraint firstItem="jic-EZ-yE6" firstAttribute="centerY" secondItem="bKT-cA-uHp" secondAttribute="centerY" id="Wda-ST-jEI"/>
                                                            <constraint firstAttribute="trailing" secondItem="5Gt-tX-ckh" secondAttribute="trailing" constant="30" id="aMw-6u-Shz"/>
                                                            <constraint firstAttribute="trailing" secondItem="b05-He-1LZ" secondAttribute="trailing" id="acQ-gq-Htl"/>
                                                            <constraint firstItem="5Gt-tX-ckh" firstAttribute="leading" secondItem="bKT-cA-uHp" secondAttribute="leading" id="cnC-tD-ZEs"/>
                                                            <constraint firstItem="b05-He-1LZ" firstAttribute="leading" secondItem="bKT-cA-uHp" secondAttribute="leading" id="e2b-wo-SAZ"/>
                                                            <constraint firstItem="5Gt-tX-ckh" firstAttribute="top" secondItem="bKT-cA-uHp" secondAttribute="top" id="haj-cO-sTk"/>
                                                            <constraint firstAttribute="trailing" secondItem="jic-EZ-yE6" secondAttribute="trailing" constant="4" id="lor-aw-0Gt"/>
                                                            <constraint firstAttribute="bottom" secondItem="b05-He-1LZ" secondAttribute="bottom" id="n4u-F4-Ifc"/>
                                                        </constraints>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="uRu-pg-5KD">
                                                        <rect key="frame" x="0.0" y="116" width="337.5" height="46"/>
                                                        <subviews>
                                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="aDW-M9-kPy">
                                                                <rect key="frame" x="0.0" y="0.0" width="307.5" height="44"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="44" id="BG8-yL-Jrj"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                <textInputTraits key="textInputTraits"/>
                                                            </textField>
                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_dropdown" translatesAutoresizingMaskIntoConstraints="NO" id="R78-8K-DqM">
                                                                <rect key="frame" x="317.5" y="15" width="16" height="16"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="16" id="ACU-Gm-edf"/>
                                                                    <constraint firstAttribute="height" constant="16" id="fZD-eK-Uqd"/>
                                                                </constraints>
                                                            </imageView>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="p1g-sn-agR">
                                                                <rect key="frame" x="0.0" y="45" width="337.5" height="1"/>
                                                                <color key="backgroundColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="1" id="f8A-sQ-Def"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                <color key="textColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstAttribute="trailing" secondItem="R78-8K-DqM" secondAttribute="trailing" constant="4" id="1b9-DG-E0j"/>
                                                            <constraint firstItem="p1g-sn-agR" firstAttribute="top" secondItem="aDW-M9-kPy" secondAttribute="bottom" constant="1" id="9CY-6Z-ZLJ"/>
                                                            <constraint firstAttribute="bottom" secondItem="p1g-sn-agR" secondAttribute="bottom" id="BpB-pv-0J3"/>
                                                            <constraint firstItem="aDW-M9-kPy" firstAttribute="leading" secondItem="uRu-pg-5KD" secondAttribute="leading" id="SP7-R6-hKi"/>
                                                            <constraint firstAttribute="trailing" secondItem="p1g-sn-agR" secondAttribute="trailing" id="VtJ-nk-RYP"/>
                                                            <constraint firstAttribute="trailing" secondItem="aDW-M9-kPy" secondAttribute="trailing" constant="30" id="awL-MZ-9pe"/>
                                                            <constraint firstItem="p1g-sn-agR" firstAttribute="leading" secondItem="uRu-pg-5KD" secondAttribute="leading" id="mAT-ZS-gEJ"/>
                                                            <constraint firstItem="R78-8K-DqM" firstAttribute="centerY" secondItem="uRu-pg-5KD" secondAttribute="centerY" id="weM-vR-xUx"/>
                                                            <constraint firstItem="aDW-M9-kPy" firstAttribute="top" secondItem="uRu-pg-5KD" secondAttribute="top" id="xr1-k0-wZi"/>
                                                        </constraints>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="v7t-Lg-40s">
                                                        <rect key="frame" x="0.0" y="174" width="337.5" height="46"/>
                                                        <subviews>
                                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="tmQ-eI-QAh">
                                                                <rect key="frame" x="0.0" y="0.0" width="307.5" height="44"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="44" id="of4-MN-R5v"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                <textInputTraits key="textInputTraits"/>
                                                            </textField>
                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_dropdown" translatesAutoresizingMaskIntoConstraints="NO" id="8z1-rj-dcD">
                                                                <rect key="frame" x="317.5" y="15" width="16" height="16"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="16" id="9rZ-P1-6cS"/>
                                                                    <constraint firstAttribute="height" constant="16" id="bhd-sn-KiU"/>
                                                                </constraints>
                                                            </imageView>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="sqP-Tb-foT">
                                                                <rect key="frame" x="0.0" y="45" width="337.5" height="1"/>
                                                                <color key="backgroundColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="1" id="TmR-fB-mpg"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                <color key="textColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstItem="tmQ-eI-QAh" firstAttribute="top" secondItem="v7t-Lg-40s" secondAttribute="top" id="CY8-yz-hNK"/>
                                                            <constraint firstItem="tmQ-eI-QAh" firstAttribute="leading" secondItem="v7t-Lg-40s" secondAttribute="leading" id="CcC-bn-653"/>
                                                            <constraint firstAttribute="trailing" secondItem="tmQ-eI-QAh" secondAttribute="trailing" constant="30" id="Knh-ek-zfM"/>
                                                            <constraint firstItem="8z1-rj-dcD" firstAttribute="centerY" secondItem="v7t-Lg-40s" secondAttribute="centerY" id="SIe-ZK-geA"/>
                                                            <constraint firstItem="sqP-Tb-foT" firstAttribute="top" secondItem="tmQ-eI-QAh" secondAttribute="bottom" constant="1" id="VTi-Gi-b14"/>
                                                            <constraint firstAttribute="trailing" secondItem="sqP-Tb-foT" secondAttribute="trailing" id="bUG-No-n4W"/>
                                                            <constraint firstItem="sqP-Tb-foT" firstAttribute="leading" secondItem="v7t-Lg-40s" secondAttribute="leading" id="fvu-Ll-y9V"/>
                                                            <constraint firstAttribute="bottom" secondItem="sqP-Tb-foT" secondAttribute="bottom" id="q26-oa-S4E"/>
                                                            <constraint firstAttribute="trailing" secondItem="8z1-rj-dcD" secondAttribute="trailing" constant="4" id="wdz-RV-fHP"/>
                                                        </constraints>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="xOa-08-ptu">
                                                        <rect key="frame" x="0.0" y="232" width="337.5" height="46"/>
                                                        <subviews>
                                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="x11-oA-C69">
                                                                <rect key="frame" x="0.0" y="0.0" width="307.5" height="44"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="44" id="Gmi-oT-Tck"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                <textInputTraits key="textInputTraits"/>
                                                            </textField>
                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" alpha="0.0" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_dropdown" translatesAutoresizingMaskIntoConstraints="NO" id="Pc4-HD-X5b">
                                                                <rect key="frame" x="317.5" y="15" width="16" height="16"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="16" id="BL9-ux-Xt9"/>
                                                                    <constraint firstAttribute="width" constant="16" id="rze-on-kDZ"/>
                                                                </constraints>
                                                            </imageView>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="710-dD-aeQ">
                                                                <rect key="frame" x="0.0" y="45" width="337.5" height="1"/>
                                                                <color key="backgroundColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="1" id="bSY-qA-rWV"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                <color key="textColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstAttribute="bottom" secondItem="710-dD-aeQ" secondAttribute="bottom" id="8sm-PV-Zhj"/>
                                                            <constraint firstItem="710-dD-aeQ" firstAttribute="leading" secondItem="xOa-08-ptu" secondAttribute="leading" id="BZx-Eb-jBy"/>
                                                            <constraint firstItem="710-dD-aeQ" firstAttribute="top" secondItem="x11-oA-C69" secondAttribute="bottom" constant="1" id="K1j-Ij-XOP"/>
                                                            <constraint firstAttribute="trailing" secondItem="710-dD-aeQ" secondAttribute="trailing" id="Sze-Zp-FG8"/>
                                                            <constraint firstItem="Pc4-HD-X5b" firstAttribute="centerY" secondItem="xOa-08-ptu" secondAttribute="centerY" id="UoN-Zz-dMB"/>
                                                            <constraint firstItem="x11-oA-C69" firstAttribute="top" secondItem="xOa-08-ptu" secondAttribute="top" id="ZZd-9e-hYf"/>
                                                            <constraint firstAttribute="trailing" secondItem="Pc4-HD-X5b" secondAttribute="trailing" constant="4" id="kBA-4X-wuk"/>
                                                            <constraint firstAttribute="trailing" secondItem="x11-oA-C69" secondAttribute="trailing" constant="30" id="nct-Kb-l2E"/>
                                                            <constraint firstItem="x11-oA-C69" firstAttribute="leading" secondItem="xOa-08-ptu" secondAttribute="leading" id="pHA-rX-wjW"/>
                                                        </constraints>
                                                    </view>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="1" translatesAutoresizingMaskIntoConstraints="NO" id="4ne-fp-Ffm">
                                                        <rect key="frame" x="0.0" y="290" width="337.5" height="57"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Registration Number" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ujB-1q-eul">
                                                                <rect key="frame" x="0.0" y="0.0" width="337.5" height="20"/>
                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="20" id="2cb-Yh-hkE"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" name=".AppleSystemUIFont" family=".AppleSystemUIFont" pointSize="15"/>
                                                                <color key="textColor" systemColor="systemGrayColor"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="MH01XX1111" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="naV-a3-GLA">
                                                                <rect key="frame" x="0.0" y="21" width="337.5" height="34"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="34" id="T0k-UB-4ud"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                <textInputTraits key="textInputTraits" autocapitalizationType="allCharacters"/>
                                                            </textField>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="24r-Q6-wLF">
                                                                <rect key="frame" x="0.0" y="56" width="337.5" height="1"/>
                                                                <color key="backgroundColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="1" id="RlY-ne-02c"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                <color key="textColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                    </stackView>
                                                </subviews>
                                            </stackView>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="JLc-dN-d7h">
                                                <rect key="frame" x="16" y="553.5" width="44" height="44"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="44" id="0EU-o6-byN"/>
                                                    <constraint firstAttribute="width" constant="44" id="i2i-59-V3c"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="20"/>
                                                <state key="normal" title="Skip">
                                                    <color key="titleColor" name="PrimaryColor"/>
                                                </state>
                                                <connections>
                                                    <action selector="skipAction:" destination="xdf-Lr-4wg" eventType="touchUpInside" id="GTb-zO-EmP"/>
                                                </connections>
                                            </button>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" usesAttributedText="YES" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Olw-Q2-Jt9">
                                                <rect key="frame" x="28" y="431" width="319" height="33"/>
                                                <attributedString key="attributedText">
                                                    <fragment content="We can improve the accuracy of your charging data if we know which vehicle you drive.">
                                                        <attributes>
                                                            <color key="NSColor" red="0.15294117647058825" green="0.14117647058823529" blue="0.14117647058823529" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                            <font key="NSFont" size="14" name="Roboto-Medium"/>
                                                            <paragraphStyle key="NSParagraphStyle" alignment="center" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0"/>
                                                        </attributes>
                                                    </fragment>
                                                </attributedString>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="Olw-Q2-Jt9" firstAttribute="centerX" secondItem="N7v-yF-Ylo" secondAttribute="centerX" id="8Y4-xJ-2CZ"/>
                                            <constraint firstAttribute="height" constant="600.5" id="9hg-aO-jP4"/>
                                            <constraint firstItem="1dW-ot-jno" firstAttribute="width" secondItem="N7v-yF-Ylo" secondAttribute="width" multiplier="0.9" id="BHE-Sg-6Ab"/>
                                            <constraint firstItem="1dW-ot-jno" firstAttribute="centerX" secondItem="N7v-yF-Ylo" secondAttribute="centerX" id="I0g-Uo-ZvV"/>
                                            <constraint firstItem="1dW-ot-jno" firstAttribute="top" secondItem="7se-g7-kpH" secondAttribute="bottom" constant="12" id="LA2-sr-xwd"/>
                                            <constraint firstItem="7se-g7-kpH" firstAttribute="leading" secondItem="N7v-yF-Ylo" secondAttribute="leading" constant="16" id="OkW-1v-dUf"/>
                                            <constraint firstItem="JLc-dN-d7h" firstAttribute="leading" secondItem="N7v-yF-Ylo" secondAttribute="leading" constant="16" id="Yhr-OC-aTF"/>
                                            <constraint firstItem="7se-g7-kpH" firstAttribute="top" secondItem="N7v-yF-Ylo" secondAttribute="top" constant="8" id="dnl-pP-UOK"/>
                                            <constraint firstItem="Olw-Q2-Jt9" firstAttribute="top" secondItem="1dW-ot-jno" secondAttribute="bottom" constant="30" id="qT8-IM-XKG"/>
                                            <constraint firstItem="Olw-Q2-Jt9" firstAttribute="width" secondItem="N7v-yF-Ylo" secondAttribute="width" multiplier="0.85" id="rwB-87-tsc"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="N7v-yF-Ylo" firstAttribute="top" secondItem="jZD-Os-kP3" secondAttribute="top" id="6rI-C5-iDt"/>
                                    <constraint firstAttribute="trailing" secondItem="N7v-yF-Ylo" secondAttribute="trailing" id="UWb-I7-7la"/>
                                    <constraint firstItem="N7v-yF-Ylo" firstAttribute="leading" secondItem="jZD-Os-kP3" secondAttribute="leading" id="cn9-HQ-Heh"/>
                                    <constraint firstItem="N7v-yF-Ylo" firstAttribute="centerX" secondItem="4ag-hk-2hR" secondAttribute="centerX" id="tA6-Kj-VTY"/>
                                    <constraint firstAttribute="bottom" secondItem="N7v-yF-Ylo" secondAttribute="bottom" id="zNw-MB-vXN"/>
                                </constraints>
                                <viewLayoutGuide key="contentLayoutGuide" id="MSc-Mi-W78"/>
                                <viewLayoutGuide key="frameLayoutGuide" id="jZD-Os-kP3"/>
                            </scrollView>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="hn7-T6-xGe">
                                <rect key="frame" x="19" y="223.5" width="337.5" height="46"/>
                                <connections>
                                    <action selector="typeTapped:" destination="xdf-Lr-4wg" eventType="touchUpInside" id="Aga-Ru-ecE"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="GmX-jJ-0fK">
                                <rect key="frame" x="19" y="281.5" width="337.5" height="46"/>
                                <connections>
                                    <action selector="modelTapped:" destination="xdf-Lr-4wg" eventType="touchUpInside" id="15I-ba-Uq0"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Ga4-qW-6ki">
                                <rect key="frame" x="19" y="107.5" width="337.5" height="46"/>
                                <connections>
                                    <action selector="brandTapped:" destination="xdf-Lr-4wg" eventType="touchUpInside" id="ZiU-Kc-DLW"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="m8P-rD-PVB">
                                <rect key="frame" x="0.0" y="3" width="44" height="44"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="44" id="Ibx-DJ-Ydg"/>
                                    <constraint firstAttribute="height" constant="44" id="P2s-gG-tV8"/>
                                </constraints>
                                <inset key="imageEdgeInsets" minX="12" minY="12" maxX="12" maxY="12"/>
                                <state key="normal" image="ic_backarrow"/>
                                <connections>
                                    <action selector="backAction:" destination="xdf-Lr-4wg" eventType="touchUpInside" id="OMl-ED-wXL"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="hcM-eb-xG7">
                                <rect key="frame" x="307" y="607" width="44" height="44"/>
                                <color key="backgroundColor" name="PrimaryColor"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="44" id="0bl-f9-p5k"/>
                                    <constraint firstAttribute="height" constant="44" id="E9o-0F-MSk"/>
                                </constraints>
                                <inset key="imageEdgeInsets" minX="11" minY="11" maxX="11" maxY="11"/>
                                <state key="normal" image="ic_next"/>
                                <connections>
                                    <action selector="nextAction:" destination="xdf-Lr-4wg" eventType="touchUpInside" id="qFp-RC-qok"/>
                                </connections>
                            </button>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="P9F-a9-gz7"/>
                        <color key="backgroundColor" name="PrimaryColor"/>
                        <constraints>
                            <constraint firstItem="GmX-jJ-0fK" firstAttribute="centerY" secondItem="v7t-Lg-40s" secondAttribute="centerY" id="1Eg-1k-KLf"/>
                            <constraint firstItem="4ag-hk-2hR" firstAttribute="height" secondItem="P9F-a9-gz7" secondAttribute="height" multiplier="0.92" id="9di-Rh-iTE"/>
                            <constraint firstItem="4ag-hk-2hR" firstAttribute="leading" secondItem="P9F-a9-gz7" secondAttribute="leading" id="9yf-CM-fQy"/>
                            <constraint firstItem="Ga4-qW-6ki" firstAttribute="height" secondItem="2wO-rG-epM" secondAttribute="height" id="C2J-6h-v1C"/>
                            <constraint firstItem="GmX-jJ-0fK" firstAttribute="height" secondItem="v7t-Lg-40s" secondAttribute="height" id="JP0-38-JuQ"/>
                            <constraint firstItem="P9F-a9-gz7" firstAttribute="bottom" secondItem="hcM-eb-xG7" secondAttribute="bottom" constant="16" id="JsP-oQ-2B2"/>
                            <constraint firstItem="JLc-dN-d7h" firstAttribute="centerY" secondItem="hcM-eb-xG7" secondAttribute="centerY" id="L38-Fs-0tm"/>
                            <constraint firstItem="P9F-a9-gz7" firstAttribute="bottom" secondItem="4ag-hk-2hR" secondAttribute="bottom" id="Mbk-7C-XAL"/>
                            <constraint firstItem="hn7-T6-xGe" firstAttribute="centerX" secondItem="uRu-pg-5KD" secondAttribute="centerX" id="Pnf-Yu-1la"/>
                            <constraint firstItem="hn7-T6-xGe" firstAttribute="width" secondItem="uRu-pg-5KD" secondAttribute="width" id="SLT-gM-RBx"/>
                            <constraint firstItem="Ga4-qW-6ki" firstAttribute="width" secondItem="2wO-rG-epM" secondAttribute="width" id="SfP-gS-3GR"/>
                            <constraint firstItem="GmX-jJ-0fK" firstAttribute="centerX" secondItem="v7t-Lg-40s" secondAttribute="centerX" id="Xa5-W0-NaN"/>
                            <constraint firstItem="Ga4-qW-6ki" firstAttribute="centerX" secondItem="2wO-rG-epM" secondAttribute="centerX" id="aSr-vH-xpt"/>
                            <constraint firstItem="hn7-T6-xGe" firstAttribute="height" secondItem="uRu-pg-5KD" secondAttribute="height" id="hNI-WT-lof"/>
                            <constraint firstItem="m8P-rD-PVB" firstAttribute="top" secondItem="P9F-a9-gz7" secondAttribute="top" constant="3" id="kgj-uZ-Sj9"/>
                            <constraint firstItem="hn7-T6-xGe" firstAttribute="centerY" secondItem="uRu-pg-5KD" secondAttribute="centerY" id="lbP-cd-V36"/>
                            <constraint firstItem="P9F-a9-gz7" firstAttribute="trailing" secondItem="hcM-eb-xG7" secondAttribute="trailing" constant="24" id="oTe-px-PT7"/>
                            <constraint firstItem="P9F-a9-gz7" firstAttribute="trailing" secondItem="4ag-hk-2hR" secondAttribute="trailing" id="qPE-0g-wrh"/>
                            <constraint firstItem="GmX-jJ-0fK" firstAttribute="width" secondItem="v7t-Lg-40s" secondAttribute="width" id="tbJ-pp-u5C"/>
                            <constraint firstItem="m8P-rD-PVB" firstAttribute="leading" secondItem="P9F-a9-gz7" secondAttribute="leading" id="tbT-rz-VId"/>
                            <constraint firstItem="Ga4-qW-6ki" firstAttribute="centerY" secondItem="2wO-rG-epM" secondAttribute="centerY" id="zLA-gp-Xn8"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="brandButton" destination="Ga4-qW-6ki" id="3uR-6I-nWb"/>
                        <outlet property="btnNext" destination="hcM-eb-xG7" id="Bnq-fa-swF"/>
                        <outlet property="btnSkip" destination="JLc-dN-d7h" id="ahu-EV-gQl"/>
                        <outlet property="modelButton" destination="GmX-jJ-0fK" id="xaY-u4-va6"/>
                        <outlet property="scrollDetails" destination="4ag-hk-2hR" id="j1g-b8-a6N"/>
                        <outlet property="txtBrand" destination="vjc-tz-DkC" id="brX-SM-HTm"/>
                        <outlet property="txtModel" destination="tmQ-eI-QAh" id="dsh-ic-xk1"/>
                        <outlet property="txtOtherBrand" destination="5Gt-tX-ckh" id="ooi-Bt-oWZ"/>
                        <outlet property="txtOtherModel" destination="x11-oA-C69" id="9Pr-jZ-iOf"/>
                        <outlet property="txtRegNo" destination="naV-a3-GLA" id="XfG-ds-4fu"/>
                        <outlet property="txtType" destination="aDW-M9-kPy" id="I8B-MD-Asa"/>
                        <outlet property="typeButton" destination="hn7-T6-xGe" id="Go5-ag-7Pm"/>
                        <outlet property="viewOtherBrand" destination="bKT-cA-uHp" id="agY-pa-Yfd"/>
                        <outlet property="viewOtherModel" destination="xOa-08-ptu" id="CzQ-Vn-11a"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="ayL-z9-Esz" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-396" y="132.68365817091455"/>
        </scene>
    </scenes>
    <resources>
        <image name="ic_backarrow" width="64" height="64"/>
        <image name="ic_dropdown" width="64" height="64"/>
        <image name="ic_next" width="64" height="64"/>
        <namedColor name="PrimaryColor">
            <color red="0.015686274509803921" green="0.49411764705882355" blue="0.42745098039215684" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="systemGrayColor">
            <color red="0.55686274509803924" green="0.55686274509803924" blue="0.57647058823529407" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>

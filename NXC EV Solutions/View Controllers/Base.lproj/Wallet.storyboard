<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="23727" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_9" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23721"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--WalletVC-->
        <scene sceneID="s0d-6b-0kx">
            <objects>
                <viewController storyboardIdentifier="WalletVC" id="Y6W-OH-hqX" customClass="WalletVC" customModule="NXC_EV_Solutions" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="5EZ-qb-Rvc">
                        <rect key="frame" x="0.0" y="0.0" width="440" height="956"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="8EO-JG-DoG">
                                <rect key="frame" x="0.0" y="124" width="440" height="44"/>
                                <subviews>
                                    <button opaque="NO" alpha="0.0" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="V6o-ag-F3k">
                                        <rect key="frame" x="0.0" y="0.0" width="44" height="44"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="44" id="8gc-uR-z8F"/>
                                            <constraint firstAttribute="height" constant="44" id="Oe7-lc-mqu"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="12" minY="12" maxX="12" maxY="12"/>
                                        <state key="normal" image="ic_back_arrow"/>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Wallet" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="YbI-Fi-fQS">
                                        <rect key="frame" x="195.33333333333334" y="11.999999999999998" width="49.666666666666657" height="20.333333333333329"/>
                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                        <color key="textColor" red="0.070588235289999995" green="0.070588235289999995" blue="0.070588235289999995" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="44" id="FSM-4q-23d"/>
                                    <constraint firstItem="V6o-ag-F3k" firstAttribute="leading" secondItem="8EO-JG-DoG" secondAttribute="leading" id="GlP-KD-EgT"/>
                                    <constraint firstItem="YbI-Fi-fQS" firstAttribute="centerY" secondItem="8EO-JG-DoG" secondAttribute="centerY" id="Oow-tr-Bix"/>
                                    <constraint firstItem="V6o-ag-F3k" firstAttribute="top" secondItem="8EO-JG-DoG" secondAttribute="top" id="kiC-9K-9Hu"/>
                                    <constraint firstItem="YbI-Fi-fQS" firstAttribute="centerX" secondItem="8EO-JG-DoG" secondAttribute="centerX" id="x15-Y3-aA2"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="OzM-K4-J4K">
                                <rect key="frame" x="33" y="184" width="374" height="150"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" alignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="0iL-pq-ws1">
                                        <rect key="frame" x="125" y="18" width="124" height="114"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" usesAttributedText="YES" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="JeQ-kA-65Y">
                                                <rect key="frame" x="2.6666666666666572" y="0.0" width="119" height="38"/>
                                                <attributedString key="attributedText">
                                                    <fragment content="Available ">
                                                        <attributes>
                                                            <color key="NSColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <font key="NSFont" metaFont="system" size="15"/>
                                                        </attributes>
                                                    </fragment>
                                                    <fragment content="Balance">
                                                        <attributes>
                                                            <color key="NSColor" red="0.015686274509803921" green="0.49411764705882355" blue="0.42745098039215684" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                            <font key="NSFont" metaFont="system" size="15"/>
                                                        </attributes>
                                                    </fragment>
                                                </attributedString>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="₹ " textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="BcS-d5-eY2">
                                                <rect key="frame" x="49.333333333333343" y="38" width="25.333333333333329" height="38"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="30"/>
                                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ClG-1i-jeO">
                                                <rect key="frame" x="0.0" y="76" width="124" height="38"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" alignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="NrJ-fW-yGD">
                                                        <rect key="frame" x="8" y="0.0" width="108" height="38"/>
                                                        <subviews>
                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_add" translatesAutoresizingMaskIntoConstraints="NO" id="3uJ-kj-Kp1">
                                                                <rect key="frame" x="0.0" y="0.0" width="18" height="38"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="18" id="vum-BF-MvS"/>
                                                                </constraints>
                                                            </imageView>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Add Money" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="6Pm-W2-Fpp">
                                                                <rect key="frame" x="18" y="10" width="90" height="18"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="90" id="4W2-Ok-q3n"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                    </stackView>
                                                </subviews>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="38" id="3fl-mM-o1d"/>
                                                    <constraint firstAttribute="trailing" secondItem="NrJ-fW-yGD" secondAttribute="trailing" constant="8" id="4rI-1w-YUP"/>
                                                    <constraint firstItem="NrJ-fW-yGD" firstAttribute="top" secondItem="ClG-1i-jeO" secondAttribute="top" id="7Ve-2H-xhC"/>
                                                    <constraint firstItem="NrJ-fW-yGD" firstAttribute="leading" secondItem="ClG-1i-jeO" secondAttribute="leading" constant="8" id="EPQ-bi-g9s"/>
                                                    <constraint firstAttribute="bottom" secondItem="NrJ-fW-yGD" secondAttribute="bottom" id="Pyc-Rp-wC9"/>
                                                </constraints>
                                            </view>
                                        </subviews>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="0iL-pq-ws1" firstAttribute="centerX" secondItem="OzM-K4-J4K" secondAttribute="centerX" id="5DG-Zg-wXy"/>
                                    <constraint firstAttribute="height" constant="150" id="RVZ-1t-ah8"/>
                                    <constraint firstItem="0iL-pq-ws1" firstAttribute="centerY" secondItem="OzM-K4-J4K" secondAttribute="centerY" id="qcw-hO-5oT"/>
                                </constraints>
                            </view>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="252" text="See Activity" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="DsX-ZJ-pcf">
                                <rect key="frame" x="177.33333333333334" y="350" width="85.666666666666657" height="19.333333333333314"/>
                                <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                <color key="textColor" white="0.66666666669999997" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="U5B-Ke-gZD">
                                <rect key="frame" x="0.0" y="377.33333333333326" width="440" height="458.66666666666674"/>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <prototypes>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="none" indentationWidth="10" reuseIdentifier="cell" id="tdO-Ja-IuR" customClass="WalletCell" customModule="NXC_EV_Solutions" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="50" width="440" height="58"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="tdO-Ja-IuR" id="g4P-8f-jTR">
                                            <rect key="frame" x="0.0" y="0.0" width="440" height="58"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ehY-Zd-6DK">
                                                    <rect key="frame" x="4" y="4" width="432" height="50"/>
                                                    <subviews>
                                                        <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="22i-7J-igH">
                                                            <rect key="frame" x="4" y="0.0" width="424" height="44"/>
                                                            <subviews>
                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="FsD-DN-Dcx">
                                                                    <rect key="frame" x="0.0" y="3" width="38" height="38"/>
                                                                    <subviews>
                                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_car_wallet" translatesAutoresizingMaskIntoConstraints="NO" id="DtI-4j-a5k">
                                                                            <rect key="frame" x="8" y="8" width="22" height="22"/>
                                                                            <constraints>
                                                                                <constraint firstAttribute="width" constant="22" id="Cvp-eG-aZ1"/>
                                                                                <constraint firstAttribute="height" constant="22" id="n3G-IR-EGC"/>
                                                                            </constraints>
                                                                        </imageView>
                                                                    </subviews>
                                                                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="height" constant="38" id="4dh-yV-i7G"/>
                                                                        <constraint firstItem="DtI-4j-a5k" firstAttribute="centerX" secondItem="FsD-DN-Dcx" secondAttribute="centerX" id="672-w0-iAj"/>
                                                                        <constraint firstAttribute="width" constant="38" id="Uiz-yh-IR3"/>
                                                                        <constraint firstItem="DtI-4j-a5k" firstAttribute="centerY" secondItem="FsD-DN-Dcx" secondAttribute="centerY" id="f0V-Up-3hw"/>
                                                                    </constraints>
                                                                </view>
                                                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="VZb-1C-1ed">
                                                                    <rect key="frame" x="46.000000000000014" y="0.0" width="200.33333333333337" height="44"/>
                                                                    <subviews>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Paid to EVC Charge" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="VGg-MU-B8a">
                                                                            <rect key="frame" x="0.0" y="0.0" width="200.33333333333334" height="24"/>
                                                                            <constraints>
                                                                                <constraint firstAttribute="height" constant="24" id="vBK-ML-MQO"/>
                                                                            </constraints>
                                                                            <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                            <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Payment Sent" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="96B-xc-e72">
                                                                            <rect key="frame" x="0.0" y="24" width="200.33333333333334" height="20"/>
                                                                            <constraints>
                                                                                <constraint firstAttribute="height" constant="20" id="7Dh-s8-An3"/>
                                                                            </constraints>
                                                                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                            <color key="textColor" name="PrimarySelection"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                    </subviews>
                                                                </stackView>
                                                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="iQc-qR-g2O">
                                                                    <rect key="frame" x="254.33333333333331" y="0.0" width="169.66666666666669" height="44"/>
                                                                    <subviews>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="- ₹ 390" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="BcX-J3-Hyg">
                                                                            <rect key="frame" x="0.0" y="0.0" width="169.66666666666666" height="24"/>
                                                                            <constraints>
                                                                                <constraint firstAttribute="height" constant="24" id="Tbs-Rb-H63"/>
                                                                            </constraints>
                                                                            <fontDescription key="fontDescription" type="boldSystem" pointSize="15"/>
                                                                            <color key="textColor" name="WalletDeductText"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="15 Aug " textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="e7m-jj-IA9">
                                                                            <rect key="frame" x="0.0" y="24" width="169.66666666666666" height="20"/>
                                                                            <constraints>
                                                                                <constraint firstAttribute="height" constant="20" id="g27-QX-Jdx"/>
                                                                            </constraints>
                                                                            <fontDescription key="fontDescription" type="system" pointSize="13"/>
                                                                            <color key="textColor" name="PrimarySelection"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                    </subviews>
                                                                </stackView>
                                                            </subviews>
                                                            <constraints>
                                                                <constraint firstItem="iQc-qR-g2O" firstAttribute="width" secondItem="22i-7J-igH" secondAttribute="width" multiplier="0.4" id="4k4-QX-Gux"/>
                                                            </constraints>
                                                        </stackView>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="H8H-6t-1g4">
                                                            <rect key="frame" x="0.0" y="49.666666666666664" width="432" height="0.3333333333333357"/>
                                                            <color key="backgroundColor" name="PrimaryTextColor"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="0.5" id="4x1-E4-R9y"/>
                                                            </constraints>
                                                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                            <nil key="textColor"/>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                    </subviews>
                                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <constraints>
                                                        <constraint firstItem="22i-7J-igH" firstAttribute="leading" secondItem="ehY-Zd-6DK" secondAttribute="leading" constant="4" id="2eX-Ns-ima"/>
                                                        <constraint firstAttribute="bottom" secondItem="H8H-6t-1g4" secondAttribute="bottom" id="Djy-dp-4ML"/>
                                                        <constraint firstItem="22i-7J-igH" firstAttribute="top" secondItem="ehY-Zd-6DK" secondAttribute="top" id="JcM-5P-75V"/>
                                                        <constraint firstAttribute="bottom" secondItem="22i-7J-igH" secondAttribute="bottom" constant="6" id="QzZ-VY-lxd"/>
                                                        <constraint firstItem="H8H-6t-1g4" firstAttribute="leading" secondItem="ehY-Zd-6DK" secondAttribute="leading" id="fBi-Ay-PWs"/>
                                                        <constraint firstAttribute="trailing" secondItem="H8H-6t-1g4" secondAttribute="trailing" id="hlU-EU-zVP"/>
                                                        <constraint firstAttribute="trailing" secondItem="22i-7J-igH" secondAttribute="trailing" constant="4" id="vcT-W2-mSw"/>
                                                    </constraints>
                                                </view>
                                            </subviews>
                                            <constraints>
                                                <constraint firstAttribute="bottom" secondItem="ehY-Zd-6DK" secondAttribute="bottom" constant="4" id="0eW-To-OI4"/>
                                                <constraint firstItem="ehY-Zd-6DK" firstAttribute="leading" secondItem="g4P-8f-jTR" secondAttribute="leading" constant="4" id="DzL-bL-o44"/>
                                                <constraint firstAttribute="trailing" secondItem="ehY-Zd-6DK" secondAttribute="trailing" constant="4" id="c5l-Xs-3n1"/>
                                                <constraint firstItem="ehY-Zd-6DK" firstAttribute="top" secondItem="g4P-8f-jTR" secondAttribute="top" constant="4" id="owr-PW-REc"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <connections>
                                            <outlet property="imgWallet" destination="DtI-4j-a5k" id="rMY-Av-cEF"/>
                                            <outlet property="lblAmount" destination="BcX-J3-Hyg" id="32w-xE-HPh"/>
                                            <outlet property="lblDate" destination="e7m-jj-IA9" id="8TZ-dG-cro"/>
                                            <outlet property="lblStatus" destination="96B-xc-e72" id="JwM-5F-9IS"/>
                                            <outlet property="lblType" destination="VGg-MU-B8a" id="5l8-Kx-cQc"/>
                                            <outlet property="viewMain" destination="ehY-Zd-6DK" id="9Xc-1X-SSW"/>
                                            <outlet property="viewTypeBorder" destination="FsD-DN-Dcx" id="BxE-Xd-sVj"/>
                                        </connections>
                                    </tableViewCell>
                                </prototypes>
                            </tableView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="vzF-Y1-mbV">
                                <rect key="frame" x="4" y="377.33333333333326" width="432" height="458.66666666666674"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="XT7-qf-TzV">
                                        <rect key="frame" x="130.33333333333337" y="170.33333333333331" width="171.66666666666663" height="118"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_no_data" translatesAutoresizingMaskIntoConstraints="NO" id="A29-hQ-mtt">
                                                <rect key="frame" x="10.666666666666657" y="0.0" width="150" height="100"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="150" id="Zrv-Xd-oFs"/>
                                                    <constraint firstAttribute="height" constant="100" id="jCi-mq-0zT"/>
                                                </constraints>
                                            </imageView>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="No transactions available" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="N4X-i2-VTn">
                                                <rect key="frame" x="0.0" y="100" width="171.66666666666666" height="18"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                <color key="textColor" white="0.0" alpha="0.69999999999999996" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="XT7-qf-TzV" firstAttribute="centerX" secondItem="vzF-Y1-mbV" secondAttribute="centerX" id="QA2-IP-xGZ"/>
                                    <constraint firstItem="XT7-qf-TzV" firstAttribute="centerY" secondItem="vzF-Y1-mbV" secondAttribute="centerY" id="QRZ-Ra-RBV"/>
                                </constraints>
                            </view>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="5Ul-hH-Wdd">
                                <rect key="frame" x="181.66666666666666" y="840" width="76.999999999999972" height="44"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="44" id="8w7-eu-IHa"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="boldSystem" pointSize="15"/>
                                <state key="normal" title="View More">
                                    <color key="titleColor" red="0.015686274510000001" green="0.4941176471" blue="0.42745098040000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                </state>
                                <connections>
                                    <action selector="moreAction:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="ITv-eP-91U"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="hf8-Cl-5Lc">
                                <rect key="frame" x="158" y="278" width="124" height="38"/>
                                <fontDescription key="fontDescription" type="boldSystem" pointSize="15"/>
                                <state key="normal">
                                    <color key="titleColor" red="0.015686274510000001" green="0.4941176471" blue="0.42745098040000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                </state>
                                <connections>
                                    <action selector="addMoneyAction:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="8lf-fD-TJR"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="fvY-TT-Ubh">
                                <rect key="frame" x="8" y="132" width="44" height="44"/>
                                <color key="backgroundColor" name="Primary"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="44" id="9Kk-X2-XoU"/>
                                    <constraint firstAttribute="width" constant="44" id="upM-yO-kkm"/>
                                </constraints>
                                <inset key="imageEdgeInsets" minX="8" minY="8" maxX="8" maxY="8"/>
                                <state key="normal" image="ic_menu"/>
                                <connections>
                                    <action selector="menuButtonTapped:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="Mad-dt-4Jb"/>
                                </connections>
                            </button>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="HxF-3w-GA7">
                                <rect key="frame" x="0.0" y="124" width="440" height="764"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="LAx-zx-E3h">
                                        <rect key="frame" x="0.0" y="0.0" width="352" height="764"/>
                                        <subviews>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="jfI-pu-U6Z">
                                                <rect key="frame" x="12" y="8" width="328" height="99.333333333333329"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="2" translatesAutoresizingMaskIntoConstraints="NO" id="2UQ-SH-jbA">
                                                        <rect key="frame" x="12" y="12" width="304" height="75.333333333333329"/>
                                                        <subviews>
                                                            <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="aYF-4h-sII">
                                                                <rect key="frame" x="0.0" y="15.666666666666657" width="278" height="44"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="J" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Rro-pF-9A0">
                                                                        <rect key="frame" x="0.0" y="0.0" width="44" height="44"/>
                                                                        <color key="backgroundColor" name="Primary"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="width" constant="44" id="lwG-lb-LDb"/>
                                                                            <constraint firstAttribute="height" constant="44" id="zGq-G7-rcz"/>
                                                                        </constraints>
                                                                        <fontDescription key="fontDescription" type="boldSystem" pointSize="22"/>
                                                                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="rsC-aY-owI">
                                                                        <rect key="frame" x="56" y="1.3333333333333428" width="222" height="41.333333333333336"/>
                                                                        <subviews>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Jobin Macwan" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="7Mg-9I-ewK">
                                                                                <rect key="frame" x="0.0" y="0.0" width="222" height="20.333333333333332"/>
                                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                                <nil key="textColor"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Avail Balance : ₹ 25000" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="fTv-rz-izR">
                                                                                <rect key="frame" x="0.0" y="24.333333333333343" width="222" height="17"/>
                                                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                                <color key="textColor" red="0.015686274510000001" green="0.4941176471" blue="0.42745098040000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                        </subviews>
                                                                    </stackView>
                                                                </subviews>
                                                            </stackView>
                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_logout" translatesAutoresizingMaskIntoConstraints="NO" id="HNz-Kh-dUI">
                                                                <rect key="frame" x="280" y="25.666666666666657" width="24" height="24"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="24" id="MCD-1a-EMb"/>
                                                                    <constraint firstAttribute="height" constant="24" id="Pge-WH-TgN"/>
                                                                </constraints>
                                                            </imageView>
                                                        </subviews>
                                                    </stackView>
                                                </subviews>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstItem="2UQ-SH-jbA" firstAttribute="top" secondItem="jfI-pu-U6Z" secondAttribute="top" constant="12" id="Ct8-2W-Kxk"/>
                                                    <constraint firstAttribute="trailing" secondItem="2UQ-SH-jbA" secondAttribute="trailing" constant="12" id="HTm-YF-1fl"/>
                                                    <constraint firstItem="2UQ-SH-jbA" firstAttribute="leading" secondItem="jfI-pu-U6Z" secondAttribute="leading" constant="12" id="YM6-uL-Kbu"/>
                                                    <constraint firstAttribute="bottom" secondItem="2UQ-SH-jbA" secondAttribute="bottom" constant="12" id="aYs-mo-z13"/>
                                                </constraints>
                                            </view>
                                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="-1" estimatedSectionHeaderHeight="-1" sectionFooterHeight="-1" estimatedSectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="ahY-Gx-Rk1">
                                                <rect key="frame" x="12" y="119.33333333333334" width="328" height="128"/>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="128" id="zaT-Sj-wyd"/>
                                                </constraints>
                                                <prototypes>
                                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="none" indentationWidth="10" reuseIdentifier="cell" rowHeight="50" id="vW1-ER-q6v" customClass="MenuCell" customModule="NXC_EV_Solutions" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="50" width="328" height="50"/>
                                                        <autoresizingMask key="autoresizingMask"/>
                                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="vW1-ER-q6v" id="V8J-Hw-BFw">
                                                            <rect key="frame" x="0.0" y="0.0" width="328" height="50"/>
                                                            <autoresizingMask key="autoresizingMask"/>
                                                            <subviews>
                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="3LR-bq-gWN">
                                                                    <rect key="frame" x="0.0" y="4" width="328" height="42"/>
                                                                    <subviews>
                                                                        <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="T7C-Tp-gHd">
                                                                            <rect key="frame" x="12" y="2" width="310" height="38"/>
                                                                            <subviews>
                                                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="f0e-cq-QWg">
                                                                                    <rect key="frame" x="0.0" y="10" width="18" height="18"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="height" constant="18" id="Gw9-In-xxD"/>
                                                                                        <constraint firstAttribute="width" constant="18" id="qQL-Fc-yVx"/>
                                                                                    </constraints>
                                                                                </imageView>
                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Profile" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="843-26-Vmj">
                                                                                    <rect key="frame" x="34" y="0.0" width="276" height="38"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="height" constant="44" id="gzz-8J-x0M"/>
                                                                                    </constraints>
                                                                                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="14"/>
                                                                                    <nil key="textColor"/>
                                                                                    <nil key="highlightedColor"/>
                                                                                </label>
                                                                            </subviews>
                                                                        </stackView>
                                                                    </subviews>
                                                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="trailing" secondItem="T7C-Tp-gHd" secondAttribute="trailing" constant="6" id="GXB-vJ-XmE"/>
                                                                        <constraint firstItem="T7C-Tp-gHd" firstAttribute="top" secondItem="3LR-bq-gWN" secondAttribute="top" constant="2" id="TL4-PI-L7z"/>
                                                                        <constraint firstAttribute="bottom" secondItem="T7C-Tp-gHd" secondAttribute="bottom" constant="2" id="YO5-me-01w"/>
                                                                        <constraint firstItem="T7C-Tp-gHd" firstAttribute="leading" secondItem="3LR-bq-gWN" secondAttribute="leading" constant="12" id="uSg-FW-rUg"/>
                                                                    </constraints>
                                                                </view>
                                                            </subviews>
                                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstAttribute="bottom" secondItem="3LR-bq-gWN" secondAttribute="bottom" constant="4" id="BFC-9x-Mvb"/>
                                                                <constraint firstItem="3LR-bq-gWN" firstAttribute="top" secondItem="V8J-Hw-BFw" secondAttribute="top" constant="4" id="Ce4-hx-gjR"/>
                                                                <constraint firstAttribute="trailing" secondItem="3LR-bq-gWN" secondAttribute="trailing" id="ZE9-gr-lTS"/>
                                                                <constraint firstItem="3LR-bq-gWN" firstAttribute="leading" secondItem="V8J-Hw-BFw" secondAttribute="leading" id="oy8-vX-oXX"/>
                                                            </constraints>
                                                        </tableViewCellContentView>
                                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <connections>
                                                            <outlet property="imgMenu" destination="f0e-cq-QWg" id="pAj-Rk-Th4"/>
                                                            <outlet property="lblMenu" destination="843-26-Vmj" id="8sQ-xv-0v4"/>
                                                            <outlet property="viewMain" destination="3LR-bq-gWN" id="UfB-Bh-0By"/>
                                                        </connections>
                                                    </tableViewCell>
                                                </prototypes>
                                            </tableView>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="lJ0-Zt-in8">
                                                <rect key="frame" x="294" y="35.666666666666657" width="44" height="44"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="44" id="9es-K7-gs4"/>
                                                    <constraint firstAttribute="width" secondItem="lJ0-Zt-in8" secondAttribute="height" multiplier="1:1" id="AhE-v8-Aae"/>
                                                </constraints>
                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                <connections>
                                                    <action selector="logoutTapped:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="sCJ-q7-fq8"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="jfI-pu-U6Z" firstAttribute="height" secondItem="LAx-zx-E3h" secondAttribute="height" multiplier="0.13" id="100-co-kJ2"/>
                                            <constraint firstAttribute="trailing" secondItem="ahY-Gx-Rk1" secondAttribute="trailing" constant="12" id="5CJ-7y-fqI"/>
                                            <constraint firstAttribute="trailing" secondItem="jfI-pu-U6Z" secondAttribute="trailing" constant="12" id="EaM-xt-wMx"/>
                                            <constraint firstItem="ahY-Gx-Rk1" firstAttribute="leading" secondItem="LAx-zx-E3h" secondAttribute="leading" constant="12" id="S8c-Kb-ofM"/>
                                            <constraint firstItem="jfI-pu-U6Z" firstAttribute="top" secondItem="LAx-zx-E3h" secondAttribute="top" constant="8" id="kLi-Es-XFK"/>
                                            <constraint firstItem="lJ0-Zt-in8" firstAttribute="centerY" secondItem="HNz-Kh-dUI" secondAttribute="centerY" id="m3u-t2-9Bs"/>
                                            <constraint firstItem="jfI-pu-U6Z" firstAttribute="leading" secondItem="LAx-zx-E3h" secondAttribute="leading" constant="12" id="n6Y-ba-KjP"/>
                                            <constraint firstItem="ahY-Gx-Rk1" firstAttribute="top" secondItem="jfI-pu-U6Z" secondAttribute="bottom" constant="12" id="tOF-fx-sPg"/>
                                            <constraint firstItem="lJ0-Zt-in8" firstAttribute="centerX" secondItem="HNz-Kh-dUI" secondAttribute="centerX" id="yKI-UQ-ax4"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.75" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="LAx-zx-E3h" firstAttribute="top" secondItem="HxF-3w-GA7" secondAttribute="top" id="3eF-lf-5Yy"/>
                                    <constraint firstItem="LAx-zx-E3h" firstAttribute="leading" secondItem="HxF-3w-GA7" secondAttribute="leading" id="BSY-R4-yEJ"/>
                                    <constraint firstItem="LAx-zx-E3h" firstAttribute="width" secondItem="HxF-3w-GA7" secondAttribute="width" multiplier="0.8" id="H5Y-bN-j4E"/>
                                    <constraint firstAttribute="bottom" secondItem="LAx-zx-E3h" secondAttribute="bottom" id="bud-6W-lGL"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="vDu-zF-Fre"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="vDu-zF-Fre" firstAttribute="trailing" secondItem="8EO-JG-DoG" secondAttribute="trailing" id="4oF-F5-6uZ"/>
                            <constraint firstItem="vDu-zF-Fre" firstAttribute="trailing" secondItem="U5B-Ke-gZD" secondAttribute="trailing" id="BNY-0y-6PZ"/>
                            <constraint firstItem="DsX-ZJ-pcf" firstAttribute="top" secondItem="OzM-K4-J4K" secondAttribute="bottom" constant="16" id="FLz-me-pBI"/>
                            <constraint firstItem="hf8-Cl-5Lc" firstAttribute="centerY" secondItem="ClG-1i-jeO" secondAttribute="centerY" id="FMe-rM-8Px"/>
                            <constraint firstItem="8EO-JG-DoG" firstAttribute="top" secondItem="vDu-zF-Fre" secondAttribute="top" id="Hpa-GV-I2Z"/>
                            <constraint firstItem="hf8-Cl-5Lc" firstAttribute="centerX" secondItem="ClG-1i-jeO" secondAttribute="centerX" id="JgL-5r-y6r"/>
                            <constraint firstItem="5Ul-hH-Wdd" firstAttribute="centerX" secondItem="5EZ-qb-Rvc" secondAttribute="centerX" id="NRs-cF-E9H"/>
                            <constraint firstItem="HxF-3w-GA7" firstAttribute="top" secondItem="vDu-zF-Fre" secondAttribute="top" id="PEF-GM-Fob"/>
                            <constraint firstItem="5Ul-hH-Wdd" firstAttribute="top" secondItem="U5B-Ke-gZD" secondAttribute="bottom" constant="4" id="S51-v3-OgY"/>
                            <constraint firstItem="U5B-Ke-gZD" firstAttribute="top" secondItem="DsX-ZJ-pcf" secondAttribute="bottom" constant="8" id="TzY-MP-rLm"/>
                            <constraint firstItem="vzF-Y1-mbV" firstAttribute="leading" secondItem="vDu-zF-Fre" secondAttribute="leading" constant="4" id="VTU-VP-jZe"/>
                            <constraint firstItem="hf8-Cl-5Lc" firstAttribute="width" secondItem="ClG-1i-jeO" secondAttribute="width" id="W5Y-CF-wqT"/>
                            <constraint firstItem="HxF-3w-GA7" firstAttribute="trailing" secondItem="vDu-zF-Fre" secondAttribute="trailing" id="bBd-ws-hBB"/>
                            <constraint firstItem="vzF-Y1-mbV" firstAttribute="top" secondItem="DsX-ZJ-pcf" secondAttribute="bottom" constant="8" id="c9M-EQ-Id9"/>
                            <constraint firstItem="fvY-TT-Ubh" firstAttribute="top" secondItem="vDu-zF-Fre" secondAttribute="top" constant="8" id="cTm-kD-30L"/>
                            <constraint firstItem="HxF-3w-GA7" firstAttribute="leading" secondItem="vDu-zF-Fre" secondAttribute="leading" id="d10-71-fpq"/>
                            <constraint firstItem="HxF-3w-GA7" firstAttribute="bottom" secondItem="vDu-zF-Fre" secondAttribute="bottom" id="eag-T7-rgf"/>
                            <constraint firstItem="hf8-Cl-5Lc" firstAttribute="height" secondItem="ClG-1i-jeO" secondAttribute="height" id="f5D-3t-Ets"/>
                            <constraint firstItem="5Ul-hH-Wdd" firstAttribute="top" secondItem="vzF-Y1-mbV" secondAttribute="bottom" constant="4" id="hiX-1d-jzR"/>
                            <constraint firstItem="OzM-K4-J4K" firstAttribute="top" secondItem="8EO-JG-DoG" secondAttribute="bottom" constant="16" id="kEq-G9-XWS"/>
                            <constraint firstItem="OzM-K4-J4K" firstAttribute="centerX" secondItem="5EZ-qb-Rvc" secondAttribute="centerX" id="ndO-LN-cfk"/>
                            <constraint firstItem="OzM-K4-J4K" firstAttribute="width" secondItem="vDu-zF-Fre" secondAttribute="width" multiplier="0.85" id="qoJ-UI-CHw"/>
                            <constraint firstItem="8EO-JG-DoG" firstAttribute="leading" secondItem="vDu-zF-Fre" secondAttribute="leading" id="uo6-sF-5Y8"/>
                            <constraint firstItem="DsX-ZJ-pcf" firstAttribute="centerX" secondItem="5EZ-qb-Rvc" secondAttribute="centerX" id="vke-Fy-lVa"/>
                            <constraint firstItem="vDu-zF-Fre" firstAttribute="bottom" secondItem="5Ul-hH-Wdd" secondAttribute="bottom" constant="4" id="wZE-oI-5sI"/>
                            <constraint firstItem="U5B-Ke-gZD" firstAttribute="leading" secondItem="vDu-zF-Fre" secondAttribute="leading" id="wc9-Gx-s6Z"/>
                            <constraint firstItem="fvY-TT-Ubh" firstAttribute="leading" secondItem="vDu-zF-Fre" secondAttribute="leading" constant="8" id="wiw-Ta-SyM"/>
                            <constraint firstItem="vDu-zF-Fre" firstAttribute="trailing" secondItem="vzF-Y1-mbV" secondAttribute="trailing" constant="4" id="y8q-vn-2Ig"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="btnMore" destination="5Ul-hH-Wdd" id="OJK-mM-SCo"/>
                        <outlet property="lblAddMoney" destination="6Pm-W2-Fpp" id="7vf-MW-ZRa"/>
                        <outlet property="lblAvailableBalance" destination="JeQ-kA-65Y" id="Mbw-dS-yVq"/>
                        <outlet property="lblBalance" destination="fTv-rz-izR" id="87P-jc-hQd"/>
                        <outlet property="lblInitial" destination="Rro-pF-9A0" id="gqb-vK-unx"/>
                        <outlet property="lblNoData" destination="N4X-i2-VTn" id="sKC-Bh-nzI"/>
                        <outlet property="lblSeeActivity" destination="DsX-ZJ-pcf" id="h1R-h3-ynF"/>
                        <outlet property="lblTitle" destination="YbI-Fi-fQS" id="oND-yp-xK5"/>
                        <outlet property="lblUserName" destination="7Mg-9I-ewK" id="W7j-gT-XjP"/>
                        <outlet property="lblWalletAmt" destination="BcS-d5-eY2" id="Ntr-VS-kpl"/>
                        <outlet property="menuButton" destination="fvY-TT-Ubh" id="Wcx-se-gcZ"/>
                        <outlet property="tableHeight" destination="zaT-Sj-wyd" id="GdR-sD-9nc"/>
                        <outlet property="tableList" destination="U5B-Ke-gZD" id="TAR-Gf-ulc"/>
                        <outlet property="tableMenu" destination="ahY-Gx-Rk1" id="LpK-9t-CtY"/>
                        <outlet property="viewAddMoney" destination="ClG-1i-jeO" id="Rto-sF-DRv"/>
                        <outlet property="viewBgMenu" destination="HxF-3w-GA7" id="ymd-yh-HBK"/>
                        <outlet property="viewDetails" destination="OzM-K4-J4K" id="PaZ-bS-b00"/>
                        <outlet property="viewMainMenu" destination="LAx-zx-E3h" id="aAT-Tr-yHG"/>
                        <outlet property="viewNoData" destination="vzF-Y1-mbV" id="P7O-Yu-9FV"/>
                        <outlet property="viewProfile" destination="jfI-pu-U6Z" id="iMA-Z6-g6k"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Ief-a0-LHa" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="132" y="73.313343328335833"/>
        </scene>
        <!--HistoryVC-->
        <scene sceneID="twe-dS-Nr6">
            <objects>
                <viewController storyboardIdentifier="HistoryVC" id="wQh-C4-SKZ" customClass="HistoryVC" customModule="NXC_EV_Solutions" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Q9V-G3-m5e">
                        <rect key="frame" x="0.0" y="0.0" width="440" height="956"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="vpq-Rf-F1m">
                                <rect key="frame" x="0.0" y="124" width="440" height="44"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="QKE-gX-KMZ">
                                        <rect key="frame" x="12" y="5" width="34" height="34"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="34" id="aW3-uc-g81"/>
                                            <constraint firstAttribute="width" constant="34" id="elK-6U-hiU"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="8" minY="6" maxX="8" maxY="6"/>
                                        <state key="normal" image="ic_back_arrow"/>
                                        <connections>
                                            <action selector="backAction:" destination="wQh-C4-SKZ" eventType="touchUpInside" id="ACs-sP-P6h"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="History" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="0sk-uq-DZO">
                                        <rect key="frame" x="191" y="11.999999999999998" width="58" height="20.333333333333329"/>
                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                        <color key="textColor" red="0.070588235289999995" green="0.070588235289999995" blue="0.070588235289999995" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="nYj-yt-9W1">
                                        <rect key="frame" x="394" y="5" width="34" height="34"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="34" id="Lzd-Gj-sZh"/>
                                            <constraint firstAttribute="height" constant="34" id="gBp-u3-xQJ"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="8" minY="8" maxX="8" maxY="8"/>
                                        <state key="normal" image="ic_filter"/>
                                        <connections>
                                            <action selector="filterWalletAction:" destination="wQh-C4-SKZ" eventType="touchUpInside" id="ylh-gf-Upg"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="QKE-gX-KMZ" firstAttribute="centerY" secondItem="vpq-Rf-F1m" secondAttribute="centerY" id="4zx-9t-UvT"/>
                                    <constraint firstAttribute="height" constant="44" id="CrA-1A-9x5"/>
                                    <constraint firstItem="0sk-uq-DZO" firstAttribute="centerX" secondItem="vpq-Rf-F1m" secondAttribute="centerX" id="DKg-2r-KQV"/>
                                    <constraint firstItem="0sk-uq-DZO" firstAttribute="centerY" secondItem="vpq-Rf-F1m" secondAttribute="centerY" id="Fto-m3-GKn"/>
                                    <constraint firstItem="nYj-yt-9W1" firstAttribute="centerY" secondItem="vpq-Rf-F1m" secondAttribute="centerY" id="Lmd-mK-hla"/>
                                    <constraint firstAttribute="trailing" secondItem="nYj-yt-9W1" secondAttribute="trailing" constant="12" id="Ver-ZL-CZs"/>
                                    <constraint firstItem="QKE-gX-KMZ" firstAttribute="leading" secondItem="vpq-Rf-F1m" secondAttribute="leading" constant="12" id="mIj-9m-nKi"/>
                                </constraints>
                            </view>
                            <stackView opaque="NO" contentMode="scaleToFill" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="hOW-de-5D2">
                                <rect key="frame" x="12" y="176" width="294" height="30"/>
                                <subviews>
                                    <button opaque="NO" tag="101" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="xvo-R0-dMk">
                                        <rect key="frame" x="0.0" y="0.0" width="30" height="30"/>
                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="15"/>
                                        <state key="normal" title="All">
                                            <color key="titleColor" name="PrimaryColor"/>
                                        </state>
                                        <connections>
                                            <action selector="filterAction:" destination="wQh-C4-SKZ" eventType="touchUpInside" id="sI1-K2-sqT"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" tag="102" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="LuA-R5-jHH">
                                        <rect key="frame" x="40" y="0.0" width="128" height="30"/>
                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="15"/>
                                        <state key="normal" title="Paid For Charging">
                                            <color key="titleColor" name="PrimarySelection"/>
                                        </state>
                                        <connections>
                                            <action selector="filterAction:" destination="wQh-C4-SKZ" eventType="touchUpInside" id="urE-wg-wGT"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" tag="103" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="qUj-9t-TSB">
                                        <rect key="frame" x="178" y="0.0" width="116" height="30"/>
                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="15"/>
                                        <state key="normal" title="Added To Wallet">
                                            <color key="titleColor" name="PrimarySelection"/>
                                        </state>
                                        <connections>
                                            <action selector="filterAction:" destination="wQh-C4-SKZ" eventType="touchUpInside" id="LTb-yX-rWe"/>
                                        </connections>
                                    </button>
                                </subviews>
                            </stackView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="dh9-GB-E8j">
                                <rect key="frame" x="4" y="216" width="432" height="594"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="bBr-Sc-da2">
                                        <rect key="frame" x="130.33333333333337" y="238" width="171.66666666666663" height="118"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_no_data" translatesAutoresizingMaskIntoConstraints="NO" id="snB-sn-pG6">
                                                <rect key="frame" x="10.666666666666657" y="0.0" width="150" height="100"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="150" id="BAh-93-Wps"/>
                                                    <constraint firstAttribute="height" constant="100" id="JPy-Kh-NcV"/>
                                                </constraints>
                                            </imageView>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="No transactions available" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="1aa-JM-4yv">
                                                <rect key="frame" x="0.0" y="100" width="171.66666666666666" height="18"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                <color key="textColor" white="0.0" alpha="0.69999999999999996" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="bBr-Sc-da2" firstAttribute="centerY" secondItem="dh9-GB-E8j" secondAttribute="centerY" id="ROT-ps-fUx"/>
                                    <constraint firstItem="bBr-Sc-da2" firstAttribute="centerX" secondItem="dh9-GB-E8j" secondAttribute="centerX" id="Xhv-Dc-guc"/>
                                </constraints>
                            </view>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="insetGrouped" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="18" sectionFooterHeight="18" translatesAutoresizingMaskIntoConstraints="NO" id="yfZ-5N-vRR">
                                <rect key="frame" x="0.0" y="216" width="440" height="594"/>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <prototypes>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="none" indentationWidth="10" reuseIdentifier="cell" id="x2D-I3-Eqs" customClass="WalletCell" customModule="NXC_EV_Solutions" customModuleProvider="target">
                                        <rect key="frame" x="20" y="55.333332061767578" width="400" height="58"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="x2D-I3-Eqs" id="ftF-px-47a">
                                            <rect key="frame" x="0.0" y="0.0" width="400" height="58"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="CUo-eN-LA4">
                                                    <rect key="frame" x="2" y="4" width="396" height="50"/>
                                                    <subviews>
                                                        <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="VEu-1f-97t">
                                                            <rect key="frame" x="2" y="0.0" width="392" height="44"/>
                                                            <subviews>
                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="FrX-s4-iD2">
                                                                    <rect key="frame" x="0.0" y="3" width="38" height="38"/>
                                                                    <subviews>
                                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_car_wallet" translatesAutoresizingMaskIntoConstraints="NO" id="g5p-YJ-ILz">
                                                                            <rect key="frame" x="8" y="8" width="22" height="22"/>
                                                                            <constraints>
                                                                                <constraint firstAttribute="width" constant="22" id="dh5-5z-fmI"/>
                                                                                <constraint firstAttribute="height" constant="22" id="pPu-OI-jsT"/>
                                                                            </constraints>
                                                                        </imageView>
                                                                    </subviews>
                                                                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="width" constant="38" id="box-5y-b5U"/>
                                                                        <constraint firstAttribute="height" constant="38" id="eZt-1w-SmS"/>
                                                                        <constraint firstItem="g5p-YJ-ILz" firstAttribute="centerY" secondItem="FrX-s4-iD2" secondAttribute="centerY" id="ffW-Fe-j8u"/>
                                                                        <constraint firstItem="g5p-YJ-ILz" firstAttribute="centerX" secondItem="FrX-s4-iD2" secondAttribute="centerX" id="nmd-6U-dUI"/>
                                                                    </constraints>
                                                                </view>
                                                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="5CT-ic-sRP">
                                                                    <rect key="frame" x="46.000000000000014" y="0.0" width="228.33333333333337" height="44"/>
                                                                    <subviews>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Paid to EVC Charge" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ldx-D9-Lzl">
                                                                            <rect key="frame" x="0.0" y="0.0" width="228.33333333333334" height="24"/>
                                                                            <constraints>
                                                                                <constraint firstAttribute="height" constant="24" id="8wY-lu-a9f"/>
                                                                            </constraints>
                                                                            <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                            <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Payment Sent" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="LJx-nK-fCv">
                                                                            <rect key="frame" x="0.0" y="24" width="228.33333333333334" height="20"/>
                                                                            <constraints>
                                                                                <constraint firstAttribute="height" constant="20" id="WUq-cQ-LtS"/>
                                                                            </constraints>
                                                                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                            <color key="textColor" name="PrimarySelection"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                    </subviews>
                                                                </stackView>
                                                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="91W-fA-Ozr">
                                                                    <rect key="frame" x="282.33333333333331" y="0.0" width="109.66666666666669" height="44"/>
                                                                    <subviews>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="- ₹ 390" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="lDu-uU-tcV">
                                                                            <rect key="frame" x="0.0" y="0.0" width="109.66666666666667" height="24"/>
                                                                            <constraints>
                                                                                <constraint firstAttribute="height" constant="24" id="bWA-ME-LvD"/>
                                                                            </constraints>
                                                                            <fontDescription key="fontDescription" type="boldSystem" pointSize="15"/>
                                                                            <color key="textColor" name="WalletDeductText"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="15 Aug " textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="d1A-lo-xbb">
                                                                            <rect key="frame" x="0.0" y="24" width="109.66666666666667" height="20"/>
                                                                            <constraints>
                                                                                <constraint firstAttribute="height" constant="20" id="SKd-iX-iU9"/>
                                                                            </constraints>
                                                                            <fontDescription key="fontDescription" type="system" pointSize="13"/>
                                                                            <color key="textColor" name="PrimarySelection"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                    </subviews>
                                                                </stackView>
                                                            </subviews>
                                                            <constraints>
                                                                <constraint firstItem="91W-fA-Ozr" firstAttribute="width" secondItem="VEu-1f-97t" secondAttribute="width" multiplier="0.28" id="Br4-1F-05k"/>
                                                            </constraints>
                                                        </stackView>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="iFD-0U-zvm">
                                                            <rect key="frame" x="10" y="49" width="376" height="1"/>
                                                            <color key="backgroundColor" red="0.93725490196078431" green="0.94901960784313721" blue="0.96862745098039216" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="1" id="s2O-Yl-g6y"/>
                                                            </constraints>
                                                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                            <nil key="textColor"/>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                    </subviews>
                                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <constraints>
                                                        <constraint firstItem="iFD-0U-zvm" firstAttribute="centerX" secondItem="CUo-eN-LA4" secondAttribute="centerX" id="GCD-c4-VcR"/>
                                                        <constraint firstAttribute="bottom" secondItem="VEu-1f-97t" secondAttribute="bottom" constant="6" id="GoJ-5w-zKt"/>
                                                        <constraint firstItem="iFD-0U-zvm" firstAttribute="width" secondItem="CUo-eN-LA4" secondAttribute="width" multiplier="0.95" id="SuK-Ti-Nhd"/>
                                                        <constraint firstItem="VEu-1f-97t" firstAttribute="top" secondItem="CUo-eN-LA4" secondAttribute="top" id="aJ3-Wp-vLz"/>
                                                        <constraint firstAttribute="trailing" secondItem="VEu-1f-97t" secondAttribute="trailing" constant="2" id="c6C-aD-lJt"/>
                                                        <constraint firstItem="VEu-1f-97t" firstAttribute="leading" secondItem="CUo-eN-LA4" secondAttribute="leading" constant="2" id="gKm-Yn-kAD"/>
                                                        <constraint firstAttribute="bottom" secondItem="iFD-0U-zvm" secondAttribute="bottom" id="iG2-Mj-qFg"/>
                                                    </constraints>
                                                </view>
                                            </subviews>
                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstAttribute="trailing" secondItem="CUo-eN-LA4" secondAttribute="trailing" constant="2" id="83f-bu-bYJ"/>
                                                <constraint firstItem="CUo-eN-LA4" firstAttribute="top" secondItem="ftF-px-47a" secondAttribute="top" constant="4" id="laJ-JO-Nt5"/>
                                                <constraint firstAttribute="bottom" secondItem="CUo-eN-LA4" secondAttribute="bottom" constant="4" id="pI6-3d-qeJ"/>
                                                <constraint firstItem="CUo-eN-LA4" firstAttribute="leading" secondItem="ftF-px-47a" secondAttribute="leading" constant="2" id="w9c-Wm-y62"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <connections>
                                            <outlet property="imgWallet" destination="g5p-YJ-ILz" id="Wej-gm-qRz"/>
                                            <outlet property="lblAmount" destination="lDu-uU-tcV" id="Hp8-69-xwq"/>
                                            <outlet property="lblDate" destination="d1A-lo-xbb" id="3de-H3-t6J"/>
                                            <outlet property="lblStatus" destination="LJx-nK-fCv" id="hNx-km-wxA"/>
                                            <outlet property="lblType" destination="ldx-D9-Lzl" id="hSG-sL-P0E"/>
                                            <outlet property="viewMain" destination="CUo-eN-LA4" id="NAK-kf-WAA"/>
                                            <outlet property="viewTypeBorder" destination="FrX-s4-iD2" id="qvj-83-Yr8"/>
                                        </connections>
                                    </tableViewCell>
                                </prototypes>
                            </tableView>
                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="20" translatesAutoresizingMaskIntoConstraints="NO" id="ww7-z7-oaA">
                                <rect key="frame" x="8" y="818" width="424" height="62"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="vcO-nT-Yiq">
                                        <rect key="frame" x="0.0" y="0.0" width="202" height="62"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="cY0-Nr-pJi">
                                                <rect key="frame" x="8" y="8" width="186" height="46"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Total Debited" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="be0-Zw-Ho8">
                                                        <rect key="frame" x="0.0" y="0.0" width="186" height="20"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="20" id="FnC-2d-sgO"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="₹ 2500" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="XpU-u1-lQY">
                                                        <rect key="frame" x="0.0" y="20" width="186" height="26"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="26" id="tbF-uo-NkE"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="boldSystem" pointSize="20"/>
                                                        <color key="textColor" name="WalletDeductText"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="cY0-Nr-pJi" firstAttribute="top" secondItem="vcO-nT-Yiq" secondAttribute="top" constant="8" id="Do0-Ra-RbG"/>
                                            <constraint firstItem="cY0-Nr-pJi" firstAttribute="leading" secondItem="vcO-nT-Yiq" secondAttribute="leading" constant="8" id="Hz9-z5-fuX"/>
                                            <constraint firstAttribute="trailing" secondItem="cY0-Nr-pJi" secondAttribute="trailing" constant="8" id="kdf-sU-EMJ"/>
                                            <constraint firstAttribute="bottom" secondItem="cY0-Nr-pJi" secondAttribute="bottom" constant="8" id="ko2-6T-cdG"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Xoi-z1-ddA">
                                        <rect key="frame" x="222" y="0.0" width="202" height="62"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="f9I-me-vzA">
                                                <rect key="frame" x="8" y="8" width="186" height="46"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Total Credited" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="awt-cf-V10">
                                                        <rect key="frame" x="0.0" y="0.0" width="186" height="20"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="20" id="lVz-T1-SQ8"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="₹ 5000" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="2uc-Nm-VMg">
                                                        <rect key="frame" x="0.0" y="20" width="186" height="26"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="26" id="na8-zg-eCk"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="boldSystem" pointSize="20"/>
                                                        <color key="textColor" name="WalletAddText"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="f9I-me-vzA" firstAttribute="leading" secondItem="Xoi-z1-ddA" secondAttribute="leading" constant="8" id="7qP-B4-LHx"/>
                                            <constraint firstAttribute="trailing" secondItem="f9I-me-vzA" secondAttribute="trailing" constant="8" id="U0G-0J-gLL"/>
                                            <constraint firstAttribute="bottom" secondItem="f9I-me-vzA" secondAttribute="bottom" constant="8" id="ZtZ-fq-t9E"/>
                                            <constraint firstItem="f9I-me-vzA" firstAttribute="top" secondItem="Xoi-z1-ddA" secondAttribute="top" constant="8" id="wph-62-nQq"/>
                                        </constraints>
                                    </view>
                                </subviews>
                            </stackView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="gwa-Sz-EBi">
                                <rect key="frame" x="0.0" y="124" width="440" height="764"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="0BP-2b-1hq">
                                        <rect key="frame" x="0.0" y="468" width="440" height="296"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Filter By" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="UmC-TZ-4sX">
                                                <rect key="frame" x="12" y="8" width="56" height="34"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="34" id="MkN-2p-HMH"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="W2Z-uD-O7Q">
                                                <rect key="frame" x="402" y="8" width="34" height="34"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="34" id="Tev-Y3-wIg"/>
                                                    <constraint firstAttribute="height" constant="34" id="rKR-8J-kCM"/>
                                                </constraints>
                                                <inset key="imageEdgeInsets" minX="10" minY="10" maxX="10" maxY="10"/>
                                                <state key="normal" image="ic_cancel"/>
                                                <connections>
                                                    <action selector="closeFilterAction:" destination="wQh-C4-SKZ" eventType="touchUpInside" id="LuN-3C-fIa"/>
                                                </connections>
                                            </button>
                                            <label opaque="NO" userInteractionEnabled="NO" alpha="0.0" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="7ap-MN-qnr">
                                                <rect key="frame" x="22" y="50" width="396" height="1"/>
                                                <color key="backgroundColor" name="PrimaryText"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="1" id="9eo-Dm-D62"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Select Vehicle" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="NGH-YC-7Ic">
                                                <rect key="frame" x="12" y="59" width="91.333333333333329" height="20"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="20" id="HGd-HP-QEM"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                <color key="textColor" name="Primary"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="KYP-UY-Yvb">
                                                <rect key="frame" x="16" y="87" width="408" height="44"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="CfY-Kt-Beb">
                                                        <rect key="frame" x="8" y="4" width="373" height="36"/>
                                                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                        <nil key="textColor"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_dropdown" translatesAutoresizingMaskIntoConstraints="NO" id="Zq1-Xd-REn">
                                                        <rect key="frame" x="385" y="14.666666666666629" width="15" height="15"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="15" id="ISf-02-FyW"/>
                                                            <constraint firstAttribute="width" constant="15" id="zVu-TC-J6K"/>
                                                        </constraints>
                                                    </imageView>
                                                </subviews>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstItem="Zq1-Xd-REn" firstAttribute="leading" secondItem="CfY-Kt-Beb" secondAttribute="trailing" constant="4" id="5FC-ta-tEK"/>
                                                    <constraint firstItem="CfY-Kt-Beb" firstAttribute="leading" secondItem="KYP-UY-Yvb" secondAttribute="leading" constant="8" id="AVe-hP-mF4"/>
                                                    <constraint firstAttribute="trailing" secondItem="Zq1-Xd-REn" secondAttribute="trailing" constant="8" id="DQW-vy-oks"/>
                                                    <constraint firstItem="CfY-Kt-Beb" firstAttribute="top" secondItem="KYP-UY-Yvb" secondAttribute="top" constant="4" id="L5f-cK-k6x"/>
                                                    <constraint firstAttribute="height" constant="44" id="Z5M-Ye-jNn"/>
                                                    <constraint firstItem="Zq1-Xd-REn" firstAttribute="centerY" secondItem="KYP-UY-Yvb" secondAttribute="centerY" id="dSx-UY-Frx"/>
                                                    <constraint firstAttribute="bottom" secondItem="CfY-Kt-Beb" secondAttribute="bottom" constant="4" id="mbF-fX-I5K"/>
                                                </constraints>
                                            </view>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Dt4-cc-UPU">
                                                <rect key="frame" x="22" y="139" width="396" height="1"/>
                                                <color key="backgroundColor" name="PrimaryText"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="1" id="hG5-7p-Vad"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Select Date" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="mQm-ZH-QWY">
                                                <rect key="frame" x="12" y="148" width="75" height="20"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="20" id="qMD-DO-Xra"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                <color key="textColor" name="Primary"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <stackView opaque="NO" contentMode="scaleToFill" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="HRP-7N-PLY">
                                                <rect key="frame" x="16" y="176" width="408" height="44"/>
                                                <subviews>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="njV-Bf-ia1">
                                                        <rect key="frame" x="0.0" y="0.0" width="163.33333333333334" height="44"/>
                                                        <subviews>
                                                            <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="8Cy-gH-TIQ">
                                                                <rect key="frame" x="8" y="4" width="147.33333333333334" height="36"/>
                                                                <subviews>
                                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_calendar" translatesAutoresizingMaskIntoConstraints="NO" id="8mB-NO-egB">
                                                                        <rect key="frame" x="0.0" y="10.666666666666629" width="15" height="15"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="15" id="DJ1-7F-3ei"/>
                                                                            <constraint firstAttribute="width" constant="15" id="qC6-Ue-awv"/>
                                                                        </constraints>
                                                                    </imageView>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="HaS-Cw-PWQ">
                                                                        <rect key="frame" x="26.999999999999993" y="0.0" width="120.33333333333331" height="36"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                                <constraints>
                                                                    <constraint firstItem="HaS-Cw-PWQ" firstAttribute="height" secondItem="8Cy-gH-TIQ" secondAttribute="height" id="ztd-3D-G37"/>
                                                                </constraints>
                                                            </stackView>
                                                        </subviews>
                                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="44" id="QLx-Ih-q5h"/>
                                                            <constraint firstAttribute="bottom" secondItem="8Cy-gH-TIQ" secondAttribute="bottom" constant="4" id="ffD-tB-GeX"/>
                                                            <constraint firstItem="8Cy-gH-TIQ" firstAttribute="top" secondItem="njV-Bf-ia1" secondAttribute="top" constant="4" id="lIl-FZ-Ld1"/>
                                                            <constraint firstAttribute="trailing" secondItem="8Cy-gH-TIQ" secondAttribute="trailing" constant="8" id="my6-bJ-1Km"/>
                                                            <constraint firstItem="8Cy-gH-TIQ" firstAttribute="leading" secondItem="njV-Bf-ia1" secondAttribute="leading" constant="8" id="w9D-CV-OoD"/>
                                                        </constraints>
                                                    </view>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="To" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="91S-ik-z7Y">
                                                        <rect key="frame" x="171.33333333333334" y="0.0" width="65.333333333333343" height="44"/>
                                                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                        <nil key="textColor"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="26H-iK-idE">
                                                        <rect key="frame" x="244.66666666666669" y="0.0" width="163.33333333333331" height="44"/>
                                                        <subviews>
                                                            <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="mVi-Gr-mT7">
                                                                <rect key="frame" x="8" y="4" width="147.33333333333334" height="36"/>
                                                                <subviews>
                                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_calendar" translatesAutoresizingMaskIntoConstraints="NO" id="1OY-nx-o0e">
                                                                        <rect key="frame" x="0.0" y="10.666666666666629" width="15" height="15"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="15" id="eh7-Zd-6mN"/>
                                                                            <constraint firstAttribute="width" constant="15" id="w3N-6W-9yE"/>
                                                                        </constraints>
                                                                    </imageView>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="KKW-6b-McC">
                                                                        <rect key="frame" x="26.999999999999993" y="0.0" width="120.33333333333331" height="36"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                                <constraints>
                                                                    <constraint firstItem="KKW-6b-McC" firstAttribute="height" secondItem="mVi-Gr-mT7" secondAttribute="height" id="6JD-9a-qjA"/>
                                                                </constraints>
                                                            </stackView>
                                                        </subviews>
                                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="44" id="JFJ-vz-2wW"/>
                                                            <constraint firstItem="mVi-Gr-mT7" firstAttribute="top" secondItem="26H-iK-idE" secondAttribute="top" constant="4" id="N3Z-PX-Imu"/>
                                                            <constraint firstAttribute="trailing" secondItem="mVi-Gr-mT7" secondAttribute="trailing" constant="8" id="PC2-c5-Xl1"/>
                                                            <constraint firstItem="mVi-Gr-mT7" firstAttribute="leading" secondItem="26H-iK-idE" secondAttribute="leading" constant="8" id="Pxv-Bf-yrt"/>
                                                            <constraint firstAttribute="bottom" secondItem="mVi-Gr-mT7" secondAttribute="bottom" constant="4" id="ykg-kI-KYB"/>
                                                        </constraints>
                                                    </view>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstItem="26H-iK-idE" firstAttribute="width" secondItem="HRP-7N-PLY" secondAttribute="width" multiplier="0.4" id="30U-pi-uLf"/>
                                                    <constraint firstItem="njV-Bf-ia1" firstAttribute="width" secondItem="HRP-7N-PLY" secondAttribute="width" multiplier="0.4" id="qee-Y0-XeV"/>
                                                </constraints>
                                            </stackView>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="fK6-ED-Jqs">
                                                <rect key="frame" x="22" y="236" width="396" height="44"/>
                                                <color key="backgroundColor" name="PrimaryColor"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="44" id="fC8-IS-0lF"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                                <state key="normal" title="Proceed">
                                                    <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                </state>
                                                <connections>
                                                    <action selector="submitAction:" destination="wQh-C4-SKZ" eventType="touchUpInside" id="zVL-ZO-mB4"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="4SB-9W-Q6O">
                                                <rect key="frame" x="16" y="87" width="408" height="44"/>
                                                <color key="tintColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <state key="normal" title="Button"/>
                                                <buttonConfiguration key="configuration" style="plain"/>
                                                <connections>
                                                    <action selector="selectVehicleAction:" destination="wQh-C4-SKZ" eventType="touchUpInside" id="dJu-Vb-NNp"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="aJM-mA-ekR">
                                                <rect key="frame" x="16" y="176" width="163.33333333333334" height="44"/>
                                                <color key="tintColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <state key="normal" title="Button"/>
                                                <buttonConfiguration key="configuration" style="plain"/>
                                                <connections>
                                                    <action selector="fromDateAction:" destination="wQh-C4-SKZ" eventType="touchUpInside" id="oBh-v1-JFA"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="guI-EJ-u8p">
                                                <rect key="frame" x="260.66666666666669" y="176" width="163.33333333333331" height="44"/>
                                                <color key="tintColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <state key="normal" title="Button"/>
                                                <buttonConfiguration key="configuration" style="plain"/>
                                                <connections>
                                                    <action selector="toDateAction:" destination="wQh-C4-SKZ" eventType="touchUpInside" id="aO4-e0-QIH"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="NGH-YC-7Ic" firstAttribute="top" secondItem="7ap-MN-qnr" secondAttribute="bottom" constant="8" id="13u-jS-KkH"/>
                                            <constraint firstAttribute="trailing" secondItem="KYP-UY-Yvb" secondAttribute="trailing" constant="16" id="2QT-ag-njP"/>
                                            <constraint firstItem="aJM-mA-ekR" firstAttribute="centerY" secondItem="njV-Bf-ia1" secondAttribute="centerY" id="35I-nv-gq4"/>
                                            <constraint firstItem="KYP-UY-Yvb" firstAttribute="leading" secondItem="0BP-2b-1hq" secondAttribute="leading" constant="16" id="3GQ-d2-FI0"/>
                                            <constraint firstItem="aJM-mA-ekR" firstAttribute="height" secondItem="njV-Bf-ia1" secondAttribute="height" id="4mp-1a-Tcc"/>
                                            <constraint firstItem="7ap-MN-qnr" firstAttribute="centerX" secondItem="0BP-2b-1hq" secondAttribute="centerX" id="6oU-Zl-zeV"/>
                                            <constraint firstItem="guI-EJ-u8p" firstAttribute="centerY" secondItem="26H-iK-idE" secondAttribute="centerY" id="DQV-og-fdz"/>
                                            <constraint firstItem="NGH-YC-7Ic" firstAttribute="leading" secondItem="0BP-2b-1hq" secondAttribute="leading" constant="12" id="Da1-J9-H1i"/>
                                            <constraint firstItem="4SB-9W-Q6O" firstAttribute="centerY" secondItem="KYP-UY-Yvb" secondAttribute="centerY" id="DhG-81-sv4"/>
                                            <constraint firstItem="7ap-MN-qnr" firstAttribute="width" secondItem="0BP-2b-1hq" secondAttribute="width" multiplier="0.9" id="DrD-oc-vpc"/>
                                            <constraint firstItem="Dt4-cc-UPU" firstAttribute="top" secondItem="KYP-UY-Yvb" secondAttribute="bottom" constant="8" id="F9n-hs-G2j"/>
                                            <constraint firstItem="UmC-TZ-4sX" firstAttribute="leading" secondItem="0BP-2b-1hq" secondAttribute="leading" constant="12" id="GVS-QF-Kub"/>
                                            <constraint firstItem="UmC-TZ-4sX" firstAttribute="top" secondItem="0BP-2b-1hq" secondAttribute="top" constant="8" id="Gbn-BQ-LaD"/>
                                            <constraint firstItem="guI-EJ-u8p" firstAttribute="centerX" secondItem="26H-iK-idE" secondAttribute="centerX" id="IrD-aA-oJh"/>
                                            <constraint firstItem="mQm-ZH-QWY" firstAttribute="top" secondItem="Dt4-cc-UPU" secondAttribute="bottom" constant="8" id="Lxi-go-1Mp"/>
                                            <constraint firstAttribute="trailing" secondItem="HRP-7N-PLY" secondAttribute="trailing" constant="16" id="M0e-vR-IqG"/>
                                            <constraint firstItem="HRP-7N-PLY" firstAttribute="top" secondItem="mQm-ZH-QWY" secondAttribute="bottom" constant="8" id="Nur-Pd-AAF"/>
                                            <constraint firstItem="fK6-ED-Jqs" firstAttribute="top" secondItem="HRP-7N-PLY" secondAttribute="bottom" constant="16" id="RjQ-wk-Y3s"/>
                                            <constraint firstItem="4SB-9W-Q6O" firstAttribute="centerX" secondItem="KYP-UY-Yvb" secondAttribute="centerX" id="Wsb-8w-YBf"/>
                                            <constraint firstAttribute="trailing" secondItem="W2Z-uD-O7Q" secondAttribute="trailing" constant="4" id="ZCE-tW-jT5"/>
                                            <constraint firstAttribute="bottom" secondItem="fK6-ED-Jqs" secondAttribute="bottom" constant="16" id="bd7-ht-BKb"/>
                                            <constraint firstItem="7ap-MN-qnr" firstAttribute="top" secondItem="UmC-TZ-4sX" secondAttribute="bottom" constant="8" id="dGb-2d-WK6"/>
                                            <constraint firstItem="fK6-ED-Jqs" firstAttribute="width" secondItem="0BP-2b-1hq" secondAttribute="width" multiplier="0.9" id="ehu-U1-9Ap"/>
                                            <constraint firstItem="HRP-7N-PLY" firstAttribute="leading" secondItem="0BP-2b-1hq" secondAttribute="leading" constant="16" id="gci-8D-7fb"/>
                                            <constraint firstItem="guI-EJ-u8p" firstAttribute="width" secondItem="26H-iK-idE" secondAttribute="width" id="hsk-Gs-Koh"/>
                                            <constraint firstItem="4SB-9W-Q6O" firstAttribute="height" secondItem="KYP-UY-Yvb" secondAttribute="height" id="jmg-xa-GeJ"/>
                                            <constraint firstItem="mQm-ZH-QWY" firstAttribute="leading" secondItem="0BP-2b-1hq" secondAttribute="leading" constant="12" id="qH5-Gd-FWW"/>
                                            <constraint firstItem="aJM-mA-ekR" firstAttribute="width" secondItem="njV-Bf-ia1" secondAttribute="width" id="rey-DL-l1h"/>
                                            <constraint firstItem="fK6-ED-Jqs" firstAttribute="centerX" secondItem="0BP-2b-1hq" secondAttribute="centerX" id="sZX-hf-Xgu"/>
                                            <constraint firstItem="Dt4-cc-UPU" firstAttribute="centerX" secondItem="0BP-2b-1hq" secondAttribute="centerX" id="vzn-tK-H7B"/>
                                            <constraint firstItem="4SB-9W-Q6O" firstAttribute="width" secondItem="KYP-UY-Yvb" secondAttribute="width" id="w27-0W-lqd"/>
                                            <constraint firstItem="KYP-UY-Yvb" firstAttribute="top" secondItem="NGH-YC-7Ic" secondAttribute="bottom" constant="8" id="wb2-3P-odk"/>
                                            <constraint firstItem="aJM-mA-ekR" firstAttribute="centerX" secondItem="njV-Bf-ia1" secondAttribute="centerX" id="xxg-4U-PvW"/>
                                            <constraint firstItem="guI-EJ-u8p" firstAttribute="height" secondItem="26H-iK-idE" secondAttribute="height" id="zBE-Gl-5c8"/>
                                            <constraint firstItem="Dt4-cc-UPU" firstAttribute="width" secondItem="0BP-2b-1hq" secondAttribute="width" multiplier="0.9" id="zDx-DG-AIF"/>
                                            <constraint firstItem="W2Z-uD-O7Q" firstAttribute="top" secondItem="0BP-2b-1hq" secondAttribute="top" constant="8" id="zca-FZ-hAa"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.75" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="0BP-2b-1hq" firstAttribute="width" secondItem="gwa-Sz-EBi" secondAttribute="width" id="0bp-ay-Gah"/>
                                    <constraint firstAttribute="bottom" secondItem="0BP-2b-1hq" secondAttribute="bottom" id="3ab-Od-eX8"/>
                                    <constraint firstItem="0BP-2b-1hq" firstAttribute="centerX" secondItem="gwa-Sz-EBi" secondAttribute="centerX" id="3s4-2S-y12"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="KVH-FC-esn">
                                <rect key="frame" x="0.0" y="124" width="440" height="764"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="huJ-dS-E2k">
                                        <rect key="frame" x="33" y="240.33333333333329" width="374" height="283.66666666666674"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Select Date Of Birth" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="dVM-qa-GDJ">
                                                <rect key="frame" x="104" y="16" width="166" height="21.666666666666671"/>
                                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="18"/>
                                                <color key="textColor" name="Primary"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="g83-Ip-VFH">
                                                <rect key="frame" x="332" y="8" width="34" height="34"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="34" id="7BL-DN-nwt"/>
                                                    <constraint firstAttribute="width" constant="34" id="gBj-kA-V5O"/>
                                                </constraints>
                                                <inset key="imageEdgeInsets" minX="10" minY="10" maxX="10" maxY="10"/>
                                                <state key="normal" image="ic_cancel"/>
                                                <connections>
                                                    <action selector="cancelDateAction:" destination="wQh-C4-SKZ" eventType="touchUpInside" id="ME0-P2-xQI"/>
                                                </connections>
                                            </button>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="20" translatesAutoresizingMaskIntoConstraints="NO" id="CVk-g8-pJm">
                                                <rect key="frame" x="8" y="53.666666666666686" width="358" height="214"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" alignment="center" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="Abc-ii-jlK">
                                                        <rect key="frame" x="0.0" y="0.0" width="358" height="150"/>
                                                        <subviews>
                                                            <datePicker contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" datePickerMode="date" style="wheels" translatesAutoresizingMaskIntoConstraints="NO" id="WBY-Qg-L2N">
                                                                <rect key="frame" x="19" y="0.0" width="320" height="150"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="150" id="Vl0-Qr-FJf"/>
                                                                </constraints>
                                                            </datePicker>
                                                        </subviews>
                                                    </stackView>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="24" translatesAutoresizingMaskIntoConstraints="NO" id="qFS-V8-TZc">
                                                        <rect key="frame" x="18" y="170" width="322" height="44"/>
                                                        <subviews>
                                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="58T-Qd-zTu">
                                                                <rect key="frame" x="0.0" y="0.0" width="322" height="44"/>
                                                                <subviews>
                                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="yVg-Ax-fc7">
                                                                        <rect key="frame" x="0.0" y="0.0" width="322" height="44"/>
                                                                        <color key="backgroundColor" name="Primary"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="44" id="qlb-l7-FbA"/>
                                                                        </constraints>
                                                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                                                        <state key="normal" title="OK">
                                                                            <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        </state>
                                                                        <connections>
                                                                            <action selector="okDateAction:" destination="wQh-C4-SKZ" eventType="touchUpInside" id="t4a-aY-yEd"/>
                                                                        </connections>
                                                                    </button>
                                                                </subviews>
                                                            </stackView>
                                                        </subviews>
                                                    </stackView>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstItem="Abc-ii-jlK" firstAttribute="width" secondItem="CVk-g8-pJm" secondAttribute="width" id="76T-WJ-hyi"/>
                                                    <constraint firstItem="qFS-V8-TZc" firstAttribute="width" secondItem="CVk-g8-pJm" secondAttribute="width" multiplier="0.9" id="Akg-uK-Dnf"/>
                                                </constraints>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="bottom" secondItem="CVk-g8-pJm" secondAttribute="bottom" constant="16" id="8ZI-zr-3E5"/>
                                            <constraint firstAttribute="trailing" secondItem="g83-Ip-VFH" secondAttribute="trailing" constant="8" id="8qE-gh-IVy"/>
                                            <constraint firstItem="g83-Ip-VFH" firstAttribute="top" secondItem="huJ-dS-E2k" secondAttribute="top" constant="8" id="AMd-1E-Dvw"/>
                                            <constraint firstItem="CVk-g8-pJm" firstAttribute="leading" secondItem="huJ-dS-E2k" secondAttribute="leading" constant="8" id="Adi-MY-R55"/>
                                            <constraint firstItem="dVM-qa-GDJ" firstAttribute="top" secondItem="huJ-dS-E2k" secondAttribute="top" constant="16" id="Cgj-N8-pvn"/>
                                            <constraint firstItem="dVM-qa-GDJ" firstAttribute="centerX" secondItem="huJ-dS-E2k" secondAttribute="centerX" id="S5N-Uy-Jbb"/>
                                            <constraint firstItem="CVk-g8-pJm" firstAttribute="top" secondItem="dVM-qa-GDJ" secondAttribute="bottom" constant="16" id="afR-rw-Gx0"/>
                                            <constraint firstAttribute="trailing" secondItem="CVk-g8-pJm" secondAttribute="trailing" constant="8" id="bPX-rQ-sGU"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.5" colorSpace="custom" customColorSpace="sRGB"/>
                                <constraints>
                                    <constraint firstItem="huJ-dS-E2k" firstAttribute="centerY" secondItem="KVH-FC-esn" secondAttribute="centerY" id="5lY-vm-Ysf"/>
                                    <constraint firstItem="huJ-dS-E2k" firstAttribute="width" secondItem="KVH-FC-esn" secondAttribute="width" multiplier="0.85" id="G1A-Qc-WkS"/>
                                    <constraint firstItem="huJ-dS-E2k" firstAttribute="centerX" secondItem="KVH-FC-esn" secondAttribute="centerX" id="XGZ-T5-ZLl"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="att-R5-xS9"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="ww7-z7-oaA" firstAttribute="top" secondItem="yfZ-5N-vRR" secondAttribute="bottom" constant="8" id="1Go-J5-Qtl"/>
                            <constraint firstItem="att-R5-xS9" firstAttribute="bottom" secondItem="gwa-Sz-EBi" secondAttribute="bottom" id="1aq-ZB-s1X"/>
                            <constraint firstItem="yfZ-5N-vRR" firstAttribute="top" secondItem="hOW-de-5D2" secondAttribute="bottom" constant="10" id="3a1-is-x12"/>
                            <constraint firstItem="gwa-Sz-EBi" firstAttribute="leading" secondItem="att-R5-xS9" secondAttribute="leading" id="8c3-4h-QNx"/>
                            <constraint firstItem="gwa-Sz-EBi" firstAttribute="top" secondItem="att-R5-xS9" secondAttribute="top" id="9ar-bn-4Sh"/>
                            <constraint firstItem="hOW-de-5D2" firstAttribute="top" secondItem="vpq-Rf-F1m" secondAttribute="bottom" constant="8" id="Agg-On-fwL"/>
                            <constraint firstItem="vpq-Rf-F1m" firstAttribute="leading" secondItem="att-R5-xS9" secondAttribute="leading" id="CZk-ve-Ymv"/>
                            <constraint firstItem="KVH-FC-esn" firstAttribute="leading" secondItem="att-R5-xS9" secondAttribute="leading" id="EHI-q2-ANz"/>
                            <constraint firstItem="vpq-Rf-F1m" firstAttribute="top" secondItem="att-R5-xS9" secondAttribute="top" id="EP6-Wa-1PI"/>
                            <constraint firstItem="dh9-GB-E8j" firstAttribute="leading" secondItem="att-R5-xS9" secondAttribute="leading" constant="4" id="JMR-zl-0Ab"/>
                            <constraint firstItem="att-R5-xS9" firstAttribute="trailing" secondItem="KVH-FC-esn" secondAttribute="trailing" id="KHH-9T-i2y"/>
                            <constraint firstItem="att-R5-xS9" firstAttribute="trailing" secondItem="gwa-Sz-EBi" secondAttribute="trailing" id="KJG-qJ-Ieg"/>
                            <constraint firstItem="att-R5-xS9" firstAttribute="trailing" secondItem="vpq-Rf-F1m" secondAttribute="trailing" id="Rn4-Jd-sgB"/>
                            <constraint firstItem="hOW-de-5D2" firstAttribute="leading" secondItem="att-R5-xS9" secondAttribute="leading" constant="12" id="SRx-4b-us7"/>
                            <constraint firstItem="KVH-FC-esn" firstAttribute="top" secondItem="att-R5-xS9" secondAttribute="top" id="Xtb-6g-vbp"/>
                            <constraint firstItem="att-R5-xS9" firstAttribute="trailing" secondItem="dh9-GB-E8j" secondAttribute="trailing" constant="4" id="a8m-jp-LWV"/>
                            <constraint firstItem="att-R5-xS9" firstAttribute="bottom" secondItem="ww7-z7-oaA" secondAttribute="bottom" constant="8" id="aal-wA-gIn"/>
                            <constraint firstItem="ww7-z7-oaA" firstAttribute="leading" secondItem="att-R5-xS9" secondAttribute="leading" constant="8" id="bIY-C4-j3g"/>
                            <constraint firstItem="att-R5-xS9" firstAttribute="trailing" secondItem="yfZ-5N-vRR" secondAttribute="trailing" id="cXx-m3-PLu"/>
                            <constraint firstItem="att-R5-xS9" firstAttribute="bottom" secondItem="KVH-FC-esn" secondAttribute="bottom" id="fvH-U0-nfq"/>
                            <constraint firstItem="att-R5-xS9" firstAttribute="trailing" secondItem="ww7-z7-oaA" secondAttribute="trailing" constant="8" id="oUT-Jk-1dX"/>
                            <constraint firstItem="dh9-GB-E8j" firstAttribute="top" secondItem="hOW-de-5D2" secondAttribute="bottom" constant="10" id="pDK-a3-VTY"/>
                            <constraint firstItem="yfZ-5N-vRR" firstAttribute="leading" secondItem="att-R5-xS9" secondAttribute="leading" id="tBU-7m-DkH"/>
                            <constraint firstItem="ww7-z7-oaA" firstAttribute="top" secondItem="dh9-GB-E8j" secondAttribute="bottom" constant="8" id="v7e-bd-dXQ"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="btnAddToWallet" destination="qUj-9t-TSB" id="zT5-NI-rN7"/>
                        <outlet property="btnAll" destination="xvo-R0-dMk" id="rJa-oW-lMF"/>
                        <outlet property="btnBack" destination="QKE-gX-KMZ" id="Op4-9W-feu"/>
                        <outlet property="btnFilter" destination="nYj-yt-9W1" id="dml-oL-Lzq"/>
                        <outlet property="btnOKDate" destination="yVg-Ax-fc7" id="GT7-P3-Sy9"/>
                        <outlet property="btnPaidForCharging" destination="LuA-R5-jHH" id="Wf0-3h-X28"/>
                        <outlet property="btnSubmit" destination="fK6-ED-Jqs" id="Tmn-1v-GcE"/>
                        <outlet property="datePicker" destination="WBY-Qg-L2N" id="UAn-cn-idO"/>
                        <outlet property="lblCredit" destination="2uc-Nm-VMg" id="6Vp-34-NzR"/>
                        <outlet property="lblDOBTitle" destination="dVM-qa-GDJ" id="I5d-os-5px"/>
                        <outlet property="lblDebit" destination="XpU-u1-lQY" id="cPN-wz-TG2"/>
                        <outlet property="lblFilterBy" destination="UmC-TZ-4sX" id="AKG-UE-cCw"/>
                        <outlet property="lblFromDate" destination="HaS-Cw-PWQ" id="PtF-2j-Xd0"/>
                        <outlet property="lblSelectDateTitle" destination="mQm-ZH-QWY" id="rmg-Kg-7ST"/>
                        <outlet property="lblSelectVehicleTitle" destination="NGH-YC-7Ic" id="Med-n9-rBS"/>
                        <outlet property="lblTitle" destination="0sk-uq-DZO" id="tj6-r4-jk3"/>
                        <outlet property="lblToDate" destination="KKW-6b-McC" id="Zgb-vV-4yt"/>
                        <outlet property="lblVehicle" destination="CfY-Kt-Beb" id="rtp-yq-OE7"/>
                        <outlet property="tableList" destination="yfZ-5N-vRR" id="mfy-4I-TeT"/>
                        <outlet property="viewBgDate" destination="KVH-FC-esn" id="Rnk-bq-ROy"/>
                        <outlet property="viewBgFilter" destination="gwa-Sz-EBi" id="ZBJ-VN-pya"/>
                        <outlet property="viewCredit" destination="Xoi-z1-ddA" id="HLg-xQ-Hm9"/>
                        <outlet property="viewDebit" destination="vcO-nT-Yiq" id="dfC-ey-uQ9"/>
                        <outlet property="viewFromDate" destination="njV-Bf-ia1" id="RaH-gP-G5E"/>
                        <outlet property="viewMainDate" destination="huJ-dS-E2k" id="Qmf-F9-Y7l"/>
                        <outlet property="viewMainFilter" destination="0BP-2b-1hq" id="t8g-zn-Rna"/>
                        <outlet property="viewNoData" destination="dh9-GB-E8j" id="OnZ-6N-WPy"/>
                        <outlet property="viewToDate" destination="26H-iK-idE" id="8M8-57-ULM"/>
                        <outlet property="viewVehicle" destination="KYP-UY-Yvb" id="COT-nK-FAU"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Q3B-M1-94z" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="832.79999999999995" y="71.514242878560722"/>
        </scene>
        <!--Add MoneyVC-->
        <scene sceneID="14z-kG-bkG">
            <objects>
                <viewController storyboardIdentifier="AddMoneyVC" id="Vlg-Fs-MUY" customClass="AddMoneyVC" customModule="NXC_EV_Solutions" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="I3y-cN-e3d">
                        <rect key="frame" x="0.0" y="0.0" width="440" height="956"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="hY6-6Y-gvs">
                                <rect key="frame" x="0.0" y="124" width="440" height="44"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Z4v-v9-5P4">
                                        <rect key="frame" x="12" y="5" width="34" height="34"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="34" id="QzK-Rs-1Fe"/>
                                            <constraint firstAttribute="height" constant="34" id="WtR-d3-9iv"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="8" minY="6" maxX="8" maxY="6"/>
                                        <state key="normal" image="ic_back_arrow"/>
                                        <connections>
                                            <action selector="backAction:" destination="Vlg-Fs-MUY" eventType="touchUpInside" id="JkR-pS-RXt"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Add Money" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="rcX-Fg-Q3L">
                                        <rect key="frame" x="175.33333333333334" y="11.999999999999998" width="89.666666666666657" height="20.333333333333329"/>
                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                        <color key="textColor" red="0.070588235289999995" green="0.070588235289999995" blue="0.070588235289999995" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="rcX-Fg-Q3L" firstAttribute="centerX" secondItem="hY6-6Y-gvs" secondAttribute="centerX" id="7K6-Zy-hRx"/>
                                    <constraint firstAttribute="height" constant="44" id="9UN-HY-hGo"/>
                                    <constraint firstItem="Z4v-v9-5P4" firstAttribute="centerY" secondItem="hY6-6Y-gvs" secondAttribute="centerY" id="ZwB-TG-f8i"/>
                                    <constraint firstItem="rcX-Fg-Q3L" firstAttribute="centerY" secondItem="hY6-6Y-gvs" secondAttribute="centerY" id="gJE-h6-zNu"/>
                                    <constraint firstItem="Z4v-v9-5P4" firstAttribute="leading" secondItem="hY6-6Y-gvs" secondAttribute="leading" constant="12" id="now-vs-cq2"/>
                                </constraints>
                            </view>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" translatesAutoresizingMaskIntoConstraints="NO" id="YXG-Gi-HrT">
                                <rect key="frame" x="0.0" y="176" width="440" height="644"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="18" translatesAutoresizingMaskIntoConstraints="NO" id="Qqg-tv-gwn">
                                        <rect key="frame" x="12" y="0.0" width="416" height="186"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="lrB-WL-YYg">
                                                <rect key="frame" x="0.0" y="0.0" width="416" height="72"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Enter Amount" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="S4F-PG-j4G">
                                                        <rect key="frame" x="0.0" y="0.0" width="416" height="24"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="24" id="X2T-EK-omd"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="system" weight="medium" pointSize="15"/>
                                                        <color key="textColor" name="Primary"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="h5T-v9-XDv">
                                                        <rect key="frame" x="0.0" y="28" width="416" height="44"/>
                                                        <color key="tintColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="44" id="rlh-Dj-OhY"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                                        <textInputTraits key="textInputTraits" keyboardType="numberPad"/>
                                                    </textField>
                                                </subviews>
                                            </stackView>
                                            <stackView hidden="YES" opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="GQ0-QA-qN8">
                                                <rect key="frame" x="0.0" y="81" width="416" height="100"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Enter Promo Code" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="D8E-lB-CAP">
                                                        <rect key="frame" x="0.0" y="0.0" width="416" height="24"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="24" id="9d6-zV-hKG"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="system" weight="medium" pointSize="15"/>
                                                        <color key="textColor" name="Primary"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="JIF-Yg-lpV">
                                                        <rect key="frame" x="0.0" y="28" width="416" height="44"/>
                                                        <subviews>
                                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="YV1-AR-UNP">
                                                                <rect key="frame" x="0.0" y="0.0" width="324" height="44"/>
                                                                <color key="tintColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="44" id="R4Q-33-pJg"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                                                <textInputTraits key="textInputTraits"/>
                                                            </textField>
                                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="bzo-76-m4N">
                                                                <rect key="frame" x="332" y="-1" width="85" height="46"/>
                                                                <color key="backgroundColor" name="Primary"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="46" id="OU8-bS-QvX"/>
                                                                    <constraint firstAttribute="width" constant="85" id="pBr-jp-sZy"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                                                <state key="normal" title="Apply">
                                                                    <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                </state>
                                                                <connections>
                                                                    <action selector="applyPromoTapped:" destination="Vlg-Fs-MUY" eventType="touchUpInside" id="LsS-q4-k0X"/>
                                                                    <action selector="okDateAction:" destination="wQh-C4-SKZ" eventType="touchUpInside" id="9Kv-ED-VPU"/>
                                                                </connections>
                                                            </button>
                                                        </subviews>
                                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstItem="YV1-AR-UNP" firstAttribute="leading" secondItem="JIF-Yg-lpV" secondAttribute="leading" id="0Yr-eW-bjd"/>
                                                            <constraint firstAttribute="trailing" secondItem="bzo-76-m4N" secondAttribute="trailing" constant="-1" id="Jne-ij-W1p"/>
                                                            <constraint firstItem="bzo-76-m4N" firstAttribute="leading" secondItem="YV1-AR-UNP" secondAttribute="trailing" constant="8" id="PJ2-EH-czr"/>
                                                            <constraint firstItem="YV1-AR-UNP" firstAttribute="top" secondItem="JIF-Yg-lpV" secondAttribute="top" id="RwA-zL-fPk"/>
                                                            <constraint firstAttribute="bottom" secondItem="YV1-AR-UNP" secondAttribute="bottom" id="g0F-MC-hga"/>
                                                            <constraint firstAttribute="bottom" secondItem="bzo-76-m4N" secondAttribute="bottom" constant="-1" id="uh6-GI-STI"/>
                                                            <constraint firstItem="bzo-76-m4N" firstAttribute="top" secondItem="JIF-Yg-lpV" secondAttribute="top" constant="-1" id="vW8-0C-xiv"/>
                                                            <constraint firstAttribute="height" constant="44" id="yDp-yr-O6g"/>
                                                        </constraints>
                                                    </view>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Promo code is not valid" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="dSP-JL-HSl">
                                                        <rect key="frame" x="0.0" y="76" width="416" height="24"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="24" id="nPD-Vo-rFL"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="system" pointSize="13"/>
                                                        <color key="textColor" name="WalletDeductText"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                            </stackView>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="AdE-YG-LXv">
                                                <rect key="frame" x="0.0" y="90" width="416" height="44"/>
                                                <subviews>
                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_razorpay" translatesAutoresizingMaskIntoConstraints="NO" id="4f8-aH-qW5">
                                                        <rect key="frame" x="12" y="4" width="100" height="36"/>
                                                    </imageView>
                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_ticked" translatesAutoresizingMaskIntoConstraints="NO" id="fdP-Hf-ZpO">
                                                        <rect key="frame" x="377.66666666666669" y="8.6666666666666874" width="26.333333333333314" height="26.666666666666671"/>
                                                        <constraints>
                                                            <constraint firstAttribute="width" secondItem="fdP-Hf-ZpO" secondAttribute="height" multiplier="1:1" id="V32-b3-oKC"/>
                                                        </constraints>
                                                    </imageView>
                                                </subviews>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <constraints>
                                                    <constraint firstItem="fdP-Hf-ZpO" firstAttribute="centerY" secondItem="AdE-YG-LXv" secondAttribute="centerY" id="GIU-h2-qWh"/>
                                                    <constraint firstAttribute="bottom" secondItem="4f8-aH-qW5" secondAttribute="bottom" constant="4" id="PBi-UY-gJw"/>
                                                    <constraint firstAttribute="height" constant="44" id="Tos-al-7BJ"/>
                                                    <constraint firstItem="4f8-aH-qW5" firstAttribute="top" secondItem="AdE-YG-LXv" secondAttribute="top" constant="4" id="eBf-3M-U5F"/>
                                                    <constraint firstAttribute="trailing" secondItem="fdP-Hf-ZpO" secondAttribute="trailing" constant="12" id="jeD-oV-KQF"/>
                                                    <constraint firstItem="4f8-aH-qW5" firstAttribute="leading" secondItem="AdE-YG-LXv" secondAttribute="leading" constant="12" id="lgf-Oc-Zom"/>
                                                    <constraint firstItem="fdP-Hf-ZpO" firstAttribute="height" secondItem="AdE-YG-LXv" secondAttribute="height" multiplier="0.6" id="lgf-Sx-RB9"/>
                                                </constraints>
                                            </view>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="4S1-Su-eG4">
                                                <rect key="frame" x="0.0" y="152" width="416" height="34"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Note : Money should be added in multiples of 10" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="2Sf-sd-v6Z">
                                                        <rect key="frame" x="52.666666666666686" y="8.6666666666666856" width="311" height="17"/>
                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                        <nil key="textColor"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                                <color key="backgroundColor" name="PaymentNote"/>
                                                <constraints>
                                                    <constraint firstItem="2Sf-sd-v6Z" firstAttribute="centerX" secondItem="4S1-Su-eG4" secondAttribute="centerX" id="Yb6-U3-Wm8"/>
                                                    <constraint firstItem="2Sf-sd-v6Z" firstAttribute="centerY" secondItem="4S1-Su-eG4" secondAttribute="centerY" id="mit-ht-a0c"/>
                                                    <constraint firstAttribute="height" constant="34" id="wqT-RS-AfL"/>
                                                </constraints>
                                            </view>
                                            <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Oi1-w1-Ceu">
                                                <rect key="frame" x="0.0" y="186" width="416" height="221"/>
                                                <subviews>
                                                    <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" pagingEnabled="YES" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" translatesAutoresizingMaskIntoConstraints="NO" id="QzG-LW-QP2">
                                                        <rect key="frame" x="8" y="0.0" width="400" height="175"/>
                                                        <subviews>
                                                            <view clipsSubviews="YES" alpha="0.0" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="OTF-lc-Bxm">
                                                                <rect key="frame" x="0.0" y="0.0" width="400" height="175"/>
                                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                            </view>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstItem="OTF-lc-Bxm" firstAttribute="top" secondItem="QzG-LW-QP2" secondAttribute="top" id="0Um-Jg-EhI"/>
                                                            <constraint firstItem="OTF-lc-Bxm" firstAttribute="centerY" secondItem="QzG-LW-QP2" secondAttribute="centerY" id="5RN-Sj-AGy"/>
                                                            <constraint firstAttribute="height" constant="175" id="OqM-iK-P1A"/>
                                                            <constraint firstItem="OTF-lc-Bxm" firstAttribute="leading" secondItem="QzG-LW-QP2" secondAttribute="leading" id="RM5-25-sr7"/>
                                                            <constraint firstAttribute="bottom" secondItem="OTF-lc-Bxm" secondAttribute="bottom" id="YOp-b0-DQ8"/>
                                                            <constraint firstItem="OTF-lc-Bxm" firstAttribute="centerX" secondItem="QzG-LW-QP2" secondAttribute="centerX" id="f4U-UO-41J"/>
                                                            <constraint firstAttribute="trailing" secondItem="OTF-lc-Bxm" secondAttribute="trailing" id="ptj-04-vFe"/>
                                                        </constraints>
                                                        <viewLayoutGuide key="contentLayoutGuide" id="R8f-LD-0K4"/>
                                                        <viewLayoutGuide key="frameLayoutGuide" id="rS4-uK-7HA"/>
                                                    </scrollView>
                                                    <pageControl opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" numberOfPages="3" translatesAutoresizingMaskIntoConstraints="NO" id="UCa-mX-yG0">
                                                        <rect key="frame" x="171.66666666666666" y="183" width="73" height="30"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="30" id="RGz-vX-iaT"/>
                                                        </constraints>
                                                        <color key="pageIndicatorTintColor" name="TextNotSelected"/>
                                                        <color key="currentPageIndicatorTintColor" name="Primary"/>
                                                    </pageControl>
                                                </subviews>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <constraints>
                                                    <constraint firstItem="QzG-LW-QP2" firstAttribute="top" secondItem="Oi1-w1-Ceu" secondAttribute="top" id="78x-Ju-fEJ"/>
                                                    <constraint firstAttribute="bottom" secondItem="UCa-mX-yG0" secondAttribute="bottom" constant="8" id="7MK-0O-pJw"/>
                                                    <constraint firstItem="UCa-mX-yG0" firstAttribute="top" secondItem="QzG-LW-QP2" secondAttribute="bottom" constant="8" id="W1Q-tS-Nb1"/>
                                                    <constraint firstItem="UCa-mX-yG0" firstAttribute="centerX" secondItem="QzG-LW-QP2" secondAttribute="centerX" id="fRN-QX-fB6"/>
                                                    <constraint firstItem="QzG-LW-QP2" firstAttribute="leading" secondItem="Oi1-w1-Ceu" secondAttribute="leading" constant="8" id="mxV-X6-LK6"/>
                                                    <constraint firstAttribute="trailing" secondItem="QzG-LW-QP2" secondAttribute="trailing" constant="8" id="uOc-9u-fhr"/>
                                                </constraints>
                                            </view>
                                            <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="8La-mu-6KZ">
                                                <rect key="frame" x="0.0" y="186" width="416" height="302"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Promotions" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="a2r-TO-1Rl">
                                                        <rect key="frame" x="8" y="8" width="80.666666666666671" height="24"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="24" id="LU5-Of-iD3"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="system" weight="medium" pointSize="15"/>
                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" scrollEnabled="NO" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="-1" estimatedSectionHeaderHeight="-1" sectionFooterHeight="-1" estimatedSectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="9tZ-62-PcU">
                                                        <rect key="frame" x="4" y="40" width="408" height="250"/>
                                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="250" id="mtL-DS-CO7"/>
                                                        </constraints>
                                                        <prototypes>
                                                            <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="none" indentationWidth="10" reuseIdentifier="cell" rowHeight="178" id="R2u-b8-mCe" customClass="OfferCouponCell" customModule="NXC_EV_Solutions" customModuleProvider="target">
                                                                <rect key="frame" x="0.0" y="50" width="408" height="178"/>
                                                                <autoresizingMask key="autoresizingMask"/>
                                                                <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="R2u-b8-mCe" id="XnT-WE-b4O">
                                                                    <rect key="frame" x="0.0" y="0.0" width="408" height="178"/>
                                                                    <autoresizingMask key="autoresizingMask"/>
                                                                    <subviews>
                                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="tvW-0n-o4H">
                                                                            <rect key="frame" x="4" y="4" width="400" height="170"/>
                                                                            <subviews>
                                                                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="top" spacing="2" translatesAutoresizingMaskIntoConstraints="NO" id="7kN-kH-Xyi">
                                                                                    <rect key="frame" x="8" y="8" width="384" height="154"/>
                                                                                    <subviews>
                                                                                        <stackView opaque="NO" contentMode="scaleToFill" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="66K-AV-2vb">
                                                                                            <rect key="frame" x="0.0" y="0.0" width="235.66666666666666" height="34"/>
                                                                                            <subviews>
                                                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="j86-tY-m2C">
                                                                                                    <rect key="frame" x="0.0" y="0.0" width="177.66666666666666" height="34"/>
                                                                                                    <subviews>
                                                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="oiuytrertyu" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ANe-we-04v">
                                                                                                            <rect key="frame" x="8" y="4" width="161.66666666666666" height="26"/>
                                                                                                            <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                                                            <color key="textColor" red="0.015686274509803921" green="0.49411764705882355" blue="0.42745098039215684" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                                                            <nil key="highlightedColor"/>
                                                                                                        </label>
                                                                                                    </subviews>
                                                                                                    <color key="backgroundColor" red="0.96862745098039216" green="0.96078431372549022" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                                                    <constraints>
                                                                                                        <constraint firstAttribute="bottom" secondItem="ANe-we-04v" secondAttribute="bottom" constant="4" id="8fq-0I-fUf"/>
                                                                                                        <constraint firstItem="ANe-we-04v" firstAttribute="top" secondItem="j86-tY-m2C" secondAttribute="top" constant="4" id="ACi-6S-sfc"/>
                                                                                                        <constraint firstAttribute="height" constant="34" id="KpA-zC-8a9"/>
                                                                                                        <constraint firstAttribute="trailing" secondItem="ANe-we-04v" secondAttribute="trailing" constant="8" id="udG-D1-2Er"/>
                                                                                                        <constraint firstItem="ANe-we-04v" firstAttribute="leading" secondItem="j86-tY-m2C" secondAttribute="leading" constant="8" id="w12-VX-sBe"/>
                                                                                                    </constraints>
                                                                                                </view>
                                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Copy" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="DuE-by-Wuc">
                                                                                                    <rect key="frame" x="185.66666666666666" y="0.0" width="50" height="34"/>
                                                                                                    <constraints>
                                                                                                        <constraint firstAttribute="width" constant="50" id="8m3-Ah-ln3"/>
                                                                                                    </constraints>
                                                                                                    <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                                                    <color key="textColor" red="0.94901960780000005" green="0.50980392159999999" blue="0.31764705879999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                                                    <nil key="highlightedColor"/>
                                                                                                </label>
                                                                                            </subviews>
                                                                                        </stackView>
                                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Get 50% cashback" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="unR-1P-8kK">
                                                                                            <rect key="frame" x="0.0" y="36" width="141" height="34"/>
                                                                                            <constraints>
                                                                                                <constraint firstAttribute="height" constant="34" id="ilG-vH-A4u"/>
                                                                                            </constraints>
                                                                                            <fontDescription key="fontDescription" type="system" weight="medium" pointSize="16"/>
                                                                                            <nil key="textColor"/>
                                                                                            <nil key="highlightedColor"/>
                                                                                        </label>
                                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Use code and get ₹30 cashback on your first transaction" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="qI9-GD-J7G">
                                                                                            <rect key="frame" x="0.0" y="72" width="375.33333333333331" height="50"/>
                                                                                            <constraints>
                                                                                                <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="34" id="s0A-3X-yPV"/>
                                                                                            </constraints>
                                                                                            <fontDescription key="fontDescription" type="system" weight="medium" pointSize="14"/>
                                                                                            <color key="textColor" name="GrayPlaceholder"/>
                                                                                            <nil key="highlightedColor"/>
                                                                                        </label>
                                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Terms &amp; Conditions" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="L8q-2n-lSr">
                                                                                            <rect key="frame" x="0.0" y="124" width="134.66666666666666" height="30"/>
                                                                                            <constraints>
                                                                                                <constraint firstAttribute="height" constant="30" id="mzP-iO-Xkl"/>
                                                                                            </constraints>
                                                                                            <fontDescription key="fontDescription" type="boldSystem" pointSize="14"/>
                                                                                            <color key="textColor" name="WalletAddText"/>
                                                                                            <nil key="highlightedColor"/>
                                                                                        </label>
                                                                                    </subviews>
                                                                                </stackView>
                                                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="4qL-zv-2mm">
                                                                                    <rect key="frame" x="8" y="127" width="134.66666666666666" height="40"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="height" constant="40" id="hJA-Pt-Tdx"/>
                                                                                    </constraints>
                                                                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                                </button>
                                                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="6cu-ul-pHe">
                                                                                    <rect key="frame" x="188.66666666666666" y="5" width="60" height="40"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="height" constant="40" id="9Me-Lb-Qtx"/>
                                                                                        <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="40" id="PBH-AP-RNm"/>
                                                                                    </constraints>
                                                                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                                </button>
                                                                            </subviews>
                                                                            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                                            <constraints>
                                                                                <constraint firstItem="7kN-kH-Xyi" firstAttribute="top" secondItem="tvW-0n-o4H" secondAttribute="top" constant="8" id="9yx-Oj-lrW"/>
                                                                                <constraint firstAttribute="trailing" secondItem="7kN-kH-Xyi" secondAttribute="trailing" constant="8" id="N82-Yt-Xlu"/>
                                                                                <constraint firstItem="7kN-kH-Xyi" firstAttribute="leading" secondItem="tvW-0n-o4H" secondAttribute="leading" constant="8" id="Sbb-a6-X0C"/>
                                                                                <constraint firstItem="6cu-ul-pHe" firstAttribute="centerY" secondItem="DuE-by-Wuc" secondAttribute="centerY" id="T3d-VI-tyB"/>
                                                                                <constraint firstItem="6cu-ul-pHe" firstAttribute="width" secondItem="DuE-by-Wuc" secondAttribute="width" multiplier="1.2" id="TDS-X4-096"/>
                                                                                <constraint firstItem="4qL-zv-2mm" firstAttribute="width" secondItem="L8q-2n-lSr" secondAttribute="width" id="TJc-f6-46F"/>
                                                                                <constraint firstAttribute="bottom" secondItem="7kN-kH-Xyi" secondAttribute="bottom" constant="8" id="lCw-xG-xSb"/>
                                                                                <constraint firstItem="4qL-zv-2mm" firstAttribute="centerY" secondItem="L8q-2n-lSr" secondAttribute="centerY" id="s2l-H7-jdY"/>
                                                                                <constraint firstItem="6cu-ul-pHe" firstAttribute="centerX" secondItem="DuE-by-Wuc" secondAttribute="centerX" id="vA0-jn-gAb"/>
                                                                                <constraint firstItem="4qL-zv-2mm" firstAttribute="centerX" secondItem="L8q-2n-lSr" secondAttribute="centerX" id="y4W-px-0qZ"/>
                                                                            </constraints>
                                                                        </view>
                                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_offerLine" translatesAutoresizingMaskIntoConstraints="NO" id="T2z-8P-au8">
                                                                            <rect key="frame" x="12" y="174" width="384" height="2"/>
                                                                            <constraints>
                                                                                <constraint firstAttribute="height" constant="2" id="UmU-9U-5rh"/>
                                                                            </constraints>
                                                                        </imageView>
                                                                    </subviews>
                                                                    <constraints>
                                                                        <constraint firstAttribute="bottom" secondItem="tvW-0n-o4H" secondAttribute="bottom" constant="4" id="5ig-ZK-oSp"/>
                                                                        <constraint firstItem="tvW-0n-o4H" firstAttribute="leading" secondItem="XnT-WE-b4O" secondAttribute="leading" constant="4" id="65J-Uf-2js"/>
                                                                        <constraint firstAttribute="trailing" secondItem="T2z-8P-au8" secondAttribute="trailing" constant="12" id="AKx-FR-RHM"/>
                                                                        <constraint firstItem="T2z-8P-au8" firstAttribute="leading" secondItem="XnT-WE-b4O" secondAttribute="leading" constant="12" id="JIM-pE-wCJ"/>
                                                                        <constraint firstAttribute="trailing" secondItem="tvW-0n-o4H" secondAttribute="trailing" constant="4" id="UQI-oJ-Wlo"/>
                                                                        <constraint firstItem="tvW-0n-o4H" firstAttribute="top" secondItem="XnT-WE-b4O" secondAttribute="top" constant="4" id="V6D-rV-sde"/>
                                                                        <constraint firstAttribute="bottom" secondItem="T2z-8P-au8" secondAttribute="bottom" constant="2" id="fgF-Je-OUg"/>
                                                                    </constraints>
                                                                </tableViewCellContentView>
                                                                <connections>
                                                                    <outlet property="btnCopyCode" destination="6cu-ul-pHe" id="A9h-2t-DJ4"/>
                                                                    <outlet property="btnTerms" destination="4qL-zv-2mm" id="jUg-1j-1HG"/>
                                                                    <outlet property="imgOfferLine" destination="T2z-8P-au8" id="Quu-g7-51Y"/>
                                                                    <outlet property="lblCashback" destination="unR-1P-8kK" id="Rem-ed-dgm"/>
                                                                    <outlet property="lblCashbackDetail" destination="qI9-GD-J7G" id="WOV-KY-zmf"/>
                                                                    <outlet property="lblCode" destination="ANe-we-04v" id="lHg-kH-b8p"/>
                                                                    <outlet property="viewCode" destination="j86-tY-m2C" id="JjO-K5-ytB"/>
                                                                    <outlet property="viewMain" destination="tvW-0n-o4H" id="wP5-df-MOf"/>
                                                                </connections>
                                                            </tableViewCell>
                                                        </prototypes>
                                                    </tableView>
                                                </subviews>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <constraints>
                                                    <constraint firstAttribute="bottom" secondItem="9tZ-62-PcU" secondAttribute="bottom" constant="12" id="9xu-Mc-3GR"/>
                                                    <constraint firstItem="9tZ-62-PcU" firstAttribute="top" secondItem="a2r-TO-1Rl" secondAttribute="bottom" constant="8" id="H2W-h7-zjd"/>
                                                    <constraint firstItem="a2r-TO-1Rl" firstAttribute="top" secondItem="8La-mu-6KZ" secondAttribute="top" constant="8" id="H9u-IV-shk"/>
                                                    <constraint firstAttribute="trailing" secondItem="9tZ-62-PcU" secondAttribute="trailing" constant="4" id="I6h-pP-Zhd"/>
                                                    <constraint firstItem="9tZ-62-PcU" firstAttribute="leading" secondItem="8La-mu-6KZ" secondAttribute="leading" constant="4" id="RPi-9X-Gat"/>
                                                    <constraint firstItem="a2r-TO-1Rl" firstAttribute="leading" secondItem="8La-mu-6KZ" secondAttribute="leading" constant="8" id="wQ9-st-VQ2"/>
                                                </constraints>
                                            </view>
                                        </subviews>
                                        <constraints>
                                            <constraint firstItem="4S1-Su-eG4" firstAttribute="width" secondItem="Qqg-tv-gwn" secondAttribute="width" id="315-AL-Sbm"/>
                                        </constraints>
                                    </stackView>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="Qqg-tv-gwn" firstAttribute="centerX" secondItem="bvt-Ey-yFe" secondAttribute="centerX" id="7NE-AB-JfP"/>
                                    <constraint firstItem="Qqg-tv-gwn" firstAttribute="bottom" secondItem="fVx-xt-2hG" secondAttribute="bottom" constant="-20" id="7qy-r1-DDu"/>
                                    <constraint firstItem="fVx-xt-2hG" firstAttribute="leading" secondItem="Qqg-tv-gwn" secondAttribute="leading" constant="-12" id="Gp4-wP-nwV"/>
                                    <constraint firstItem="Qqg-tv-gwn" firstAttribute="trailing" secondItem="fVx-xt-2hG" secondAttribute="trailing" id="Ikr-qU-NaC"/>
                                    <constraint firstItem="fVx-xt-2hG" firstAttribute="top" secondItem="Qqg-tv-gwn" secondAttribute="top" id="mrW-aL-p7c"/>
                                </constraints>
                                <viewLayoutGuide key="contentLayoutGuide" id="fVx-xt-2hG"/>
                                <viewLayoutGuide key="frameLayoutGuide" id="bvt-Ey-yFe"/>
                            </scrollView>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="JUk-LY-Ulx">
                                <rect key="frame" x="22" y="828" width="396" height="44"/>
                                <color key="backgroundColor" name="PrimaryColor"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="44" id="KM8-U8-o6Q"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                <state key="normal" title="Proceed">
                                    <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                </state>
                                <connections>
                                    <action selector="proceedAction:" destination="Vlg-Fs-MUY" eventType="touchUpInside" id="oqa-Cq-jYx"/>
                                </connections>
                            </button>
                            <view alpha="0.0" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ar3-ja-xTU">
                                <rect key="frame" x="0.0" y="124" width="440" height="764"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="MZG-NK-maZ">
                                        <rect key="frame" x="0.0" y="414" width="440" height="350"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Terms and Conditions" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="TIj-FE-dlW">
                                                <rect key="frame" x="142.33333333333337" y="8" width="155.66666666666663" height="34"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="34" id="cRt-g8-Hxa"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="15"/>
                                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Shn-Bu-SPU">
                                                <rect key="frame" x="386" y="8" width="38" height="34"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="34" id="Lwq-G6-Slk"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="15"/>
                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                <state key="normal" title="Done">
                                                    <color key="titleColor" red="0.015686274510000001" green="0.4941176471" blue="0.42745098040000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                </state>
                                                <connections>
                                                    <action selector="termsDoneTapped:" destination="Vlg-Fs-MUY" eventType="touchUpInside" id="VbP-RJ-6ai"/>
                                                </connections>
                                            </button>
                                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" scrollEnabled="NO" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="-1" estimatedSectionHeaderHeight="-1" sectionFooterHeight="-1" estimatedSectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="4lf-qy-bB6">
                                                <rect key="frame" x="12" y="54" width="416" height="284"/>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <prototypes>
                                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="none" indentationWidth="10" reuseIdentifier="cell" rowHeight="178" id="yhF-7c-H8r" customClass="TermsCell" customModule="NXC_EV_Solutions" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="50" width="416" height="178"/>
                                                        <autoresizingMask key="autoresizingMask"/>
                                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="yhF-7c-H8r" id="NWc-Bd-cKW">
                                                            <rect key="frame" x="0.0" y="0.0" width="416" height="178"/>
                                                            <autoresizingMask key="autoresizingMask"/>
                                                            <subviews>
                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="v3H-2N-mlj">
                                                                    <rect key="frame" x="4" y="4" width="408" height="170"/>
                                                                    <subviews>
                                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_blackCircle" translatesAutoresizingMaskIntoConstraints="NO" id="xbB-zd-UMR">
                                                                            <rect key="frame" x="8" y="12" width="8" height="8"/>
                                                                            <constraints>
                                                                                <constraint firstAttribute="height" constant="8" id="3KQ-qS-GoS"/>
                                                                                <constraint firstAttribute="width" constant="8" id="lzQ-hz-SdH"/>
                                                                            </constraints>
                                                                        </imageView>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Use code and get ₹30 cashback on your first transaction" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="QBn-6u-fZk">
                                                                            <rect key="frame" x="28" y="8" width="372" height="154"/>
                                                                            <constraints>
                                                                                <constraint firstAttribute="height" relation="lessThanOrEqual" constant="34" id="7uK-TD-AaJ"/>
                                                                            </constraints>
                                                                            <fontDescription key="fontDescription" type="system" weight="medium" pointSize="14"/>
                                                                            <color key="textColor" name="GrayPlaceholder"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                    </subviews>
                                                                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                                    <constraints>
                                                                        <constraint firstItem="QBn-6u-fZk" firstAttribute="leading" secondItem="xbB-zd-UMR" secondAttribute="trailing" constant="12" id="2cu-Ih-vqM"/>
                                                                        <constraint firstItem="xbB-zd-UMR" firstAttribute="top" secondItem="v3H-2N-mlj" secondAttribute="top" constant="12" id="H5h-bC-zhC"/>
                                                                        <constraint firstAttribute="bottom" secondItem="QBn-6u-fZk" secondAttribute="bottom" constant="8" id="JLF-AA-nxa"/>
                                                                        <constraint firstAttribute="trailing" secondItem="QBn-6u-fZk" secondAttribute="trailing" constant="8" id="gKH-3A-u72"/>
                                                                        <constraint firstItem="QBn-6u-fZk" firstAttribute="top" secondItem="v3H-2N-mlj" secondAttribute="top" constant="8" id="sLI-QP-qNP"/>
                                                                        <constraint firstItem="xbB-zd-UMR" firstAttribute="leading" secondItem="v3H-2N-mlj" secondAttribute="leading" constant="8" id="wLU-fC-oCx"/>
                                                                    </constraints>
                                                                </view>
                                                            </subviews>
                                                            <constraints>
                                                                <constraint firstAttribute="trailing" secondItem="v3H-2N-mlj" secondAttribute="trailing" constant="4" id="E6m-ZG-7mi"/>
                                                                <constraint firstItem="v3H-2N-mlj" firstAttribute="top" secondItem="NWc-Bd-cKW" secondAttribute="top" constant="4" id="gEa-hQ-dYi"/>
                                                                <constraint firstAttribute="bottom" secondItem="v3H-2N-mlj" secondAttribute="bottom" constant="4" id="jSP-er-pdQ"/>
                                                                <constraint firstItem="v3H-2N-mlj" firstAttribute="leading" secondItem="NWc-Bd-cKW" secondAttribute="leading" constant="4" id="nwf-5p-CT1"/>
                                                            </constraints>
                                                        </tableViewCellContentView>
                                                        <connections>
                                                            <outlet property="lblTerms" destination="QBn-6u-fZk" id="xOA-ze-Ndl"/>
                                                            <outlet property="viewMain" destination="v3H-2N-mlj" id="xEH-CB-3hx"/>
                                                        </connections>
                                                    </tableViewCell>
                                                </prototypes>
                                            </tableView>
                                        </subviews>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstItem="4lf-qy-bB6" firstAttribute="top" secondItem="TIj-FE-dlW" secondAttribute="bottom" constant="12" id="6Ad-Xz-2NR"/>
                                            <constraint firstItem="TIj-FE-dlW" firstAttribute="centerX" secondItem="MZG-NK-maZ" secondAttribute="centerX" id="BV9-6f-FYJ"/>
                                            <constraint firstItem="Shn-Bu-SPU" firstAttribute="top" secondItem="MZG-NK-maZ" secondAttribute="top" constant="8" id="HSt-mR-MgN"/>
                                            <constraint firstItem="4lf-qy-bB6" firstAttribute="leading" secondItem="MZG-NK-maZ" secondAttribute="leading" constant="12" id="HwW-AR-WcG"/>
                                            <constraint firstItem="TIj-FE-dlW" firstAttribute="top" secondItem="MZG-NK-maZ" secondAttribute="top" constant="8" id="WQ1-SO-IbQ"/>
                                            <constraint firstAttribute="height" constant="350" id="hNT-YI-J36"/>
                                            <constraint firstAttribute="trailing" secondItem="4lf-qy-bB6" secondAttribute="trailing" constant="12" id="iLJ-E0-kcj"/>
                                            <constraint firstAttribute="bottom" secondItem="4lf-qy-bB6" secondAttribute="bottom" constant="12" id="jvz-xH-HnL"/>
                                            <constraint firstAttribute="trailing" secondItem="Shn-Bu-SPU" secondAttribute="trailing" constant="16" id="lr8-ij-aHu"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.5" colorSpace="custom" customColorSpace="sRGB"/>
                                <constraints>
                                    <constraint firstItem="MZG-NK-maZ" firstAttribute="centerX" secondItem="ar3-ja-xTU" secondAttribute="centerX" id="KM0-oT-smo"/>
                                    <constraint firstItem="MZG-NK-maZ" firstAttribute="leading" secondItem="ar3-ja-xTU" secondAttribute="leading" id="dbt-qj-kga"/>
                                    <constraint firstAttribute="trailing" secondItem="MZG-NK-maZ" secondAttribute="trailing" id="fcJ-n0-WiY"/>
                                    <constraint firstAttribute="bottom" secondItem="MZG-NK-maZ" secondAttribute="bottom" id="vtq-lp-DNN"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="Nb8-0q-tra"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="YXG-Gi-HrT" firstAttribute="top" secondItem="hY6-6Y-gvs" secondAttribute="bottom" constant="8" id="0hm-ia-AR3"/>
                            <constraint firstItem="YXG-Gi-HrT" firstAttribute="centerX" secondItem="I3y-cN-e3d" secondAttribute="centerX" id="5zZ-qO-X5M"/>
                            <constraint firstItem="ar3-ja-xTU" firstAttribute="trailing" secondItem="Nb8-0q-tra" secondAttribute="trailing" id="7Gy-rO-tzd"/>
                            <constraint firstItem="ar3-ja-xTU" firstAttribute="top" secondItem="Nb8-0q-tra" secondAttribute="top" id="Doj-kE-7D2"/>
                            <constraint firstItem="JUk-LY-Ulx" firstAttribute="width" secondItem="Nb8-0q-tra" secondAttribute="width" multiplier="0.9" id="RJT-pe-t74"/>
                            <constraint firstItem="JUk-LY-Ulx" firstAttribute="top" secondItem="YXG-Gi-HrT" secondAttribute="bottom" constant="8" id="Rzd-mo-IQ6"/>
                            <constraint firstItem="hY6-6Y-gvs" firstAttribute="leading" secondItem="Nb8-0q-tra" secondAttribute="leading" id="Zvp-FT-e5G"/>
                            <constraint firstItem="Nb8-0q-tra" firstAttribute="trailing" secondItem="YXG-Gi-HrT" secondAttribute="trailing" id="aP1-Cn-IFt"/>
                            <constraint firstItem="Nb8-0q-tra" firstAttribute="trailing" secondItem="hY6-6Y-gvs" secondAttribute="trailing" id="dDY-Q7-XfS"/>
                            <constraint firstItem="YXG-Gi-HrT" firstAttribute="leading" secondItem="Nb8-0q-tra" secondAttribute="leading" id="dlx-rp-6Wg"/>
                            <constraint firstItem="ar3-ja-xTU" firstAttribute="leading" secondItem="Nb8-0q-tra" secondAttribute="leading" id="i20-li-UQS"/>
                            <constraint firstItem="ar3-ja-xTU" firstAttribute="bottom" secondItem="Nb8-0q-tra" secondAttribute="bottom" id="p9K-js-Xm6"/>
                            <constraint firstItem="JUk-LY-Ulx" firstAttribute="centerX" secondItem="I3y-cN-e3d" secondAttribute="centerX" id="vFu-QV-BXJ"/>
                            <constraint firstItem="Nb8-0q-tra" firstAttribute="bottom" secondItem="JUk-LY-Ulx" secondAttribute="bottom" constant="16" id="w0y-4b-jQT"/>
                            <constraint firstItem="hY6-6Y-gvs" firstAttribute="top" secondItem="Nb8-0q-tra" secondAttribute="top" id="yoy-aT-oKY"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="btnApplyPromo" destination="bzo-76-m4N" id="1XG-gi-RBX"/>
                        <outlet property="btnBack" destination="Z4v-v9-5P4" id="lmf-qO-HME"/>
                        <outlet property="btnDone" destination="Shn-Bu-SPU" id="YEf-sh-hYu"/>
                        <outlet property="btnProceed" destination="JUk-LY-Ulx" id="InZ-Df-naq"/>
                        <outlet property="lblEnterAmountTitle" destination="S4F-PG-j4G" id="m4V-QS-wFP"/>
                        <outlet property="lblEnterPromoTitle" destination="D8E-lB-CAP" id="rzR-Ws-tA8"/>
                        <outlet property="lblNoteTitle" destination="2Sf-sd-v6Z" id="QBL-YB-fm3"/>
                        <outlet property="lblPromoNotValid" destination="dSP-JL-HSl" id="fMg-O1-ciF"/>
                        <outlet property="lblPromotionsTitle" destination="a2r-TO-1Rl" id="I0q-pq-Hi7"/>
                        <outlet property="lblTCTitle" destination="TIj-FE-dlW" id="7Ue-Th-d4i"/>
                        <outlet property="lblTitle" destination="rcX-Fg-Q3L" id="RB6-LJ-hFH"/>
                        <outlet property="pageController" destination="UCa-mX-yG0" id="Lox-dd-7YY"/>
                        <outlet property="scrollBanners" destination="QzG-LW-QP2" id="Qhp-d7-wAf"/>
                        <outlet property="scrollDetails" destination="YXG-Gi-HrT" id="Mt7-y5-QnS"/>
                        <outlet property="tableCoupon" destination="9tZ-62-PcU" id="WBA-4X-hcN"/>
                        <outlet property="tableHeight" destination="mtL-DS-CO7" id="hp5-JK-OtL"/>
                        <outlet property="tableTerms" destination="4lf-qy-bB6" id="IG4-uz-BWc"/>
                        <outlet property="txtAmount" destination="h5T-v9-XDv" id="Eev-5F-4Z5"/>
                        <outlet property="txtPromoCode" destination="YV1-AR-UNP" id="nUi-TU-Y2M"/>
                        <outlet property="viewBanner" destination="Oi1-w1-Ceu" id="Mzk-dB-Bxp"/>
                        <outlet property="viewBgTerms" destination="ar3-ja-xTU" id="J66-ZE-nDf"/>
                        <outlet property="viewMainTerms" destination="MZG-NK-maZ" id="1d9-tR-Mtk"/>
                        <outlet property="viewNote" destination="4S1-Su-eG4" id="i0E-FW-eWp"/>
                        <outlet property="viewOfferCoupons" destination="8La-mu-6KZ" id="Ew7-ub-SAQ"/>
                        <outlet property="viewPromoCode" destination="JIF-Yg-lpV" id="9I5-2p-r2N"/>
                        <outlet property="viewRazor" destination="AdE-YG-LXv" id="7gP-tf-Gcb"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="S6f-Dg-Lhi" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1525.5999999999999" y="72.413793103448285"/>
        </scene>
        <!--Payment DetailsVC-->
        <scene sceneID="oh3-NE-9ny">
            <objects>
                <viewController storyboardIdentifier="PaymentDetailsVC" id="xxM-z7-U42" customClass="PaymentDetailsVC" customModule="NXC_EV_Solutions" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="N0M-F7-gT5">
                        <rect key="frame" x="0.0" y="0.0" width="440" height="956"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="CcJ-qd-lMf">
                                <rect key="frame" x="0.0" y="124" width="440" height="44"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="yaP-yY-eBs">
                                        <rect key="frame" x="12" y="5" width="34" height="34"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="34" id="hVH-OQ-g3D"/>
                                            <constraint firstAttribute="width" constant="34" id="yqP-MQ-JnD"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="8" minY="6" maxX="8" maxY="6"/>
                                        <state key="normal" image="ic_back_arrow"/>
                                        <connections>
                                            <action selector="backAction:" destination="xxM-z7-U42" eventType="touchUpInside" id="o2i-fj-9pe"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Payment Details" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="QtA-pn-olt">
                                        <rect key="frame" x="155.66666666666666" y="11.999999999999998" width="128.99999999999997" height="20.333333333333329"/>
                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                        <color key="textColor" red="0.070588235289999995" green="0.070588235289999995" blue="0.070588235289999995" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="QtA-pn-olt" firstAttribute="centerX" secondItem="CcJ-qd-lMf" secondAttribute="centerX" id="3U5-O1-mps"/>
                                    <constraint firstItem="QtA-pn-olt" firstAttribute="centerY" secondItem="CcJ-qd-lMf" secondAttribute="centerY" id="Muk-H8-bFD"/>
                                    <constraint firstItem="yaP-yY-eBs" firstAttribute="centerY" secondItem="CcJ-qd-lMf" secondAttribute="centerY" id="Yvh-Gr-dRY"/>
                                    <constraint firstItem="yaP-yY-eBs" firstAttribute="leading" secondItem="CcJ-qd-lMf" secondAttribute="leading" constant="12" id="dAT-p1-zai"/>
                                    <constraint firstAttribute="height" constant="44" id="jdr-ug-8Sh"/>
                                </constraints>
                            </view>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ok3-D0-AJU">
                                <rect key="frame" x="22" y="828" width="396" height="44"/>
                                <color key="backgroundColor" name="PrimaryColor"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="44" id="HEp-I6-bwy"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                <state key="normal" title="Add Complaint">
                                    <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                </state>
                                <connections>
                                    <action selector="addComplainAction:" destination="xxM-z7-U42" eventType="touchUpInside" id="Zd3-VT-7eS"/>
                                </connections>
                            </button>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="B2G-ZI-bgd">
                                <rect key="frame" x="22" y="180" width="396" height="186"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" translatesAutoresizingMaskIntoConstraints="NO" id="eSW-WZ-Bcf">
                                        <rect key="frame" x="8" y="8" width="380" height="170"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" translatesAutoresizingMaskIntoConstraints="NO" id="66W-1V-9bB">
                                                <rect key="frame" x="0.0" y="0.0" width="380" height="34"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Transaction ID" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="rSx-jk-QiK">
                                                        <rect key="frame" x="0.0" y="0.0" width="190" height="34"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="34" id="Fad-so-P3G"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                        <color key="textColor" name="PrimarySelection"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="#89865763" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="frS-lN-Gjw">
                                                        <rect key="frame" x="190" y="0.0" width="190" height="34"/>
                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                            </stackView>
                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" translatesAutoresizingMaskIntoConstraints="NO" id="Bhf-eB-1B6">
                                                <rect key="frame" x="0.0" y="34" width="380" height="34"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Date/Time" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="GcB-EE-dRM">
                                                        <rect key="frame" x="0.0" y="0.0" width="190" height="34"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="34" id="fQs-1w-Fww"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                        <color key="textColor" name="PrimarySelection"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="20 Aug 2021 03:15 PM" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="6Wa-xI-FV9">
                                                        <rect key="frame" x="190" y="0.0" width="190" height="34"/>
                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                            </stackView>
                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" translatesAutoresizingMaskIntoConstraints="NO" id="hdK-40-4vt">
                                                <rect key="frame" x="0.0" y="68" width="380" height="34"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Amount" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="dxC-dC-Pdw">
                                                        <rect key="frame" x="0.0" y="0.0" width="190" height="34"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="34" id="cQ2-39-gfK"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                        <color key="textColor" name="PrimarySelection"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="₹ 2500" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="fh4-Ab-Con">
                                                        <rect key="frame" x="190" y="0.0" width="190" height="34"/>
                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                            </stackView>
                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" translatesAutoresizingMaskIntoConstraints="NO" id="e9g-Mq-rdV">
                                                <rect key="frame" x="0.0" y="102" width="380" height="34"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Payment Mode" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="cLB-pB-Nwc">
                                                        <rect key="frame" x="0.0" y="0.0" width="190" height="34"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="34" id="yrQ-Jy-bB8"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                        <color key="textColor" name="PrimarySelection"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="RazorPay" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="0rX-fx-K1Y">
                                                        <rect key="frame" x="190" y="0.0" width="190" height="34"/>
                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                            </stackView>
                                            <stackView opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Nvz-gg-bdI">
                                                <rect key="frame" x="0.0" y="136" width="380" height="34"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Payment Status" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="eOq-o5-lK1">
                                                        <rect key="frame" x="0.0" y="0.0" width="266" height="34"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="34" id="Kfm-gO-3hI"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                        <color key="textColor" name="PrimarySelection"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Success" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="dqU-B1-h98">
                                                        <rect key="frame" x="266" y="0.0" width="114" height="34"/>
                                                        <color key="backgroundColor" red="0.1647058824" green="0.72941176470000002" blue="0.0" alpha="0.10000000000000001" colorSpace="custom" customColorSpace="sRGB"/>
                                                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                        <color key="textColor" red="0.1647058824" green="0.72941176470000002" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstItem="dqU-B1-h98" firstAttribute="width" secondItem="Nvz-gg-bdI" secondAttribute="width" multiplier="0.3" id="YdP-br-JrM"/>
                                                </constraints>
                                            </stackView>
                                        </subviews>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="bottom" secondItem="eSW-WZ-Bcf" secondAttribute="bottom" constant="8" id="dFp-zo-zdi"/>
                                    <constraint firstAttribute="trailing" secondItem="eSW-WZ-Bcf" secondAttribute="trailing" constant="8" id="dhO-xJ-gju"/>
                                    <constraint firstItem="eSW-WZ-Bcf" firstAttribute="leading" secondItem="B2G-ZI-bgd" secondAttribute="leading" constant="8" id="fL5-ap-qAo"/>
                                    <constraint firstItem="eSW-WZ-Bcf" firstAttribute="top" secondItem="B2G-ZI-bgd" secondAttribute="top" constant="8" id="zFt-lG-PCr"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="uid-Wf-6vP"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="B2G-ZI-bgd" firstAttribute="top" secondItem="CcJ-qd-lMf" secondAttribute="bottom" constant="12" id="J0t-aO-MRl"/>
                            <constraint firstItem="uid-Wf-6vP" firstAttribute="trailing" secondItem="CcJ-qd-lMf" secondAttribute="trailing" id="L4H-Ud-HTn"/>
                            <constraint firstItem="ok3-D0-AJU" firstAttribute="width" secondItem="uid-Wf-6vP" secondAttribute="width" multiplier="0.9" id="Opl-Yg-dgd"/>
                            <constraint firstItem="ok3-D0-AJU" firstAttribute="centerX" secondItem="N0M-F7-gT5" secondAttribute="centerX" id="cW6-bN-NDE"/>
                            <constraint firstItem="CcJ-qd-lMf" firstAttribute="leading" secondItem="uid-Wf-6vP" secondAttribute="leading" id="czF-YE-Cxg"/>
                            <constraint firstItem="uid-Wf-6vP" firstAttribute="bottom" secondItem="ok3-D0-AJU" secondAttribute="bottom" constant="16" id="dvZ-Ke-O3O"/>
                            <constraint firstItem="B2G-ZI-bgd" firstAttribute="width" secondItem="uid-Wf-6vP" secondAttribute="width" multiplier="0.9" id="ouT-Ad-DOI"/>
                            <constraint firstItem="B2G-ZI-bgd" firstAttribute="centerX" secondItem="N0M-F7-gT5" secondAttribute="centerX" id="pEH-ra-fDc"/>
                            <constraint firstItem="CcJ-qd-lMf" firstAttribute="top" secondItem="uid-Wf-6vP" secondAttribute="top" id="xgf-b2-fmT"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="btnAddComplain" destination="ok3-D0-AJU" id="P9h-m3-rZK"/>
                        <outlet property="btnBack" destination="yaP-yY-eBs" id="iA4-oB-tCl"/>
                        <outlet property="lblAmount" destination="fh4-Ab-Con" id="Qa7-Zt-I8f"/>
                        <outlet property="lblDateTime" destination="6Wa-xI-FV9" id="pLB-Ss-rFB"/>
                        <outlet property="lblPaymentMode" destination="0rX-fx-K1Y" id="TXq-nl-jso"/>
                        <outlet property="lblStatus" destination="dqU-B1-h98" id="G4H-s3-Zpn"/>
                        <outlet property="lblTransactionID" destination="frS-lN-Gjw" id="Z1h-PX-IMW"/>
                        <outlet property="viewMain" destination="B2G-ZI-bgd" id="SpR-Y1-9v3"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="qzw-9V-se3" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="2236" y="72.413793103448285"/>
        </scene>
        <!--Transaction DetailsVC-->
        <scene sceneID="VU4-DB-uV4">
            <objects>
                <viewController storyboardIdentifier="TransactionDetailsVC" id="lty-oh-6rU" customClass="TransactionDetailsVC" customModule="NXC_EV_Solutions" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Y4Y-Oq-XTq">
                        <rect key="frame" x="0.0" y="0.0" width="440" height="956"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="cqG-Qz-LRT">
                                <rect key="frame" x="0.0" y="124" width="440" height="44"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="lAv-tp-aA8">
                                        <rect key="frame" x="12" y="5" width="34" height="34"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="34" id="LQX-YS-V7J"/>
                                            <constraint firstAttribute="height" constant="34" id="o52-yS-2kW"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="8" minY="6" maxX="8" maxY="6"/>
                                        <state key="normal" image="ic_back_arrow"/>
                                        <connections>
                                            <action selector="backButtonTapped:" destination="lty-oh-6rU" eventType="touchUpInside" id="lz0-Af-BkE"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Payment Details" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="lWh-Ya-U41">
                                        <rect key="frame" x="155.66666666666666" y="11.999999999999998" width="128.99999999999997" height="20.333333333333329"/>
                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                        <color key="textColor" red="0.070588235289999995" green="0.070588235289999995" blue="0.070588235289999995" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <button opaque="NO" alpha="0.0" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="KGM-VH-0xw">
                                        <rect key="frame" x="394" y="5" width="34" height="34"/>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="34" id="WeC-x0-R3f"/>
                                            <constraint firstAttribute="height" constant="34" id="ZwK-Yj-a4O"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="8" minY="8" maxX="8" maxY="8"/>
                                        <state key="normal" image="ic_filter"/>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="44" id="3yh-xK-dkC"/>
                                    <constraint firstItem="lWh-Ya-U41" firstAttribute="centerY" secondItem="cqG-Qz-LRT" secondAttribute="centerY" id="CfT-Uh-VmG"/>
                                    <constraint firstAttribute="trailing" secondItem="KGM-VH-0xw" secondAttribute="trailing" constant="12" id="O09-1W-7yr"/>
                                    <constraint firstItem="lAv-tp-aA8" firstAttribute="centerY" secondItem="cqG-Qz-LRT" secondAttribute="centerY" id="Tqk-fQ-s5k"/>
                                    <constraint firstItem="KGM-VH-0xw" firstAttribute="centerY" secondItem="cqG-Qz-LRT" secondAttribute="centerY" id="Ual-2H-6N1"/>
                                    <constraint firstItem="lWh-Ya-U41" firstAttribute="centerX" secondItem="cqG-Qz-LRT" secondAttribute="centerX" id="YTE-H9-JTb"/>
                                    <constraint firstItem="lAv-tp-aA8" firstAttribute="leading" secondItem="cqG-Qz-LRT" secondAttribute="leading" constant="12" id="n3O-Ew-mCf"/>
                                </constraints>
                            </view>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="6M5-5o-GLL">
                                <rect key="frame" x="22" y="828" width="396" height="44"/>
                                <color key="backgroundColor" name="PrimaryColor"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="44" id="yVa-xo-1f8"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                <state key="normal" title="Add Complaint">
                                    <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                </state>
                                <connections>
                                    <action selector="addComplaintTapped:" destination="lty-oh-6rU" eventType="touchUpInside" id="TiZ-sF-RYJ"/>
                                </connections>
                            </button>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Ly0-R0-Ngh">
                                <rect key="frame" x="0.0" y="168" width="440" height="644"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="6" translatesAutoresizingMaskIntoConstraints="NO" id="Ij6-6n-wIH">
                                        <rect key="frame" x="6" y="4" width="428" height="1272"/>
                                        <subviews>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Uub-Qz-TWf">
                                                <rect key="frame" x="0.0" y="0.0" width="428" height="197"/>
                                                <subviews>
                                                    <stackView opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="Lku-tD-gF9">
                                                        <rect key="frame" x="0.0" y="0.0" width="428" height="193"/>
                                                        <subviews>
                                                            <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="CWM-GL-GzV">
                                                                <rect key="frame" x="0.0" y="0.0" width="428" height="34"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Transaction" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="VKK-JB-aTN">
                                                                        <rect key="frame" x="4" y="0.0" width="416" height="34"/>
                                                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="15"/>
                                                                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                                <color key="backgroundColor" name="Primary"/>
                                                                <constraints>
                                                                    <constraint firstItem="VKK-JB-aTN" firstAttribute="top" secondItem="CWM-GL-GzV" secondAttribute="top" id="Bjq-LS-AgM"/>
                                                                    <constraint firstAttribute="bottom" secondItem="VKK-JB-aTN" secondAttribute="bottom" id="h5a-Ri-IAe"/>
                                                                    <constraint firstAttribute="trailing" secondItem="VKK-JB-aTN" secondAttribute="trailing" constant="8" id="kES-1n-5XG"/>
                                                                    <constraint firstItem="VKK-JB-aTN" firstAttribute="leading" secondItem="CWM-GL-GzV" secondAttribute="leading" constant="4" id="qYc-2j-95y"/>
                                                                    <constraint firstAttribute="height" constant="34" id="sQf-Qh-hN8"/>
                                                                </constraints>
                                                            </view>
                                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="UsD-dM-Hc7">
                                                                <rect key="frame" x="10.666666666666657" y="37" width="406.66666666666674" height="50"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Transaction ID" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="9ra-gC-bH3">
                                                                        <rect key="frame" x="0.0" y="0.0" width="202" height="50"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Of0-Wg-tXE">
                                                                        <rect key="frame" x="205" y="0.0" width="201.66666666666663" height="50"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="agg-uZ-M7B">
                                                                <rect key="frame" x="10.666666666666657" y="90" width="406.66666666666674" height="50"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Date" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="1AY-cN-XbA">
                                                                        <rect key="frame" x="0.0" y="0.0" width="202" height="50"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="9bd-dK-kJs">
                                                                        <rect key="frame" x="205" y="0.0" width="201.66666666666663" height="50"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="OGl-l1-cXJ">
                                                                <rect key="frame" x="10.666666666666657" y="143" width="406.66666666666674" height="50"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Time" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="UI8-UP-o2t">
                                                                        <rect key="frame" x="0.0" y="0.0" width="202" height="50"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="E9a-a8-5fJ">
                                                                        <rect key="frame" x="205" y="0.0" width="201.66666666666663" height="50"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstItem="UsD-dM-Hc7" firstAttribute="width" secondItem="Lku-tD-gF9" secondAttribute="width" multiplier="0.95" id="4Nd-bB-9Wr"/>
                                                            <constraint firstItem="agg-uZ-M7B" firstAttribute="width" secondItem="Lku-tD-gF9" secondAttribute="width" multiplier="0.95" id="aIl-lC-y4H"/>
                                                            <constraint firstItem="OGl-l1-cXJ" firstAttribute="width" secondItem="Lku-tD-gF9" secondAttribute="width" multiplier="0.95" id="qBf-Tb-iWX"/>
                                                            <constraint firstItem="CWM-GL-GzV" firstAttribute="width" secondItem="Lku-tD-gF9" secondAttribute="width" id="rW5-ID-lWP"/>
                                                        </constraints>
                                                    </stackView>
                                                </subviews>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <constraints>
                                                    <constraint firstAttribute="trailing" secondItem="Lku-tD-gF9" secondAttribute="trailing" id="9h8-E3-zNy"/>
                                                    <constraint firstItem="Lku-tD-gF9" firstAttribute="top" secondItem="Uub-Qz-TWf" secondAttribute="top" id="MxU-4d-QuB"/>
                                                    <constraint firstAttribute="bottom" secondItem="Lku-tD-gF9" secondAttribute="bottom" constant="4" id="XyF-Kh-ZbI"/>
                                                    <constraint firstItem="Lku-tD-gF9" firstAttribute="leading" secondItem="Uub-Qz-TWf" secondAttribute="leading" id="wd1-dl-4NH"/>
                                                </constraints>
                                            </view>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="qL2-5R-dtL">
                                                <rect key="frame" x="0.0" y="203" width="428" height="122"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="gKP-ba-ZLt">
                                                        <rect key="frame" x="0.0" y="0.0" width="428" height="118"/>
                                                        <subviews>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="K7W-up-xuR">
                                                                <rect key="frame" x="0.0" y="0.0" width="428" height="34"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Charger Details" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="gar-Ne-323">
                                                                        <rect key="frame" x="4" y="0.0" width="416" height="34"/>
                                                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="15"/>
                                                                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                                <color key="backgroundColor" name="Primary"/>
                                                                <constraints>
                                                                    <constraint firstItem="gar-Ne-323" firstAttribute="top" secondItem="K7W-up-xuR" secondAttribute="top" id="Qpd-QY-weP"/>
                                                                    <constraint firstItem="gar-Ne-323" firstAttribute="leading" secondItem="K7W-up-xuR" secondAttribute="leading" constant="4" id="Umq-rV-1xU"/>
                                                                    <constraint firstAttribute="height" constant="34" id="aRp-Vg-7bk"/>
                                                                    <constraint firstAttribute="trailing" secondItem="gar-Ne-323" secondAttribute="trailing" constant="8" id="e5J-Yk-GVs"/>
                                                                    <constraint firstAttribute="bottom" secondItem="gar-Ne-323" secondAttribute="bottom" id="tuM-hM-E2y"/>
                                                                </constraints>
                                                            </view>
                                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="BZ2-S0-d7H">
                                                                <rect key="frame" x="10.666666666666657" y="37" width="406.66666666666674" height="28"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Station Name" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="w7C-18-zIp">
                                                                        <rect key="frame" x="0.0" y="0.0" width="202" height="28"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="right" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="wOm-Mf-NR5">
                                                                        <rect key="frame" x="205" y="0.0" width="201.66666666666663" height="28"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="AFc-q5-1Ci">
                                                                <rect key="frame" x="10.666666666666657" y="68" width="406.66666666666674" height="50"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Charger" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="bWQ-Ge-cKX">
                                                                        <rect key="frame" x="0.0" y="0.0" width="202" height="50"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Eei-jf-Qib">
                                                                        <rect key="frame" x="205" y="0.0" width="201.66666666666663" height="50"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstItem="BZ2-S0-d7H" firstAttribute="width" secondItem="gKP-ba-ZLt" secondAttribute="width" multiplier="0.95" id="AHo-F6-FuZ"/>
                                                            <constraint firstItem="AFc-q5-1Ci" firstAttribute="width" secondItem="gKP-ba-ZLt" secondAttribute="width" multiplier="0.95" id="VcV-jE-Vue"/>
                                                            <constraint firstItem="K7W-up-xuR" firstAttribute="width" secondItem="gKP-ba-ZLt" secondAttribute="width" id="ywu-fq-eNS"/>
                                                        </constraints>
                                                    </stackView>
                                                </subviews>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <constraints>
                                                    <constraint firstItem="gKP-ba-ZLt" firstAttribute="top" secondItem="qL2-5R-dtL" secondAttribute="top" id="2X3-Ap-3I6"/>
                                                    <constraint firstAttribute="trailing" secondItem="gKP-ba-ZLt" secondAttribute="trailing" id="Ds6-he-f1Y"/>
                                                    <constraint firstAttribute="bottom" secondItem="gKP-ba-ZLt" secondAttribute="bottom" constant="4" id="GvZ-TS-L9t"/>
                                                    <constraint firstItem="gKP-ba-ZLt" firstAttribute="leading" secondItem="qL2-5R-dtL" secondAttribute="leading" id="Mun-fT-PlJ"/>
                                                </constraints>
                                            </view>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="7ml-KF-NGZ">
                                                <rect key="frame" x="0.0" y="331" width="428" height="250"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="k54-5p-GaW">
                                                        <rect key="frame" x="0.0" y="0.0" width="428" height="246"/>
                                                        <subviews>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="2Pp-50-fT8">
                                                                <rect key="frame" x="0.0" y="0.0" width="428" height="34"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Fix Charge" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Efw-gE-clV">
                                                                        <rect key="frame" x="4" y="0.0" width="416" height="34"/>
                                                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="15"/>
                                                                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                                <color key="backgroundColor" name="Primary"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="34" id="CYb-PI-ZYk"/>
                                                                    <constraint firstAttribute="bottom" secondItem="Efw-gE-clV" secondAttribute="bottom" id="Nts-8c-402"/>
                                                                    <constraint firstItem="Efw-gE-clV" firstAttribute="top" secondItem="2Pp-50-fT8" secondAttribute="top" id="Ver-SB-iec"/>
                                                                    <constraint firstAttribute="trailing" secondItem="Efw-gE-clV" secondAttribute="trailing" constant="8" id="cQz-Vg-8MV"/>
                                                                    <constraint firstItem="Efw-gE-clV" firstAttribute="leading" secondItem="2Pp-50-fT8" secondAttribute="leading" constant="4" id="lrp-7S-ng9"/>
                                                                </constraints>
                                                            </view>
                                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="bXz-G0-JMG">
                                                                <rect key="frame" x="10.666666666666657" y="37" width="406.66666666666674" height="50"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Free Unit" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="w06-Uz-hen">
                                                                        <rect key="frame" x="0.0" y="0.0" width="202" height="50"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="FYs-fB-iHl">
                                                                        <rect key="frame" x="205" y="0.0" width="201.66666666666663" height="50"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="j7q-7E-oMA">
                                                                <rect key="frame" x="10.666666666666657" y="90" width="406.66666666666674" height="50"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Net Total" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Pt1-QH-f2a">
                                                                        <rect key="frame" x="0.0" y="0.0" width="202" height="50"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="50m-W7-eF9">
                                                                        <rect key="frame" x="205" y="0.0" width="201.66666666666663" height="50"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="nXz-G1-JMH">
                                                                <rect key="frame" x="10.666666666666657" y="143" width="406.66666666666674" height="50"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Tax" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="mXz-G2-JMI">
                                                                        <rect key="frame" x="0.0" y="0.0" width="202" height="50"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="pXz-G3-JMJ">
                                                                        <rect key="frame" x="205" y="0.0" width="201.66666666666663" height="50"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="rXz-G5-JML">
                                                                <rect key="frame" x="10.666666666666657" y="196" width="406.66666666666674" height="50"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Total" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="sXz-G6-JMM">
                                                                        <rect key="frame" x="0.0" y="0.0" width="202" height="50"/>
                                                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="14"/>
                                                                        <color key="textColor" name="Primary"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="tXz-G7-JMN">
                                                                        <rect key="frame" x="205" y="0.0" width="201.66666666666663" height="50"/>
                                                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="14"/>
                                                                        <color key="textColor" red="0.0" green="0.70980392160000005" blue="0.2901960784" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstItem="bXz-G0-JMG" firstAttribute="width" secondItem="k54-5p-GaW" secondAttribute="width" multiplier="0.95" id="6hL-81-g9H"/>
                                                            <constraint firstItem="j7q-7E-oMA" firstAttribute="width" secondItem="k54-5p-GaW" secondAttribute="width" multiplier="0.95" id="FNJ-sJ-K8i"/>
                                                            <constraint firstItem="2Pp-50-fT8" firstAttribute="width" secondItem="k54-5p-GaW" secondAttribute="width" id="JLx-Tp-QMF"/>
                                                            <constraint firstItem="nXz-G1-JMH" firstAttribute="width" secondItem="k54-5p-GaW" secondAttribute="width" multiplier="0.95" id="vXz-G9-JMP"/>
                                                            <constraint firstItem="rXz-G5-JML" firstAttribute="width" secondItem="k54-5p-GaW" secondAttribute="width" multiplier="0.95" id="wXz-GA-JMQ"/>
                                                        </constraints>
                                                    </stackView>
                                                </subviews>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <constraints>
                                                    <constraint firstAttribute="bottom" secondItem="k54-5p-GaW" secondAttribute="bottom" constant="4" id="7MV-EP-ggs"/>
                                                    <constraint firstItem="k54-5p-GaW" firstAttribute="top" secondItem="7ml-KF-NGZ" secondAttribute="top" id="Cr1-k4-SGg"/>
                                                    <constraint firstAttribute="trailing" secondItem="k54-5p-GaW" secondAttribute="trailing" id="bP3-yf-R2V"/>
                                                    <constraint firstItem="k54-5p-GaW" firstAttribute="leading" secondItem="7ml-KF-NGZ" secondAttribute="leading" id="qrg-os-4hB"/>
                                                </constraints>
                                            </view>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="SSq-w0-b46">
                                                <rect key="frame" x="0.0" y="587" width="428" height="409"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="KS3-Mo-vpM">
                                                        <rect key="frame" x="0.0" y="0.0" width="428" height="405"/>
                                                        <subviews>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="LxW-XO-k4c">
                                                                <rect key="frame" x="0.0" y="0.0" width="428" height="34"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Consumption Charges" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="8Vu-sY-QUW">
                                                                        <rect key="frame" x="4" y="0.0" width="416" height="34"/>
                                                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="15"/>
                                                                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                                <color key="backgroundColor" name="Primary"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="trailing" secondItem="8Vu-sY-QUW" secondAttribute="trailing" constant="8" id="8fV-Ke-sOe"/>
                                                                    <constraint firstItem="8Vu-sY-QUW" firstAttribute="top" secondItem="LxW-XO-k4c" secondAttribute="top" id="BSR-Ao-cbn"/>
                                                                    <constraint firstAttribute="height" constant="34" id="Brf-RE-nGz"/>
                                                                    <constraint firstAttribute="bottom" secondItem="8Vu-sY-QUW" secondAttribute="bottom" id="pIh-MR-6ty"/>
                                                                    <constraint firstItem="8Vu-sY-QUW" firstAttribute="leading" secondItem="LxW-XO-k4c" secondAttribute="leading" constant="4" id="zQb-L2-oWA"/>
                                                                </constraints>
                                                            </view>
                                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="CFp-bT-rEB">
                                                                <rect key="frame" x="10.666666666666657" y="37" width="406.66666666666674" height="50"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Used Unit" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="eFc-Tk-HO1">
                                                                        <rect key="frame" x="0.0" y="0.0" width="202" height="50"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="o1c-OB-wJT">
                                                                        <rect key="frame" x="205" y="0.0" width="201.66666666666663" height="50"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillProportionally" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="Uly-YA-fae">
                                                                <rect key="frame" x="10.666666666666657" y="90" width="406.66666666666674" height="50"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Free Unit(-)" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontForContentSizeCategory="YES" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="lQi-Li-jRI">
                                                                        <rect key="frame" x="0.0" y="0.0" width="75" height="50"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontForContentSizeCategory="YES" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Opi-fC-zyl">
                                                                        <rect key="frame" x="78" y="0.0" width="328.66666666666669" height="50"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="cXz-G1-JMH">
                                                                <rect key="frame" x="10.666666666666657" y="143" width="406.66666666666674" height="50"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Chargeable Unit" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="dXz-G2-JMI">
                                                                        <rect key="frame" x="0.0" y="0.0" width="202" height="50"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="eXz-G3-JMJ">
                                                                        <rect key="frame" x="205" y="0.0" width="201.66666666666663" height="50"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="7a4-4c-SwC">
                                                                <rect key="frame" x="10.666666666666657" y="196" width="406.66666666666674" height="50"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Per Unit Price" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="TA6-Zb-tz0">
                                                                        <rect key="frame" x="0.0" y="0.0" width="202" height="50"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="NIX-Nv-5q1">
                                                                        <rect key="frame" x="205" y="0.0" width="201.66666666666663" height="50"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="gXz-G5-JML">
                                                                <rect key="frame" x="10.666666666666657" y="249" width="406.66666666666674" height="50"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Net Total" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="hXz-G6-JMM">
                                                                        <rect key="frame" x="0.0" y="0.0" width="202" height="50"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="iXz-G7-JMN">
                                                                        <rect key="frame" x="205" y="0.0" width="201.66666666666663" height="50"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="kXz-G9-JMP">
                                                                <rect key="frame" x="10.666666666666657" y="302" width="406.66666666666674" height="50"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Tax" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="lXz-GA-JMQ">
                                                                        <rect key="frame" x="0.0" y="0.0" width="202" height="50"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="mXz-GB-JMR">
                                                                        <rect key="frame" x="205" y="0.0" width="201.66666666666663" height="50"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="RwQ-ax-PcH">
                                                                <rect key="frame" x="10.666666666666657" y="355" width="406.66666666666674" height="50"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Total" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Js8-kZ-jLH">
                                                                        <rect key="frame" x="0.0" y="0.0" width="202" height="50"/>
                                                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="14"/>
                                                                        <color key="textColor" name="Primary"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="w2z-TF-c7n">
                                                                        <rect key="frame" x="205" y="0.0" width="201.66666666666663" height="50"/>
                                                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="14"/>
                                                                        <color key="textColor" name="Primary"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstItem="LxW-XO-k4c" firstAttribute="width" secondItem="KS3-Mo-vpM" secondAttribute="width" id="Ezg-T1-puT"/>
                                                            <constraint firstItem="Uly-YA-fae" firstAttribute="width" secondItem="KS3-Mo-vpM" secondAttribute="width" multiplier="0.95" id="IK1-vW-5su"/>
                                                            <constraint firstItem="RwQ-ax-PcH" firstAttribute="width" secondItem="KS3-Mo-vpM" secondAttribute="width" multiplier="0.95" id="RfR-sW-f5e"/>
                                                            <constraint firstItem="7a4-4c-SwC" firstAttribute="width" secondItem="KS3-Mo-vpM" secondAttribute="width" multiplier="0.95" id="YtR-TZ-Dgh"/>
                                                            <constraint firstItem="CFp-bT-rEB" firstAttribute="width" secondItem="KS3-Mo-vpM" secondAttribute="width" multiplier="0.95" id="fjn-qP-xWH"/>
                                                            <constraint firstItem="cXz-G1-JMH" firstAttribute="width" secondItem="KS3-Mo-vpM" secondAttribute="width" multiplier="0.95" id="oXz-GD-JMT"/>
                                                            <constraint firstItem="gXz-G5-JML" firstAttribute="width" secondItem="KS3-Mo-vpM" secondAttribute="width" multiplier="0.95" id="pXz-GE-JMU"/>
                                                            <constraint firstItem="kXz-G9-JMP" firstAttribute="width" secondItem="KS3-Mo-vpM" secondAttribute="width" multiplier="0.95" id="qXz-GF-JMV"/>
                                                        </constraints>
                                                    </stackView>
                                                </subviews>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <constraints>
                                                    <constraint firstItem="KS3-Mo-vpM" firstAttribute="top" secondItem="SSq-w0-b46" secondAttribute="top" id="Cfh-pb-jmf"/>
                                                    <constraint firstAttribute="trailing" secondItem="KS3-Mo-vpM" secondAttribute="trailing" id="baJ-i2-d3F"/>
                                                    <constraint firstAttribute="bottom" secondItem="KS3-Mo-vpM" secondAttribute="bottom" constant="4" id="fgb-Pt-I9I"/>
                                                    <constraint firstItem="KS3-Mo-vpM" firstAttribute="leading" secondItem="SSq-w0-b46" secondAttribute="leading" id="jAv-52-aYV"/>
                                                </constraints>
                                            </view>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="8RW-wt-VNv">
                                                <rect key="frame" x="0.0" y="1002" width="428" height="152"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="UtO-oQ-xeF">
                                                        <rect key="frame" x="0.0" y="0.0" width="428" height="148"/>
                                                        <subviews>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ZMZ-gn-St7">
                                                                <rect key="frame" x="0.0" y="0.0" width="428" height="34"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Other Charges" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="BBT-Hg-zLB">
                                                                        <rect key="frame" x="4" y="0.0" width="416" height="34"/>
                                                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="15"/>
                                                                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                                <color key="backgroundColor" name="Primary"/>
                                                                <constraints>
                                                                    <constraint firstItem="BBT-Hg-zLB" firstAttribute="top" secondItem="ZMZ-gn-St7" secondAttribute="top" id="CMW-re-EZ1"/>
                                                                    <constraint firstItem="BBT-Hg-zLB" firstAttribute="leading" secondItem="ZMZ-gn-St7" secondAttribute="leading" constant="4" id="Pzf-JN-PAs"/>
                                                                    <constraint firstAttribute="bottom" secondItem="BBT-Hg-zLB" secondAttribute="bottom" id="Z84-R4-Y4z"/>
                                                                    <constraint firstAttribute="trailing" secondItem="BBT-Hg-zLB" secondAttribute="trailing" constant="8" id="e1O-eU-bqK"/>
                                                                    <constraint firstAttribute="height" constant="34" id="jyb-uF-Hgq"/>
                                                                </constraints>
                                                            </view>
                                                            <tableView clipsSubviews="YES" contentMode="scaleToFill" spacing="3" alwaysBounceVertical="YES" scrollEnabled="NO" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="DLs-6z-2TS">
                                                                <rect key="frame" x="10.666666666666657" y="37" width="406.66666666666674" height="5"/>
                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="5" id="PQO-bj-RxG"/>
                                                                </constraints>
                                                                <prototypes>
                                                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="none" indentationWidth="10" reuseIdentifier="cell" rowHeight="140" id="oVm-iB-cdJ" customClass="TransactionCell" customModule="NXC_EV_Solutions" customModuleProvider="target">
                                                                        <rect key="frame" x="0.0" y="50" width="406.66666666666674" height="140"/>
                                                                        <autoresizingMask key="autoresizingMask"/>
                                                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="oVm-iB-cdJ" id="w5c-mj-f7O">
                                                                            <rect key="frame" x="0.0" y="0.0" width="406.66666666666674" height="140"/>
                                                                            <autoresizingMask key="autoresizingMask"/>
                                                                            <subviews>
                                                                                <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="BNe-mR-3DO">
                                                                                    <rect key="frame" x="0.0" y="0.0" width="406.66666666666669" height="140"/>
                                                                                    <subviews>
                                                                                        <stackView opaque="NO" contentMode="scaleToFill" ambiguous="YES" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="nWF-oZ-9YE">
                                                                                            <rect key="frame" x="0.0" y="0.0" width="406.66666666666669" height="140"/>
                                                                                            <subviews>
                                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" ambiguous="YES" text="Label" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="pyU-0t-vYc">
                                                                                                    <rect key="frame" x="0.0" y="0.0" width="298.66666666666669" height="140"/>
                                                                                                    <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                                                    <nil key="textColor"/>
                                                                                                    <nil key="highlightedColor"/>
                                                                                                </label>
                                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="252" ambiguous="YES" text="Label" textAlignment="right" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontForContentSizeCategory="YES" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Tqy-JU-ect">
                                                                                                    <rect key="frame" x="306.66666666666669" y="0.0" width="100" height="140"/>
                                                                                                    <constraints>
                                                                                                        <constraint firstAttribute="width" constant="100" id="90z-ao-n0M"/>
                                                                                                    </constraints>
                                                                                                    <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                                                    <nil key="textColor"/>
                                                                                                    <nil key="highlightedColor"/>
                                                                                                </label>
                                                                                            </subviews>
                                                                                        </stackView>
                                                                                    </subviews>
                                                                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="trailing" secondItem="nWF-oZ-9YE" secondAttribute="trailing" id="7WO-CN-ObX"/>
                                                                                        <constraint firstItem="nWF-oZ-9YE" firstAttribute="top" secondItem="BNe-mR-3DO" secondAttribute="top" id="EbI-Qm-Xeo"/>
                                                                                        <constraint firstAttribute="bottom" secondItem="nWF-oZ-9YE" secondAttribute="bottom" id="Jm0-DH-lbO"/>
                                                                                        <constraint firstItem="nWF-oZ-9YE" firstAttribute="leading" secondItem="BNe-mR-3DO" secondAttribute="leading" id="xDO-tV-ZXu"/>
                                                                                    </constraints>
                                                                                </view>
                                                                            </subviews>
                                                                            <constraints>
                                                                                <constraint firstAttribute="bottom" secondItem="BNe-mR-3DO" secondAttribute="bottom" id="Gmh-zd-TBF"/>
                                                                                <constraint firstItem="BNe-mR-3DO" firstAttribute="top" secondItem="w5c-mj-f7O" secondAttribute="top" id="IlK-jX-b7X"/>
                                                                                <constraint firstItem="BNe-mR-3DO" firstAttribute="leading" secondItem="w5c-mj-f7O" secondAttribute="leading" id="RdK-ZL-E4a"/>
                                                                                <constraint firstAttribute="trailing" secondItem="BNe-mR-3DO" secondAttribute="trailing" id="VIz-R6-Eeq"/>
                                                                            </constraints>
                                                                        </tableViewCellContentView>
                                                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <connections>
                                                                            <outlet property="lblTitle" destination="pyU-0t-vYc" id="EH8-HT-TDa"/>
                                                                            <outlet property="lblValue" destination="Tqy-JU-ect" id="s62-S5-RBZ"/>
                                                                            <outlet property="viewMain" destination="BNe-mR-3DO" id="cjP-jO-kbp"/>
                                                                        </connections>
                                                                    </tableViewCell>
                                                                </prototypes>
                                                            </tableView>
                                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="tax-stack-view">
                                                                <rect key="frame" x="10.666666666666657" y="45" width="406.66666666666674" height="50"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Tax" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="tax-label-title">
                                                                        <rect key="frame" x="0.0" y="0.0" width="202" height="50"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="tax-label-value">
                                                                        <rect key="frame" x="205" y="0.0" width="201.66666666666663" height="50"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="idJ-rz-ixD">
                                                                <rect key="frame" x="10.666666666666657" y="98" width="406.66666666666674" height="50"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Total" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="vYZ-yA-9A7">
                                                                        <rect key="frame" x="0.0" y="0.0" width="202" height="50"/>
                                                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="14"/>
                                                                        <color key="textColor" name="Primary"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="jJw-cp-RpX">
                                                                        <rect key="frame" x="205" y="0.0" width="201.66666666666663" height="50"/>
                                                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="14"/>
                                                                        <color key="textColor" name="Primary"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstItem="ZMZ-gn-St7" firstAttribute="width" secondItem="UtO-oQ-xeF" secondAttribute="width" id="47F-CD-6qV"/>
                                                            <constraint firstItem="DLs-6z-2TS" firstAttribute="width" secondItem="UtO-oQ-xeF" secondAttribute="width" multiplier="0.95" id="UmP-Q0-V4S"/>
                                                            <constraint firstItem="idJ-rz-ixD" firstAttribute="width" secondItem="UtO-oQ-xeF" secondAttribute="width" multiplier="0.95" id="VYY-iR-UdI"/>
                                                            <constraint firstItem="tax-stack-view" firstAttribute="width" secondItem="UtO-oQ-xeF" secondAttribute="width" multiplier="0.95" id="tax-width-constraint"/>
                                                        </constraints>
                                                    </stackView>
                                                </subviews>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <constraints>
                                                    <constraint firstItem="UtO-oQ-xeF" firstAttribute="leading" secondItem="8RW-wt-VNv" secondAttribute="leading" id="3so-7F-amJ"/>
                                                    <constraint firstItem="UtO-oQ-xeF" firstAttribute="top" secondItem="8RW-wt-VNv" secondAttribute="top" id="Fep-pT-1NO"/>
                                                    <constraint firstAttribute="trailing" secondItem="UtO-oQ-xeF" secondAttribute="trailing" id="oMI-8E-2Wa"/>
                                                    <constraint firstAttribute="bottom" secondItem="UtO-oQ-xeF" secondAttribute="bottom" constant="4" id="xqk-Nn-jMx"/>
                                                </constraints>
                                            </view>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="kb6-zL-Uv3">
                                                <rect key="frame" x="0.0" y="1160" width="428" height="112"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="9oP-Sb-KS2">
                                                        <rect key="frame" x="0.0" y="0.0" width="428" height="108"/>
                                                        <subviews>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="YQX-9y-9iQ">
                                                                <rect key="frame" x="0.0" y="0.0" width="428" height="34"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Grand Total" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Eze-KH-BKV">
                                                                        <rect key="frame" x="4" y="0.0" width="416" height="34"/>
                                                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="15"/>
                                                                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                                <color key="backgroundColor" name="Primary"/>
                                                                <constraints>
                                                                    <constraint firstItem="Eze-KH-BKV" firstAttribute="top" secondItem="YQX-9y-9iQ" secondAttribute="top" id="1OF-Hb-DnC"/>
                                                                    <constraint firstAttribute="trailing" secondItem="Eze-KH-BKV" secondAttribute="trailing" constant="8" id="5wQ-3U-5OI"/>
                                                                    <constraint firstAttribute="bottom" secondItem="Eze-KH-BKV" secondAttribute="bottom" id="D8R-xm-qRF"/>
                                                                    <constraint firstItem="Eze-KH-BKV" firstAttribute="leading" secondItem="YQX-9y-9iQ" secondAttribute="leading" constant="4" id="uze-3J-Dmv"/>
                                                                    <constraint firstAttribute="height" constant="34" id="w9v-2M-SaM"/>
                                                                </constraints>
                                                            </view>
                                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="c5h-ia-B9z">
                                                                <rect key="frame" x="10.666666666666657" y="37" width="406.66666666666674" height="34"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Grand Total" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Oce-Bu-2ug">
                                                                        <rect key="frame" x="0.0" y="0.0" width="202" height="34"/>
                                                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="14"/>
                                                                        <color key="textColor" name="Primary"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="XIe-O2-GLG">
                                                                        <rect key="frame" x="205" y="0.0" width="201.66666666666663" height="34"/>
                                                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="14"/>
                                                                        <color key="textColor" name="Primary"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="34" id="lbr-e9-WRw"/>
                                                                </constraints>
                                                            </stackView>
                                                            <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="z4E-kQ-4md">
                                                                <rect key="frame" x="114.33333333333333" y="74" width="199.33333333333337" height="34"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Download Receipt" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="LaJ-bV-Xwm">
                                                                        <rect key="frame" x="0.0" y="0.0" width="155.33333333333334" height="34"/>
                                                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="14"/>
                                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_download_receipt" translatesAutoresizingMaskIntoConstraints="NO" id="Xd2-qZ-lmv">
                                                                        <rect key="frame" x="163.33333333333337" y="7" width="20" height="20"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="width" constant="20" id="0CQ-yc-LC4"/>
                                                                            <constraint firstAttribute="height" constant="20" id="E4g-Q8-s1v"/>
                                                                        </constraints>
                                                                    </imageView>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="IdE-tW-Ck9">
                                                                        <rect key="frame" x="191.33333333333337" y="13" width="8" height="8"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="8" id="5Js-Gt-2Cu"/>
                                                                            <constraint firstAttribute="width" constant="8" id="qr4-ZG-Qsl"/>
                                                                        </constraints>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                        <nil key="textColor"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                                <constraints>
                                                                    <constraint firstItem="LaJ-bV-Xwm" firstAttribute="height" secondItem="z4E-kQ-4md" secondAttribute="height" id="1Zs-nF-7xf"/>
                                                                    <constraint firstAttribute="height" constant="34" id="9rz-2W-x2m"/>
                                                                </constraints>
                                                            </stackView>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstItem="c5h-ia-B9z" firstAttribute="width" secondItem="9oP-Sb-KS2" secondAttribute="width" multiplier="0.95" id="2wa-d8-Yft"/>
                                                            <constraint firstItem="z4E-kQ-4md" firstAttribute="width" secondItem="9oP-Sb-KS2" secondAttribute="width" multiplier="0.466197" id="RoZ-lY-8Tz"/>
                                                            <constraint firstItem="YQX-9y-9iQ" firstAttribute="width" secondItem="9oP-Sb-KS2" secondAttribute="width" id="qEJ-CP-co4"/>
                                                        </constraints>
                                                    </stackView>
                                                </subviews>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <constraints>
                                                    <constraint firstAttribute="trailing" secondItem="9oP-Sb-KS2" secondAttribute="trailing" id="1wQ-0O-V6M"/>
                                                    <constraint firstItem="9oP-Sb-KS2" firstAttribute="top" secondItem="kb6-zL-Uv3" secondAttribute="top" id="Drm-Lo-uTZ"/>
                                                    <constraint firstItem="9oP-Sb-KS2" firstAttribute="leading" secondItem="kb6-zL-Uv3" secondAttribute="leading" id="rw4-J6-h8J"/>
                                                    <constraint firstAttribute="bottom" secondItem="9oP-Sb-KS2" secondAttribute="bottom" constant="4" id="wbL-3s-FIx"/>
                                                </constraints>
                                            </view>
                                        </subviews>
                                    </stackView>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="CZG-XE-745">
                                        <rect key="frame" x="120.33333333333333" y="1238" width="199.33333333333337" height="34"/>
                                        <connections>
                                            <action selector="downloadReceiptTapped:" destination="lty-oh-6rU" eventType="touchUpInside" id="UxF-j3-RHE"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="Ij6-6n-wIH" firstAttribute="leading" secondItem="Ly0-R0-Ngh" secondAttribute="leading" constant="6" id="I5f-px-ATB"/>
                                    <constraint firstItem="CZG-XE-745" firstAttribute="centerY" secondItem="z4E-kQ-4md" secondAttribute="centerY" id="PNp-kL-aLG"/>
                                    <constraint firstAttribute="bottom" secondItem="Ij6-6n-wIH" secondAttribute="bottom" constant="8" id="PrG-tQ-vbk"/>
                                    <constraint firstItem="CZG-XE-745" firstAttribute="width" secondItem="z4E-kQ-4md" secondAttribute="width" id="QVs-9x-bVH"/>
                                    <constraint firstItem="CZG-XE-745" firstAttribute="centerX" secondItem="z4E-kQ-4md" secondAttribute="centerX" id="eHC-7b-68H"/>
                                    <constraint firstItem="CZG-XE-745" firstAttribute="height" secondItem="z4E-kQ-4md" secondAttribute="height" id="mDp-Uw-Loa"/>
                                    <constraint firstItem="Ij6-6n-wIH" firstAttribute="top" secondItem="Ly0-R0-Ngh" secondAttribute="top" constant="4" id="mSK-YX-RfO"/>
                                    <constraint firstAttribute="trailing" secondItem="Ij6-6n-wIH" secondAttribute="trailing" constant="6" id="vsz-d9-W6i"/>
                                    <constraint firstItem="Ij6-6n-wIH" firstAttribute="centerX" secondItem="Ly0-R0-Ngh" secondAttribute="centerX" id="zN6-LB-JkI"/>
                                </constraints>
                            </scrollView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="tzr-BZ-73W"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="tzr-BZ-73W" firstAttribute="trailing" secondItem="Ly0-R0-Ngh" secondAttribute="trailing" id="0mn-8T-xz2"/>
                            <constraint firstItem="tzr-BZ-73W" firstAttribute="bottom" secondItem="6M5-5o-GLL" secondAttribute="bottom" constant="16" id="3j2-dg-4JW"/>
                            <constraint firstItem="6M5-5o-GLL" firstAttribute="top" secondItem="Ly0-R0-Ngh" secondAttribute="bottom" constant="16" id="Cfe-Bw-xSv"/>
                            <constraint firstItem="cqG-Qz-LRT" firstAttribute="leading" secondItem="tzr-BZ-73W" secondAttribute="leading" id="D0N-t0-n8M"/>
                            <constraint firstItem="cqG-Qz-LRT" firstAttribute="top" secondItem="tzr-BZ-73W" secondAttribute="top" id="FQz-7T-BQr"/>
                            <constraint firstItem="tzr-BZ-73W" firstAttribute="trailing" secondItem="cqG-Qz-LRT" secondAttribute="trailing" id="Fq1-S6-qpf"/>
                            <constraint firstItem="Ly0-R0-Ngh" firstAttribute="top" secondItem="cqG-Qz-LRT" secondAttribute="bottom" id="OV8-Jn-BhE"/>
                            <constraint firstItem="Ly0-R0-Ngh" firstAttribute="leading" secondItem="tzr-BZ-73W" secondAttribute="leading" id="a31-Ua-ID3"/>
                            <constraint firstItem="6M5-5o-GLL" firstAttribute="width" secondItem="tzr-BZ-73W" secondAttribute="width" multiplier="0.9" id="meV-es-oym"/>
                            <constraint firstItem="6M5-5o-GLL" firstAttribute="centerX" secondItem="Y4Y-Oq-XTq" secondAttribute="centerX" id="yV2-5g-OE1"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="btnAddComplain" destination="6M5-5o-GLL" id="atv-WO-cxh"/>
                        <outlet property="btnBack" destination="lAv-tp-aA8" id="rvf-Nz-nQ8"/>
                        <outlet property="btnMenu" destination="KGM-VH-0xw" id="UiH-PJ-CsR"/>
                        <outlet property="lblChargerName" destination="Eei-jf-Qib" id="Y9T-aH-MhA"/>
                        <outlet property="lblConsumptionChargeableUnit" destination="eXz-G3-JMJ" id="CXz-GJ-JMZ"/>
                        <outlet property="lblConsumptionNetTotal" destination="iXz-G7-JMN" id="DXz-GK-JNA"/>
                        <outlet property="lblConsumptionTax" destination="mXz-GB-JMR" id="EXz-GL-JNB"/>
                        <outlet property="lblDate" destination="9bd-dK-kJs" id="RFk-hU-t3h"/>
                        <outlet property="lblFixChargeFreeUnit" destination="FYs-fB-iHl" id="VfU-mt-vy0"/>
                        <outlet property="lblFixChargeNetTotal" destination="50m-W7-eF9" id="65P-PK-zJp"/>
                        <outlet property="lblFixChargeTax" destination="pXz-G3-JMJ" id="xXz-GB-JMR"/>
                        <outlet property="lblFixChargeTotal" destination="tXz-G7-JMN" id="yXz-GC-JMS"/>
                        <outlet property="lblFreeUnit" destination="Opi-fC-zyl" id="AdO-Ec-LrD"/>
                        <outlet property="lblGrandTotal" destination="XIe-O2-GLG" id="jyL-Q6-fpY"/>
                        <outlet property="lblOtherTax" destination="tax-label-value" id="other-tax-outlet"/>
                        <outlet property="lblStationName" destination="wOm-Mf-NR5" id="uJF-5r-fRJ"/>
                        <outlet property="lblTime" destination="E9a-a8-5fJ" id="3Iu-mJ-Vcz"/>
                        <outlet property="lblTitle" destination="lWh-Ya-U41" id="1Ze-Zc-zTZ"/>
                        <outlet property="lblTotalConspCharge" destination="w2z-TF-c7n" id="1Om-BW-vF8"/>
                        <outlet property="lblTotalOtherCharge" destination="jJw-cp-RpX" id="1kk-3C-jHv"/>
                        <outlet property="lblTransactionID" destination="Of0-Wg-tXE" id="LEN-oT-q5W"/>
                        <outlet property="lblUnitPrice" destination="NIX-Nv-5q1" id="zXz-GG-JMW"/>
                        <outlet property="lblUsedUnit" destination="o1c-OB-wJT" id="AXz-GH-JMX"/>
                        <outlet property="scrollDetails" destination="Ly0-R0-Ngh" id="jHI-rd-vyM"/>
                        <outlet property="stackFreeUnit" destination="Uly-YA-fae" id="BXz-GI-JMY"/>
                        <outlet property="stackReceipt" destination="z4E-kQ-4md" id="LTM-G1-yFL"/>
                        <outlet property="tableChargeHeight" destination="PQO-bj-RxG" id="1oB-LT-RSH"/>
                        <outlet property="tableOtherCharge" destination="DLs-6z-2TS" id="Q98-cF-N1t"/>
                        <outlet property="viewChargeDetailsTop" destination="K7W-up-xuR" id="xu5-LT-TM2"/>
                        <outlet property="viewChargerDetails" destination="qL2-5R-dtL" id="1V1-hB-nr2"/>
                        <outlet property="viewConsumptionCharge" destination="SSq-w0-b46" id="pgD-aG-t9L"/>
                        <outlet property="viewConsumptionTop" destination="LxW-XO-k4c" id="2I7-aK-a7Q"/>
                        <outlet property="viewFixCharge" destination="7ml-KF-NGZ" id="3Zy-3d-IXV"/>
                        <outlet property="viewFixChargeTop" destination="2Pp-50-fT8" id="CBQ-i1-MCX"/>
                        <outlet property="viewGrandTotal" destination="kb6-zL-Uv3" id="sfM-ZG-mJV"/>
                        <outlet property="viewGrandTotalTop" destination="YQX-9y-9iQ" id="fHR-qW-aJA"/>
                        <outlet property="viewOtherCharge" destination="8RW-wt-VNv" id="xbx-WY-dpI"/>
                        <outlet property="viewOtherChargeTop" destination="ZMZ-gn-St7" id="zcb-hO-ZfY"/>
                        <outlet property="viewTransaction" destination="Uub-Qz-TWf" id="Y2f-oN-qEq"/>
                        <outlet property="viewTxnTop" destination="CWM-GL-GzV" id="oPg-Tx-1Oi"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="hFl-fY-5u7" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="2904.8000000000002" y="71.514242878560722"/>
        </scene>
    </scenes>
    <resources>
        <image name="ic_add" width="128" height="128"/>
        <image name="ic_back_arrow" width="64" height="64"/>
        <image name="ic_blackCircle" width="64" height="64"/>
        <image name="ic_calendar" width="15" height="16"/>
        <image name="ic_cancel" width="64" height="64"/>
        <image name="ic_car_wallet" width="500" height="316"/>
        <image name="ic_download_receipt" width="500" height="500"/>
        <image name="ic_dropdown" width="64" height="64"/>
        <image name="ic_filter" width="500" height="501"/>
        <image name="ic_logout" width="24" height="24"/>
        <image name="ic_menu" width="500" height="500"/>
        <image name="ic_no_data" width="275" height="183"/>
        <image name="ic_offerLine" width="309" height="2"/>
        <image name="ic_razorpay" width="100" height="32"/>
        <image name="ic_ticked" width="64" height="64"/>
        <namedColor name="GrayPlaceholder">
            <color red="0.0" green="0.0" blue="0.0" alpha="0.5" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="PaymentNote">
            <color red="0.82352941176470584" green="0.47058823529411764" blue="0.14117647058823529" alpha="0.15000000596046448" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Primary">
            <color red="0.016000000759959221" green="0.49399998784065247" blue="0.42699998617172241" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="PrimaryColor">
            <color red="0.015686274509803921" green="0.49411764705882355" blue="0.42745098039215684" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="PrimarySelection">
            <color red="0.59999999999999998" green="0.59999999999999998" blue="0.59999999999999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="PrimaryText">
            <color red="0.72500002384185791" green="0.72899997234344482" blue="0.7839999794960022" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="PrimaryTextColor">
            <color red="0.72500002384185791" green="0.72899997234344482" blue="0.7839999794960022" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="TextNotSelected">
            <color red="0.59999999999999998" green="0.59999999999999998" blue="0.59999999999999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="WalletAddText">
            <color red="0.23100000619888306" green="0.48600000143051147" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="WalletDeductText">
            <color red="0.90200001001358032" green="0.15299999713897705" blue="0.027000000700354576" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>

//
//  BuyChargerVC.swift
//  NXC EV Solutions
//
//  Created by <PERSON><PERSON><PERSON> on 15/11/21.
//

import UIKit
import WebKit
import Alamofire

class BuyChargerVC: UIViewController {
    
    // MARK: - IBOutlets
    @IBOutlet weak var btnBack: UIButton!
    @IBOutlet weak var webDetails: WKWebView!
        
    
    // MARK: - View LifeCycle Methods
    override func viewDidLoad() {
        super.viewDidLoad()

        webDetails.navigationDelegate = self
        getBuyCharger()
    }
    
    override func viewDidAppear(_ animated: Bool) {
        AppDelegate.shared.isSwipeBack = 1
        navigationController?.interactivePopGestureRecognizer?.delegate = self
        navigationController?.interactivePopGestureRecognizer?.isEnabled = true
    }
    
    override func viewDidLayoutSubviews() {
        btnBack.addBorder(color: Constants.backArrowBorderColor!, width: Int(1))
        btnBack.maskClipCorner(cornerRadius: 10)
    }

    // MARK: - Button Actions
    @IBAction func backTapped(_ sender: UIButton) {
        navigationController?.popViewController(animated: true)
    }
    
    // MARK: - WebService
    func getBuyCharger() {
        AppDelegate.shared.showHUD()
        let url = Constants.BASE_URL + API.BUY_CHARGER
        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]
        AF.request(url, method: .get, parameters: nil, encoding:  URLEncoding.default, headers: headers).responseJSON { response in
            print(response)
            AppDelegate.shared.hideHUD()
            switch response.result {
            case .success:
                
                let JSON = response.value as! NSDictionary
                print(JSON)
                
                if "\(JSON["code"]!)" == "200" && "\(JSON["status"]!)" == "1" {
                    self.webDetails.load(URLRequest(url: URL(string: "\(JSON["data"]!)")!))
                    AppDelegate.shared.showHUD()
                } else {
                    AppDelegate.shared.apiKeyLogout()
                }
                break
            case .failure(let error):
                print(error)
                AppDelegate.shared.hideHUD()
            }
        }
    }
}
extension BuyChargerVC: WKNavigationDelegate {
    func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        AppDelegate.shared.hideHUD()
    }
}
extension BuyChargerVC:UIGestureRecognizerDelegate {
    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldBeRequiredToFailBy otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        return true
    }
}

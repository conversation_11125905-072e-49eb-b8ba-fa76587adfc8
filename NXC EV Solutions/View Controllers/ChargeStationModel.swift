//
//  ChargeStationModel.swift
//  ChargeStationModel
//
//  Created by Developer on 09/09/21.
//

import Foundation


struct NoOfConnectors {
    var count:String = String()
    var connector:String = String()
    var image:String = String()

    init(dic:NSDictionary) {
        self.count = "\(dic["count"]!)"
        self.connector = "\(dic["connector"]!)"
        self.image = "\(dic["image"]!)"
    }
}

struct NearMeList {
    var cs_id:String = String()
    var lat:String = String()
    var long:String = String()

    // Original initializer for API responses
    init(dic:NSDictionary) {
        self.cs_id = "\(dic["cs_id"]!)"
        self.lat = "\(dic["cs_latitude"]!)"
        self.long = "\(dic["cs_longitude"]!)"
    }

    // New initializer for direct creation
    init(cs_id: String, lat: String, long: String) {
        self.cs_id = cs_id
        self.lat = lat
        self.long = long
    }
}

struct AddressDetails {
    var address:String = String()
    var cs_id:String = String()
    var lat:String = String()
    var long:String = String()

    init(dic:NSDictionary) {
        self.address = "\(dic["address"]!)"
        self.cs_id = "\(dic["cs_id"]!)"
        self.lat = "\(dic["lat"]!)"
        self.long = "\(dic["long"]!)"
    }
}

struct SearchStationList {
    var cs_id:String = String()
    var cs_name:String = String()
    var address:String = String()

    init(dic:NSDictionary) {
        self.cs_id = "\(dic["cs_id"]!)"
        self.cs_name = "\(dic["cs_name"]!)"
        self.address = "\(dic["address"]!)"
    }
}


struct ChargeStation {

    var avg_rating:String = String()
    var charge_fav:String = String()
    var charge_station_id:String = String()
    var charge_station_name:String = String()
    var charger_type:String = String()
    var create_date:String = String()
    var cs_address:String = String()
    var distance:String = String()
    var email:String = String()
    var lat:String = String()
    var long:String = String()
    var phone:String = String()
    var user_rating:String = String()
    var user_rating_count:String = String()

    init(dic:NSDictionary) {
        self.avg_rating = "\(dic["avg_rating"]!)"
        self.charge_fav = ""//\(dic["charge_fav"]!)"
        self.charge_station_id = "\(dic["charge_station_id"]!)"
        self.charge_station_name = "\(dic["charge_station_name"]!)"
        self.charger_type = ""//\(dic["charger_type"]!)"
//        self.create_date = "\(dic["create_date"]!)"
        self.cs_address = "\(dic["cs_address"]!)"
        self.distance = "\(dic["distance"]!)"
        self.email = "\(dic["email"]!)"
        self.lat = "\(dic["lat"]!)"
        self.long = "\(dic["long"]!)"
//        self.phone = "\(dic["phone"]!)"
        self.user_rating = "\(dic["user_rating"]!)"
        self.user_rating_count = "\(dic["user_rating_count"]!)"
    }
}

struct ChargeStationInfo {
    var charge_station_id:String = String()
    var charge_station_name:String = String()
    var cs_address:String = String()
    var charge_fav:String = String()
    var charger_type:String = String()
    var distance:String = String()
    var lat:String = String()
    var long:String = String()
    var email:String = String()
    var phone:String = String()
    var create_date:String = String()

    init(dic:NSDictionary) {
        self.charge_station_id = "\(dic["charge_station_id"]!)"
        self.charge_station_name = "\(dic["charge_station_name"]!)"
        self.cs_address = "\(dic["cs_address"]!)"
        self.charge_fav = "\(dic["charge_fav"]!)"
        self.charger_type = "\(dic["charger_type"]!)"
        self.distance = "\(dic["distance"]!)"
        self.lat = "\(dic["lat"]!)"
        self.long = "\(dic["long"]!)"
        self.email = "\(dic["email"]!)"
        self.phone = "\(dic["phone"]!)"
        self.create_date = "\(dic["create_date"]!)"
    }
}

struct ChargeStationDay {
    var day:String = String()
    var time:[String] = []

    init(dic:NSDictionary) {
        self.day = "\(dic["day"]!)"
        if !"\(dic["time"]!)".isEmpty {
            for item in dic["time"] as! NSArray {
                print(item)
                self.time.append(item as! String)
            }
        }
    }
}




struct Charger {

    var charge_point_code:String = String()
    var connector_details:[ConnectorDetails] = []

    init(dic:NSDictionary) {
        self.charge_point_code = "\(dic["charge_point_code"]!)"
        if !"\(dic["connector_details"]!)".isEmpty {
            for item in dic["connector_details"] as! NSArray {
                self.connector_details.append(ConnectorDetails(dic: item as! NSDictionary))
            }
        }
    }
}

struct ConnectorDetails {

    var connector_count:String = String()
    var connector_current_type:String = String()
    var connector_image:String = String()
    var connector_name:String = String()
    var connector_power_type:String = String()
    var connector_type_id:String = String()
    var connector_type_details:[ConnectorTypeDetails] = []
    var status_details:[StatusDetails] = []

    init(dic:NSDictionary) {
        self.connector_count = "\(dic["connector_count"]!)"
        self.connector_current_type = "\(dic["connector_current_type"]!)"
        self.connector_image = "\(dic["connector_image"]!)"
        self.connector_name = "\(dic["connector_name"]!)"
        self.connector_power_type = "\(dic["connector_power_type"]!)"
        self.connector_type_id = "\(dic["connector_type_id"]!)"
        if !"\(dic["connector_type_details"]!)".isEmpty {
            for item in dic["connector_type_details"] as! NSArray {
                self.connector_type_details.append(ConnectorTypeDetails(dic: item as! NSDictionary))
            }
        }
        if !"\(dic["status_details"]!)".isEmpty {
            for item in dic["status_details"] as! NSArray {
                self.status_details.append(StatusDetails(dic: item as! NSDictionary))
            }
        }
    }
}


struct ConnectorTypeDetails {

    var charger_code:String = String()
    var connector_pk:String = String()
    var connector_power_rating:String = String()
    var connector_price:String = String()
    var connector_status:String = String()
    var plug_id:String = String()
    var power_type:String = String()

    init(dic:NSDictionary) {
        self.charger_code = "\(dic["charger_code"]!)"
        self.connector_pk = "\(dic["connector_pk"]!)"
        self.connector_power_rating = "\(dic["connector_power_rating"]!)"
        self.connector_price = "\(dic["connector_price"]!)"
        self.connector_status = "\(dic["connector_status"]!)"
        self.plug_id = "\(dic["plug_id"]!)"
        self.power_type = "\(dic["power_type"]!)"
    }

    // Add this method to convert the struct to a dictionary
    func toDictionary() -> NSDictionary {
        return [
            "charger_code": self.charger_code,
            "connector_pk": self.connector_pk,
            "connector_power_rating": self.connector_power_rating,
            "connector_price": self.connector_price,
            "connector_status": self.connector_status,
            "plug_id": self.plug_id,
            "power_type": self.power_type
        ] as NSDictionary
    }
}

struct ConnectorTypeDetailsCS {
    var connector_status:String = String()
    var connector_status_type:[ConnectorStatusType] = []

    init(dic:NSDictionary) {
        self.connector_status = "\(dic["connector_status"]!)"
        if !"\(dic["connector_status_type"]!)".isEmpty {
            for item in dic["connector_status_type"] as! NSArray {
                self.connector_status_type.append(ConnectorStatusType(dic: item as! NSDictionary))
            }
        }
    }
}

struct ConnectorStatusType {
    var connector_power_rating:String = String()
    var connector_pk:String = String()
    var connector_price:String = String()
    var power_type:String = String()
    var plug_id:String = String()

    init(dic:NSDictionary) {
        self.connector_power_rating = "\(dic["connector_power_rating"]!)"
        self.connector_pk = "\(dic["connector_pk"]!)"
        self.connector_price = "\(dic["connector_price"]!)"
        self.power_type = "\(dic["power_type"]!)"
        self.plug_id = "\(dic["plug_id"]!)"
    }
}

struct StatusDetails {

    var status:String = String()
    var total:String = String()

    init(dic:NSDictionary) {
        self.status = "\(dic["status"]!)"
        self.total = "\(dic["total"]!)"
    }
}

struct ConnectorType {

    var ct_id:String = String()
    var ct_name:String = String()
    var ct_image:String = String()
    var statusType:String = String()

    init(dic:NSDictionary) {
        self.ct_id = "\(dic["ct_id"]!)"
        self.ct_name = "\(dic["ct_name"]!)"
        self.ct_image = "\(dic["ct_image"]!)"
        self.statusType = "1"
    }
}


struct DaysData {

    var days:String = String()
    var isSelected:String = String()

    init(dic:NSDictionary) {
        self.days = "\(dic["days"]!)"
        self.isSelected = "\(dic["isSelected"]!)"
    }
}

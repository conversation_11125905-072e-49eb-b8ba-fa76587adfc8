//
//  ComplainCell.swift
//  ComplainCell
//
//  Created by <PERSON><PERSON><PERSON> on 17/08/21.
//

import UIKit

class ComplainCell: UITableViewCell {

    
    //MARK: - IBOutlets
    @IBOutlet weak var viewMain: UIView!
    @IBOutlet weak var lblComplainType: UILabel!
    @IBOutlet weak var lblTransactionID: UILabel!
    @IBOutlet weak var lblStatus: UILabel!
    @IBOutlet weak var lblDate: UILabel!
    @IBOutlet weak var lblTime: UILabel!
    @IBOutlet weak var lblDetails: UILabel!
        
    
    //MARK: -  View LifeCycle Methods
    override func awakeFromNib() {
        super.awakeFromNib()
        
        viewMain.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        lblComplainType.roundCorners(corners: [.topLeft,.topRight], radius: 10)
    }

    override func setSelected(_ selected: <PERSON><PERSON>, animated: <PERSON><PERSON>) {
        super.setSelected(selected, animated: animated)

    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        print("prepareForReuse")
        //set cell to initial state here
        //set like button to initial state - title, font, color, etc.
    }
}

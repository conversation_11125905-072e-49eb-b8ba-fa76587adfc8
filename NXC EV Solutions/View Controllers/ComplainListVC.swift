//
//  ComplainListVC.swift
//  ComplainListVC
//
//  Created by <PERSON><PERSON><PERSON> on 17/08/21.
//

import UIKit
import Alamofire

class ComplainListVC: UIViewController {


    // MARK: - IBOutlets
    @IBOutlet weak var tableList: UITableView!
    @IBOutlet weak var btnAddComplain: UIButton!

    @IBOutlet weak var viewBgFilter: UIView!
    @IBOutlet weak var viewMainFilter: UIView!

    @IBOutlet weak var viewAll: UIView!
    @IBOutlet weak var viewPending: UIView!
    @IBOutlet weak var viewInProgress: UIView!
    @IBOutlet weak var viewResolved: UIView!

    @IBOutlet weak var lblAll: UILabel!
    @IBOutlet weak var lblPending: UILabel!
    @IBOutlet weak var lblInProgress: UILabel!
    @IBOutlet weak var lblResolved: UILabel!

    @IBOutlet weak var btnApplyFilter: UIButton!

    @IBOutlet weak var viewNoData: UIView!
    @IBOutlet weak var viewFromDate: UIView!
    @IBOutlet weak var viewToDate: UIView!
//    @IBOutlet weak var btnSubmit: UIButton!

    @IBOutlet weak var lblFromDate: UILabel!
    @IBOutlet weak var lblToDate: UILabel!

    @IBOutlet weak var viewBgDate: UIView!
    @IBOutlet weak var viewMainDate: UIView!
    @IBOutlet weak var datePicker: UIDatePicker!
    @IBOutlet weak var btnOKDate: UIButton!

    @IBOutlet weak var btnBack: UIButton!
    @IBOutlet weak var btnFilter: UIButton!


    var dateType:String = String()
    var paramComplain:[String:Any] = [:]
    var complainList:[Complain] = []
    var fromDate:String = String()
    var toDate:String = String()

    var strComplainStatus = String()
    var state: State = .noData {
        didSet {
            switch state {
            case .loaded:
                tableList.isHidden = false
                viewNoData.isHidden = true
            case .noData:
                tableList.isHidden = true
                viewNoData.isHidden = false
            }
        }
    }


    // MARK: - View LifeCycle Methods
    override func viewDidLoad() {
        super.viewDidLoad()

        tableList.delegate = self
        tableList.dataSource = self

        viewBgFilter.isHidden = true
        viewBgDate.isHidden = true

        tableList.estimatedRowHeight = 44.0
        tableList.rowHeight = UITableView.automaticDimension

        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "dd MMM yyyy"
        datePicker.maximumDate = Date()

        let dateFormatter1 = DateFormatter()
        dateFormatter1.timeZone = .current
        dateFormatter1.dateFormat = "yyyy-MM-dd"
        toDate = dateFormatter1.string(from: Date())
        print("toDate:-",toDate)

        let calendar = Calendar.current
        let date = calendar.date(byAdding: .day, value: -30, to: Date())
        fromDate = dateFormatter1.string(from: date!)
        print("fromDate:-",fromDate)

        lblFromDate.text = dateFormatter.string(from: date!)
        lblToDate.text = dateFormatter.string(from: Date())


    }

    override func viewWillAppear(_ animated: Bool) {
        filterType(typeID: 101)
        getComplainList(status: "4")
    }

    override func viewDidAppear(_ animated: Bool) {
        AppDelegate.shared.isSwipeBack = 1
        navigationController?.interactivePopGestureRecognizer?.delegate = self
        navigationController?.interactivePopGestureRecognizer?.isEnabled = true
    }

    override func viewDidLayoutSubviews() {

        btnApplyFilter.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        btnAddComplain.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)

        viewMainFilter.roundCorners(corners: [.topLeft,.topRight], radius: 16)

        viewFromDate.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        viewToDate.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)

        btnOKDate.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)

        viewMainDate.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)

        viewAll.addBorder(color: Constants.textNotSelectedColor!, width: Int(1))
        viewAll.maskClipCorner(cornerRadius: 8)

        viewPending.addBorder(color: Constants.textNotSelectedColor!, width: Int(1))
        viewPending.maskClipCorner(cornerRadius: 8)

        viewInProgress.addBorder(color: Constants.textNotSelectedColor!, width: Int(1))
        viewInProgress.maskClipCorner(cornerRadius: 8)

        viewResolved.addBorder(color: Constants.textNotSelectedColor!, width: Int(1))
        viewResolved.maskClipCorner(cornerRadius: 8)

        btnBack.addBorder(color: Constants.backArrowBorderColor!, width: Int(1))
        btnBack.maskClipCorner(cornerRadius: 10)

        btnFilter.addBorder(color: Constants.backArrowBorderColor!, width: Int(1))
        btnFilter.maskClipCorner(cornerRadius: 10)
    }

    // MARK: - Status Type
    func filterType(typeID:Int) {

        //    0 = Pending,
        //    1 = Resolved,
        //    3 = Inprogress,
        //    4 = All,
        if typeID == 101 {
            viewAll.backgroundColor = Constants.primaryColor
            lblAll.textColor = .white

            viewPending.backgroundColor = .white
            lblPending.textColor = Constants.secondaryGrayText

            viewInProgress.backgroundColor = .white
            lblInProgress.textColor = Constants.secondaryGrayText

            viewResolved.backgroundColor = .white
            lblResolved.textColor = Constants.secondaryGrayText

            strComplainStatus = "4"
        } else if typeID == 102 {
            viewAll.backgroundColor = .white
            lblAll.textColor = Constants.secondaryGrayText

            viewPending.backgroundColor = Constants.primaryColor
            lblPending.textColor = .white

            viewInProgress.backgroundColor = .white
            lblInProgress.textColor = Constants.secondaryGrayText

            viewResolved.backgroundColor = .white
            lblResolved.textColor = Constants.secondaryGrayText

            strComplainStatus = "0"
        } else if typeID == 103 {
            viewAll.backgroundColor = .white
            lblAll.textColor = Constants.secondaryGrayText

            viewPending.backgroundColor = .white
            lblPending.textColor = Constants.secondaryGrayText

            viewInProgress.backgroundColor = Constants.primaryColor
            lblInProgress.textColor = .white

            viewResolved.backgroundColor = .white
            lblResolved.textColor = Constants.secondaryGrayText

            strComplainStatus = "1"
        } else {
            viewAll.backgroundColor = .white
            lblAll.textColor = Constants.secondaryGrayText

            viewPending.backgroundColor = .white
            lblPending.textColor = Constants.secondaryGrayText

            viewInProgress.backgroundColor = .white
            lblInProgress.textColor = Constants.secondaryGrayText

            viewResolved.backgroundColor = Constants.primaryColor
            lblResolved.textColor = .white

            strComplainStatus = "2"
        }
    }

    // MARK: - Button Actions
    @IBAction func backAction(_ sender: UIButton) {
        navigationController?.popViewController(animated: true)
    }

    @IBAction func filterAction(_ sender: UIButton) {
        viewBgFilter.isHidden = false
    }

    @IBAction func addComplainAction(_ sender: UIButton) {
        AppDelegate.shared.isFromMenu = true
        let vc = AddComplainVC.instantiate(appStoryboard: .Complain)
        navigationController?.pushViewController(vc, animated: true)
    }

    @IBAction func filterCloseAction(_ sender: UIButton) {
        viewBgFilter.isHidden = true
        viewBgDate.isHidden = true
    }

    @IBAction func complainStatusAction(_ sender: UIButton) {
        filterType(typeID: sender.tag)
    }

    @IBAction func fromDateAction(_ sender: UIButton) {
        dateType = "1"
        viewBgDate.isHidden = false
    }

    @IBAction func toDateAction(_ sender: UIButton) {
        dateType = "2"
        viewBgDate.isHidden = false
    }

    @IBAction func applyFilterAction(_ sender: UIButton) {
        viewBgFilter.isHidden = true
        viewBgDate.isHidden = true
        getComplainList(status: strComplainStatus)
    }

    @IBAction func okDateAction(_ sender: UIButton) {
        let dateFormatter = DateFormatter()
        dateFormatter.timeZone = .current
        dateFormatter.dateFormat = "dd MMM yyyy"
        if dateType == "1" {
            lblFromDate.text = dateFormatter.string(from: datePicker.date)
            fromDate = lblFromDate.text!.convertDateString(fromFormat: "dd MMM yyyy", toFormat: "yyyy-MM-dd")!
        } else {
            lblToDate.text = dateFormatter.string(from: datePicker.date)
            toDate = lblToDate.text!.convertDateString(fromFormat: "dd MMM yyyy", toFormat: "yyyy-MM-dd")!
        }
        viewBgDate.isHidden = true
    }

    @IBAction func cancelDateAction(_ sender: UIButton) {
        viewBgDate.isHidden = true
    }

    //MARK: - Webservice Methods
//    0 = Pending,
//    1 = In process,
//    2 = Resolved,
//    4 = All,
    func getComplainList(status:String) {

        AppDelegate.shared.showHUD()
        let url = Constants.BASE_URL + API.USER_COMPLAIN_LIST
        paramComplain = ["complain_status"  : status,
                         "start_date"       : fromDate,
                         "last_date"        : toDate]
        print("paramComplain:-",paramComplain)
        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]
        AF.request(url, method: .post, parameters: paramComplain, encoding:  URLEncoding.default, headers: headers).responseJSON { response in
        print(response)
        AppDelegate.shared.hideHUD()
        switch response.result {
        case .success:

            let JSON = response.value as! NSDictionary
            print(JSON)

            if "\(JSON["code"]!)" == "200" && "\(JSON["status"]!)" == "1" {

                let jsonData = JSON["data"] as! NSDictionary

                self.complainList.removeAll()
                for item in jsonData["complain_data"] as! NSArray {
                    self.complainList.append(Complain(dic: item as! NSDictionary))
                }

                if self.complainList.count > 0 {
                    self.state = .loaded
                } else {
                    self.state = .noData
                }

                self.tableList.reloadData()

            } else {

                if "\(JSON["code"]!)" == "100" {
                    AppDelegate.shared.apiKeyLogout()
                }

                self.complainList.removeAll()
                if self.complainList.count > 0 {
                    self.state = .loaded
                } else {
                    self.state = .noData
                }
                self.tableList.reloadData()
                AppDelegate.shared.hideHUD()
            }
            break
        case .failure(let error):
            print(error)
            AppDelegate.shared.hideHUD()
            }
        }
    }
}
extension ComplainListVC: UITableViewDelegate, UITableViewDataSource {

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return complainList.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "cell", for: indexPath) as! ComplainCell

        cell.lblComplainType.text = complainList[indexPath.row].ucm_name
        cell.lblTransactionID.text = complainList[indexPath.row].uc_trans_id
        cell.lblDetails.text = "\(complainList[indexPath.row].uc_description)"

        if "\(complainList[indexPath.row].uc_description)".isEmpty {
            cell.lblDetails.alpha = 0
        } else {
            cell.lblDetails.alpha = 1
        }

        let localDate = "\(complainList[indexPath.row].uc_date)".utcToLocal(dateFromValue: "yyyy-MM-dd HH:mm:ss", dateToValue: "dd MMM yyyy", dateStr: "\(complainList[indexPath.row].uc_date)")
        cell.lblDate.text = localDate

        let localTime = "\(complainList[indexPath.row].uc_date)".utcToLocal(dateFromValue: "yyyy-MM-dd HH:mm:ss", dateToValue: "hh:mm a", dateStr: "\(complainList[indexPath.row].uc_date)")
        cell.lblTime.text = localTime

        if complainList[indexPath.row].uc_status == "0" {
            cell.lblStatus.text = "Pending"
            cell.lblStatus.textColor = UIColor.red
        } else if complainList[indexPath.row].uc_status == "1" {
            cell.lblStatus.text = "In Progress"
            cell.lblStatus.textColor = UIColor.systemBlue
        } else if complainList[indexPath.row].uc_status == "2" {
            cell.lblStatus.text = "Resolved"
            cell.lblStatus.textColor = Constants.primaryColor
        }

        cell.layoutIfNeeded()
        cell.updateConstraints()
        return cell
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {

    }
}
extension ComplainListVC:UIGestureRecognizerDelegate {
    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldBeRequiredToFailBy otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        return true
    }
}

//
//  ComplainModel.swift
//  ComplainModel
//
//  Created by Developer on 03/09/21.
//

import Foundation

struct Complain {
    var ucm_name:String = String()
    var uc_description:String = String()
    var uc_status:String = String()
    var uc_note:String = String()
    var uc_date:String = String()
    var uc_trans_id:String = String()
    
    init(dic:NSDictionary) {
        self.ucm_name = "\(dic["ucm_name"]!)"
        self.uc_description = "\(dic["uc_description"]!)"
        self.uc_status = "\(dic["uc_status"]!)"
        self.uc_note = "\(dic["uc_note"]!)"
        self.uc_date = "\(dic["uc_date"]!)"
        self.uc_trans_id = "\(dic["uc_trans_id"]!)"
    }
}


struct ComplainType {
    var ucm_id:String = String()
    var ucm_name:String = String()
    
    init(dic:NSDictionary) {
        self.ucm_id = "\(dic["ucm_id"]!)"
        self.ucm_name = "\(dic["ucm_name"]!)"
    }
}

struct LastTransaction {
    var date:String = String()
    var type:String = String()
    var amount:String = String()
    var discription:String = String()
    var order_id:String = String()
    var status:String = String()
    var time:String = String()
    var transaction_id:String = String()

    init(dic:NSDictionary) {
        self.date = "\(dic["date"]!)"
        self.type = "\(dic["type"]!)"
        self.amount = "\(dic["amount"]!)"
        self.discription = "\(dic["discription"]!)"
        self.order_id = "\(dic["order_id"]!)"
        self.status = "\(dic["status"]!)"
        self.time = "\(dic["time"]!)"
        self.transaction_id = "\(dic["transaction_id"]!)"
    }
}


struct ComplaintFilterType {
    var id:String = String()
    var code:String = String()
    
    init(dic:NSDictionary) {
        self.id = "\(dic["id"]!)"
        self.code = "\(dic["code"]!)"
    }
}

//amount = "1480.00";
//date = "07 Sep 2021";
//discription = "Money Added to Account";
//"order_id" = "R - pay_HuVxkCYRrkosaf";
//status = success;
//time = "08:48:05";
//"transaction_id" = "pay_HuVxkCYRrkosaf";
//type = 2;

//
//  ConnectionLostVC.swift
//  NXC EV Solutions
//
//  Created by <PERSON><PERSON><PERSON> on 18/11/21.
//

import UIKit

class ConnectionLostVC: UIViewController {

    // MARK: - IBOutlets
    @IBOutlet weak var btnBack: UIButton!
    @IBOutlet weak var btnTryAgain: UIButton!
    
    
    // MARK: - View LifeCycle Methods
    override func viewDidLoad() {
        super.viewDidLoad()

    }
    
    override func viewDidLayoutSubviews() {
        
        btnBack.addBorder(color: Constants.backArrowBorderColor!, width: Int(1))
        btnBack.maskClipCorner(cornerRadius: 10)
        
        btnTryAgain.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
    }

    override func viewDidAppear(_ animated: Bool) {
        AppDelegate.shared.isSwipeBack = 1
        navigationController?.interactivePopGestureRecognizer?.delegate = self
        navigationController?.interactivePopGestureRecognizer?.isEnabled = true
    }
    
    // MARK: - Button Actions
    @IBAction func backActionTapped(_ sender: UIButton) {
        navigationController?.popViewController(animated: true)
    }
    
    @IBAction func tryAgainTapped(_ sender: UIButton) {
        AppDelegate.shared.tabIndex = 0
        let vc = HomeTabVC.instantiate(appStoryboard: .Home)
        navigationController?.pushViewController(vc, animated: true)
    }
    
}
extension ConnectionLostVC:UIGestureRecognizerDelegate {
    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldBeRequiredToFailBy otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        return true
    }
}

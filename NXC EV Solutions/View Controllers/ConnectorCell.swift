//
//  ConnectorCell.swift
//  ConnectorCell
//
//  Created by <PERSON><PERSON><PERSON> on 23/08/21.
//

import UIKit

class ConnectorCell: UITableViewCell {

    // MARK: - IBOutlets
    @IBOutlet weak var viewMain: UIView!
    @IBOutlet weak var imgConnectorName: UIImageView!
    @IBOutlet weak var lblConnectorName: UILabel!
    @IBOutlet weak var lblTotalConnectorStatus: UILabel!
    @IBOutlet weak var viewAvail: UIView!
    @IBOutlet weak var lblAvail: UILabel!
    @IBOutlet weak var viewUnavail: UIView!
    @IBOutlet weak var lblUnavail: UILabel!
    @IBOutlet weak var viewInUse: UIView!
    @IBOutlet weak var lblInUse: UILabel!
    @IBOutlet weak var viewBooked: UIView!
    @IBOutlet weak var lblBooked: UILabel!
    @IBOutlet weak var imgConnectorType: UIImageView!
    @IBOutlet weak var lblConnectorType: UILabel!    
    @IBOutlet weak var lblApproxPrice: UILabel!
    
    // MARK: - View LifeCycle Methods
    override func awakeFromNib() {
        super.awakeFromNib()
        
        viewMain.maskClipCorner(cornerRadius: 12)
        viewAvail.maskClipCorner(cornerRadius: 4)
        viewUnavail.maskClipCorner(cornerRadius: 4)
        viewInUse.maskClipCorner(cornerRadius: 6)
        viewBooked.maskClipCorner(cornerRadius: 6)

    }

    override func setSelected(_ selected: Bool, animated: Bool) {
        super.setSelected(selected, animated: animated)

    }

}

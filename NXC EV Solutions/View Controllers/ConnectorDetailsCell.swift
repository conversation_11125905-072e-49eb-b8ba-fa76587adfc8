//
//  ConnectorDetailsCell.swift
//  ConnectorDetailsCell
//
//  Created by <PERSON><PERSON><PERSON> on 23/08/21.
//

import UIKit

class ConnectorDetailsCell: UITableViewCell {
    
    // MARK: - IBOutlets    
    @IBOutlet weak var viewMain: UIView!
    @IBOutlet weak var imgAvailable: UIImageView!
    @IBOutlet weak var lblAvailable: UILabel!
    @IBOutlet weak var lblChargeName: UILabel!
    @IBOutlet weak var lblSpeed: UILabel!
    @IBOutlet weak var lblPrice: UILabel!
    @IBOutlet weak var imgSpeedType: UIImageView!
    @IBOutlet weak var lblSpeedType: UILabel!
    
//    @IBOutlet weak var viewMain: UIView!
//    @IBOutlet weak var viewType: UIView!
//    @IBOutlet weak var lblType: UILabel!
//    @IBOutlet weak var imgType: UIImageView!
//    @IBOutlet weak var lblSpeed: UILabel!
//    @IBOutlet weak var imgSpeedType: UIImageView!
//    @IBOutlet weak var lblSpeedType: UILabel!
//    @IBOutlet weak var imgACDC: UIImageView!
//    @IBOutlet weak var lblACDC: UILabel!
//    @IBOutlet weak var lblPrice1: UILabel!
//    @IBOutlet weak var lblPrice2: UILabel!
//    @IBOutlet weak var lblPerHourCharges: UILabel!
//    @IBOutlet weak var lblServiceCharges: UILabel!
    
    
    // MARK: - View LifeCycle Methods
    override func awakeFromNib() {
        super.awakeFromNib()

        viewMain.maskClipCorner(cornerRadius: 12)
//        viewType.maskClipCorner(cornerRadius: 8)
    }

    override func setSelected(_ selected: Bool, animated: Bool) {
        super.setSelected(selected, animated: animated)

    }
}

//
//  ConnectorDetailsVC.swift
//  ConnectorDetailsVC
//
//  Created by Dev<PERSON>per on 23/08/21.
//

import UIKit
import Cosmos
import Alamofire
import SDWebImage
import MapKit

class ConnectorDetailsVC: UIViewController {

    // MARK: - IBOutlets
    @IBOutlet weak var btnBack: UIButton!
    @IBOutlet weak var btnHistory: UIButton!
    @IBOutlet weak var scrollDetails: UIScrollView!
    @IBOutlet weak var imgChargeStation: UIImageView!
    @IBOutlet weak var lblChargeStationName: UILabel!
    @IBOutlet weak var lblChargeStationAddress: UILabel!

    @IBOutlet weak var chargeStationRatings: CosmosView!
    @IBOutlet weak var lblChargeStationReviews: UILabel!
    @IBOutlet weak var viewGetDirections: UIView!

    @IBOutlet weak var viewOpenClose: UIView!
    @IBOutlet weak var viewOpenCloseDetails: UIView!

    @IBOutlet weak var tableConnector: UITableView!

    @IBOutlet weak var btnReserveSlot: UIButton!
    @IBOutlet weak var btnStartCharging: UIButton!

    @IBOutlet weak var viewChargeStationInfo: UIView!
    @IBOutlet weak var viewOpenCloseInfo: UIView!
    @IBOutlet weak var viewConnectorListInfo: UIView!

    @IBOutlet weak var viewConnectorListDetailInfo: UIView!
    @IBOutlet weak var tableHeight: NSLayoutConstraint!

    @IBOutlet weak var viewBgReserve: UIView!
    @IBOutlet weak var viewMainReserve: UIView!

    @IBOutlet weak var btnInfoReserve: UIButton!
    @IBOutlet weak var viewReserveDetaiils: UIView!
    @IBOutlet weak var imgReserveConnector: UIImageView!
    @IBOutlet weak var lblReserveConnectorName: UILabel!
    @IBOutlet weak var lblReserveUnit: UILabel!
    @IBOutlet weak var lblReservePrice: UILabel!

    @IBOutlet weak var viewReserve15: UIView!
    @IBOutlet weak var lblReserve15: UILabel!

    @IBOutlet weak var viewReserve30: UIView!
    @IBOutlet weak var lblReserve30: UILabel!

    @IBOutlet weak var viewReserve45: UIView!
    @IBOutlet weak var lblReserve45: UILabel!

    @IBOutlet weak var viewReserve60: UIView!
    @IBOutlet weak var lblReserve60: UILabel!
    @IBOutlet weak var btnReserveSlot1: UIButton!

    @IBOutlet weak var collectionTime: UICollectionView!
    @IBOutlet weak var lblTimeDetails: UILabel!

    @IBOutlet weak var scrollChargerImages: UIScrollView!
    @IBOutlet weak var pageController: UIPageControl!

    var arrDays:[DaysData] = []

    var dateType:String = String()
    var paramCS:[String:Any] = [:]

    var chargeStationDetails:[ChargeStation] = []
    var connectorList:[ConnectorDetails] = []

//    let scrollView = UIScrollView(frame: CGRect(x:0, y:0, width:320,height: 300))
//    var colors:[UIColor] = [UIColor.red, UIColor.blue, UIColor.green, UIColor.yellow]
//    var frame: CGRect = CGRect(x:0, y:0, width:0, height:0)
//    var pageControl : UIPageControl = UIPageControl(frame: CGRect(x:50,y: 300, width:200, height:50))

    var strReserveStatus = String()
    var indexSelected = Int()

    var cs_lat = Double()
    var cs_lng = Double()

    var frame = CGRect.zero
    var arrImage:[String] = []



    // MARK: - Helper Methods
    private func formatPowerTypeForDisplay(_ powerType: String) -> String {
        switch powerType.uppercased() {
        case "SLOW":
            return "SLOW"
        case "MODERATE", "MODRATE":
            return "MODERATE"
        case "FAST":
            return "FAST"
        default:
            return powerType
        }
    }

    private func updateScrollViewContentSize() {
        DispatchQueue.main.async {
            // Force layout update to get accurate measurements
            self.view.layoutIfNeeded()

            // Set table height with minimal padding
            self.tableHeight.constant = self.tableConnector.contentSize.height + 20

            // Force another layout update after changing table height
            self.view.layoutIfNeeded()

            // Calculate scroll view content size with proper bottom padding
            let bottomSafeArea = self.view.safeAreaInsets.bottom
            let additionalBottomPadding: CGFloat = 5 // Extra padding to ensure last item is fully visible

            let totalContentHeight = self.viewConnectorListInfo.frame.origin.y +
                                   self.viewConnectorListInfo.frame.height +
                                   bottomSafeArea +
                                   additionalBottomPadding

            self.scrollDetails.contentSize = CGSize(width: self.scrollDetails.frame.width,
                                                  height: totalContentHeight)

            print("🔧 Updated scroll content size - Height: \(totalContentHeight)")
        }
    }

    // MARK: - View LifeCycle Methods
    override func viewDidLoad() {
        super.viewDidLoad()

        tableConnector.delegate = self
        tableConnector.dataSource = self
        viewBgReserve.isHidden = true
        collectionTime.delegate = self
        collectionTime.dataSource = self

        chargeStationRatings.isUserInteractionEnabled = false
        chargeStationRatings.settings.starMargin = -2

        // Configure table view for proper content size calculation
        tableConnector.tableFooterView = UIView(frame: CGRect.zero)
        tableConnector.sectionFooterHeight = 0.0
        tableConnector.sectionHeaderHeight = 0.0
        tableConnector.estimatedRowHeight = 90
        tableConnector.rowHeight = UITableView.automaticDimension
        tableConnector.isScrollEnabled = false // Disable scrolling since it's inside a scroll view

        pageController.isUserInteractionEnabled = false

        scrollChargerImages.delegate = self
        getCSDetails()

    }

    override func viewDidLayoutSubviews() {

        btnBack.addBorder(color: Constants.backArrowBorderColor!, width: Int(1))
        btnBack.maskClipCorner(cornerRadius: 10)

        btnHistory.addBorder(color: Constants.backArrowBorderColor!, width: Int(1))
        btnHistory.maskClipCorner(cornerRadius: 10)

        btnReserveSlot.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        btnStartCharging.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)

        viewChargeStationInfo.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        viewOpenCloseInfo.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        viewConnectorListInfo.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        viewConnectorListDetailInfo.maskClipCorner(cornerRadius: 12)
        viewOpenCloseDetails.maskClipCorner(cornerRadius: 12)

        imgChargeStation.roundCorners(corners: [.topLeft,.topRight], radius: 12)
        scrollChargerImages.roundCorners(corners: [.topLeft,.topRight], radius: 12)

        viewOpenClose.roundCorners(corners: [.topLeft,.topRight], radius: 12)
        viewGetDirections.roundCorners(corners: [.bottomLeft,.bottomRight], radius: 12)

        tableConnector.maskClipCorner(cornerRadius: 12)

        btnReserveSlot1.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)

        viewReserveDetaiils.maskClipCorner(cornerRadius: 12)

        viewMainReserve.roundCorners(corners: [.topLeft,.topRight], radius: 16)

        viewReserve15.addBorder(color: Constants.textNotSelectedColor!, width: Int(1))
        viewReserve15.maskClipCorner(cornerRadius: 8)

        viewReserve30.addBorder(color: Constants.textNotSelectedColor!, width: Int(1))
        viewReserve30.maskClipCorner(cornerRadius: 8)

        viewReserve45.addBorder(color: Constants.textNotSelectedColor!, width: Int(1))
        viewReserve45.maskClipCorner(cornerRadius: 8)

        viewReserve60.addBorder(color: Constants.textNotSelectedColor!, width: Int(1))
        viewReserve60.maskClipCorner(cornerRadius: 8)

    }

    override func viewWillAppear(_ animated: Bool) {
        collectionTime.isScrollEnabled = false
        arrDays.removeAll()
        fetchDays()
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        navigationController?.interactivePopGestureRecognizer?.delegate = self
        navigationController?.interactivePopGestureRecognizer?.isEnabled = true

        // Update scroll view content size after view has appeared and layout is finalized
        if !connectorList.isEmpty {
            updateScrollViewContentSize()
        }
    }

    // MARK: - Button Actions
    @IBAction func backAction(_ sender: UIButton) {
        navigationController?.popViewController(animated: true)
    }

    @IBAction func reserveSlotTapped(_ sender: UIButton) {
        viewBgReserve.isHidden = false
    }

    @IBAction func startChargingTapped(_ sender: UIButton) {
        AppDelegate.shared.tabIndex = 0
        let vc = HomeTabVC.instantiate(appStoryboard: .Home)
        navigationController?.pushViewController(vc, animated: true)
    }

    @IBAction func getDirectionsTapped(_ sender: UIButton) {
//        if (UIApplication.shared.canOpenURL(URL(string:"comgooglemaps://")!)) {
//            UIApplication.shared.open(URL(string:
//                "comgooglemaps://?saddr\(UserDefaults.standard.value(forKey: Constants.LATITUDE)!),\(UserDefaults.standard.value(forKey: Constants.LONGITUDE)!)=&daddr=\(self.cs_lat),\(self.cs_lng)&directionsmode=driving")!)
//        } else {
//            print("Can't use comgooglemaps://")
//        }

        let alert = UIAlertController(title: "", message: "Choose Application", preferredStyle: .actionSheet)

        alert.addAction(UIAlertAction(title: "Maps", style: .default , handler:{ (UIAlertAction)in
            print("User click Approve button")
            let source = MKMapItem(placemark: MKPlacemark(coordinate: CLLocationCoordinate2D(latitude: AppDelegate.shared.lat, longitude: AppDelegate.shared.lng)))
            source.name = "Source"

            let destination = MKMapItem(placemark: MKPlacemark(coordinate: CLLocationCoordinate2D(latitude: self.cs_lat, longitude: self.cs_lng)))
            destination.name = "Destination"

            MKMapItem.openMaps(
                with: [source, destination],
                launchOptions: [MKLaunchOptionsDirectionsModeKey: MKLaunchOptionsDirectionsModeDriving]
            )
        }))

        alert.addAction(UIAlertAction(title: "Google Maps", style: .default , handler:{ (UIAlertAction)in
            print("User click Edit button")
            if (UIApplication.shared.canOpenURL(URL(string:"comgooglemaps://")!)) {
                UIApplication.shared.open(URL(string:
                                                "comgooglemaps://?saddr\(UserDefaults.standard.value(forKey: Constants.LATITUDE)!),\(UserDefaults.standard.value(forKey: Constants.LONGITUDE)!)=&daddr=\(self.cs_lat),\(self.cs_lng)&directionsmode=driving")!)
            } else {
                print("Can't use comgooglemaps://")
            }
        }))

        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel, handler:{ (UIAlertAction)in
               print("User click Dismiss button")
           }))

        //uncomment for iPad Support
        alert.popoverPresentationController?.sourceView = self.view

        self.present(alert, animated: true, completion: {
            print("completion block")
        })
    }

    @IBAction func openCloseTapped(_ sender: UIButton) {
//        viewOpenCloseDetails.isHidden = !viewOpenCloseDetails.isHidden
    }

    @IBAction func reserveSlotPopUpTapped(_ sender: UIButton) {
        viewBgReserve.isHidden = true
    }

    @IBAction func reserveTime(_ sender: UIButton) {
        filterType(typeID: sender.tag)
    }

    @IBAction func reserveInfoTapped(_ sender: UIButton) {

    }

    @IBAction func reservePopupCloseTapped(_ sender: UIButton) {
        viewBgReserve.isHidden = true
    }


    // MARK: - Setup Page Controller
    func setupScreens() {
        print("arrImage:-",arrImage)
        for index in 0..<arrImage.count {
            // 1.
            frame.origin.x = scrollChargerImages.frame.size.width * CGFloat(index)
            frame.size = scrollChargerImages.frame.size

            // 2.
            let imgView = UIImageView(frame: frame)
//            imgView.image = UIImage(named: arrImage[index])
            let imagePath = arrImage[index]
            imgView.sd_setImage(with: URL(string: imagePath)) { (image, error, type, url) in
                if (error != nil) {
                    imgView.image = UIImage(named: "ic_new_nxc_logo")
                } else {
                    imgView.image = image
                }
            }

            self.scrollChargerImages.addSubview(imgView)

            if arrImage.count == 0 {
                imgView.image = UIImage(named: "ic_new_nxc_logo")
            }
        }

        // 3.
        scrollChargerImages.contentSize = CGSize(width: (scrollChargerImages.frame.size.width * CGFloat(arrImage.count)), height: scrollChargerImages.frame.size.height)

    }


    // MARK: - Fetch Days
    func fetchDays() {
        let cal = Calendar.current
        var date = cal.startOfDay(for: Date())

        for i in 1 ... 7 {
            print(i)
            let day = cal.component(.weekday, from: date)
            var dic = NSDictionary()

            // Set today (first day) as selected by default
            let isSelected = (i == 1) ? "1" : "0"

            if day == 1 {
                dic = ["days"       :"Su",
                       "isSelected" : isSelected]
                arrDays.append(DaysData(dic: dic))
            } else if day == 2 {
                dic = ["days"       :"Mo",
                       "isSelected" : isSelected]
                arrDays.append(DaysData(dic: dic))
            } else if day == 3 {
                dic = ["days"       :"Tu",
                       "isSelected" : isSelected]
                arrDays.append(DaysData(dic: dic))
            } else if day == 4 {
                dic = ["days"       :"We",
                       "isSelected" : isSelected]
                arrDays.append(DaysData(dic: dic))
            } else if day == 5 {
                dic = ["days"       :"Th",
                       "isSelected" : isSelected]
                arrDays.append(DaysData(dic: dic))
            } else if day == 6 {
                dic = ["days"       :"Fr",
                       "isSelected" : isSelected]
                arrDays.append(DaysData(dic: dic))
            } else if day == 7 {
                dic = ["days"       : "Sa",
                       "isSelected" : isSelected]
                arrDays.append(DaysData(dic: dic))
            }
            date = cal.date(byAdding: .weekday , value: 1, to: date)!
        }
        print("days:-",arrDays)

        // Reload collection view to reflect the default selection
        DispatchQueue.main.async {
            self.collectionTime.reloadData()
        }
    }


    // MARK: - Status Type
    func filterType(typeID:Int) {

        //    0 = Pending,
        //    1 = Resolved,
        //    3 = Inprogress,
        //    4 = All,
        if typeID == 101 {
            viewReserve15.backgroundColor = Constants.primaryColor
            lblReserve15.textColor = .white

            viewReserve30.backgroundColor = .white
            lblReserve30.textColor = Constants.secondaryGrayText

            viewReserve45.backgroundColor = .white
            lblReserve45.textColor = Constants.secondaryGrayText

            viewReserve60.backgroundColor = .white
            lblReserve60.textColor = Constants.secondaryGrayText

            strReserveStatus = "15"
        } else if typeID == 102 {
            viewReserve15.backgroundColor = .white
            lblReserve15.textColor = Constants.secondaryGrayText

            viewReserve30.backgroundColor = Constants.primaryColor
            lblReserve30.textColor = .white

            viewReserve45.backgroundColor = .white
            lblReserve45.textColor = Constants.secondaryGrayText

            viewReserve60.backgroundColor = .white
            lblReserve60.textColor = Constants.secondaryGrayText

            strReserveStatus = "30"
        } else if typeID == 103 {
            viewReserve15.backgroundColor = .white
            lblReserve15.textColor = Constants.secondaryGrayText

            viewReserve30.backgroundColor = .white
            lblReserve30.textColor = Constants.secondaryGrayText

            viewReserve45.backgroundColor = Constants.primaryColor
            lblReserve45.textColor = .white

            viewReserve60.backgroundColor = .white
            lblReserve60.textColor = Constants.secondaryGrayText

            strReserveStatus = "45"
        } else {
            viewReserve15.backgroundColor = .white
            lblReserve15.textColor = Constants.secondaryGrayText

            viewReserve30.backgroundColor = .white
            lblReserve30.textColor = Constants.secondaryGrayText

            viewReserve45.backgroundColor = .white
            lblReserve45.textColor = Constants.secondaryGrayText

            viewReserve60.backgroundColor = Constants.primaryColor
            lblReserve60.textColor = .white

            strReserveStatus = "60"
        }
    }


    // MARK: - WebService
    func getCSDetails() {
        //        AppDelegate.shared.showHUD()
        let url = Constants.BASE_URL + API.CS_DETAILS
        paramCS = [   "lat"               :  "\(UserDefaults.standard.value(forKey: Constants.LATITUDE)!)",
                      "long"              :  "\(UserDefaults.standard.value(forKey: Constants.LONGITUDE)!)",
                      "charge_station_id" :  "\(AppDelegate.shared.strAddressID)"]
        print("paramCS:-",paramCS)
        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]
        AF.request(url, method: .post, parameters: paramCS, encoding:  URLEncoding.default, headers: headers).responseJSON { response in
            switch response.result {
            case .success:
                print(response)
                AppDelegate.shared.hideHUD()
                let JSON = response.value as! NSDictionary

                if "\(JSON["code"]!)" == "200" && "\(JSON["status"]!)" == "1" {

                    let jsonData = JSON["data"] as! NSDictionary

                    // Fix: "charge_station" is a dictionary, not an array
                    if let csData = jsonData["charge_station"] as? NSDictionary {
                        self.chargeStationDetails.removeAll()
                        self.chargeStationDetails.append(ChargeStation(dic: csData))

                        print(self.chargeStationDetails)
                        self.lblChargeStationName.text = self.chargeStationDetails[0].charge_station_name
                        self.lblChargeStationAddress.text = self.chargeStationDetails[0].cs_address
                        self.lblChargeStationReviews.text = "\(self.chargeStationDetails[0].avg_rating)"
                        self.chargeStationRatings.rating = Double(self.chargeStationDetails[0].avg_rating)!
                        self.cs_lat = Double(self.chargeStationDetails[0].lat)!
                        self.cs_lng = Double(self.chargeStationDetails[0].long)!
                    }

                    self.lblTimeDetails.text = "24 Hours"

                    // Handle images
                    self.arrImage.removeAll()
                    if let csImages = jsonData["charge_station"] as? NSDictionary,
                       let images = csImages["cs_images"] as? NSArray {
                        for item in images {
                            self.arrImage.append("\(item)")
                        }
                    }

                    self.pageController.numberOfPages = self.arrImage.count
                    self.setupScreens()
                    if self.arrImage.count > 0 {
                        let imagePath = self.arrImage[0]
                        self.imgChargeStation.sd_setImage(with: URL(string: imagePath)) { (image, error, type, url) in
                        }
                    } else {
                        self.imgChargeStation.image = UIImage(named: "ic_new_nxc_logo")
                    }

                    // Handle connectors from charge_boxes
                    self.connectorList.removeAll()
                    if let chargeBoxes = jsonData["charge_boxes"] as? NSArray {
                        for chargeBox in chargeBoxes {
                            if let cbDict = chargeBox as? NSDictionary,
                               let connectors = cbDict["connectors"] as? NSArray {

                                // Group connectors by connector_type.name
                                var connectorsByType: [String: [NSDictionary]] = [:]

                                for connector in connectors {
                                    if let connDict = connector as? NSDictionary,
                                       let connType = connDict["connector_type"] as? NSDictionary,
                                       let typeName = connType["name"] as? String {

                                        if connectorsByType[typeName] == nil {
                                            connectorsByType[typeName] = []
                                        }
                                        connectorsByType[typeName]?.append(connDict)
                                    }
                                }

                                // Create ConnectorDetails objects for each type
                                for (typeName, typeConnectors) in connectorsByType {
                                    print("🔌 Processing connector type: \(typeName) with \(typeConnectors.count) connectors")
                                    if let firstConn = typeConnectors.first,
                                       let connType = firstConn["connector_type"] as? NSDictionary {

                                        // Create connector type details
                                        var connTypeDetails: [ConnectorTypeDetails] = []
                                        for conn in typeConnectors {
                                            let status = conn["connector_status"] as? String ?? "Unknown"
                                            let powerRating = conn["connector_power_rating"] as? String ?? ""
                                            let powerType = (connType["power_type"] as? String) ?? ""
                                            print("⚡ Power rating: \(powerRating) for connector: \(conn["connector_id"] ?? "unknown")")

                                            // Use connector_pricing_summary directly from the new API structure
                                            // Handle both String and Number types for pricing summary
                                            let pricingSummary: String
                                            if let priceString = conn["connector_pricing_summary"] as? String {
                                                pricingSummary = priceString
                                            } else if let priceNumber = conn["connector_pricing_summary"] as? NSNumber {
                                                pricingSummary = priceNumber.stringValue
                                            } else {
                                                pricingSummary = "0.00"
                                            }
                                            print("🔍 Connector pricing summary: \(pricingSummary) for connector: \(conn["connector_id"] ?? "unknown")")

                                            // Format pricing to ensure 2 decimal places
                                            let formattedPrice: String
                                            if let priceValue = Double(pricingSummary) {
                                                formattedPrice = String(format: "%.2f", priceValue)
                                            } else {
                                                formattedPrice = "0.00"
                                            }
                                            print("💰 Formatted price: \(formattedPrice)")

                                            let detailsDict: NSDictionary = [
                                                "charger_code": conn["connector_cb_code"] ?? "",
                                                "connector_pk": conn["connector_pk"] ?? "",
                                                "connector_power_rating": powerRating,
                                                "connector_price": formattedPrice,
                                                "connector_status": status,
                                                "plug_id": conn["connector_id"] ?? "",
                                                "power_type": powerType
                                            ]
                                            connTypeDetails.append(ConnectorTypeDetails(dic: detailsDict))
                                        }

                                        // Create status details
                                        var availableCount = 0
                                        var unavailableCount = 0

                                        for conn in typeConnectors {
                                            if (conn["connector_status"] as? String) == "Available" {
                                                availableCount += 1
                                            } else {
                                                unavailableCount += 1
                                            }
                                        }

                                        var statusDetailsArray: NSArray = []
                                        var statusDetails: [[String: String]] = []

                                        if availableCount > 0 {
                                            statusDetails.append(["status": "Available", "total": "\(availableCount)"])
                                        }
                                        if unavailableCount > 0 {
                                            statusDetails.append(["status": "Unavailable", "total": "\(unavailableCount)"])
                                        }

                                        statusDetailsArray = statusDetails as NSArray

                                        // Convert ConnectorTypeDetails to array of dictionaries
                                        let connTypeDetailsArray = connTypeDetails.map { $0.toDictionary() } as NSArray

                                        // Create the connector details object
                                        let connDetailsDict: NSDictionary = [
                                            "connector_count": "\(typeConnectors.count)",
                                            "connector_current_type": connType["current_type"] ?? "",
                                            "connector_image": connType["image"] ?? "",
                                            "connector_name": typeName,
                                            "connector_power_type": connType["power_type"] ?? "",
                                            "connector_type_id": "1",
                                            "connector_type_details": connTypeDetailsArray,
                                            "status_details": statusDetailsArray
                                        ]

                                        self.connectorList.append(ConnectorDetails(dic: connDetailsDict))
                                    }
                                }
                            }
                        }
                    }

                    self.tableConnector.reloadData()

                    // Update scroll view content size to ensure last connector is visible
                    self.updateScrollViewContentSize()

                } else {
                    AppDelegate.shared.hideHUD()

                    if "\(JSON["code"]!)" == "100" {
                        AppDelegate.shared.apiKeyLogout()
                    } else {
                        self.alert(title: "Error", message: "\(JSON["msg"]!)")
                    }
                }
            case let .failure(error):
                print(error)

            }
        }
    }
}
extension ConnectorDetailsVC: UITableViewDelegate, UITableViewDataSource {

    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        let headerView = UIView.init(frame: CGRect.init(x: 0, y: 0, width: tableView.frame.width, height: 44))

        let img = UIImageView()
        img.frame = CGRect.init(x: 5, y: 0, width: 30, height: 30)
        let imagePath = self.connectorList[section].connector_image
        img.sd_setImage(with: URL(string: imagePath)) { (image, error, type, url) in
            img.image = img.image?.withRenderingMode(.alwaysTemplate)
            img.tintColor = Constants.primaryColor
        }

        headerView.addSubview(img)

        let lblConnectorName = UILabel()
        lblConnectorName.frame = CGRect.init(x: img.frame.minX + img.frame.width + 2, y: 0, width: headerView.frame.width-10, height: headerView.frame.height-15)
        lblConnectorName.text = self.connectorList[section].connector_name
        lblConnectorName.font = .systemFont(ofSize: 14, weight: .semibold)
        lblConnectorName.textColor = .black
        headerView.addSubview(lblConnectorName)

        let lblPlug = UILabel()
        lblPlug.frame = CGRect.init(x: headerView.frame.width - headerView.frame.height-15, y: 0, width: headerView.frame.width-10, height: headerView.frame.height-15)

        lblPlug.text = "\(self.connectorList[section].connector_count) Plug"
        lblPlug.font = .systemFont(ofSize: 14, weight: .semibold)
        lblPlug.textColor = Constants.orangeTextColor
        headerView.addSubview(lblPlug)

        return headerView
    }

    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return 24
    }

    func tableView(_ tableView: UITableView, viewForFooterInSection section: Int) -> UIView? {
        let footerView = UIView.init(frame: CGRect.init(x: 0, y: 0, width: tableView.frame.width, height: 0))
        return footerView
    }

    func tableView(_ tableView: UITableView, heightForFooterInSection section: Int) -> CGFloat {
        return 1
    }

    func numberOfSections(in tableView: UITableView) -> Int {
        return self.connectorList.count
    }

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return self.connectorList[section].connector_type_details.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "cell", for: indexPath) as! ConnectorDetailsCell

        let connectorInfo = connectorList[indexPath.section].connector_type_details[indexPath.row]
        print("connectorInfo1:-",connectorInfo)

        if connectorInfo.connector_status == "Unavailable" {
            cell.lblAvailable.textColor = .red
            cell.lblAvailable.text = connectorInfo.connector_status
            cell.imgAvailable!.image = cell.imgAvailable!.image?.withRenderingMode(.alwaysTemplate)
            cell.imgAvailable!.tintColor = .systemRed
        } else {
            cell.lblAvailable.textColor = .systemGreen
            cell.lblAvailable.text = connectorInfo.connector_status
            cell.imgAvailable!.image = cell.imgAvailable!.image?.withRenderingMode(.alwaysTemplate)
            cell.imgAvailable!.tintColor = .systemGreen
        }

        cell.lblChargeName.text = connectorInfo.charger_code

        // Format power type for display
        let powerTypeDisplay = formatPowerTypeForDisplay(connectorInfo.power_type)
        print("🚀 Power type: '\(connectorInfo.power_type)' -> Display: '\(powerTypeDisplay)'")

        cell.lblSpeedType.text = powerTypeDisplay

        cell.lblSpeed.text = connectorInfo.connector_power_rating
        cell.lblPrice.text = "₹ \(connectorInfo.connector_price)"

        if connectorInfo.power_type == "FAST" {
            cell.imgSpeedType!.image = cell.imgSpeedType!.image?.withRenderingMode(.alwaysTemplate)
            cell.imgSpeedType!.tintColor = Constants.primaryColor
        } else {
            cell.imgSpeedType!.image = cell.imgSpeedType!.image?.withRenderingMode(.alwaysTemplate)
            cell.imgSpeedType!.tintColor = .systemRed
        }

        return cell
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        self.indexSelected = indexPath.row
    }
}
extension ConnectorDetailsVC: UICollectionViewDelegate,UICollectionViewDataSource,UICollectionViewDelegateFlowLayout {

    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return CGSize(width: collectionView.frame.width/7, height: 55)
    }

    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return arrDays.count
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {

        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "cell", for: indexPath) as! ConnectorTimeCell

        cell.lblTime.text = arrDays[indexPath.item].days
        if arrDays[indexPath.item].isSelected == "1" {
            cell.viewMain.backgroundColor = Constants.timeSelectBgColor
            cell.lblTime.textColor = .white
            cell.imgTime!.image = cell.imgTime.image?.withRenderingMode(.alwaysTemplate)
            cell.imgTime!.tintColor = Constants.timeSelectDotColor
        } else {
            cell.viewMain.backgroundColor = .white
            cell.lblTime.textColor = .black
            cell.imgTime!.image = cell.imgTime.image?.withRenderingMode(.alwaysTemplate)
            cell.imgTime!.tintColor = .white
        }

        cell.layoutIfNeeded()
        cell.updateConstraints()

        return cell
    }

    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
//        var dic = NSDictionary()
//        dic = ["days"       :"\(self.arrDays[indexPath.item].days)",
//               "isSelected" : "1"]

        if let index = arrDays.firstIndex(where: {$0.isSelected == "1"}) {
            arrDays[index].isSelected = "0"
        }

        if let index = arrDays.firstIndex(where: {$0.days == "\(self.arrDays[indexPath.item].days)"}) {
            arrDays[index].isSelected = "1"
//            self.lblTimeDetails.text = "\(self.arrDays[indexPath.item].days) . 24 Hours"
            self.lblTimeDetails.text = "24 Hours"
        }

        collectionView.reloadData()

//        self.arrDays.replaceSubrange(, with: )
//        self.arrDays[indexPath.item].isSelected = "1"
//        collectionTime.reloadData()
    }
}
extension ConnectorDetailsVC: UIScrollViewDelegate {
    func scrollViewDidEndDecelerating(_ scrollView: UIScrollView) {
        let pageNumber = scrollView.contentOffset.x / scrollView.frame.size.width
        print("pageNumber:-",pageNumber)
        pageController.currentPage = Int(pageNumber)
    }
}
extension ConnectorDetailsVC: UIGestureRecognizerDelegate {
    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldBeRequiredToFailBy otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        return true
    }
}


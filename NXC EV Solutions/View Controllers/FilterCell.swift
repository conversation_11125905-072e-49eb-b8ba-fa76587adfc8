//
//  FilterCell.swift
//  NXC EV Solutions
//
//  Created by Dev<PERSON>per on 30/07/21.
//

import UIKit

class FilterCell: UICollectionViewCell {
    
    @IBOutlet weak var viewMain: UIView!
    @IBOutlet weak var imgType: UIImageView!
    @IBOutlet weak var lblType: UILabel!
    
    
    override func awakeFromNib() {
        super.awakeFromNib()

//        viewMain.addBorder(color: Constants.textNotSelectedColor!, width: 1)
        
        viewMain.backgroundColor = .white
        viewMain.shadowWithCRadius(radius: 12, color: .clear)
        
//        viewMain.roundCorners(corners: <#T##UIRectCorner#>, radius: <#T##CGFloat#>)
    }
}

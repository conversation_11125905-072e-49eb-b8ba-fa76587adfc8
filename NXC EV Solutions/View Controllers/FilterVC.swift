//
//  FilterVC.swift
//  NXC EV Solutions
//
//  Created by <PERSON><PERSON><PERSON> on 22/07/21.
//

import UIKit
import Alamofire

class FilterVC: UIViewController {


    // MARK: - IBOutlets
    @IBOutlet weak var scrollDetails: UIScrollView!

    @IBOutlet weak var btnAll: UIButton!
    @IBOutlet weak var btnPublic: UIButton!
    @IBOutlet weak var btnPrivate: UIButton!

    @IBOutlet weak var collectionType: UICollectionView!

    @IBOutlet weak var collectionHeight: NSLayoutConstraint!
    @IBOutlet weak var btnApply: UIButton!

    @IBOutlet weak var img4: UIImageView!
    @IBOutlet weak var img3: UIImageView!
    @IBOutlet weak var img2: UIImageView!
    @IBOutlet weak var img1: UIImageView!

    @IBOutlet weak var btnBack: UIButton!

    // MARK: - Favourite Filter Outlets (Hidden for now)
    @IBOutlet weak var stackViewOnlyFavourite: UIStackView!

    var connectorType:[ConnectorType] = []


    // MARK: - View LifeCycle Methods
    override func viewDidLoad() {
        super.viewDidLoad()

        collectionType.delegate = self
        collectionType.dataSource = self

        // Hide the "Only Favourite" filter option as it's not fully implemented
        stackViewOnlyFavourite.isHidden = true

        if AppDelegate.shared.strChargerType == "0" {
            chargerType(typeID: 101)
        } else if AppDelegate.shared.strChargerType == "4" {
            chargerType(typeID: 102)
        } else if AppDelegate.shared.strChargerType == "1" {
            chargerType(typeID: 103)
        }
        getConnectorType()
    }

    override func viewDidAppear(_ animated: Bool) {
        AppDelegate.shared.isSwipeBack = 1
        navigationController?.interactivePopGestureRecognizer?.delegate = self
        navigationController?.interactivePopGestureRecognizer?.isEnabled = true
    }

    override func viewDidLayoutSubviews() {
        btnApply.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)

        btnAll.maskClipCorner(cornerRadius: 12)
        btnPublic.maskClipCorner(cornerRadius: 12)
        btnPrivate.maskClipCorner(cornerRadius: 12)

        btnBack.addBorder(color: Constants.backArrowBorderColor!, width: Int(1))
        btnBack.maskClipCorner(cornerRadius: 10)

    }

    // MARK: - Charge Type Methods
    func chargerType(typeID:Int) {
        if typeID == 101 {

            btnAll.backgroundColor = Constants.primaryColor
            btnAll.setTitleColor(.white, for: .normal)

            btnPublic.backgroundColor = .clear
            btnPublic.setTitleColor(Constants.textNotSelectedColor, for: .normal)

            btnPrivate.backgroundColor = .clear
            btnPrivate.setTitleColor(Constants.textNotSelectedColor, for: .normal)

            AppDelegate.shared.strChargerType = "0"

        } else if typeID == 102 {

            btnAll.backgroundColor = .clear
            btnAll.setTitleColor(Constants.textNotSelectedColor, for: .normal)

            btnPublic.backgroundColor = Constants.primaryColor
            btnPublic.setTitleColor(.white, for: .normal)

            btnPrivate.backgroundColor = .clear
            btnPrivate.setTitleColor(Constants.textNotSelectedColor, for: .normal)

            AppDelegate.shared.strChargerType = "4"

        } else {

            btnAll.backgroundColor = .clear
            btnAll.setTitleColor(Constants.textNotSelectedColor, for: .normal)

            btnPublic.backgroundColor = .clear
            btnPublic.setTitleColor(Constants.textNotSelectedColor, for: .normal)

            btnPrivate.backgroundColor = Constants.primaryColor
            btnPrivate.setTitleColor(.white, for: .normal)

            AppDelegate.shared.strChargerType = "1"

        }
    }

    // MARK: - Ratings Methods
    func ratingsType(typeID:Int) {
        if typeID == 104 {

            img4.isHighlighted = true
            img3.isHighlighted = false
            img2.isHighlighted = false
            img1.isHighlighted = false

        } else if typeID == 103 {

            img4.isHighlighted = false
            img3.isHighlighted = true
            img2.isHighlighted = false
            img1.isHighlighted = false

        } else if typeID == 102 {

            img4.isHighlighted = false
            img3.isHighlighted = false
            img2.isHighlighted = true
            img1.isHighlighted = false

        } else {

            img4.isHighlighted = false
            img3.isHighlighted = false
            img2.isHighlighted = false
            img1.isHighlighted = true
        }
    }

    // MARK: - Button Actions
    @IBAction func backAction(_ sender: UIButton) {
        navigationController?.popViewController(animated: true)
    }

    @IBAction func chargerTypeActions(_ sender: UIButton) {
        chargerType(typeID: sender.tag)
    }

    @IBAction func ratingsAction(_ sender: UIButton) {
        ratingsType(typeID: sender.tag)
    }

    @IBAction func applyAction(_ sender: UIButton) {
        navigationController?.popViewController(animated: true)
    }

    func connectorTapped(indexValue:Int) {

        print("connectorTapped \(indexValue)")

        var item:NSDictionary = NSDictionary()
        if self.connectorType[indexValue].statusType == "1" {
            item = ["ct_id"         : "\(self.connectorType[indexValue].ct_id)",
                    "ct_name"       : "\(self.connectorType[indexValue].ct_name)",
                    "ct_image"      : "\(self.connectorType[indexValue].ct_image)",
                    "statusType"    : "0"]
        } else {
            item = ["ct_id"         : "\(self.connectorType[indexValue].ct_id)",
                    "ct_name"       : "\(self.connectorType[indexValue].ct_name)",
                    "ct_image"      : "\(self.connectorType[indexValue].ct_image)",
                    "statusType"    : "1"]
        }

        if self.connectorType[indexValue].statusType == "1" {
            self.connectorType[indexValue].statusType = "0"
        } else {
            self.connectorType[indexValue].statusType = "1"
        }
        print(item)

        var arrC:[ConnectorType] = []
        arrC.append(ConnectorType(dic: item))
        if AppDelegate.shared.arrCType.count == 0 {
            AppDelegate.shared.arrCType.append(ConnectorType(dic: item))
        } else {
            if let row = AppDelegate.shared.arrCType.index(where: {$0.ct_id == "\(item["ct_id"]!)"}) {
                AppDelegate.shared.arrCType.remove(at: row)
            } else {
                AppDelegate.shared.arrCType.append(ConnectorType(dic: item))
            }
        }

        print("AppDelegate.shared.arrCType \(AppDelegate.shared.arrCType)")
        self.collectionType.reloadData()
    }

    //MARK: - Webservice Methods
    func getConnectorType() {
        let url = Constants.BASE_URL + API.CONNECTER_TYPE
        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]
        AF.request(url, method: .get, parameters: nil, encoding:  URLEncoding.default, headers: headers).responseJSON { response in
            switch response.result {
            case .success:
                print(response)
                let JSON = response.value as! NSDictionary
                print(JSON)
                if "\(JSON["code"]!)" == "200" && "\(JSON["status"]!)" == "1" {

                    let jsonData = JSON["data"] as! NSDictionary

                    self.connectorType.removeAll()
                    for item in jsonData["connecter_data"] as! NSArray {
                        self.connectorType.append(ConnectorType(dic: item as! NSDictionary))
                    }

                    print("self.connectorType:-",self.connectorType)
                    AppDelegate.shared.arrCType = self.connectorType

                    self.collectionType.reloadData()

                    let height = self.collectionType.collectionViewLayout.collectionViewContentSize.height
                    self.collectionHeight.constant = height
                    self.view.layoutIfNeeded()

                } else {
                    AppDelegate.shared.hideHUD()
                }
            case let .failure(error):
                print(error)
                AppDelegate.shared.hideHUD()
            }
        }
    }
}
extension FilterVC: UICollectionViewDelegate,UICollectionViewDataSource,UICollectionViewDelegateFlowLayout {

    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let _outletoflbl = UILabel(frame: CGRect.zero)
        let text = self.connectorType[indexPath.item].ct_name
        _outletoflbl.text = text
        return CGSize(width: _outletoflbl.intrinsicContentSize.width + 50, height: 55)
    }

    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return self.connectorType.count
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {

        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "cell", for: indexPath) as! FilterCell
        cell.lblType.text = self.connectorType[indexPath.item].ct_name

        let imagePath = self.connectorType[indexPath.item].ct_image

        if self.connectorType[indexPath.item].statusType == "1" {
            cell.viewMain.backgroundColor = Constants.primaryColor
            cell.lblType.textColor = .white
            cell.lblType.sizeToFit()
            cell.imgType.sd_setImage(with: URL(string: imagePath)) { (image, error, type, url) in
                cell.imgType.image = cell.imgType.image?.withRenderingMode(.alwaysTemplate)
                cell.imgType.tintColor = .white
            }
            cell.viewMain.addBorder(color:.white, width: Int(1))
        } else {
            cell.viewMain.backgroundColor = .clear
            cell.lblType.textColor = .black
            cell.lblType.sizeToFit()
            cell.imgType.sd_setImage(with: URL(string: imagePath)) { (image, error, type, url) in
                cell.imgType.image = cell.imgType.image?.withRenderingMode(.alwaysTemplate)
                cell.imgType.tintColor = Constants.primaryColor
            }
            cell.viewMain.addBorder(color: Constants.textBorderColor!, width: Int(1))
        }

        cell.layoutIfNeeded()
        cell.updateConstraints()

        return cell
    }

    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        self.connectorTapped(indexValue: indexPath.item)
    }
}
extension FilterVC:UIGestureRecognizerDelegate {
    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldBeRequiredToFailBy otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        return true
    }
}

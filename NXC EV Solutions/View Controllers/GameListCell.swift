//
//  GameListCell.swift
//  NXC EV Solutions
//
//  Created by Dev<PERSON>per on 09/10/21.
//

import UIKit

class GameListCell: UICollectionViewCell {
    
    @IBOutlet weak var viewMain: UIView!
    @IBOutlet weak var imgBg: UIImageView!
    @IBOutlet weak var imgGameName: UIImageView!
    @IBOutlet weak var lblGameName: UILabel!
        
    
    // MARK: - View LifeCycle Methods
    override func awakeFromNib() {
        super.awakeFromNib()

        viewMain.backgroundColor = Constants.primaryColor
        viewMain.maskClipCorner(cornerRadius: 16)
    }

//    override func setSelected(_ selected: <PERSON><PERSON>, animated: <PERSON><PERSON>) {
//        super.setSelected(selected, animated: animated)
//
//    }
}

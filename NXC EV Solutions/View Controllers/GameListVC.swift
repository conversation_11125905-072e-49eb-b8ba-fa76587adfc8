//
//  GameListVC.swift
//  NXC EV Solutions
//
//  Created by <PERSON><PERSON><PERSON> on 09/10/21.
//

import UIKit
import Alamofire

class GameListVC: UIViewController {

    // MARK: - IBOutlets
    @IBOutlet weak var btnBack: UIButton!
    @IBOutlet weak var collectionList: UICollectionView!
    @IBOutlet weak var viewNoData: UIView!

    var gameList:[Games] = []

    // MARK: - View LifeCycle Methods
    override func viewDidLoad() {
        super.viewDidLoad()

        collectionList.delegate = self
        collectionList.dataSource = self
        setupUI()
    }

    override func viewWillAppear(_ animated: Bool) {
        getGameList()
    }

    override func viewDidAppear(_ animated: Bool) {
        AppDelegate.shared.isSwipeBack = 1
        navigationController?.interactivePopGestureRecognizer?.delegate = self
        navigationController?.interactivePopGestureRecognizer?.isEnabled = true
    }

    override func viewDidLayoutSubviews() {
        btnBack.addBorder(color: Constants.backArrowBorderColor!, width: Int(1))
        btnBack.maskClipCorner(cornerRadius: 10)
    }

    // MARK: - Setup UI
    func setupUI() {
        // Initially hide the no data view
        viewNoData.isHidden = true
    }

    // MARK: - Button Actions
    @IBAction func backTapped(_ sender: UIButton) {
        navigationController?.popViewController(animated: true)
    }

    // MARK: - Update UI
    func updateUI() {
        if gameList.count > 0 {
            collectionList.isHidden = false
            viewNoData.isHidden = true
        } else {
            collectionList.isHidden = true
            viewNoData.isHidden = false
        }
        collectionList.reloadData()
    }

    // MARK: - Webservice
    func getGameList() {

        AppDelegate.shared.showHUD()
        let url = Constants.BASE_URL + API.GAME_LIST
        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]
        AF.request(url, method: .get, parameters: nil, encoding:  URLEncoding.default, headers: headers).responseJSON { response in
            print(response)
            AppDelegate.shared.hideHUD()
            switch response.result {
            case .success:

                let JSON = response.value as! NSDictionary
                print(JSON)

                if "\(JSON["code"]!)" == "200" && "\(JSON["status"]!)" == "1" {

                    let jsonData = JSON["data"] as! NSDictionary
                    print(jsonData)

                    self.gameList.removeAll()
                    for item in jsonData["game_list"] as! NSArray {
                        self.gameList.append(Games(dic: item as! NSDictionary))
                    }

                    self.updateUI()
                } else {
                    // Only logout if it's an authentication error (code 100)
                    if "\(JSON["code"]!)" == "100" {
                        AppDelegate.shared.apiKeyLogout()
                    } else {
                        // Show "No Games Found" view
                        self.gameList.removeAll()
                        self.updateUI()
                    }
                }
                break
            case .failure(let error):
                print(error)
                AppDelegate.shared.hideHUD()
                // Show "No Games Found" view on network error
                self.gameList.removeAll()
                self.updateUI()
            }
        }
    }

}
extension GameListVC: UICollectionViewDelegate,UICollectionViewDataSource,UICollectionViewDelegateFlowLayout {

    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
//        let _outletoflbl = UILabel(frame: CGRect.zero)
//        let text = self.connectorType[indexPath.item].ct_name
//        _outletoflbl.text = text
        let collectionViewWidth = collectionView.bounds.width
        return CGSize(width: (collectionViewWidth/2.0) - 8, height: 130)
    }

    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return gameList.count
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {

        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "cell", for: indexPath) as! GameListCell

        // Get the background color value from the API
        let bgColorValue = gameList[indexPath.item].bg_image

        // Set the background color based on the value
        // Check if it's a named color or a hex code
        if let namedColor = getColorFromName(bgColorValue) {
            // It's a named color
            cell.viewMain.backgroundColor = namedColor
        } else {
            // Try to parse it as a hex color
            cell.viewMain.backgroundColor = UIColor(hexString: bgColorValue) ?? Constants.primaryColor
        }

        let imgGameIcon = gameList[indexPath.item].icon
        cell.imgGameName.sd_setImage(with: URL(string: imgGameIcon)) { (image, error, type, url) in

        }

        cell.lblGameName.text = gameList[indexPath.item].name

        cell.layoutIfNeeded()
        cell.updateConstraints()

        return cell
    }

    // Helper function to convert color names to UIColor
    func getColorFromName(_ colorName: String) -> UIColor? {
        switch colorName.lowercased() {
        case "red":
            return UIColor.red
        case "green":
            return UIColor.green
        case "blue":
            return UIColor.blue
        case "yellow":
            return UIColor.yellow
        case "orange":
            return UIColor.orange
        case "purple":
            return UIColor.purple
        case "black":
            return UIColor.black
        case "white":
            return UIColor.white
        case "gray", "grey":
            return UIColor.gray
        case "cyan":
            return UIColor.cyan
        case "magenta":
            return UIColor.magenta
        case "brown":
            return UIColor.brown
        default:
            return nil
        }
    }

    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        AppDelegate.shared.gameURL = gameList[indexPath.item].url
        let vc = GameDetailVC.instantiate(appStoryboard: .Help)
        self.navigationController?.pushViewController(vc, animated: true)
    }
}
extension GameListVC:UIGestureRecognizerDelegate {
    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldBeRequiredToFailBy otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        return true
    }
}

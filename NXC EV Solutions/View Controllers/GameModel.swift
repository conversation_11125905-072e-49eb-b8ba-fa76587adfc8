//
//  GameModel.swift
//  NXC EV Solutions
//
//  Created by Developer on 09/10/21.
//

import Foundation

struct Games {

    var name:String = String()
    var icon:String = String()
    var bg_image:String = String()
    var url:String = String()

    init(dic:NSDictionary) {
        self.name = "\(dic["name"]!)"
        self.icon = "\(dic["icon"]!)"
        self.bg_image = "\(dic["bg_image"]!)"
        self.url = "\(dic["url"]!)"
    }
}

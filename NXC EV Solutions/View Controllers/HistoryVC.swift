//
//  HistoryVC.swift
//  NXC EV Solutions
//
//  Created by <PERSON><PERSON><PERSON> on 06/08/21.
//

import UIKit
import DropDown
import Alamofire

class HistoryVC: UIViewController {

    // MARK: - IBOutlets
    @IBOutlet weak var btnAll: UIButton!
    @IBOutlet weak var btnPaidForCharging: UIButton!
    @IBOutlet weak var btnAddToWallet: UIButton!
    @IBOutlet weak var tableList: UITableView!
    @IBOutlet weak var viewDebit: UIView!
    @IBOutlet weak var lblDebit: UILabel!
    @IBOutlet weak var viewCredit: UIView!
    @IBOutlet weak var lblCredit: UILabel!

    @IBOutlet weak var viewBgFilter: UIView!
    @IBOutlet weak var viewMainFilter: UIView!

    @IBOutlet weak var viewVehicle: UIView!
    @IBOutlet weak var lblVehicle: UILabel!

    @IBOutlet weak var viewFromDate: UIView!
    @IBOutlet weak var viewToDate: UIView!
    @IBOutlet weak var btnSubmit: UIButton!

    @IBOutlet weak var lblFromDate: UILabel!
    @IBOutlet weak var lblToDate: UILabel!

    @IBOutlet weak var viewBgDate: UIView!
    @IBOutlet weak var viewMainDate: UIView!
    @IBOutlet weak var datePicker: UIDatePicker!
    @IBOutlet weak var btnOKDate: UIButton!

    @IBOutlet weak var btnBack: UIButton!
    @IBOutlet weak var viewNoData: UIView!
    @IBOutlet weak var btnFilter: UIButton!

    @IBOutlet weak var lblDOBTitle: UILabel!
    @IBOutlet weak var lblFilterBy: UILabel!
    @IBOutlet weak var lblSelectVehicleTitle: UILabel!
    @IBOutlet weak var lblSelectDateTitle: UILabel!

    @IBOutlet weak var lblTitle: UILabel!


    let vehicleDropDown = DropDown()
    var arrVehicle: [String] = []
    var dateType:String = String()

    var walletList:[Wallet] = []
    var paramWallet:[String:Any] = [:]
    var fromDate:String = String()
    var toDate:String = String()

    var dbcrStatus = String()
    var vehicleDetails:[VehicleDetails] = []
    var state: State = .noData {
        didSet {
            switch state {
            case .loaded:
                tableList.isHidden = false
                viewNoData.isHidden = true
            case .noData:
                tableList.isHidden = true
                viewNoData.isHidden = false
            }
        }
    }

    let yourAttributes: [NSAttributedString.Key: Any] = [
        .font: UIFont.boldSystemFont(ofSize: 15), //systemFont(ofSize: 14),
        .foregroundColor: Constants.primaryColor!,
        .underlineStyle: NSUnderlineStyle.single.rawValue
    ] // .double.rawValue, .thick.rawValue



    // MARK: - View LifeCycle
    override func viewDidLoad() {
        super.viewDidLoad()

        tableList.delegate = self
        tableList.dataSource = self

        viewBgFilter.isHidden = true
        viewBgDate.isHidden = true

        setupDropDowns()

        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "dd MMM yyyy"
        datePicker.maximumDate = Date()

        let dateFormatter1 = DateFormatter()
        dateFormatter1.timeZone = .current
        dateFormatter1.dateFormat = "yyyy-MM-dd"
        toDate = dateFormatter1.string(from: Date())
        print("toDate:-",toDate)

        let calendar = Calendar.current
        let date = calendar.date(byAdding: .day, value: -30, to: Date())
        fromDate = dateFormatter1.string(from: date!)
        print("fromDate:-",fromDate)

        lblFromDate.text = dateFormatter.string(from: date!)
        lblToDate.text = dateFormatter.string(from: Date())

        self.viewProfile()

    }

    override func viewWillAppear(_ animated: Bool) {
        //        lblTitle.text = "History".localiz()
        //        lblFilterBy.text = "Filter By".localiz()
        //        lblSelectVehicleTitle.text = "Select Vehicle".localiz()
        //        lblSelectDateTitle.text = "Select Date".localiz()
        //        lblTitle.text = "History".localiz()
        //        lblTitle.text = "History".localiz()
        //        btnAll.setTitle("All".localiz(), for: .normal)
        //        btnPaidForCharging.setTitle("Paid For Charging".localiz(), for: .normal)
        //        btnAddToWallet.setTitle("Added To Wallet".localiz(), for: .normal)
    }

    override func viewDidAppear(_ animated: Bool) {
        AppDelegate.shared.isSwipeBack = 1
        navigationController?.interactivePopGestureRecognizer?.delegate = self
        navigationController?.interactivePopGestureRecognizer?.isEnabled = true
    }

    override func viewDidLayoutSubviews() {

        btnBack.addBorder(color: Constants.backArrowBorderColor!, width: Int(1))
        btnBack.maskClipCorner(cornerRadius: 10)

        btnFilter.addBorder(color: Constants.backArrowBorderColor!, width: Int(1))
        btnFilter.maskClipCorner(cornerRadius: 10)

        btnSubmit.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)

        viewMainFilter.roundCorners(corners: [.topLeft,.topRight], radius: 16)

        viewFromDate.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        viewToDate.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        btnOKDate.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        viewMainDate.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        viewDebit.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        viewCredit.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)

        vehicleDropDown.backgroundColor = .white
        vehicleDropDown.textColor = .black
        vehicleDropDown.selectedTextColor = .white
        vehicleDropDown.selectionBackgroundColor = Constants.primaryColor!

        vehicleDropDown.maskClipCorner(cornerRadius: 8)
    }


    //MARK: -  DropDown Methods
    func setupDropDowns() {
        setupVehicleDropDown()
    }

    func setupVehicleDropDown() {
        vehicleDropDown.anchorView = viewVehicle
        vehicleDropDown.bottomOffset = CGPoint(x: 0, y: viewVehicle.bounds.height)
        vehicleDropDown.selectionAction = { [weak self] (index, item) in
            self!.lblVehicle.text = "\(item)"
            // Store the enhanced display text for consistency
            UserDefaults.standard.set("\(item)", forKey: Constants.VEHICLE_NAME)

            if index == 0 {
                // "All Vehicle" option selected - set vehicle ID to 0
                UserDefaults.standard.set("0", forKey: Constants.VEHICLE_ID)
                AppDelegate.shared.strVehicleID = "0"
            } else {
                // Specific vehicle selected - adjust index for vehicleDetails array (subtract 1 for "All Vehicle" option)
                let vehicleIndex = index - 1
                UserDefaults.standard.set("\(self!.vehicleDetails[vehicleIndex].vehicle_id)", forKey: Constants.VEHICLE_ID)
                AppDelegate.shared.strVehicleID = "\(self!.vehicleDetails[vehicleIndex].vehicle_id)"
            }
        }
    }


    // MARK: - Filter
    func filterType(typeID:Int) {
        if typeID == 101 {
            btnAll.setTitleColor(Constants.primaryColor, for: .normal)
            btnPaidForCharging.setTitleColor(UIColor(named: Colors.PRIMARY_SELECTION), for: .normal)
            btnAddToWallet.setTitleColor(UIColor(named: Colors.PRIMARY_SELECTION), for: .normal)
            dbcrStatus = "3"
        } else if typeID == 102 {
            btnAll.setTitleColor(UIColor(named: Colors.PRIMARY_SELECTION), for: .normal)
            btnPaidForCharging.setTitleColor(Constants.primaryColor, for: .normal)
            btnAddToWallet.setTitleColor(UIColor(named: Colors.PRIMARY_SELECTION), for: .normal)
            dbcrStatus = "1"
        } else {
            btnAll.setTitleColor(UIColor(named: Colors.PRIMARY_SELECTION), for: .normal)
            btnPaidForCharging.setTitleColor(UIColor(named: Colors.PRIMARY_SELECTION), for: .normal)
            btnAddToWallet.setTitleColor(Constants.primaryColor, for: .normal)
            dbcrStatus = "2"
        }
        getWalletList(db_cr_status: dbcrStatus, vehicleID: "\(UserDefaults.standard.value(forKey: Constants.VEHICLE_ID) ?? "0")")
    }


    // MARK: - Button Actions
    @IBAction func backAction(_ sender: UIButton) {
        navigationController?.popViewController(animated: true)
        //        AppDelegate.shared.OpenCloseDrawer()
    }

    @IBAction func filterAction(_ sender: UIButton) {
        dateType = "0"
        filterType(typeID: sender.tag)
    }

    @IBAction func filterWalletAction(_ sender: UIButton) {
        viewBgFilter.isHidden = false
    }

    @IBAction func closeFilterAction(_ sender: UIButton) {
        viewBgFilter.isHidden = true
    }

    @IBAction func selectVehicleAction(_ sender: UIButton) {
        vehicleDropDown.show()
    }

    @IBAction func fromDateAction(_ sender: UIButton) {
        dateType = "1"
        viewBgDate.isHidden = false
    }

    @IBAction func toDateAction(_ sender: UIButton) {
        dateType = "2"
        viewBgDate.isHidden = false
    }

    @IBAction func okDateAction(_ sender: UIButton) {
        let dateFormatter = DateFormatter()
        dateFormatter.timeZone = .current
        dateFormatter.dateFormat = "dd MMM yyyy"
        if dateType == "1" {
            lblFromDate.text = dateFormatter.string(from: datePicker.date)
            let dateFormatter1 = DateFormatter()
            dateFormatter1.timeZone = .current
            dateFormatter1.dateFormat = "yyyy-MM-dd"
            fromDate = dateFormatter1.string(from:datePicker.date)
            print("fromDate:-",fromDate)
        } else if dateType == "2" {
            lblToDate.text = dateFormatter.string(from: datePicker.date)
            let dateFormatter1 = DateFormatter()
            dateFormatter1.timeZone = .current
            dateFormatter1.dateFormat = "yyyy-MM-dd"
            toDate = dateFormatter1.string(from:datePicker.date)
            print("fromDate:-",fromDate)
        }
        viewBgDate.isHidden = true
    }

    @IBAction func cancelDateAction(_ sender: UIButton) {
        viewBgDate.isHidden = true
    }

    @IBAction func submitAction(_ sender: UIButton) {
        viewBgFilter.isHidden = true
        viewBgDate.isHidden = true
        getWalletList(db_cr_status: dbcrStatus, vehicleID: "\(UserDefaults.standard.value(forKey: Constants.VEHICLE_ID) ?? "0")")
    }

    //MARK: - Webservice Methods
    func getWalletList(db_cr_status: String, vehicleID: String) {
        AppDelegate.shared.showHUD()
        let url = Constants.BASE_URL + API.GET_WALLET_HISTORY
        paramWallet = ["vehicle_id": vehicleID,
                       "start_date": fromDate,
                       "last_date": toDate,
                       "db_cr_status": db_cr_status]
        print("paramWallet:-",paramWallet)
        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]
        print("JWT_TOKEN:-",headers)
        AF.request(url, method: .post, parameters: paramWallet, encoding:  URLEncoding.default, headers: headers).responseJSON { response in
            print(response)
            AppDelegate.shared.hideHUD()

            switch response.result {
            case .success:

                let JSON = response.value as! NSDictionary
                print(JSON)

                if "\(JSON["code"]!)" == "200" && "\(JSON["status"]!)" == "1" {

                    let jsonData = JSON["data"] as! NSDictionary

                    self.lblDebit.text = "₹ \(jsonData["debited_amount"]!)"
                    self.lblCredit.text = "₹ \(jsonData["credited_amount"]!)"

                    self.walletList.removeAll()
                    for item in jsonData["wallet_data"] as! NSArray {
                        self.walletList.append(Wallet(dic: item as! NSDictionary))
                    }

                    self.tableList.reloadData()

                    if self.walletList.count > 0 {
                        self.state = .loaded
                    } else {
                        self.state = .noData
                    }

                } else {

                    if "\(JSON["code"]!)" == "100" {
                        AppDelegate.shared.apiKeyLogout()
                    }

                    if self.walletList.count > 0 {
                        self.state = .loaded
                    } else {
                        self.state = .noData
                    }
                    AppDelegate.shared.hideHUD()
                }
                break
            case .failure(let error):
                print(error)
                AppDelegate.shared.hideHUD()
            }
        }
    }

    func viewProfile() {
        //        AppDelegate.shared.showHUD()
        let url = Constants.BASE_URL + API.VIEW_PROFILE
        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]
        AF.request(url, method: .get, parameters: nil, encoding:  URLEncoding.default, headers: headers).responseJSON { response in
            print(response)
            AppDelegate.shared.hideHUD()
            switch response.result {
            case .success:

                let JSON = response.value as! NSDictionary
                print(JSON)

                if "\(JSON["code"]!)" == "200" && "\(JSON["status"]!)" == "1" {

                    let jsonData = JSON["data"] as! NSDictionary

                    self.vehicleDetails.removeAll()
                    for item in jsonData["vehicle_data"] as! NSArray {
                        self.vehicleDetails.append(VehicleDetails(dic: item as! NSDictionary))
                    }

                    // Clear dropdown data source and add "All Vehicle" as first option
                    self.vehicleDropDown.dataSource.removeAll()
                    self.vehicleDropDown.dataSource.append("All Vehicle")

                    for item in self.vehicleDetails {
                        // Create enhanced display format: "Model (Registration)"
                        let displayText = item.reg_num.isEmpty ? item.model_text : "\(item.model_text) (\(item.reg_num))"
                        self.vehicleDropDown.dataSource.append(displayText)
                    }

                    // Set "All Vehicle" as default selection
                    self.lblVehicle.text = "All Vehicle"
                    UserDefaults.standard.set("All Vehicle", forKey: Constants.VEHICLE_NAME)
                    UserDefaults.standard.set("0", forKey: Constants.VEHICLE_ID)
                    AppDelegate.shared.strVehicleID = "0"

                    self.vehicleDropDown.reloadAllComponents()
                    self.filterType(typeID: 101)

                } else {
                    AppDelegate.shared.hideHUD()
                    if "\(JSON["code"]!)" == "100" {
                        AppDelegate.shared.apiKeyLogout()
                    }
                }
                break
            case .failure(let error):
                print(error)
                AppDelegate.shared.hideHUD()
            }
        }
    }
}
extension HistoryVC: UITableViewDelegate, UITableViewDataSource {

    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        let headerView = UIView.init(frame: CGRect.init(x: 0, y: 0, width: tableView.frame.width, height: 50))

        let label = UILabel()
        label.frame = CGRect.init(x: 5, y: 0, width: headerView.frame.width-10, height: headerView.frame.height-15)
        let titleDate = self.walletList[section].date.convertDateString(fromFormat: "yyyy-MM-dd", toFormat: "MMM dd, yyyy")
        label.text = "  \(titleDate!)"
        label.font = .systemFont(ofSize: 14, weight: .semibold)
        label.textColor = Constants.menuIconColor
        headerView.addSubview(label)

        return headerView
    }

    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return 30
    }

    func numberOfSections(in tableView: UITableView) -> Int {
        return self.walletList.count
    }

    func tableView(_ tableView: UITableView, titleForHeaderInSection section: Int) -> String? {
        let titleDate = self.walletList[section].date.convertDateString(fromFormat: "yyyy-MM-dd", toFormat: "dd MMM yyyy")
        return titleDate
    }

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return self.walletList[section].walletData.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "cell", for: indexPath) as! WalletCell

        let walletInfo = walletList[indexPath.section].walletData
        cell.lblDate.text = walletInfo[indexPath.row].time.utcToLocal(dateFromValue: "HH:mm:ss", dateToValue: "hh:mm a", dateStr: walletInfo[indexPath.row].time)

        if "\(walletInfo[indexPath.row].type)" == "1" {
            cell.imgWallet.image = UIImage(named: "ic_car_wallet")
            cell.lblType.text = "Paid to NXC EV"
            cell.lblStatus.text = "Paid for charging"
            cell.lblStatus.isHidden = false
            cell.lblAmount.text = "- ₹ \(walletInfo[indexPath.row].amount)"
            cell.lblAmount.textColor = .systemRed
        } else if "\(walletInfo[indexPath.row].type)" == "2" {
            cell.imgWallet.image = UIImage(named: "ic_money_added")
            cell.lblType.text = "Money added to wallet"
            cell.lblStatus.text = "Added to wallet"
            cell.lblStatus.isHidden = false
            cell.lblAmount.text = "+ ₹ \(walletInfo[indexPath.row].amount)"
            cell.lblAmount.textColor = Constants.creditColor
        } else {
            cell.imgWallet.image = UIImage(named: "ic_money_added")
            cell.lblType.text = "Purchased RFID Card"
            cell.lblStatus.isHidden = true
            cell.lblAmount.text = "- ₹ \(walletInfo[indexPath.row].amount)"
            cell.lblAmount.textColor = .systemRed
        }

        cell.layoutIfNeeded()
        cell.updateConstraints()

        return cell
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {

        let walletInfo = walletList[indexPath.section].walletData
        AppDelegate.shared.strTransactionID = walletInfo[indexPath.row].transaction_id
        AppDelegate.shared.strTypeID = walletInfo[indexPath.row].type
        if walletInfo[indexPath.row].type == "1" || walletInfo[indexPath.row].type == "3" {
            let vc = TransactionDetailsVC.instantiate(appStoryboard: .Wallet)
            self.navigationController?.pushViewController(vc, animated: true)
        } else {
            let vc = PaymentDetailsVC.instantiate(appStoryboard: .Wallet)
            self.navigationController?.pushViewController(vc, animated: true)
        }
    }
}
extension HistoryVC:UIGestureRecognizerDelegate {
    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldBeRequiredToFailBy otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        return true
    }
}

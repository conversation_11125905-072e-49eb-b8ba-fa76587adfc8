//
//  HomeTabVC.swift
//  NXC EV Solutions
//
//  Created by <PERSON><PERSON><PERSON> on 22/07/21.
//

//import UIKit
//import AZTabBar
////import SOTabBar
//
//class HomeTabVC: UIViewController {
//
//    // MARK: - All IBOutlets
//    var tabController:AZTabBarController!
////    @IBOutlet weak var viewTItle: UIView!
////    @IBOutlet weak var btnMenu: UIButton!
//
//
//    // MARK: - View LifeCycle
//    override func viewDidLoad() {
//        super.viewDidLoad()
//
//        //The icons that will be displayed on the tabs that are not currently selected
//        var icons = [String]()
//        icons.append("ic_charging_gray")
//        icons.append("ic_map_png")
//        icons.append("ic_wallet_gray")
//
//        //The icons that will be displayed for each tab once they are selected.
//        var selectedIcons = [String]()
//        selectedIcons.append("ic_charging_white")
//        selectedIcons.append("ic_map_png")
//        selectedIcons.append("ic_wallet_white")
//
//        tabController = .insert(into: self, withTabIconNames: icons, andSelectedIconNames: selectedIcons)
//        tabController.delegate = self
//
//        let vc1 = MapChargerVC.instantiate(appStoryboard: .Charger)
//        tabController.setViewController(vc1, atIndex: 0)
//
//        let vc2 = MapChargerVC.instantiate(appStoryboard: .Charger)
//        tabController.setViewController(vc2, atIndex: 1)
//
//        let vc3 = WalletVC.instantiate(appStoryboard: .Wallet)
//        tabController.setViewController(vc3, atIndex: 2)
//
//        tabController.tabBarHeight = 50
//
//        //default color of the icons on the buttons
//        tabController.defaultColor = Constants.textNotSelectedColor!
//
//        //the color of the icon when a menu is selected
//        tabController.selectedColor = Constants.primaryColor!
//
//        //The color of the icon of a highlighted tab
//        tabController.highlightColor = .white
//
//        //The background color of the button of the highlighted tabs.
//        tabController.highlightedBackgroundColor = .green
//
//        //The background color of the tab bar
//        tabController.buttonsBackgroundColor = .white
//
//        //The color of the selection indicator.
//        tabController.selectionIndicatorColor = .green
//
//        // default is 3.0
//        tabController.selectionIndicatorHeight = 0
//
//        // change the seperator line color
//        tabController.separatorLineColor = .white
//
//        //hide or show the seperator line
//        tabController.separatorLineVisible = false
//
//        //Enable tab change animation that looks like facebook
//        tabController.animateTabChange = true
//
//        tabController.view.shadowWithCRadius(radius: 8, color: Constants.primaryColor!)
//
//    }
//
//    // MARK: - Button Actions
////    @IBAction func menuAction(_ sender: UIButton) {
////        let vc = MenuVC.instantiate(appStoryboard: .Home)
////        navigationController?.pushViewController(vc, animated: true)
////    }
//
//}
//extension HomeTabVC: AZTabBarDelegate {
//    func tabBar(_ tabBar: AZTabBarController, willMoveToTabAtIndex index: Int) {
////        print("willMoveToTabAtIndex:-", index)
////        AppDelegate.shared.typeID = index+1
//    }
//
//    func tabBar(_ tabBar: AZTabBarController, didMoveToTabAtIndex index: Int) {
////        print("didMoveToTabAtIndex:-", index)
//    }
//}
import UIKit
//import SOTabBar

class HomeTabVC: SOTabBarController {

//    var tabBar = SOTabBar()
    
    override func loadView() {
        super.loadView()
        
//        reloadTab()
        
        SOTabBarSetting.tabBarTintColor = #colorLiteral(red: 2.248547389e-05, green: 0.7047000527, blue: 0.6947537661, alpha: 1)
//        if AppDelegate.shared.isMenuOpen == 1 {
//            SOTabBarSetting.tabBarHeight = -30.0
//        } else {
            SOTabBarSetting.tabBarHeight = 55.0
//        }
        SOTabBarSetting.tabBarTintColor = Constants.primaryColor!
        SOTabBarSetting.tabBarBackground = UIColor.white
        SOTabBarSetting.tabBarCircleSize = CGSize(width: 55.0, height: 55.0)
        SOTabBarSetting.tabBarSizeImage = CGFloat(30)
        SOTabBarSetting.tabBarShadowColor = UIColor.lightGray.cgColor
        SOTabBarSetting.tabBarSizeSelectedImage = CGFloat(25)
        SOTabBarSetting.tabBarAnimationDurationTime = 0.2
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        
        self.delegate = self

        let vcCharging = ChargingVC.instantiate(appStoryboard: .Charging)
        let vcMap = MapChargerVC.instantiate(appStoryboard: .Charger)
        let vcWallet = WalletVC.instantiate(appStoryboard: .Wallet)

        vcCharging.tabBarItem = UITabBarItem(title: "", image: UIImage(named: "ic_charging_gray"), selectedImage: UIImage(named: "ic_charging_white"))
        vcMap.tabBarItem = UITabBarItem(title: "", image: UIImage(named: "ic_map_gray"), selectedImage: UIImage(named: "ic_map_white"))
        vcWallet.tabBarItem = UITabBarItem(title: "", image: UIImage(named: "ic_wallet_gray"), selectedImage: UIImage(named: "ic_wallet_white"))
        
        viewControllers = [vcCharging,vcMap,vcWallet]
        
        print("selectedIndex:-",selectedIndex)
        self.selectedIndex = 1
        print("selectedIndex:-",selectedIndex)
        
        
//        let notificationShowHideMenu = Notification.Name("notificationShowHideMenu")
//        NotificationCenter.default.addObserver(self, selector: #selector(reloadTab), name: notificationShowHideMenu, object: nil)
        
//        tabBar.layer.zPosition = -1
        
    }
    
    override func viewWillAppear(_ animated: Bool) {
//        if AppDelegate.shared.isSwipeBack == 1 {
//            self.navigationController?.interactivePopGestureRecognizer?.isEnabled = false
//        } else {
            self.navigationController?.interactivePopGestureRecognizer?.isEnabled = true
//        }
    }
    
    @objc func reloadTab() {
//        SOTabBarSetting.tabBarTintColor = #colorLiteral(red: 2.248547389e-05, green: 0.7047000527, blue: 0.6947537661, alpha: 1)
//        if AppDelegate.shared.isMenuOpen == 1 {
//            SOTabBarSetting.tabBarHeight = -30.0
//        } else {
//            SOTabBarSetting.tabBarHeight = 55.0
//        }
//        SOTabBarSetting.tabBarTintColor = Constants.primaryColor!
//        SOTabBarSetting.tabBarBackground = UIColor.white
//        SOTabBarSetting.tabBarCircleSize = CGSize(width: 55.0, height: 55.0)
//        SOTabBarSetting.tabBarSizeImage = CGFloat(30)
//        SOTabBarSetting.tabBarShadowColor = UIColor.lightGray.cgColor
//        SOTabBarSetting.tabBarSizeSelectedImage = CGFloat(25)
//        SOTabBarSetting.tabBarAnimationDurationTime = 0.2
    }
    
    @objc func showHideTabbar() {
//        if AppDelegate.shared.isMenuOpen == 1 {
//            let tabBar = SOTabBar()
//            tabBar.drawConstraint()
//        } else {
//            let tabBar = SOTabBar()
//            tabBar.drawConstraint()
//        }
    }
}

extension HomeTabVC: SOTabBarControllerDelegate {
    func tabBarController(_ tabBarController: SOTabBarController, didSelect viewController: UIViewController) {
        print(viewController.tabBarItem.title ?? "")
    }
}

//
//  IssueNewCardVC.swift
//  IssueNewCardVC
//
//  Created by <PERSON><PERSON><PERSON> on 23/09/21.
//

import UIKit
import Alamofire

class IssueNewCardVC: UIViewController {

    // MARK: - All IBOutlets
    @IBOutlet weak var backButton: UIButton!
    @IBOutlet weak var tableList: UITableView!

    @IBOutlet weak var viewBgAlert: UIView!
    @IBOutlet weak var viewMainAlert: UIView!
    @IBOutlet weak var noBlockButton: UIButton!
    @IBOutlet weak var yesBlockButton: UIButton!

    @IBOutlet weak var lblIssueCard: UILabel!
    @IBOutlet weak var lblTrackOrder: UILabel!

    @IBOutlet weak var viewTrackCard: UIView!
    @IBOutlet weak var viewAddCard: UIView!

    var rfidList:[RFIDList] = []
    var intRFIDAmount = Double()
    var intWalletAmount = Double()
    var currentCardIndex = -1 // To track which card is being blocked/unblocked


    // MARK: - View LifeCycle Methods
    override func viewDidLoad() {
        super.viewDidLoad()

        tableList.delegate = self
        tableList.dataSource = self
        viewBgAlert.isHidden = true

        self.tableList.isHidden = true
        self.viewTrackCard.isHidden = true
        self.viewAddCard.isHidden = true

        getRFIDList()

    }

    override func viewDidLayoutSubviews() {
        backButton.addBorder(color: Constants.backArrowBorderColor!, width: Int(1))
        backButton.maskClipCorner(cornerRadius: 10)

        viewMainAlert.shadowWithCRadius(radius:12, color: Constants.backArrowBorderColor!)

        yesBlockButton.shadowWithCRadius(radius:12, color: Constants.backArrowBorderColor!)
        noBlockButton.shadowWithCRadius(radius:12, color: Constants.backArrowBorderColor!)

        viewTrackCard.shadowWithCRadius(radius:12, color: Constants.backArrowBorderColor!)
        viewAddCard.shadowWithCRadius(radius:12, color: Constants.backArrowBorderColor!)
    }

    func drawDottedLine(start p0: CGPoint, end p1: CGPoint, view: UIView) {
        let shapeLayer = CAShapeLayer()
        shapeLayer.strokeColor = UIColor.lightGray.cgColor
        shapeLayer.lineWidth = 1
        shapeLayer.lineDashPattern = [7, 3] // 7 is the length of dash, 3 is length of the gap.

        let path = CGMutablePath()
        path.addLines(between: [p0, p1])
        shapeLayer.path = path
        view.layer.addSublayer(shapeLayer)
    }

    // MARK: - Button Actions
    @IBAction func issueButtonTapped(_ sender: UIButton) {
        let vc = OrderCardVC.instantiate(appStoryboard: .RFID)
        navigationController?.pushViewController(vc, animated: true)
    }

    @IBAction func trackButtonTapped(_ sender: UIButton) {
        let vc = RFIDTrackListVC.instantiate(appStoryboard: .RFID)
        navigationController?.pushViewController(vc, animated: true)
    }

    @IBAction func backButtonTapped(_ sender: UIButton) {
         //navigationController?.popViewController(animated: true)
        let vc = HomeTabVC.instantiate(appStoryboard: .Home)
        navigationController?.pushViewController(vc, animated: true)
    }

    @IBAction func trackCardTapped(_ sender: UIButton) {
        let vc = RFIDTrackListVC.instantiate(appStoryboard: .RFID)
        navigationController?.pushViewController(vc, animated: true)
    }


    @IBAction func AddCardTapped(_ sender: UIButton) {
        let vc = OrderCardVC.instantiate(appStoryboard: .RFID)
        navigationController?.pushViewController(vc, animated: true)
    }


    @objc func replaceAction(_ sender: UIButton) {
        AppDelegate.shared.replaceTagID = rfidList[sender.tag].id_tag
        let vc = ReplaceCardVC.instantiate(appStoryboard: .RFID)
        navigationController?.pushViewController(vc, animated: true)
    }

    @objc func trackAction(_ sender: UIButton) {

    }

    @objc func blockAction(_ sender: UIButton) {
        let index = sender.tag
        AppDelegate.shared.replaceTagID = rfidList[index].id_tag

        // Store the current blocked status and index for use in the blockRFID method
        currentCardIndex = index

        // Update alert title and message based on current blocked status
        if rfidList[index].blocked == "1" {
            // Card is currently unblocked (1), will be blocked (2)
            updateAlertForBlocking()
        } else {
            // Card is currently blocked (2), will be unblocked (1)
            updateAlertForUnblocking()
        }

        self.viewBgAlert.isHidden = false
    }

    // Helper method to update alert for blocking
    func updateAlertForBlocking() {
        // Find the labels in the alert view
        if let titleLabel = self.viewMainAlert.viewWithTag(100) as? UILabel,
           let messageLabel = self.viewMainAlert.viewWithTag(101) as? UILabel {
            titleLabel.text = "Block Card"
            messageLabel.text = "Are you sure you want to block the card?"
        }
    }

    // Helper method to update alert for unblocking
    func updateAlertForUnblocking() {
        // Find the labels in the alert view
        if let titleLabel = self.viewMainAlert.viewWithTag(100) as? UILabel,
           let messageLabel = self.viewMainAlert.viewWithTag(101) as? UILabel {
            titleLabel.text = "Unblock Card"
            messageLabel.text = "Are you sure you want to unblock the card?"
        }
    }

    @IBAction func closeAlertTapped(_ sender: UIButton) {
        viewBgAlert.isHidden = true
    }

    @IBAction func noBlockTapped(_ sender: UIButton) {
        self.viewBgAlert.isHidden = true
    }

    @IBAction func yesBlockTapped(_ sender: UIButton) {
        self.viewBgAlert.isHidden = true
        blockRFID()
    }


    // MARK: - WebService
    func getRFIDList() {

        AppDelegate.shared.showHUD()
        let url = Constants.BASE_URL + API.RFID_LIST
        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]
        AF.request(url, method: .get, parameters: nil, encoding:  URLEncoding.default, headers: headers).responseJSON { response in
            print(response)
            AppDelegate.shared.hideHUD()
            switch response.result {
            case .success:

                let JSON = response.value as! NSDictionary
                print(JSON)

                if "\(JSON["code"]!)" == "200" && "\(JSON["status"]!)" == "1" {

                    let jsonData = JSON["data"] as! NSDictionary

                    self.rfidList.removeAll()
                    for item in jsonData["rfid_data"] as! NSArray {
                        self.rfidList.append(RFIDList(dic: item as! NSDictionary))
                    }

                    if self.rfidList.count > 0 {
                        self.tableList.isHidden = false
                        self.viewTrackCard.isHidden = false
                        self.viewAddCard.isHidden = false
                    } else {
                        self.tableList.isHidden = true
                        self.viewTrackCard.isHidden = true
                        self.viewAddCard.isHidden = true
                    }

                    self.tableList.reloadData()

                } else {

                    if "\(JSON["code"]!)" == "100" {
                        AppDelegate.shared.apiKeyLogout()
                    }

                    self.rfidList.removeAll()
                    if self.rfidList.count > 0 {
                        self.tableList.isHidden = false
                        self.viewTrackCard.isHidden = true
                        self.viewAddCard.isHidden = true
                    } else {
                        self.tableList.isHidden = true
                        self.viewTrackCard.isHidden = true
                        self.viewAddCard.isHidden = true
                    }
                    self.tableList.reloadData()
                    AppDelegate.shared.hideHUD()
                }
                break
            case .failure(let error):
                print(error)
                AppDelegate.shared.hideHUD()
            }
        }
    }

//    BLOCK_RFID
    func blockRFID() {
        guard currentCardIndex >= 0 && currentCardIndex < rfidList.count else {
            return
        }

        let url = Constants.BASE_URL + API.BLOCK_RFID

        // Determine the blocked status to set based on current status
        let blockStatus: String
        let actionTitle: String

        if rfidList[currentCardIndex].blocked == "1" {
            // Currently unblocked (1), set to blocked (2)
            blockStatus = "2"
            actionTitle = "Block RFID"
        } else {
            // Currently blocked (2), set to unblocked (1)
            blockStatus = "1"
            actionTitle = "Unblock RFID"
        }

        let param: [String: Any] = [
            "ocpp_tag_pk": AppDelegate.shared.replaceTagID,
            "blocked": blockStatus
        ]

        print("param:-", param)
        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]

        AppDelegate.shared.showHUD()
        AF.request(url, method: .post, parameters: param, encoding: URLEncoding.default, headers: headers).responseJSON { response in
            print(response)
            AppDelegate.shared.hideHUD()
            switch response.result {
            case .success:

                let JSON = response.value as! NSDictionary
                print(JSON)

                if "\(JSON["code"]!)" == "200" && "\(JSON["status"]!)" == "1" {

                    let jsonData = JSON["data"] as! NSDictionary
                    print(jsonData)

                    // Update the local model with the new blocked status
                    if self.currentCardIndex >= 0 && self.currentCardIndex < self.rfidList.count {
                        self.rfidList[self.currentCardIndex].blocked = blockStatus

                        // Get the visible cells and update the block label for the current cell
                        if let visibleCells = self.tableList.visibleCells as? [RFIDTrackListCell] {
                            for cell in visibleCells {
                                if cell.tag == self.currentCardIndex {
                                    // Update the block label text
                                    let isBlocked = blockStatus != "1"
                                    cell.updateBlockLabel(isBlocked: isBlocked)
                                }
                            }
                        }
                    }

                    // Refresh the table to update UI
                    self.tableList.reloadData()

                    let alertController = UIAlertController(title: NSLocalizedString(actionTitle, comment: ""), message: NSLocalizedString("\(JSON["msg"]!)", comment: ""), preferredStyle: .alert)
                    let settingsAction = UIAlertAction(title: NSLocalizedString("OK", comment: ""), style: .default) { (UIAlertAction) in
                        // Refresh the RFID list to get updated data
                        self.getRFIDList()
                    }
                    alertController.addAction(settingsAction)
                    self.present(alertController, animated: true, completion: nil)

                } else {
                    AppDelegate.shared.hideHUD()

                    if "\(JSON["code"]!)" == "100" {
                        AppDelegate.shared.apiKeyLogout()
                    }
                }
                break
            case .failure(let error):
                print(error)
                self.alert(title: "", message: "\(error.localizedDescription)")
                AppDelegate.shared.hideHUD()
            }
        }
    }


}
extension IssueNewCardVC: UITableViewDelegate, UITableViewDataSource {

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 200
    }
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return rfidList.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "cell", for: indexPath) as! RFIDTrackListCell

        cell.viewMain.shadowWithCRadius(radius: 16, color: Constants.backArrowBorderColor!)
        cell.nameLabel.text = rfidList[indexPath.row].id_tag

        // Set the cell tag to match the row index for easier identification
        cell.tag = indexPath.row

        // Completely remove date display
        cell.dateLabel.text = ""

        // Hide Replace button
        cell.btnReplace.isHidden = true

        cell.btnBlock.tag = indexPath.row
        cell.btnBlock.addTarget(self, action: #selector(self.blockAction(_:)), for: .touchUpInside)

        // Update the Block/Unblock text based on current status
        // Use the new method in RFIDTrackListCell to update the label
        let isBlocked = rfidList[indexPath.row].blocked != "1"
        cell.updateBlockLabel(isBlocked: isBlocked)

        cell.layoutIfNeeded()
        cell.updateConstraints()
        return cell
    }
}

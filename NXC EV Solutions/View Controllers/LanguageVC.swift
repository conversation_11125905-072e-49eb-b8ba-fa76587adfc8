//
//  LanguageVC.swift
//  LanguageVC
//
//  Created by Dev<PERSON><PERSON> on 17/08/21.
//

import UIKit
import LanguageManager_iOS

class LanguageVC: UIViewController {

    // MARK: - Button Actions
    @IBOutlet weak var viewEnglish: UIView!
    @IBOutlet weak var imgEnglish: UIImageView!

    @IBOutlet weak var viewGujarati: UIView!
    @IBOutlet weak var imgGujarati: UIImageView!

    @IBOutlet weak var viewHindi: UIView!
    @IBOutlet weak var imgHindi: UIImageView!
    @IBOutlet weak var btnBack: UIButton!

    @IBOutlet weak var viewBgAlert: UIView!
    @IBOutlet weak var viewMainAlert: UIView!
    @IBOutlet weak var lblAlertTitle: UILabel!
    @IBOutlet weak var lblAlertText: UILabel!
    @IBOutlet weak var btnOKAlert: UIButton!


    // MARK: - Button Actions
    override func viewDidLoad() {
        super.viewDidLoad()

        // Check if language setting exists in UserDefaults
        if let languageValue = UserDefaults.standard.value(forKey: Constants.LANGUAGE) as? String {
            if languageValue == "1" {
                languageSelection(value: 101)
            } else if languageValue == "2" {
                languageSelection(value: 102)
            } else {
                languageSelection(value: 103)
            }
        } else {
            // Default to English if no language is set
            languageSelection(value: 101)
            UserDefaults.standard.set("1", forKey: Constants.LANGUAGE)
        }

        viewBgAlert.isHidden = true
    }

    override func viewWillAppear(_ animated: Bool) {
        viewBgAlert.isHidden = true
    }

    override func viewDidAppear(_ animated: Bool) {
        AppDelegate.shared.isSwipeBack = 1
        navigationController?.interactivePopGestureRecognizer?.delegate = self
        navigationController?.interactivePopGestureRecognizer?.isEnabled = true
    }

    override func viewDidLayoutSubviews() {
        btnBack.addBorder(color: Constants.backArrowBorderColor!, width: Int(1))
        btnBack.maskClipCorner(cornerRadius: 10)

        viewMainAlert.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        btnOKAlert.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
    }

    func languageSelection(value: Int) {
        // Safely unwrap Constants.primaryColor and Constants.languageDefaultColor
        let primaryColor = Constants.primaryColor ?? UIColor.blue
        let defaultColor = Constants.languageDefaultColor ?? UIColor.lightGray

        if value == 101 {
            // English selection
            viewEnglish.addBorder(color: primaryColor, width: Int(2))
            viewGujarati.addBorder(color: defaultColor, width: Int(2))
            viewHindi.addBorder(color: defaultColor, width: Int(2))

            viewEnglish.maskClipCorner(cornerRadius: 8)
            viewGujarati.maskClipCorner(cornerRadius: 8)
            viewHindi.maskClipCorner(cornerRadius: 8)

            imgEnglish.isHighlighted = true
            imgGujarati.isHighlighted = false
            imgHindi.isHighlighted = false

            // Set language in both LanguageManager and Bundle
            LanguageManager.shared.setLanguage(language: .en)
            Bundle.setLanguage("en")

            // Update LanguageManager's internal storage
            UserDefaults.standard.set("en", forKey: "LanguageManagerSelectedLanguage")

            // Save language selection
            UserDefaults.standard.set("1", forKey: Constants.LANGUAGE)

            // Force reload the main bundle
            NotificationCenter.default.post(name: NSNotification.Name("LANGUAGE_CHANGED"), object: nil)

            lblAlertTitle.text = "Application language changed"
            lblAlertText.text = "App language is now changed to English"

        } else if value == 102 {
            // Gujarati selection
            viewEnglish.addBorder(color: defaultColor, width: Int(2))
            viewGujarati.addBorder(color: primaryColor, width: Int(2))
            viewHindi.addBorder(color: defaultColor, width: Int(2))

            viewEnglish.maskClipCorner(cornerRadius: 8)
            viewGujarati.maskClipCorner(cornerRadius: 8)
            viewHindi.maskClipCorner(cornerRadius: 8)

            imgEnglish.isHighlighted = false
            imgGujarati.isHighlighted = true
            imgHindi.isHighlighted = false

            // Set language using the Gujarati language directly from the Languages enum
            LanguageManager.shared.setLanguage(language: .gu)
            Bundle.setLanguage("gu")

            // Update LanguageManager's internal storage
            UserDefaults.standard.set("gu", forKey: "LanguageManagerSelectedLanguage")

            // Save language selection
            UserDefaults.standard.set("2", forKey: Constants.LANGUAGE)

            // Update AppDelegate's currentLanguage property
            if let appDelegate = UIApplication.shared.delegate as? AppDelegate {
                appDelegate.currentLanguage = "gu"
                appDelegate.strLanguage = "2"
            }

            // Force reload the main bundle
            NotificationCenter.default.post(name: NSNotification.Name("LANGUAGE_CHANGED"), object: nil)

            lblAlertTitle.text = "ભાષા બદલાઈ"
            lblAlertText.text = "એપ્લિકેશનની ભાષા હવે ગુજરાતીમાં બદલાઈ ગઈ છે"

        } else {
            // Hindi selection
            viewEnglish.addBorder(color: defaultColor, width: Int(2))
            viewGujarati.addBorder(color: defaultColor, width: Int(2))
            viewHindi.addBorder(color: primaryColor, width: Int(2))

            viewEnglish.maskClipCorner(cornerRadius: 8)
            viewGujarati.maskClipCorner(cornerRadius: 8)
            viewHindi.maskClipCorner(cornerRadius: 8)

            imgEnglish.isHighlighted = false
            imgGujarati.isHighlighted = false
            imgHindi.isHighlighted = true

            // Set language in both LanguageManager and Bundle
            LanguageManager.shared.setLanguage(language: .hi)
            Bundle.setLanguage("hi")

            // Update LanguageManager's internal storage
            UserDefaults.standard.set("hi", forKey: "LanguageManagerSelectedLanguage")

            // Save language selection
            UserDefaults.standard.set("3", forKey: Constants.LANGUAGE)

            // Update AppDelegate's currentLanguage property
            if let appDelegate = UIApplication.shared.delegate as? AppDelegate {
                appDelegate.currentLanguage = "hi"
                appDelegate.strLanguage = "3"
            }

            // Force reload the main bundle
            NotificationCenter.default.post(name: NSNotification.Name("LANGUAGE_CHANGED"), object: nil)

            lblAlertTitle.text = "भाषा बदली"
            lblAlertText.text = "एप्लिकेशन की भाषा अब हिंदी में बदल गई है"
        }

        // Set AppDelegate's language property
        if let appDelegate = UIApplication.shared.delegate as? AppDelegate {
            appDelegate.strLanguage = UserDefaults.standard.string(forKey: Constants.LANGUAGE) ?? "1"
        }

        // Force UI update
        UIView.appearance().semanticContentAttribute =
            LanguageManager.shared.isRightToLeft ? .forceRightToLeft : .forceLeftToRight
    }

    // MARK: - Button Actions
    @IBAction func backAction(_ sender: UIButton) {
        navigationController?.popViewController(animated: true)
    }


    @IBAction func languageAction(_ sender: UIButton) {
        languageSelection(value: sender.tag)
        viewBgAlert.isHidden = false
    }

    @IBAction func okAlertTapped(_ sender: UIButton) {
//        navigationController?.popViewController(animated: true)
        let vc = HomeTabVC.instantiate(appStoryboard: .Home)
        AppDelegate.shared.intPaymentTab = 0
        self.navigationController?.pushViewController(vc, animated: true)
    }
}
extension LanguageVC:UIGestureRecognizerDelegate {
    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldBeRequiredToFailBy otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        return true
    }
}

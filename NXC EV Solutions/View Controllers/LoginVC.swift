//
//  LoginVC.swift
//  NXC EV Solutions
//
//  Created by <PERSON><PERSON><PERSON> on 16/07/21.
//

import UIKit
import Alamofire
import SwiftyJSON
import LanguageManager_iOS
class LoginVC: UIViewController {
    
    
    // MARK: - IBOUtlets
    @IBOutlet weak var viewPhone: UIView!
    @IBOutlet weak var txtPhone: UITextField!
    @IBOutlet weak var btnLogin: UIButton!
    
    @IBOutlet weak var btnTC: UIButton!
    var param:[String:Any] = [:]
    
    var i = 0
    
    // MARK: - View LifeCyle Method
    override func viewDidLoad() {
        super.viewDidLoad()
        
        txtPhone.delegate = self
//        login()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        if LanguageManager.shared.currentLanguage == .en {
            let strText = "By logging in, you agree to our Terms and Conditions"
            let attributedText = NSMutableAttributedString(string: strText)
            let rangeToUnderline = (strText as NSString).range(of: "Terms and Conditions")
            attributedText.addAttribute(NSAttributedString.Key.underlineStyle, value: NSUnderlineStyle.single.rawValue, range: rangeToUnderline)
            btnTC.setTitle("\(attributedText)", for: .normal)
            
        } else if LanguageManager.shared.currentLanguage == .hi {
            let strText = "लॉग इन करके, आप हमारे नियमों और शर्तों से सहमत होते हैं"
            let attributedText = NSMutableAttributedString(string: strText)
            let rangeToUnderline = (strText as NSString).range(of: "नियमों और शर्तों")
            attributedText.addAttribute(NSAttributedString.Key.underlineStyle, value: NSUnderlineStyle.single.rawValue, range: rangeToUnderline)
            btnTC.setTitle("\(attributedText)", for: .normal)
            
        } else if LanguageManager.shared.currentLanguage.rawValue == "gu" {
            // Use rawValue comparison for Gujarati
            let strText = "લૉગ ઇન કરીને, તમે અમારી શરતો અને નિયમોથી સંમત થાઓ છો"
            let attributedText = NSMutableAttributedString(string: strText)
            let rangeToUnderline = (strText as NSString).range(of: "શરતો અને નિયમો")
            attributedText.addAttribute(NSAttributedString.Key.underlineStyle, value: NSUnderlineStyle.single.rawValue, range: rangeToUnderline)
            btnTC.setTitle("\(attributedText)", for: .normal)
        } else {
            // Default to Hindi for any other language
            let strText = "लॉग इन करके, आप हमारे नियमों और शर्तों से सहमत होते हैं"
            let attributedText = NSMutableAttributedString(string: strText)
            let rangeToUnderline = (strText as NSString).range(of: "नियमों और शर्तों")
            attributedText.addAttribute(NSAttributedString.Key.underlineStyle, value: NSUnderlineStyle.single.rawValue, range: rangeToUnderline)
            btnTC.setTitle("\(attributedText)", for: .normal)
        }
    }

    
    override func viewDidLayoutSubviews() {
        viewPhone.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        viewPhone.addBorder(color: Constants.textBorderColor!, width: Int(0.5))
        btnLogin.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
    }
    
    
    // MARK: - Button Actions
    @IBAction func loginAction(_ sender: UIButton) {
        let _ = txtPhone.resignFirstResponder()
        if txtPhone.text!.count == 0 {
            alert(title: "", message: "Please enter phone number".localiz())
        } else if txtPhone.text!.count < 10 {
            alert(title: "", message: "Please enter valid phone number".localiz())
        } else {
            self.login()
        }
        
//        i += 1
//        print("Running \(i)")
//
//        switch i {
//        case 1:
//            let generator = UINotificationFeedbackGenerator()
//            generator.notificationOccurred(.error)
//
//        case 2:
//            let generator = UINotificationFeedbackGenerator()
//            generator.notificationOccurred(.success)
//
//        case 3:
//            let generator = UINotificationFeedbackGenerator()
//            generator.notificationOccurred(.warning)
//
//        case 4:
//            let generator = UIImpactFeedbackGenerator(style: .light)
//            generator.impactOccurred()
//
//        case 5:
//            let generator = UIImpactFeedbackGenerator(style: .medium)
//            generator.impactOccurred()
//
//        case 6:
//            let generator = UIImpactFeedbackGenerator(style: .heavy)
//            generator.impactOccurred()
//
//        default:
//            let generator = UISelectionFeedbackGenerator()
//            generator.selectionChanged()
//            i = 0
//        }
    }
    
    @IBAction func productByAction(_ sender: UIButton) {
        if let url = URL(string: "https://ev.nxccontrols.in") {
            UIApplication.shared.open(url)
        }
    }
    
    @IBAction func termsAndConditionAction(_ sender: UIButton) {
        let vc = TermsVC.instantiate(appStoryboard: .Help)
        self.navigationController?.pushViewController(vc, animated: true)
    }
    
    //MARK: - Webservice Methods
    func login() {
        AppDelegate.shared.showHUD()
        let url = Constants.BASE_URL + API.LOGIN
        print("url:-",url)
        param = ["mobile_no":txtPhone.text!]
        
        AF.request(url, method: .post, parameters: param, encoding: URLEncoding.default).responseJSON { response in
            AppDelegate.shared.hideHUD()
            switch response.result {
            case .success:
                print(response)
                let JSON = response.value as! NSDictionary
//                print("JSON:-",JSON)
                if "\(JSON["code"]!)" == "200" && "\(JSON["status"]!)" == "1" {
                    if let data = (JSON["data"] as? NSDictionary) {
                        print("data:-",data)
                        print("dataotp:-",data["otp"]!)
                    }

                    AppDelegate.shared.strLoginNo = "\(self.txtPhone.text!)"
                    let vc = VerificationVC.instantiate(appStoryboard: .PreLogin)
                    self.navigationController?.pushViewController(vc, animated: true)
                } else {
                    self.alert(title: "", message: "\(JSON["msg"]!)")
                }

                break
            case .failure(let error):
                print(error)
                self.alert(title: "", message: "\(error.localizedDescription)")
            }
        }
    }
}
extension LoginVC: UITextFieldDelegate {
    func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
        
        if textField == txtPhone {
            let maxLength = 10
            let currentString: NSString = textField.text! as NSString
            let newString: NSString = currentString.replacingCharacters(in: range, with: string) as NSString
            return newString.length <= maxLength
        }
        return true
    }
}

//
//  MapChargerVC.swift
//  NXC EV Solutions
//
//  Created by <PERSON><PERSON><PERSON> on 22/07/21.
//

class POIItem: NSObject, GMUClusterItem {

    var position: CLLocationCoordinate2D
    var csID:String!
    let marker = GMSMarker()

    init(position: CLLocationCoordinate2D,csID: String, markerImg: UIImage) {
        self.position = position
        self.csID = csID
        self.marker.icon = markerImg
    }
}

let kClusterItemCount = 10000
let kCameraLatitude = 23.0
let kCameraLongitude = 72.6


struct ChargeStations {
    let name: String
    let long: CLLocationDegrees
    let lat: CLLocationDegrees
}

import UIKit
import GoogleMaps
import GooglePlaces
import Starscream
import ACFloating<PERSON><PERSON>tfield_Swift
import Alamofire
import Cosmos
import LanguageManager_iOS
import GoogleMapsUtils
import SDWebImage
import CoreLocation
import MapKit

@available(iOS 13.0, *)
class MapChargerVC: UIViewController, GMUClusterRendererDelegate {

    // MARK: - IBOutlets
    @IBOutlet weak var btnMenu: UIButton!
    @IBOutlet weak var btnFilter: UIButton!
    @IBOutlet weak var searchBar: UISearchBar!
    @IBOutlet weak var mapView: GMSMapView!
    @IBOutlet weak var topStack: UIStackView!

    @IBOutlet weak var viewSearch: UIView!
    @IBOutlet weak var viewBgMenu: UIView!
    @IBOutlet weak var viewMainMenu: UIView!
    @IBOutlet weak var viewProfile: UIView!

    @IBOutlet weak var lblInitial: UILabel!
    @IBOutlet weak var lblUserName: UILabel!
    @IBOutlet weak var lblBalance: UILabel!
    @IBOutlet weak var tableMenu: UITableView!

    @IBOutlet weak var tableHeight: NSLayoutConstraint!
    @IBOutlet weak var viewBgDetails: UIView!
    @IBOutlet weak var viewConnectorDetails: UIView!
    @IBOutlet weak var imgStation: UIImageView!
    @IBOutlet weak var lblStationAdd1: UILabel!
    @IBOutlet weak var lblStationAdd2: UILabel!
    @IBOutlet weak var lblStationTimings: UILabel!
    @IBOutlet weak var lblStationStatus: UILabel!
    @IBOutlet weak var lblConnectorPower: UILabel!

    @IBOutlet weak var viewConnector1: UIView!
    @IBOutlet weak var imgConnector1: UIImageView!
    @IBOutlet weak var lblConnector1: UILabel!

    @IBOutlet weak var viewConnector2: UIView!
    @IBOutlet weak var imgConnector2: UIImageView!
    @IBOutlet weak var lblConnector2: UILabel!

    @IBOutlet weak var viewConnector3: UIView!
    @IBOutlet weak var imgConnector3: UIImageView!
    @IBOutlet weak var lblConnector3: UILabel!

    @IBOutlet weak var tableConnectorDetails: UITableView!
    @IBOutlet weak var viewDirection: UIView!
    @IBOutlet weak var lblKmMin: UILabel!

    @IBOutlet weak var tableConnectorHeight: NSLayoutConstraint!
    @IBOutlet weak var viewSearchList: UIView!
    @IBOutlet weak var searchListTableView: UITableView!

    @IBOutlet weak var viewSearchHeight: NSLayoutConstraint!

    @IBOutlet weak var imgUpDown: UIImageView!

    @IBOutlet weak var btnWA: UIButton!


    var cs_lat = Double()
    var cs_lng = Double()

    var isFromSearch = false
    var isSlideInMenuPresented = false
    lazy var slideInMenuPadding: CGFloat = self.view.frame.width * 0.30

    lazy var menuView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemGray5
        return view
    }()

    lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemBackground
        return view
    }()

    var menuTitleList:[String] = []
    var menuImagesList:[String] = []
    var selectedIndex = Int()

    var locationManager = CLLocationManager()
    var currentLocation: CLLocation?
    var placesClient: GMSPlacesClient!
    var zoomLevel: Float = 15.0
    var lat:CLLocationDegrees = CLLocationDegrees()
    var lng:CLLocationDegrees = CLLocationDegrees()
    var cStations:[ChargeStations] = []
    var markerDict: [String: GMSMarker] = [:]
    var clusterManager: GMUClusterManager!

    var paramNear:[String:Any] = [:]
    var paramSearch:[String:Any] = [:]
    var paramCS:[String:Any] = [:]

    var searchStationList:[SearchStationList] = []
    var filteredSearchStationList:[SearchStationList] = []
    var chargeStationDetails:[ChargeStation] = []

    var connnectorDetails:[ConnectorDetails] = []
//    var chargeStationInfo:[ChargeStationInfo] = []
//    var chargeStationTimeData:[ChargeStationDay] = []

//    var currentLocationMarker: GMSMarker?

    var addressList:[AddressDetails] = []
    var filtered: [AddressDetails] = []
    var searchActive : Bool = false
    var dictConn:[String:String] = [:]
    var arrCT:[Any] = []
    var strSearchTap:String = "0"


    // MARK: - View LifeCycle Methods
    override func viewDidLoad() {
        super.viewDidLoad()

        view.backgroundColor = .systemBackground

        menuTitleList = ["Profile","Change Language","Get Your RFID Card","News",
                         "Buy Charger","Games","Help","Complaint","About Us"]
        menuImagesList = ["ic_profile","ic_change_language","ic_complain","ic_news","ic_buy_charger","ic_game_icon","ic_help","ic_complain","ic_about"]

        viewSearchList.isHidden = true

        tableMenu.delegate = self
        tableMenu.dataSource = self

        searchListTableView.delegate = self
        searchListTableView.dataSource = self

        tableHeight.constant = CGFloat(menuTitleList.count * 60)

        tableConnectorHeight.constant = CGFloat(2 * 95)
        tableConnectorDetails.isHidden = true

        tableConnectorDetails.delegate = self
        tableConnectorDetails.dataSource = self

        viewBgDetails.isHidden = true

        do {
            // Set the map style by passing the URL of the local file.
            if let styleURL = Bundle.main.url(forResource: "mapStyle", withExtension: "json") {
                mapView.mapStyle = try GMSMapStyle(contentsOfFileURL: styleURL)
            } else {
                NSLog("Unable to find style.json")
            }
        } catch {
            NSLog("One or more of the map styles failed to load. \(error)")
        }

        searchBar.delegate = self

        view.addSubview(viewBgMenu)
        viewBgMenu.isHidden = true

        imgUpDown.image = UIImage(named: "ic_topArrow")

        let swipeUp = UISwipeGestureRecognizer(target: self, action: #selector(respondToSwipeGesture))
        swipeUp.direction = .up
        viewBgDetails.addGestureRecognizer(swipeUp)

        let swipeDown = UISwipeGestureRecognizer(target: self, action: #selector(respondToSwipeGesture))
        swipeDown.direction = .down
        viewBgDetails.addGestureRecognizer(swipeDown)

    }

    override func viewWillAppear(_ animated: Bool) {

        viewBgMenu.isHidden = true
        viewBgDetails.isHidden = true
        tableConnectorDetails.isHidden = true
        AppDelegate.shared.selectedIndex = 0
        self.tableMenu.reloadData()
        searchBar.placeholder = "Search Charge Station".localiz()

        locationManager.desiredAccuracy = kCLLocationAccuracyBest
        locationManager.requestWhenInUseAuthorization()
        locationManager.distanceFilter = 50
        locationManager.startUpdatingLocation()
        locationManager.delegate = self

        getWalletBalance()
        getStationList()

        if AppDelegate.shared.isFromFilter == true {
            print(AppDelegate.shared.arrCType)
            for item in AppDelegate.shared.arrCType {
                print(item)
                var dictConn:[String:String] = [:]
                dictConn = ["type":"\(item.ct_id)"]
                self.arrCT.append(dictConn)
            }

            print("self.arrCT \(self.arrCT)")
            self.getNearMeList()
        } else {
            if AppDelegate.shared.isNearMeListFetched == "0" {
                self.getNearMeList()
            }  else {
                AppDelegate.shared.filteredNearMeList.removeAll()
                AppDelegate.shared.filteredNearMeList = AppDelegate.shared.nearMeList
                AppDelegate.shared.isNearMeListFetched = "1"
                print("startUpdatingLocation")
                self.locationManager.startUpdatingLocation()
            }
        }
    }

    override func viewDidAppear(_ animated: Bool) {
        AppDelegate.shared.isSwipeBack = 1
        self.navigationController?.interactivePopGestureRecognizer?.isEnabled = false
    }

    override func viewDidLayoutSubviews() {

        btnWA.shadowWithCRadius(radius: btnWA.frame.height/2, color: Constants.secondaryGrayText!)

        btnMenu.shadowWithCRadius(radius: btnMenu.frame.height/2, color: Constants.secondaryGrayText!)

        viewBgDetails.shadowWithCRadius(radius: 24, color: Constants.secondaryGrayText!)
        viewSearchList.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)

        btnMenu.maskClipCorner(cornerRadius: Int(btnMenu.frame.height/2))
        btnFilter.maskClipCorner(cornerRadius: Int(btnMenu.frame.height/2))
        searchBar.maskClipCorner(cornerRadius: Int(btnMenu.frame.height/2))

        viewProfile.addBorder(color: Constants.backArrowBorderColor!, width: Int(1))
        viewProfile.maskClipCorner(cornerRadius: 10)

        searchBar.searchTextField.backgroundColor = .white
        searchBar.backgroundImage = UIImage()
        searchBar.barTintColor = .green

        viewSearch.shadowWithCRadius(radius: viewSearch.frame.height/2, color: Constants.secondaryGrayText!)

        viewConnector1.maskClipCorner(cornerRadius: 12)
        viewConnector2.maskClipCorner(cornerRadius: 12)
        viewConnector3.maskClipCorner(cornerRadius: 12)

        imgStation.maskClipCorner(cornerRadius: 6)

        lblInitial.maskClipCorner(cornerRadius: Int(lblInitial.frame.height/2))

    }

    @objc func handleTap(sender: UITapGestureRecognizer) {
        UIView.animate(withDuration: 0.5, delay: 0, usingSpringWithDamping: 0.8, initialSpringVelocity: 0, options: .curveEaseInOut) {
            self.containerView.frame.origin.x = self.isSlideInMenuPresented ? 0 : self.containerView.frame.width - self.slideInMenuPadding
        } completion: { (finished) in
            print("Animation finished: \(finished)")
            self.isSlideInMenuPresented.toggle()
        }
    }

    func openCloseMenu() {

        UIView.animate(withDuration: 10, delay: 10, options: [.curveEaseOut], animations: {
            if self.viewBgMenu.isHidden == true {
                self.viewBgMenu.isHidden = false
            } else {
                self.viewBgMenu.isHidden = true
            }
        }, completion: nil)

//        UIView.animate(withDuration: 2.3, animations: {
//            if self.viewBgMenu.isHidden == true {
//                self.viewBgMenu.isHidden = false
//            } else {
//                self.viewBgMenu.isHidden = true
//            }
//        })
//
//        UIView.animate(withDuration: 5.25, delay: 0.0, options: [], animations: {
//            print("Completion")
//
//        }, completion: { (finished: Bool) in
////            self.isOpen = true
//            if self.viewBgMenu.isHidden == true {
//                self.viewBgMenu.isHidden = false
//            } else {
//                self.viewBgMenu.isHidden = true
//            }
//        })
    }

    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        let touch = touches.first!
        if(touch.view == viewBgMenu){
            openCloseMenu()
        }
    }


    // MARK: - Swipe Gesture Method
    @objc func respondToSwipeGesture(gesture: UIGestureRecognizer) {

        if let swipeGesture = gesture as? UISwipeGestureRecognizer {

            switch swipeGesture.direction {
            case .right:
                print("Swiped right")
            case .down:
                UIView.animate(withDuration: 0.3, animations: {
                    self.imgUpDown.image = UIImage(named: "ic_topArrow")
                    self.tableConnectorDetails.isHidden = true
                    self.view.layoutIfNeeded()
                })
            case .left:
                print("Swiped left")
            case .up:
                UIView.animate(withDuration: 0.3, animations: {
                    self.imgUpDown.image = UIImage(named: "ic_downArrow")
                    self.tableConnectorDetails.isHidden = false
                    self.view.layoutIfNeeded()
                })
            default:
                break
            }
        }
    }

    // MARK: - Map Clustering Methods
    func clusterGenerator() {

        let levelsColors : [UIColor] = [Constants.primaryColor!]
        let iconGenerator = GMUDefaultClusterIconGenerator.init(buckets: [99999], backgroundColors: levelsColors)
        let algorithm = GMUNonHierarchicalDistanceBasedAlgorithm()
        let renderer = GMUDefaultClusterRenderer(mapView: mapView, clusterIconGenerator: iconGenerator)
        renderer.delegate = self
        clusterManager = GMUClusterManager(map: mapView, algorithm: algorithm, renderer: renderer)
        clusterManager.clearItems()
        generateClusterItems()
        clusterManager.cluster()
        clusterManager.setDelegate(self, mapDelegate: self)
    }

    private func generateClusterItems() {

        self.mapView.clear()

        for item1 in AppDelegate.shared.filteredNearMeList {
            let lat = Double(item1.lat)
            let lng = Double(item1.long)

            let imgMarker = UIImageView()
            imgMarker.image = self.resizeImage(image: UIImage(named: "ic_marker_map.png")!, targetSize: CGSize(width: 30.0, height: 30.0))

            let itemPOI = POIItem(position: CLLocationCoordinate2DMake(lat!, lng!), csID: item1.cs_id, markerImg: imgMarker.image!)
            itemPOI.marker.tracksViewChanges = false
            itemPOI.marker.tracksInfoWindowChanges = false
            itemPOI.marker.icon = imgMarker.image
            clusterManager.add(itemPOI)
        }
    }

    /// Returns a random value between -1.0 and 1.0.
    private func randomScale() -> Double {
        return Double(arc4random()) / Double(UINT32_MAX) * 2.0 - 1.0
    }

    // MARK: - Convert to Dictionary
    func convertToDictionary(text: String) -> [String: Any]? {
        if let data = text.data(using: .utf8) {
            do {
                return try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any]
            } catch {
                print(error.localizedDescription)
            }
        }
        return nil
    }

    // MARK: - Marker Image Method
    func imageWithImage(image:UIImage, scaledToSize newSize:CGSize) -> UIImage {
        UIGraphicsBeginImageContextWithOptions(newSize, false, 0.0)
        image.draw(in: CGRect(x: 0, y: 0, width: newSize.width, height: newSize.height))
        let newImage:UIImage = UIGraphicsGetImageFromCurrentImageContext()!
        UIGraphicsEndImageContext()
        return newImage
    }

    // MARK: - Resize image
    func resizeImage(image: UIImage, targetSize: CGSize) -> UIImage? {
        let size = image.size

        let widthRatio  = targetSize.width  / size.width
        let heightRatio = targetSize.height / size.height

        // Figure out what our orientation is, and use that to form the rectangle
        var newSize: CGSize
        if(widthRatio > heightRatio) {
            newSize = CGSize(width: size.width * heightRatio, height: size.height * heightRatio)
        } else {
            newSize = CGSize(width: size.width * widthRatio, height: size.height * widthRatio)
        }

        // This is the rect that we've calculated out and this is what is actually used below
        let rect = CGRect(origin: .zero, size: newSize)

        // Actually do the resizing to the rect using the ImageContext stuff
        UIGraphicsBeginImageContextWithOptions(newSize, false, 1.0)
        image.draw(in: rect)
        let newImage = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()

        return newImage
    }

    // MARK: - Button Actions
    @IBAction func menuAction(_ sender: UIButton) {
        searchBar.resignFirstResponder()
        AppDelegate.shared.isMenuOpen = 1
        openCloseMenu()
    }

    @IBAction func filterAction(_ sender: UIButton) {
        searchBar.resignFirstResponder()
        let vc = FilterVC.instantiate(appStoryboard: .Charger)
        navigationController?.pushViewController(vc, animated: true)
    }

    @IBAction func getDirectionAction(_ sender: UIButton) {

        let alert = UIAlertController(title: "", message: "Choose Application", preferredStyle: .actionSheet)

        alert.addAction(UIAlertAction(title: "Maps", style: .default , handler:{ (UIAlertAction)in
            print("User click Approve button")
            let source = MKMapItem(placemark: MKPlacemark(coordinate: CLLocationCoordinate2D(latitude: AppDelegate.shared.lat, longitude: AppDelegate.shared.lng)))
            source.name = "Source"

            let destination = MKMapItem(placemark: MKPlacemark(coordinate: CLLocationCoordinate2D(latitude: self.cs_lat, longitude: self.cs_lng)))
            destination.name = "Destination"

            MKMapItem.openMaps(
                with: [source, destination],
                launchOptions: [MKLaunchOptionsDirectionsModeKey: MKLaunchOptionsDirectionsModeDriving]
            )
        }))

        alert.addAction(UIAlertAction(title: "Google Maps", style: .default , handler:{ (UIAlertAction)in
            print("User click Edit button")
            if (UIApplication.shared.canOpenURL(URL(string:"comgooglemaps://")!)) {
                UIApplication.shared.open(URL(string:
                                                "comgooglemaps://?saddr\(UserDefaults.standard.value(forKey: Constants.LATITUDE)!),\(UserDefaults.standard.value(forKey: Constants.LONGITUDE)!)=&daddr=\(self.cs_lat),\(self.cs_lng)&directionsmode=driving")!)
            } else {
                print("Can't use comgooglemaps://")
            }
        }))

        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel, handler:{ (UIAlertAction)in
               print("User click Dismiss button")
           }))

        //uncomment for iPad Support
        alert.popoverPresentationController?.sourceView = self.view

        self.present(alert, animated: true, completion: {
            print("completion block")
        })

    }

    @IBAction func connectorMoreDetails(_ sender: UIButton) {
        let vc = ConnectorDetailsVC.instantiate(appStoryboard: .Charger)
        navigationController?.pushViewController(vc, animated: true)
    }

    @IBAction func showDetails(_ sender: UIButton) {
//        UIView.animate(withDuration: 0.5, animations: {
//            self.tableConnectorDetails.isHidden = !self.tableConnectorDetails.isHidden
//            self.view.layoutIfNeeded()
//        })
    }

    @IBAction func logoutTapped(_ sender: UIButton) {
        viewBgMenu.isHidden = true
        let alertController = UIAlertController(title: "Logout".localiz(), message: "Logout Message".localiz(), preferredStyle: .alert)

        // Create the actions
        let cancelAction = UIAlertAction(title: "NO".localiz(), style: UIAlertAction.Style.cancel) {
            UIAlertAction in
            print("Cancel Pressed")
        }

        let okAction = UIAlertAction(title: "YES".localiz(), style: UIAlertAction.Style.default) {
            UIAlertAction in
            print("OK Pressed")
            AppDelegate.shared.apiKeyLogout()
        }

        // Add the actions
        alertController.addAction(cancelAction)
        alertController.addAction(okAction)

        // Present the controller
        self.present(alertController, animated: true, completion: nil)
    }


    @IBAction func btnWATapped(_ sender: UIButton) {
//        let urlWhats = "whatsapp://send?phone=+919807980776&abid=12354&text=Hello"
//        if let urlString = urlWhats.addingPercentEncoding(withAllowedCharacters: NSCharacterSet.urlQueryAllowed) {
//            if let whatsappURL = URL(string: urlString) {
//                if UIApplication.shared.canOpenURL(whatsappURL!) {
//                    UIApplication.shared.openURL(whatsappURL!)
//                } else {
//                    print("Install Whatsapp")
//                }
//            }
//        }
//        9807980776
        if let url = URL(string: "https://wa.me/919807980776?text=Hi"),
           UIApplication.shared.canOpenURL(url) {
            UIApplication.shared.open(url, options: [:])
        }
    }


    // MARK: - WebService
    func getNearMeList() {
//        AppDelegate.shared.showHUD()
        let url = Constants.BASE_URL + API.NEAR_ME_LIST
        print("🔍 API URL: \(url)")
//        var dataDict:[String:Any] = [:]
        var strRatings = String()
        if AppDelegate.shared.strRatings == "101" {
            strRatings = "1"
        } else if AppDelegate.shared.strRatings == "102" {
            strRatings = "2"
        } else if AppDelegate.shared.strRatings == "103" {
            strRatings = "3"
        } else if AppDelegate.shared.strRatings == "104" {
            strRatings = "4"
        }

        paramNear = [   "lat"               :   "\(UserDefaults.standard.value(forKey: Constants.LATITUDE)!)",
                        "long"              :   "\(UserDefaults.standard.value(forKey: Constants.LONGITUDE)!)",
                        "charge_station_id" :   "\(AppDelegate.shared.strAddressID)",
                        "connector_type"    :   arrCT,
                        "fv_station_id"     :   "\(AppDelegate.shared.strFavourite)",
                        "filter_type"       :   "\(AppDelegate.shared.strChargerType)",
                        "user_id"           :   "\(UserDefaults.standard.value(forKey: Constants.USER_ID)!)",
                        "rating"            :   strRatings,
                        "available_status"  :   "0"]
        print("📦 Request Parameters: \(paramNear)")
        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]
        print("🔑 Authorization Header: \(headers)")

        // Use standard responseJSON method with improved error handling
        AF.request(url, method: .post, parameters: paramNear, encoding: URLEncoding.default, headers: headers)
            .responseData { response in
                switch response.result {
                case .success(let data):
                    print("✅ NEAR_ME_LIST API Success")
                    AppDelegate.shared.hideHUD()

                    // Try to parse the data as JSON
                    do {
                        let json = try JSONSerialization.jsonObject(with: data, options: .allowFragments)
                        print("📊 Parsed JSON: \(json)")

                        // Safely cast the response value to NSDictionary
                        guard let JSON = json as? NSDictionary else {
                            print("❌ Error: Could not cast response to NSDictionary")
                            self.handleInvalidResponse()
                            return
                        }

                        // Continue with normal processing
                        self.processNearMeListResponse(JSON)

                    } catch {
                        // If JSON parsing fails, check if we have a valid string response
                        if let stringResponse = String(data: data, encoding: .utf8) {
                            print("⚠️ JSON parsing failed, but received string response: \(stringResponse)")
                            self.handleInvalidResponse()
                        } else {
                            print("❌ Error: Could not parse response as JSON or string")
                            self.handleInvalidResponse()
                        }
                    }



                case .failure(let error):
                    print("❌ API Error: \(error)")
                    AppDelegate.shared.hideHUD()
                    self.handleInvalidResponse()
                }
            }
    }

    // Helper method to handle invalid responses
    private func handleInvalidResponse() {
        // Handle network errors by using default location if needed
        if AppDelegate.shared.filteredNearMeList.isEmpty {
            // Add a default item using the current location
            let defaultItem = NearMeList(cs_id: "1",
                                        lat: "\(UserDefaults.standard.value(forKey: Constants.LATITUDE) ?? "0.0")",
                                        long: "\(UserDefaults.standard.value(forKey: Constants.LONGITUDE) ?? "0.0")")
            AppDelegate.shared.filteredNearMeList.append(defaultItem)
            AppDelegate.shared.nearMeList.append(defaultItem)
            AppDelegate.shared.isNearMeListFetched = "1"
            print("🔄 Using default location due to invalid response")
            self.locationManager.startUpdatingLocation()
        }
        self.clusterGenerator()
    }

    // Helper method to process valid NearMeList responses
    private func processNearMeListResponse(_ JSON: NSDictionary) {
        AppDelegate.shared.filteredNearMeList.removeAll()
        self.addressList.removeAll()

        // Safely check response code and status
        let code = "\(JSON["code"] ?? "")"
        let status = "\(JSON["status"] ?? "")"
        print("📊 Response code: \(code), status: \(status)")

        if code == "200" && status == "1" {
            // Safely get the data dictionary
            guard let jsonData = JSON["data"] as? NSDictionary,
                  let nearMeList = jsonData["near_me_list"] as? NSArray else {
                print("❌ Error: Invalid data structure in response")
                self.clusterGenerator()
                return
            }

            for item in nearMeList {
                guard let itemDict = item as? NSDictionary else { continue }

                // Debug the dictionary contents
                print("📋 Item dictionary: \(itemDict)")

                // Safely create NearMeList objects
                do {
                    let nearMeItem = NearMeList(dic: itemDict)
                    AppDelegate.shared.filteredNearMeList.append(nearMeItem)
                    AppDelegate.shared.nearMeList.append(nearMeItem)
                } catch let error {
                    print("❌ Error creating NearMeList: \(error)")
                }
            }

            print("📍 Filtered near me list: \(AppDelegate.shared.filteredNearMeList)")

            AppDelegate.shared.isNearMeListFetched = "1"
            print("🔄 startUpdatingLocation")
            self.locationManager.startUpdatingLocation()

        } else {
            AppDelegate.shared.hideHUD()

            if code == "100" {
                AppDelegate.shared.apiKeyLogout()
            } else {
                AppDelegate.shared.filteredNearMeList.removeAll()
                AppDelegate.shared.nearMeList.removeAll()
                // If we have a message, show it
                if let msg = JSON["msg"] as? String {
                    self.alert(title: "Error", message: msg)
                }
            }
        }
        self.clusterGenerator()
    }

    func getStationList() {
//                AppDelegate.shared.showHUD()
        let url = Constants.BASE_URL + API.SEARCH_STATION
        print("🔍 Station API URL: \(url)")
        paramNear = ["search"     :   "\(self.searchBar.text!)"]
        print("📦 Station Request Parameters: \(paramNear)")
        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]

        // Use standard responseData method with improved error handling
        AF.request(url, method: .post, parameters: paramNear, encoding: URLEncoding.default, headers: headers)
            .responseData { response in
                switch response.result {
                case .success(let data):
                    print("✅ Station API Success")

                    // Try to parse the data as JSON
                    do {
                        let json = try JSONSerialization.jsonObject(with: data, options: .allowFragments)
                        print("📊 Parsed Station JSON: \(json)")

                        // Safely cast the response value to NSDictionary
                        guard let JSON = json as? NSDictionary else {
                            print("❌ Error: Could not cast station response to NSDictionary")
                            self.clusterGenerator()
                            return
                        }

                        // Process the station list response
                        self.processStationListResponse(JSON)

                    } catch {
                        // If JSON parsing fails, check if we have a valid string response
                        if let stringResponse = String(data: data, encoding: .utf8) {
                            print("⚠️ JSON parsing failed for station list, but received string response: \(stringResponse)")
                        } else {
                            print("❌ Error: Could not parse station response as JSON or string")
                        }
                        self.clusterGenerator()
                    }

                case .failure(let error):
                    print("❌ Station API Error: \(error)")
                    AppDelegate.shared.hideHUD()
                    self.clusterGenerator()
                }
            }
    }

    // Helper method to process valid station list responses
    private func processStationListResponse(_ JSON: NSDictionary) {
        print("📊 stationList JSON: \(JSON)")
        self.searchStationList.removeAll()

        // Safely check response code and status
        let code = "\(JSON["code"] ?? "")"
        let status = "\(JSON["status"] ?? "")"

        if code == "200" && status == "1" {
            // Safely get the data dictionary
            guard let jsonData = JSON["data"] as? NSDictionary,
                  let stationData = jsonData["station_data"] as? NSArray else {
                print("❌ Error: Invalid station data structure in response")
                self.clusterGenerator()
                return
            }

            for item in stationData {
                guard let itemDict = item as? NSDictionary else { continue }

                // Debug the dictionary contents
                print("📋 Station item dictionary: \(itemDict)")

                // Safely create SearchStationList objects
                do {
                    let stationItem = SearchStationList(dic: itemDict)
                    self.searchStationList.append(stationItem)
                } catch let error {
                    print("❌ Error creating SearchStationList: \(error)")
                }
            }

            self.filteredSearchStationList = self.searchStationList
            print("🔍 Search station list count: \(self.searchStationList.count)")

            self.viewSearchHeight.constant = CGFloat(4 * 85)

        } else {
            AppDelegate.shared.hideHUD()

            if code == "100" {
                AppDelegate.shared.apiKeyLogout()
            } else {
                // If we have a message, show it
                if let msg = JSON["msg"] as? String {
                    self.alert(title: "Error", message: msg)
                }
            }
        }
        self.clusterGenerator()
    }

//    CS_DETAILS
    func getCSDetails() {
        let url = Constants.BASE_URL + API.CS_DETAILS
        print("🔍 CS Details API URL: \(url)")
        paramCS = [   "lat"               :  "\(UserDefaults.standard.value(forKey: Constants.LATITUDE)!)",
                      "long"              :  "\(UserDefaults.standard.value(forKey: Constants.LONGITUDE)!)",
                      "charge_station_id" :  "\(AppDelegate.shared.strAddressID)"]
        print("📦 CS Details Parameters: \(paramCS)")
        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]

        // Use standard responseData method with improved error handling
        AF.request(url, method: .post, parameters: paramCS, encoding: URLEncoding.default, headers: headers)
            .responseData { response in
                switch response.result {
                case .success(let data):
                    print("✅ CS Details API Success")
                    AppDelegate.shared.hideHUD()

                    // Try to parse the data as JSON
                    do {
                        let json = try JSONSerialization.jsonObject(with: data, options: .allowFragments)
                        print("📊 Parsed CS Details JSON: \(json)")

                        // Safely cast the response value to NSDictionary
                        guard let JSON = json as? NSDictionary else {
                            print("❌ Error: Could not cast CS details response to NSDictionary")
                            self.clusterGenerator()
                            return
                        }

                        // Safely check response code and status
                        let code = "\(JSON["code"] ?? "")"
                        let status = "\(JSON["status"] ?? "")"

                    if code == "200" && status == "1" {
                        UIView.animate(withDuration: 0.75, animations: {
                            self.viewBgDetails.isHidden = false
                            self.view.layoutIfNeeded()
                        })

                        // Safely get the data dictionary
                        guard let jsonData = JSON["data"] as? NSDictionary else {
                            print("❌ Error: Invalid data structure in CS details response")
                            self.clusterGenerator()
                            return
                        }

                        self.chargeStationDetails.removeAll()
                        self.connnectorDetails.removeAll()

                        // Fix: "charge_station" is a dictionary, not an array
                        if let csData = jsonData["charge_station"] as? NSDictionary {
                            do {
                                let stationItem = ChargeStation(dic: csData)
                                self.chargeStationDetails.append(stationItem)

                                print("📋 Charge station details: \(self.chargeStationDetails)")
                                if self.chargeStationDetails.count > 0 {
                                    self.lblStationAdd1.text = self.chargeStationDetails[0].charge_station_name
                                    self.lblStationAdd2.text = self.chargeStationDetails[0].cs_address
                                    self.cs_lat = Double(self.chargeStationDetails[0].lat) ?? 0.0
                                    self.cs_lng = Double(self.chargeStationDetails[0].long) ?? 0.0
                                    self.lblKmMin.text = "\(self.chargeStationDetails[0].distance) kms"
                                }
                            } catch let error {
                                print("❌ Error creating ChargeStation: \(error)")
                            }
                        }

                        // Handle charge boxes and connectors
                        if let chargeBoxes = jsonData["charge_boxes"] as? NSArray, chargeBoxes.count > 0 {
                            for chargeBox in chargeBoxes {
                                if let cbDict = chargeBox as? NSDictionary,
                                   let connectors = cbDict["connectors"] as? NSArray {
                                    for connector in connectors {
                                        if let connDict = connector as? NSDictionary,
                                           let connType = connDict["connector_type"] as? NSDictionary {
                                            // Create connector details object
                                            do {
                                                let connDetails = ConnectorDetails(dic: [
                                                    "connector_count": "1",
                                                    "connector_current_type": connType["current_type"] ?? "",
                                                    "connector_image": connType["image"] ?? "",
                                                    "connector_name": connType["name"] ?? "",
                                                    "connector_power_type": connType["power_type"] ?? "",
                                                    "connector_type_id": "1",
                                                    "connector_type_details": [],
                                                    "status_details": [["status": connDict["connector_status"] ?? "", "total": "1"]]
                                                ])
                                                self.connnectorDetails.append(connDetails)
                                            } catch let error {
                                                print("❌ Error creating ConnectorDetails: \(error)")
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        // Handle images
                        var arrImage:[String] = []
                        if let csImages = jsonData["charge_station"] as? NSDictionary,
                           let images = csImages["cs_images"] as? NSArray {
                            for item in images {
                                arrImage.append("\(item)")
                            }
                        }

                        if arrImage.count > 0 {
                            let imagePath = arrImage[0]
                            self.imgStation.sd_setImage(with: URL(string: imagePath)) { (image, error, type, url) in
                                if let error = error {
                                    print("❌ Error loading station image: \(error)")
                                }
                            }
                        } else {
                            self.imgStation.image = UIImage(named: "ic_new_nxc_logo")
                        }

                        self.imgStation.maskClipCorner(cornerRadius: 12)

                        // Update connector UI
                        self.updateConnectorUI()

                        self.tableConnectorDetails.reloadData()

                    } else {
                        AppDelegate.shared.hideHUD()

                        if code == "100" {
                            AppDelegate.shared.apiKeyLogout()
                        } else {
                            AppDelegate.shared.filteredNearMeList.removeAll()
                            AppDelegate.shared.nearMeList.removeAll()
                            // If we have a message, show it
                            if let msg = JSON["msg"] as? String {
                                self.alert(title: "Error", message: msg)
                            }
                        }
                    }
                    self.clusterGenerator()

                    } catch {
                        // If JSON parsing fails, check if we have a valid string response
                        if let stringResponse = String(data: data, encoding: .utf8) {
                            print("⚠️ JSON parsing failed for CS details, but received string response: \(stringResponse)")
                        } else {
                            print("❌ Error: Could not parse CS details response as JSON or string")
                        }
                        self.clusterGenerator()
                    }

                case .failure(let error):
                    print("❌ CS Details API Error: \(error)")
                    AppDelegate.shared.hideHUD()
                    self.clusterGenerator()
                }
            }
    }

    // Helper method to update connector UI
    private func updateConnectorUI() {
        if self.connnectorDetails.count == 1 {
            self.viewConnector1.alpha = 1
            self.viewConnector2.alpha = 0
            self.viewConnector3.alpha = 0
        } else if self.connnectorDetails.count == 2 {
            self.viewConnector1.alpha = 1
            self.viewConnector2.alpha = 1
            self.viewConnector3.alpha = 0
        } else if self.connnectorDetails.count >= 3 {
            self.viewConnector1.alpha = 1
            self.viewConnector2.alpha = 1
            self.viewConnector3.alpha = 1
        }

        for (index, element) in self.connnectorDetails.enumerated() {
            if index == 0 {
                self.lblConnector1.text = element.connector_name
                self.imgConnector1.sd_setImage(with: URL(string: "\(element.connector_image)")) { (image, error, type, url) in
                    self.imgConnector1.image = self.imgConnector1.image?.withRenderingMode(.alwaysTemplate)
                    self.imgConnector1.tintColor = Constants.primaryColor
                }
            } else if index == 1 {
                self.lblConnector2.text = element.connector_name
                self.imgConnector2.sd_setImage(with: URL(string: "\(element.connector_image)")) { (image, error, type, url) in
                    self.imgConnector2.image = self.imgConnector2.image?.withRenderingMode(.alwaysTemplate)
                    self.imgConnector2.tintColor = Constants.primaryColor
                }
            } else if index == 2 {
                self.lblConnector3.text = element.connector_name
                self.imgConnector3.sd_setImage(with: URL(string: "\(element.connector_image)")) { (image, error, type, url) in
                    self.imgConnector3.image = self.imgConnector3.image?.withRenderingMode(.alwaysTemplate)
                    self.imgConnector3.tintColor = Constants.primaryColor
                }
            }
        }
    }

    func getWalletBalance() {
        //AppDelegate.shared.showHUD()
        let url = Constants.BASE_URL + API.WALLET_BALANCE
       // print("🔍 Wallet API URL: \(url)")
        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]

        // Use standard responseData method with improved error handling
        AF.request(url, method: .get, parameters: nil, encoding: URLEncoding.default, headers: headers)
            .responseData { response in
                switch response.result {
                case .success(let data):
              //      print("✅ Wallet API Success")
                    AppDelegate.shared.hideHUD()

                    // Try to parse the data as JSON
                    do {
                        let json = try JSONSerialization.jsonObject(with: data, options: .allowFragments)
                   //     print("📊 Parsed Wallet JSON: \(json)")

                        // Safely cast the response value to NSDictionary
                        guard let JSON = json as? NSDictionary else {
                            print("❌ Error: Could not cast wallet response to NSDictionary")
                            return
                        }

                        // Safely check response code and status
                        let code = "\(JSON["code"] ?? "")"
                        let status = "\(JSON["status"] ?? "")"

                        if code == "200" && status == "1" {
                            // Safely get the data dictionary
                            guard let jsonData = JSON["data"] as? NSDictionary else {
                                print("❌ Error: Invalid data structure in wallet response")
                                return
                            }

                            // Safely extract user data
                            let userName = "\(jsonData["user_name"] ?? "")"
                            let balance = "\(jsonData["balance"] ?? "0")"

                            if !userName.isEmpty {
                                self.lblInitial.text = userName.prefix(1).uppercased()
                                self.lblUserName.text = userName.capitalizingFirstLetter()
                            }

                            self.lblBalance.text = "Avail Balance :".localiz() + " ₹\(balance)"

                        } else {
                            if code == "100" {
                                AppDelegate.shared.apiKeyLogout()
                            }
                        }
                    } catch {
                        // If JSON parsing fails, check if we have a valid string response
                        if let stringResponse = String(data: data, encoding: .utf8) {
                            print("⚠️ JSON parsing failed for wallet, but received string response: \(stringResponse)")
                        } else {
                            print("❌ Error: Could not parse wallet response as JSON or string")
                        }
                    }

                case .failure(let error):
                    print("❌ Wallet API Error: \(error)")
                    AppDelegate.shared.hideHUD()
                }
            }
    }
}
public extension UIView {
    func edgeTo(_ view: UIView) {
        view.addSubview(self)
        translatesAutoresizingMaskIntoConstraints = false
        topAnchor.constraint(equalTo: view.topAnchor).isActive = true
        leadingAnchor.constraint(equalTo: view.leadingAnchor).isActive = true
        trailingAnchor.constraint(equalTo: view.trailingAnchor).isActive = true
        bottomAnchor.constraint(equalTo: view.bottomAnchor).isActive = true
    }

    func pinMenuTo(_ view: UIView, with constant: CGFloat) {
        view.addSubview(self)
        translatesAutoresizingMaskIntoConstraints = false
        topAnchor.constraint(equalTo: view.topAnchor).isActive = true
        leadingAnchor.constraint(equalTo: view.leadingAnchor).isActive = true
        trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -constant).isActive = true
        bottomAnchor.constraint(equalTo: view.bottomAnchor).isActive = true
    }
}
extension MapChargerVC: UITableViewDelegate, UITableViewDataSource {

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        if tableView == tableMenu {
            return 55
        } else {
            return UITableView.automaticDimension
        }
    }

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        if tableView == tableMenu {
            return menuTitleList.count
        } else if tableView == searchListTableView {
            return filteredSearchStationList.count
        } else {
            return self.connnectorDetails.count
        }
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {

        if tableView == tableMenu {
            let cell = tableView.dequeueReusableCell(withIdentifier: "cell", for: indexPath) as! MenuCell
            cell.lblMenu.text = "\(self.menuTitleList[indexPath.row])".localiz()
            cell.imgMenu.image = UIImage(named: "\(self.menuImagesList[indexPath.row])")

            if indexPath.row+1 == AppDelegate.shared.selectedIndex {
                cell.lblMenu.textColor = Constants.primaryColor
                cell.imgMenu!.image = cell.imgMenu.image?.withRenderingMode(.alwaysTemplate)
                cell.imgMenu!.tintColor = Constants.primaryColor
            } else {
                cell.lblMenu.textColor = Constants.menuTextColor
                cell.imgMenu!.image = cell.imgMenu.image?.withRenderingMode(.alwaysTemplate)
                cell.imgMenu!.tintColor = Constants.menuIconColor
            }

            return cell

        } else if tableView == searchListTableView {
            let cell = tableView.dequeueReusableCell(withIdentifier: "cell", for: indexPath) as! ChargeStationSearchCell
            cell.chargeStationNameLabel.text = self.filteredSearchStationList[indexPath.row].cs_name
            cell.addressLabel.text = self.filteredSearchStationList[indexPath.row].address
            return cell

        } else {
            let cell = tableView.dequeueReusableCell(withIdentifier: "cell", for: indexPath) as! ConnectorCell
            cell.lblConnectorName.text = self.connnectorDetails[indexPath.row].connector_name
            let imagePath = self.connnectorDetails[indexPath.row].connector_image
            cell.imgConnectorName.sd_setImage(with: URL(string: imagePath)) { (image, error, type, url) in
                cell.imgConnectorName!.image = cell.imgConnectorName.image?.withRenderingMode(.alwaysTemplate)
                cell.imgConnectorName!.tintColor = Constants.primaryColor
            }

            cell.lblConnectorType.text = self.connnectorDetails[indexPath.row].connector_current_type
            cell.lblApproxPrice.alpha = 0

            if self.connnectorDetails[indexPath.row].status_details.contains(where: { $0.status == "Unavailable" }) {
                cell.viewAvail.alpha = 0
                cell.viewUnavail.alpha = 1
                cell.viewInUse.alpha = 0
                cell.viewBooked.alpha = 0
                for item in self.connnectorDetails[indexPath.row].status_details {
                    if item.status == "Unavailable" {
                        cell.lblUnavail.text = "\(item.total)" + " \(item.status)"
                    }
                }
            } else {

            }

//            for status in self.connnectorDetails[indexPath.row].status_details {
//                print("status:-",status)
//
//                if sta
//            }

//text = "\(self.connnectorDetails[indexPath.row].connector_current_type)"

//            if self.connnectorDetails[indexPath.row].status_details.contains(where: { <#StatusDetails#> in
//                <#code#>
//            })
//            if self.connnectorDetails[indexPath.row].status_details.count == 1 {
//                cell.view.alpha = 1
//                self.viewConnector2.alpha = 0
//                self.viewConnector3.alpha = 0
//
//            } else if self.connnectorDetails.count == 2 {
//                self.viewConnector1.alpha = 1
//                self.viewConnector2.alpha = 1
//                self.viewConnector3.alpha = 0
//
//            } else if self.connnectorDetails.count == 3 {
//                self.viewConnector1.alpha = 1
//                self.viewConnector2.alpha = 1
//                self.viewConnector3.alpha = 1
//
//            } else {
//
//            }

            return cell
        }
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {

        if tableView == tableMenu {
            AppDelegate.shared.isMenuOpen = 0
            AppDelegate.shared.selectedIndex = indexPath.row+1
            openCloseMenu()
            self.tableMenu.reloadData()

            if AppDelegate.shared.selectedIndex == 1 {
                let vc = ProfileDetailsVC.instantiate(appStoryboard: .Profile)
                navigationController?.pushViewController(vc, animated: true)
            } else if AppDelegate.shared.selectedIndex == 2 {
                let vc = LanguageVC.instantiate(appStoryboard: .Language)
                navigationController?.pushViewController(vc, animated: true)
            } else if AppDelegate.shared.selectedIndex == 3 {
                AppDelegate.shared.tabIndex = 1
                let vc = IssueNewCardVC.instantiate(appStoryboard: .RFID)
                navigationController?.pushViewController(vc, animated: true)
            } else if AppDelegate.shared.selectedIndex == 4 {
                let vc = NewsVC.instantiate(appStoryboard: .Help)
                navigationController?.pushViewController(vc, animated: true)
            } else if AppDelegate.shared.selectedIndex == 5 {
                let vc = BuyChargerVC.instantiate(appStoryboard: .Help)
                navigationController?.pushViewController(vc, animated: true)
            } else if AppDelegate.shared.selectedIndex == 6 {
                let vc = GameListVC.instantiate(appStoryboard: .Help)
                navigationController?.pushViewController(vc, animated: true)
            } else if AppDelegate.shared.selectedIndex == 7 {
                let vc = HelpVC.instantiate(appStoryboard: .Help)
                navigationController?.pushViewController(vc, animated: true)
            } else if AppDelegate.shared.selectedIndex == 8 {
                let vc = ComplainListVC.instantiate(appStoryboard: .Complain)
                navigationController?.pushViewController(vc, animated: true)
            } else if AppDelegate.shared.selectedIndex == 9 {
                let vc = AboutUsVC.instantiate(appStoryboard: .Help)
                navigationController?.pushViewController(vc, animated: true)
            }
        } else if tableView == searchListTableView {
            self.viewSearchList.isHidden = true
            searchBar.resignFirstResponder()
            AppDelegate.shared.strAddressID = self.filteredSearchStationList[indexPath.row].cs_id
            self.searchBar.text = self.filteredSearchStationList[indexPath.row].cs_name
            self.isFromSearch = true
            if AppDelegate.shared.isNearMeListFetched == "0" {
                self.getNearMeList()
            } else {
                AppDelegate.shared.filteredNearMeList.removeAll()
                AppDelegate.shared.filteredNearMeList = AppDelegate.shared.nearMeList.filter { $0.cs_id == "\(AppDelegate.shared.strAddressID)" }
                AppDelegate.shared.isNearMeListFetched = "1"
                print("startUpdatingLocation")
                self.locationManager.startUpdatingLocation()
            }
        }
    }
}
// MARK: - CLLocationManagerDelegate
//1
extension MapChargerVC: CLLocationManagerDelegate {
    // 2
    func locationManager(_ manager: CLLocationManager, didChangeAuthorization status: CLAuthorizationStatus) {

        print(status)
        // 3
        guard status == .authorizedWhenInUse else {
            return
        }
        // 4
        locationManager.startUpdatingLocation()

        //5
        mapView.isMyLocationEnabled = true
        mapView.settings.myLocationButton = true

    }

    // 6
    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {

        guard let location = locations.first else {
            return
        }

        mapView.isMyLocationEnabled = true
        mapView.settings.myLocationButton = true

        var latSearch:Double = Double()
        var lngSearch:Double = Double()

        for item in AppDelegate.shared.filteredNearMeList {
            latSearch = Double(item.lat)!
            lngSearch = Double(item.long)!
        }

        if isFromSearch == true {
            print("didUpdateLocations:-isFromSearch")
            mapView.camera = GMSCameraPosition(latitude: latSearch, longitude: lngSearch, zoom: 15)  //GMSCameraPosition(target: location.coordinate, zoom: 15, bearing: 0, viewingAngle: 0)
        } else {
            print("didUpdateLocations:-")
            mapView.camera = GMSCameraPosition(target: location.coordinate, zoom: 15, bearing: 0, viewingAngle: 0)
        }
        // 7

        locationManager.stopUpdatingLocation()
    }
}
extension MapChargerVC: GMSMapViewDelegate {

    func mapView(_ mapView: GMSMapView, didTapAt coordinate: CLLocationCoordinate2D) {
        UIView.animate(withDuration: 0.75, animations: {
            self.viewBgDetails.isHidden = true
            self.view.layoutIfNeeded()
        })
    }

    func mapView(_ mapView: GMSMapView, didTap marker: GMSMarker) -> Bool {
        let bounds = GMSCoordinateBounds(coordinate: marker.position, coordinate: marker.position)
        self.mapView.animate(with: GMSCameraUpdate.fit(bounds, withPadding: 20.0))
        self.mapView.animate(toZoom: mapView.camera.zoom)
        marker.tracksViewChanges = false
        marker.tracksInfoWindowChanges = false
        if let poiItem = marker.userData as? POIItem {
            print("poiItem:-",poiItem)
            print("poiItem.name:-",poiItem.csID!)
            AppDelegate.shared.strAddressID = poiItem.csID!
            self.getCSDetails()
        } else {
            print("Did tap a normal marker")
        }
        return true

    }

    func mapView(_ mapView: GMSMapView, didChange position: GMSCameraPosition) {
//        print("position \(position)")
//        print("mapView.camera.zoom \(mapView.camera.zoom)")
    }

}
extension MapChargerVC: GMUClusterManagerDelegate {
    func clusterManager(_ clusterManager: GMUClusterManager, didTap cluster: GMUCluster) -> Bool {
        let newCamera = GMSCameraPosition.camera(withTarget: cluster.position,zoom: mapView.camera.zoom + 1)
        let update = GMSCameraUpdate.setCamera(newCamera)
        mapView.moveCamera(update)
        return false
    }
}
extension MapChargerVC: UISearchBarDelegate {
    func searchBar(_ searchBar: UISearchBar, textDidChange searchText: String) {

        filteredSearchStationList = searchStationList
        if searchText.isEmpty == false {
            viewSearchList.isHidden = false
            filteredSearchStationList = searchStationList.filter{ ($0.cs_name.localizedCaseInsensitiveContains(searchText))}

            filteredSearchStationList += searchStationList.filter{ ($0.address.localizedCaseInsensitiveContains(searchText))}

            if filteredSearchStationList.count > 4 {
                self.viewSearchHeight.constant = CGFloat(4 * 85)
            } else {
                self.viewSearchHeight.constant = CGFloat(filteredSearchStationList.count * 85)
            }

        } else {
            viewSearchList.isHidden = true
            self.isFromSearch = false
            searchBar.resignFirstResponder()
            filteredSearchStationList = searchStationList
            AppDelegate.shared.strAddressID = ""
            if AppDelegate.shared.isNearMeListFetched == "0" {
                self.getNearMeList()
            }  else {
                AppDelegate.shared.filteredNearMeList.removeAll()
                AppDelegate.shared.filteredNearMeList = AppDelegate.shared.nearMeList
                AppDelegate.shared.isNearMeListFetched = "1"
                print("startUpdatingLocation")
                self.locationManager.startUpdatingLocation()
            }
        }
        searchListTableView.reloadData()
    }

    func searchBarTextDidBeginEditing(_ searchBar: UISearchBar) {
        searchBar.showsCancelButton = false
    }

    func searchBarSearchButtonClicked(_ searchBar: UISearchBar) {
        searchBar.resignFirstResponder()
    }

    func searchBarCancelButtonClicked(_ searchBar: UISearchBar) {
        print("searchBarCancelButtonClicked")
        self.isFromSearch = false
        searchBar.showsCancelButton = false
        searchBar.text = ""
        searchBar.resignFirstResponder()

        filteredSearchStationList = searchStationList
        AppDelegate.shared.strAddressID = ""
        if AppDelegate.shared.isNearMeListFetched == "0" {
            self.getNearMeList()
        } else {
            AppDelegate.shared.filteredNearMeList.removeAll()
            AppDelegate.shared.filteredNearMeList = AppDelegate.shared.nearMeList
            AppDelegate.shared.isNearMeListFetched = "1"
            print("startUpdatingLocation")
            self.locationManager.startUpdatingLocation()
        }
    }
}
extension MapChargerVC:UIGestureRecognizerDelegate {
    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldBeRequiredToFailBy otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        return true
    }
}
//extension UIViewController {
//
//    func presentDetail(_ viewControllerToPresent: UIView) {
//        let transition = CATransition()
//        transition.duration = 0.25
//        transition.type = CATransitionType.push
//        transition.subtype = CATransitionSubtype.fromRight
//        self.view.window!.layer.add(transition, forKey: kCATransition)
//        present(viewControllerToPresent, animated: false)
//    }
//
//    func dismissDetail() {
//        let transition = CATransition()
//        transition.duration = 0.25
//        transition.type = CATransitionType.push
//        transition.subtype = CATransitionSubtype.fromLeft
//        self.view.window!.layer.add(transition, forKey: kCATransition)
//
//        dismiss(animated: false)
//    }
//}

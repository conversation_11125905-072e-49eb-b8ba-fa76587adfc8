//
//  MenuCell.swift
//  MenuCell
//
//  Created by <PERSON><PERSON><PERSON> on 19/08/21.
//

import UIKit

class MenuCell: UITableViewCell {

    // MARK: - IBOutlets
    @IBOutlet weak var viewMain: UIView!
    @IBOutlet weak var imgMenu: UIImageView!
    @IBOutlet weak var lblMenu: UILabel!
    
    
    // MARK: - Cell LifeCycle Methods
    override func awakeFromNib() {
        super.awakeFromNib()

//        viewMain.shadowWithCRadius(radius: 12, color: Constants.primaryColor!)
//        viewMain.addBorder(color: Constants.textBorderColor!, width: Int(0.5))
        
        
        viewMain.addBorder(color: Constants.backArrowBorderColor!, width: Int(1))
        viewMain.maskClipCorner(cornerRadius: 10)
        
    }

    override func setSelected(_ selected: <PERSON><PERSON>, animated: <PERSON>ol) {
        super.setSelected(selected, animated: animated)

    }
}

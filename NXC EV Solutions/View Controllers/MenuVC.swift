//
//  MenuVC.swift
//  NXC EV Solutions
//
//  Created by Dev<PERSON>per on 11/10/21.
//

import UIKit

class MenuVC: UIViewController {

    
    @IBOutlet weak var viewBgMenu: UIView!
    @IBOutlet weak var viewMainMenu: UIView!
    @IBOutlet weak var viewLogout: UIView!
    @IBOutlet weak var viewProfile: UIView!

    @IBOutlet weak var lblInitial: UILabel!
    @IBOutlet weak var lblUserName: UILabel!
    @IBOutlet weak var lblBalance: UILabel!
    @IBOutlet weak var tableMenu: UITableView!
    @IBOutlet weak var tableHeight: NSLayoutConstraint!
    
    var menuTitleList:[String] = []
    var menuImagesList:[String] = []
    var selectedIndex = Int()
    
    override func viewDidLoad() {
        super.viewDidLoad()

        menuTitleList = ["Profile","Change Language","Get Your RFID Card","News",
                         "Buy Charger","Games","Help","Complaint","About Us"]
        menuImagesList = ["ic_profile","ic_change_language","ic_complain","ic_news","ic_buy_charger","ic_game_icon","ic_help","ic_complain","ic_about"]
        
        tableHeight.constant = CGFloat(menuTitleList.count * 60)
        
        tableMenu.delegate = self
        tableMenu.dataSource = self
    }
    
    
    override func viewDidLayoutSubviews() {
        
        viewProfile.addBorder(color: Constants.backArrowBorderColor!, width: Int(1))
        viewProfile.maskClipCorner(cornerRadius: 10)
        
        lblInitial.maskClipCorner(cornerRadius: Int(lblInitial.frame.height/2))

    }
    
    
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        let touch = touches.first!
//        if(touch.view == viewBgMenu){
//            openCloseMenu()
//        }
        dismissView()
    }
    
    func dismissView() {
        let vc = MenuVC.instantiate(appStoryboard: .PreLogin)
        vc.modalPresentationStyle = .overFullScreen //or .overFullScreen for transparency
        let transition = CATransition()
        self.definesPresentationContext = true
        transition.duration = 0.5
        transition.type = CATransitionType.push
        transition.subtype = CATransitionSubtype.fromRight
        transition.timingFunction = CAMediaTimingFunction(name:CAMediaTimingFunctionName.easeInEaseOut)
        view.window!.layer.add(transition, forKey: kCATransition)
        dismiss(animated: false)
    }
    

    // MARK: - Button Actions
    
    @IBAction func logoutTapped(_ sender: UIButton) {
        dismissView()
        let alertController = UIAlertController(title: "Logout".localiz(), message: "Logout Message".localiz(), preferredStyle: .alert)

        // Create the actions
        let cancelAction = UIAlertAction(title: "NO".localiz(), style: UIAlertAction.Style.cancel) {
            UIAlertAction in
            print("Cancel Pressed")
        }

        let okAction = UIAlertAction(title: "YES".localiz(), style: UIAlertAction.Style.default) {
            UIAlertAction in
            print("OK Pressed")
            AppDelegate.shared.apiKeyLogout()
        }

        // Add the actions
        alertController.addAction(cancelAction)
        alertController.addAction(okAction)

        // Present the controller
        self.present(alertController, animated: true, completion: nil)
    }
    
}
extension MenuVC: UITableViewDelegate, UITableViewDataSource {
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return menuTitleList.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        
        let cell = tableView.dequeueReusableCell(withIdentifier: "cell", for: indexPath) as! MenuCell
        cell.lblMenu.text = "\(self.menuTitleList[indexPath.row])"
        cell.imgMenu.image = UIImage(named: "\(self.menuImagesList[indexPath.row])")
        
        if indexPath.row+1 == selectedIndex {
            cell.lblMenu.textColor = Constants.primaryColor
            cell.imgMenu!.image = cell.imgMenu!.image?.withRenderingMode(.alwaysTemplate)
            cell.imgMenu!.tintColor = Constants.primaryColor
        } else {
            cell.lblMenu.textColor = Constants.menuTextColor
            cell.imgMenu!.image = cell.imgMenu!.image?.withRenderingMode(.alwaysTemplate)
            cell.imgMenu!.tintColor = Constants.menuIconColor
        }
        
        return cell
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        
        AppDelegate.shared.selectedIndex = indexPath.row+1
        dismissView()
        self.tableMenu.reloadData()
        
        DispatchQueue.main.async {
            if AppDelegate.shared.selectedIndex == 1 {
                let vc = ProfileDetailsVC.instantiate(appStoryboard: .Profile)
                self.navigationController?.pushViewController(vc, animated: true)
            } else if AppDelegate.shared.selectedIndex == 2 {
                let vc = ComplainListVC.instantiate(appStoryboard: .Complain)
                self.navigationController?.pushViewController(vc, animated: true)
            } else if AppDelegate.shared.selectedIndex == 3 {
                let vc = LanguageVC.instantiate(appStoryboard: .Language)
                self.navigationController?.pushViewController(vc, animated: true)
            } else if AppDelegate.shared.selectedIndex == 4 {
                let vc = IssueNewCardVC.instantiate(appStoryboard: .RFID)
                self.navigationController?.pushViewController(vc, animated: true)
            } else if AppDelegate.shared.selectedIndex == 5 {
                let vc = GameListVC.instantiate(appStoryboard: .Help)
                self.navigationController?.pushViewController(vc, animated: true)
            } else if AppDelegate.shared.selectedIndex == 6 {
                let vc = AboutUsVC.instantiate(appStoryboard: .Help)
                self.navigationController?.pushViewController(vc, animated: true)
            } else if AppDelegate.shared.selectedIndex == 7 {
                let vc = HelpVC.instantiate(appStoryboard: .Help)
                self.navigationController?.pushViewController(vc, animated: true)
            }
        }
    }
}

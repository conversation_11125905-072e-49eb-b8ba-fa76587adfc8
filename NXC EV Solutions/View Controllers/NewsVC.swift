//
//  NewsVC.swift
//  NXC EV Solutions
//
//  Created by <PERSON><PERSON><PERSON> on 21/10/21.
//

import UIKit
import WebKit
import Alamofire

class NewsVC: UIViewController {

    // MARK: - IBOutlets
    @IBOutlet weak var btnBack: UIButton!
    @IBOutlet weak var webDetails: WKWebView!
    @IBOutlet weak var viewNoData: UIView!

    // MARK: - View LifeCycle Methods
    override func viewDidLoad() {
        super.viewDidLoad()

        webDetails.navigationDelegate = self
        setupUI()
        getNewsURL()
    }

    override func viewDidAppear(_ animated: Bool) {
        AppDelegate.shared.isSwipeBack = 1
        navigationController?.interactivePopGestureRecognizer?.delegate = self
        navigationController?.interactivePopGestureRecognizer?.isEnabled = true
    }

    override func viewDidLayoutSubviews() {
        btnBack.addBorder(color: Constants.backArrowBorderColor!, width: Int(1))
        btnBack.maskClipCorner(cornerRadius: 10)
    }

    // MARK: - Setup UI
    func setupUI() {
        // Initially hide the no data view
        viewNoData.isHidden = true
    }

    // MARK: - Button Actions
    @IBAction func backTapped(_ sender: UIButton) {
        navigationController?.popViewController(animated: true)
    }

    // MARK: - WebService
    func getNewsURL() {
        AppDelegate.shared.showHUD()
        let url = Constants.BASE_URL + API.NEWS_URL
        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]
        AF.request(url, method: .get, parameters: nil, encoding:  URLEncoding.default, headers: headers).responseJSON { response in
            print(response)
            AppDelegate.shared.hideHUD()
            switch response.result {
            case .success:

                let JSON = response.value as! NSDictionary
                print(JSON)

                if "\(JSON["code"]!)" == "200" && "\(JSON["status"]!)" == "1" {
                    self.webDetails.load(URLRequest(url: URL(string: "\(JSON["data"]!)")!))
                    AppDelegate.shared.showHUD()
                    self.webDetails.isHidden = false
                    self.viewNoData.isHidden = true
                } else {
                    // Show "No News Found" view instead of logging out
                    self.webDetails.isHidden = true
                    self.viewNoData.isHidden = false

                    // Only logout if it's an authentication error (code 100)
                    if "\(JSON["code"]!)" == "100" {
                        AppDelegate.shared.apiKeyLogout()
                    }
                }
                break
            case .failure(let error):
                print(error)
                AppDelegate.shared.hideHUD()
                // Show "No News Found" view on network error
                self.webDetails.isHidden = true
                self.viewNoData.isHidden = false
            }
        }
    }
}
extension NewsVC: WKNavigationDelegate {
    func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        AppDelegate.shared.hideHUD()
    }
}

extension NewsVC: UIGestureRecognizerDelegate {
    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldBeRequiredToFailBy otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        return true
    }
}

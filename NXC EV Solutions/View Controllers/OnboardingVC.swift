//
//  SplashVC.swift
//  NXC EV Solutions
//
//  Created by <PERSON><PERSON><PERSON> on 16/07/21.
//


import UIKit

class OnboardingVC: UIViewController {

    
    let walkthroughs = [
        ATCWalkthroughModel(title: "Find Charging Station", subtitle: "Find your nearby charge spot with real-time availability", icon: "onboarding_1"),
        ATCWalkthroughModel(title: "Add Money", subtitle: "Add money to your wallet to make your payments easier and faster", icon: "onboarding_2"),
        ATCWalkthroughModel(title: "Start Charging", subtitle: "Plug-in,Scan QR/RFID & start charging.\n Monitor real-time status for all your charging needs", icon: "onboarding_3")]
    
    
    override func viewDidLoad() {
        super.viewDidLoad()

        let walkthroughVC = self.walkthroughVC()
        walkthroughVC.delegate = self
        self.addChildViewControllerWithView(walkthroughVC)
    }
    
}
extension OnboardingVC: ATCWalkthroughViewControllerDelegate {
    func walkthroughViewControllerDidFinishFlow(_ vc: ATCWalkthroughViewController) {
        UIView.transition(with: self.view, duration: 1, options: .transitionCrossDissolve, animations: {
            let vc = LoginVC.instantiate(appStoryboard: .PreLogin)
//            UserDefaults.standard.set("0", forKey: Constants.ISONBOARD)
            self.navigationController?.pushViewController(vc, animated: true)
        }, completion: nil)
    }
    
    fileprivate func walkthroughVC() -> ATCWalkthroughViewController {
        let viewControllers = walkthroughs.map { ATCClassicWalkthroughViewController(model: $0, nibName: "ATCClassicWalkthroughViewController", bundle: nil) }
        return ATCWalkthroughViewController(nibName: "ATCWalkthroughViewController",
                                            bundle: nil,
                                            viewControllers: viewControllers)
    }
}

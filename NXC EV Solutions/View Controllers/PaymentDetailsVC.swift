//
//  PaymentDetailsVC.swift
//  PaymentDetailsVC
//
//  Created by Developer on 20/08/21.
//

import UIKit
import Alamofire

class PaymentDetailsVC: UIViewController {
    
    
    // MARK: - IBOutlets
    @IBOutlet weak var btnAddComplain: UIButton!
    @IBOutlet weak var viewMain: UIView!
    @IBOutlet weak var lblTransactionID: UILabel!
    @IBOutlet weak var lblDateTime: UILabel!
    @IBOutlet weak var lblAmount: UILabel!
    @IBOutlet weak var lblPaymentMode: UILabel!
    @IBOutlet weak var lblStatus: UILabel!
    @IBOutlet weak var btnBack: UIButton!
    
    var param:[String:Any] = [:]
    var payDetails:[PaymentDetails] = []

    
    // MARK: - View LifeCycle Methods
    override func viewDidLoad() {
        super.viewDidLoad()
        
        getPayDetails()
    }
    
    override func viewDidAppear(_ animated: Bool) {
        AppDelegate.shared.isSwipeBack = 1
        navigationController?.interactivePopGestureRecognizer?.delegate = self
        navigationController?.interactivePopGestureRecognizer?.isEnabled = true
    }

    override func viewDidLayoutSubviews() {
        btnAddComplain.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        lblStatus.maskClipCorner(cornerRadius: 8)
        viewMain.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        
        btnBack.addBorder(color: Constants.backArrowBorderColor!, width: Int(1))
        btnBack.maskClipCorner(cornerRadius: 10)
    }
    
    // MARK: - Button Action
    @IBAction func backAction(_ sender: UIButton) {
        navigationController?.popViewController(animated: true)
    }
    
    @IBAction func addComplainAction(_ sender: UIButton) {
        AppDelegate.shared.isFromMenu = false
        let vc = AddComplainVC.instantiate(appStoryboard: .Complain)
        navigationController?.pushViewController(vc, animated: true)
    }
    
    // MARK: - WebService
    func getPayDetails() {

        AppDelegate.shared.showHUD()
        let url = Constants.BASE_URL + API.PAYMENT_DETAILS
        param = ["transaction_id":"\(AppDelegate.shared.strTransactionID)"]
        print("PAYMENT_DETAILS:-",param)
        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]
        AF.request(url, method: .post, parameters: param, encoding:  URLEncoding.default, headers: headers).responseJSON { response in
            print(response)
            AppDelegate.shared.hideHUD()

            switch response.result {
            case .success:
                let JSON = response.value as! NSDictionary
                print(JSON)

                if "\(JSON["code"]!)" == "200" && "\(JSON["status"]!)" == "1" {

                    let jsonData = JSON["data"] as! NSDictionary

                    self.payDetails.append(PaymentDetails(dic: jsonData))
                    self.lblTransactionID.text = "\(AppDelegate.shared.strTransactionID)"
                    AppDelegate.shared.strTypeID = "\(jsonData["tras_type"]!)"

                    self.lblAmount.text = "₹ \(jsonData["balance_amount"]!)"
                    self.lblPaymentMode.text = "\(jsonData["payment_type"]!)"

                    let localTime = "\(jsonData["start_time"]!)".utcToLocal(dateFromValue: "yyyy-MM-dd HH:mm:ss", dateToValue: "dd MMM yyyy hh:mm a", dateStr: "\(jsonData["start_time"]!)")
                    
                    self.lblDateTime.text = "\(localTime!)"
                    self.lblStatus.text = "\(jsonData["status"]!)"
                                                                                
                } else {
                    
                    AppDelegate.shared.hideHUD()
                    if "\(JSON["code"]!)" == "100" {
                        AppDelegate.shared.apiKeyLogout()
                    }
                }
                
                break
            case .failure(let error):
                print(error)
                self.alert(title: "", message: "\(error.localizedDescription)")
            }
        }
    }
}
//extension PaymentDetailsVC: UITableViewDelegate, UITableViewDataSource {
//
//    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
//        return 10
//    }
//
//    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
//        let cell = tableView.dequeueReusableCell(withIdentifier: "cell", for: indexPath) as! PaymentDetailsCell
//        return cell
//    }
//
//    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
//
//    }
//}
extension PaymentDetailsVC:UIGestureRecognizerDelegate {
    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldBeRequiredToFailBy otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        return true
    }
}

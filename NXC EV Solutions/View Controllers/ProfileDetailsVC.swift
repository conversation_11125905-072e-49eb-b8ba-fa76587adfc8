//
//  ProfileDetailsVC.swift
//  ProfileDetailsVC
//
//  Created by <PERSON><PERSON><PERSON> on 20/08/21.
//

import UIKit
import Alamofire

class ProfileDetailsVC: UIViewController {
    
    
    // MARK: - IBOutlets
    @IBOutlet weak var viewProfileInitial: UIView!
    @IBOutlet weak var lblInitial: UILabel!
    @IBOutlet weak var lblName: UILabel!
    @IBOutlet weak var lblEmail: UILabel!
    
    @IBOutlet weak var viewPhone: UIView!
    @IBOutlet weak var lblPhone: UILabel!
    
    @IBOutlet weak var viewDOB: UIView!
    @IBOutlet weak var lblDOB: UILabel!
    
    @IBOutlet weak var viewGender: UIView!
    @IBOutlet weak var lblGender: UILabel!
    
    @IBOutlet weak var viewAddress: UIView!
    @IBOutlet weak var lblAddress: UILabel!
    
    @IBOutlet weak var viewLocation: UIView!
    @IBOutlet weak var lblLocation: UILabel!
    
    @IBOutlet weak var tableVehicle: UITableView!
    
    @IBOutlet weak var viewBgAlert: UIView!
    @IBOutlet weak var viewMainAlert: UIView!
    
//    @IBOutlet weak var btnCancelAlert: UIButton!
    @IBOutlet weak var btnDeleteAlert: UIButton!
    
    @IBOutlet weak var viewBgRefund: UIView!
    @IBOutlet weak var viewMainRefund: UIView!
    
    @IBOutlet weak var txtBankName: UITextField!
    @IBOutlet weak var txtBranchName: UITextField!
    @IBOutlet weak var txtAccountNo: UITextField!
    @IBOutlet weak var txtIFSC: UITextField!
    @IBOutlet weak var btnSubmitRefund: UIButton!
    
    @IBOutlet weak var btnBack: UIButton!
    
    @IBOutlet weak var stackPhone: UIStackView!
    @IBOutlet weak var stackDOB: UIStackView!
    @IBOutlet weak var stackGender: UIStackView!
    @IBOutlet weak var stackDOBGender: UIStackView!
    @IBOutlet weak var stackAddress: UIStackView!
    @IBOutlet weak var stackCity: UIStackView!
    
    var profileDetails:[ProfileDetails] = []
    var vehicleDetails:[VehicleDetails] = []
    var paramVehicle:[String:Any] = [:]
    
    
    // MARK: - View LifeCycle Methods
    override func viewDidLoad() {
        super.viewDidLoad()

        tableVehicle.delegate = self
        tableVehicle.dataSource = self
        
        viewBgAlert.isHidden = true
        viewBgRefund.isHidden = true
        
    }
    
    override func viewWillAppear(_ animated: Bool) {
        AppDelegate.shared.isProfileEdit = "0"
        AppDelegate.shared.isVehicleEdit = "0"
        viewProfile()
    }
    
    override func viewDidLayoutSubviews() {
        
        viewPhone.maskClipCorner(cornerRadius: Int(viewPhone.frame.height/2))
        viewDOB.maskClipCorner(cornerRadius: Int(viewPhone.frame.height/2))
        viewGender.maskClipCorner(cornerRadius: Int(viewPhone.frame.height/2))
        viewAddress.maskClipCorner(cornerRadius: Int(viewPhone.frame.height/2))
        viewLocation.maskClipCorner(cornerRadius: Int(viewPhone.frame.height/2))
        
        txtBankName.addBorder(color: Constants.textNotSelectedColor!, width: Int(1))
        txtBankName.maskClipCorner(cornerRadius: 8)
        
        txtBranchName.addBorder(color: Constants.textNotSelectedColor!, width: Int(1))
        txtBranchName.maskClipCorner(cornerRadius: 8)
        
        txtAccountNo.addBorder(color: Constants.textNotSelectedColor!, width: Int(1))
        txtAccountNo.maskClipCorner(cornerRadius: 8)
        
        txtIFSC.addBorder(color: Constants.textNotSelectedColor!, width: Int(1))
        txtIFSC.maskClipCorner(cornerRadius: 8)
        
        btnDeleteAlert.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        btnSubmitRefund.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        
        viewMainAlert.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        viewMainRefund.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        
        viewProfileInitial.maskClipCorner(cornerRadius: Int(viewProfileInitial.frame.height/2))
        
        btnBack.addBorder(color: Constants.backArrowBorderColor!, width: Int(1))
        btnBack.maskClipCorner(cornerRadius: 10)

    }
    

    // MARK: - Button Actions
    @IBAction func backAction(_ sender: UIButton) {
//        navigationController?.popViewController(animated: true)
        let vc = HomeTabVC.instantiate(appStoryboard: .Home)
        AppDelegate.shared.intPaymentTab = 0
        self.navigationController?.pushViewController(vc, animated: true)
    }
    
    
    @IBAction func addVehicleAction(_ sender: UIButton) {
        AppDelegate.shared.isVehicleEdit = "0"
        let vc = VehicleVC.instantiate(appStoryboard: .Vehicle)
        navigationController?.pushViewController(vc, animated: true)
    }
    
    @IBAction func editProfileAction(_ sender: UIButton) {
        AppDelegate.shared.isProfileEdit = "1"
        let vc = UserInfoVC.instantiate(appStoryboard: .Profile)
        navigationController?.pushViewController(vc, animated: true)
    }
    
    @IBAction func deleteAccountAction(_ sender: UIButton) {
        viewBgAlert.isHidden = false
    }
    
    @IBAction func cancelAlertAction(_ sender: UIButton) {
        viewBgAlert.isHidden = true
    }
    
    @IBAction func cancelRefundAction(_ sender: UIButton) {
        viewBgRefund.isHidden = true
    }
    
    @IBAction func deleteAlertAction(_ sender: UIButton) {
        viewBgAlert.isHidden = true
        viewBgRefund.isHidden = false
    }
    
    @IBAction func submitRefundAction(_ sender: UIButton) {
        viewBgRefund.isHidden = true
    }
    
    @objc func editAction(_ sender: UIButton) {
        AppDelegate.shared.isVehicleEdit = "1"
        AppDelegate.shared.strVehicleID = self.vehicleDetails[sender.tag].vehicle_id
        let vc = VehicleVC.instantiate(appStoryboard: .Vehicle)
        navigationController?.pushViewController(vc, animated: true)
    }
    
    @objc func deleteVehicleAction(_ sender: UIButton) {
        let alertController = UIAlertController(title: "Delete Vehicle".localiz(), message: "Are you sure you want to delete this vehicle".localiz(), preferredStyle: .alert)

        // Create the actions
        let cancelAction = UIAlertAction(title: "NO".localiz(), style: UIAlertAction.Style.cancel) {
            UIAlertAction in
            print("Cancel Pressed")
        }

        let okAction = UIAlertAction(title: "YES".localiz(), style: UIAlertAction.Style.default) {
            UIAlertAction in
            print("OK Pressed")
            self.deleteVehicle(vehicleID: self.vehicleDetails[sender.tag].vehicle_id)
        }

        // Add the actions
        alertController.addAction(cancelAction)
        alertController.addAction(okAction)

        // Present the controller
        self.present(alertController, animated: true, completion: nil)
    }
    
    
    
    // MARK: - Webservice
    func viewProfile() {
        AppDelegate.shared.showHUD()
        let url = Constants.BASE_URL + API.VIEW_PROFILE
        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]
        AF.request(url, method: .get, parameters: nil, encoding: URLEncoding.default, headers: headers).responseJSON { response in
            print(response)
            AppDelegate.shared.hideHUD()
            switch response.result {
            case .success:
                let JSON = response.value as! NSDictionary
                print(JSON)

                if "\(JSON["code"]!)" == "200" && "\(JSON["status"]!)" == "1" {
                    let jsonData = JSON["data"] as! NSDictionary
                    print(jsonData)

                    self.profileDetails.removeAll()
                    self.vehicleDetails.removeAll()
                    
                    // Fix: "profile_data" is a dictionary, not an array
                    if let profileData = jsonData["profile_data"] as? NSDictionary {
                        self.profileDetails.append(ProfileDetails(dic: profileData))
                    }
                    
                    for item in jsonData["vehicle_data"] as! NSArray {
                        self.vehicleDetails.append(VehicleDetails(dic: item as! NSDictionary))
                    }
                    
                    // Rest of the code remains the same
                    if let profile = self.profileDetails.first {
                        self.lblInitial.text = profile.first_name.first?.description
                        self.lblName.text = "\(profile.first_name)" + " \(profile.last_name)"
                        
                        // Fix: Don't display "<null>" for email
                        if profile.e_mail.isEmpty || profile.e_mail == "<null>" {
                            self.lblEmail.text = ""
                            // Hide email label if needed
                            self.lblEmail.isHidden = true
                        } else {
                            self.lblEmail.text = profile.e_mail
                            self.lblEmail.isHidden = false
                        }
                        
                        self.lblAddress.text = "\(profile.flat_house_no)," + " \(profile.area)," + " \(profile.flat_landmark)" + " - \(profile.zipcode)"
                        self.lblLocation.text = "\(profile.city_name)," + " \(profile.state_name)"
                        self.lblDOB.text = "\(profile.birth_day)".convertDateString(fromFormat: "yyyy-MM-dd", toFormat: "dd MMM yyyy")
                        
                        if self.lblDOB.text?.isEmpty ?? true {
                            self.stackDOB.isHidden = true
                        } else {
                            self.stackDOB.isHidden = false
                        }
                        
                        self.lblPhone.text = profile.phone
                        if self.lblPhone.text?.isEmpty ?? true {
                            self.stackPhone.isHidden = true
                        } else {
                            self.stackPhone.isHidden = false
                        }
                        
                        // Use the helper method to get display gender
                        self.lblGender.text = profile.getDisplayGender()
                        if self.lblGender.text?.isEmpty ?? true {
                            self.stackGender.isHidden = true
                        } else {
                            self.stackGender.isHidden = false
                        }
                        
                        if self.lblDOB.text?.isEmpty ?? true && self.lblGender.text?.isEmpty ?? true {
                            self.stackDOBGender.isHidden = true
                        }
                    }
                    
                    self.tableVehicle.reloadData()
                } else {
                    if "\(JSON["code"]!)" == "100" {
                        AppDelegate.shared.apiKeyLogout()
                    }
                }
                break
            case .failure(let error):
                print(error)
                AppDelegate.shared.hideHUD()
            }
        }
    }
    
    func deleteVehicle(vehicleID: String) {
        AppDelegate.shared.showHUD()
        let url = Constants.BASE_URL + API.DELETE_VEHICLE
        paramVehicle = ["vehicle_id":"\(vehicleID)"]
        print("paramVehicle:-",paramVehicle)
        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]
        AF.request(url, method: .post, parameters: paramVehicle, encoding:  URLEncoding.default, headers: headers).responseJSON { response in
        print(response)
        AppDelegate.shared.hideHUD()
        switch response.result {
        case .success:

            let JSON = response.value as! NSDictionary
            print(JSON)

            if "\(JSON["code"]!)" == "200" && "\(JSON["status"]!)" == "1" {

                let jsonData = JSON["data"] as! NSDictionary
                print(jsonData)
                print("\(JSON["msg"]!)")
                let alertController = UIAlertController(title: NSLocalizedString("Delete Vehicle", comment: ""), message: NSLocalizedString("Your vehicle delete successfully", comment: ""), preferredStyle: .alert)
                let settingsAction = UIAlertAction(title: NSLocalizedString("OK", comment: ""), style: .default) { (UIAlertAction) in
                    self.viewProfile()
                }
                alertController.addAction(settingsAction)
                self.present(alertController, animated: true, completion: nil)
                
            } else {
                if "\(JSON["code"]!)" == "100" {
                    AppDelegate.shared.apiKeyLogout()
                }
            }
            break
        case .failure(let error):
            print(error)
            AppDelegate.shared.hideHUD()
            }
        }
    }
}
extension ProfileDetailsVC: UITableViewDelegate, UITableViewDataSource {
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return vehicleDetails.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "cell", for: indexPath) as! ProfileDetailsCell
        cell.lblMfd.text = self.vehicleDetails[indexPath.row].brand_name
        cell.lblModel.text = self.vehicleDetails[indexPath.row].model_text
        cell.lblRegNo.text = self.vehicleDetails[indexPath.row].reg_num.uppercased()
        
        cell.btnEdit.tag = indexPath.row
        cell.btnEdit.addTarget(self, action: #selector(self.editAction(_:)), for: .touchUpInside)
        
        cell.btnDelete.tag = indexPath.row
        cell.btnDelete.addTarget(self, action: #selector(self.deleteVehicleAction(_:)), for: .touchUpInside)
        
        return cell
    }

}
//extension ProfileDetailsVC:UIGestureRecognizerDelegate {
//    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldBeRequiredToFailBy otherGestureRecognizer: UIGestureRecognizer) -> Bool {
//        return true
//    }
//}

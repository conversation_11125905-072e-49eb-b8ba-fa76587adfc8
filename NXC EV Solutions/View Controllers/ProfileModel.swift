//
//  ProfileModel.swift
//  ProfileModel
//
//  Created by <PERSON><PERSON><PERSON> on 27/08/21.
//

import Foundation

struct StateList {
    var state_id: String
    var state_country_id: String
    var state_name: String
    
    init(dic:NSDictionary) {
        self.state_id = "\(dic["state_id"]!)"
        self.state_country_id = "\(dic["state_country_id"]!)"
        self.state_name = "\(dic["state_name"]!)"
    }
}

struct CityList {
    var city_id: String
    var city_country_id: String
    var city_state_id: String
    var city_name: String
    
    init(dic:NSDictionary) {
        self.city_id = "\(dic["city_id"]!)"
        self.city_country_id = "\(dic["city_country_id"]!)"
        self.city_state_id = "\(dic["city_state_id"]!)"
        self.city_name = "\(dic["city_name"]!)"
    }
}

struct GenderList {
    var id: String
    var name: String
    
    init(dic:NSDictionary) {
        self.id = "\(dic["id"]!)"
        self.name = "\(dic["name"]!)"
    }
}

struct ProfileDetails {
    var user_id: String
    var first_name: String
    var last_name: String
    var birth_day: String
    var gst: String
    var sex: String
    var gender: String
    var phone: String
    var e_mail: String
    var country_id: String
    var state_id: String
    var city_id: String
    var zipcode: String
    var address: String
    var area: String
    var flat_house_no: String
    var flat_landmark: String
    var country_name: String
    var state_name: String
    var city_name: String
    var email_status: String
    
    init(dic:NSDictionary) {
        self.user_id = "\(dic["user_id"] ?? "")"
        self.first_name = "\(dic["first_name"] ?? "")"
        self.last_name = "\(dic["last_name"] ?? "")"
        self.birth_day = "\(dic["birth_day"] ?? "")"
        self.gst = "\(dic["gst"] ?? "")"
        self.sex = "\(dic["sex"] ?? "")"
        self.phone = "\(dic["phone"] ?? "")"
        
        // Handle both string and numeric gender values
        if let genderValue = dic["gender"] {
            let genderStr = "\(genderValue)"
            if genderStr == "1" || genderStr == "Male" {
                self.gender = "1"  // Store as numeric value
            } else if genderStr == "2" || genderStr == "Female" {
                self.gender = "2"  // Store as numeric value
            } else {
                self.gender = genderStr  // Keep as is if it's something else
            }
        } else {
            self.gender = ""
        }
        
        // Fix: Handle null email properly
        if let email = dic["e_mail"], !(email is NSNull), "\(email)" != "<null>" {
            self.e_mail = "\(email)"
        } else {
            self.e_mail = ""
        }
        
        self.country_id = "\(dic["country_id"] ?? "")"
        self.state_id = "\(dic["state_id"] ?? "")"
        self.city_id = "\(dic["city_id"] ?? "")"
        self.zipcode = "\(dic["zipcode"] ?? "")"
        self.address = "\(dic["address"] ?? "")"
        self.area = "\(dic["area"] ?? "")"
        self.flat_house_no = "\(dic["flat_house_no"] ?? "")"
        self.flat_landmark = "\(dic["flat_landmark"] ?? "")"
        self.country_name = "\(dic["country_name"] ?? "")"
        self.state_name = "\(dic["state_name"] ?? "")"
        self.city_name = "\(dic["city_name"] ?? "")"
        self.email_status = "\(dic["email_status"] ?? "")"
    }
    
    // Helper method to get display gender
    func getDisplayGender() -> String {
        if gender == "1" {
            return "Male"
        } else if gender == "2" {
            return "Female"
        }
        return gender
    }
}

//
//  RFIDModel.swift
//  RFIDModel
//
//  Created by Developer on 23/09/21.
//

import Foundation

struct TraceRFID {
    var datetime:String = String()
    var id:String = String()
    var status_details:String = String()
    var type:String = String()
    var user_name:String = String()

    init(dic:NSDictionary) {
        self.datetime = "\(dic["datetime"] ?? "")"
        self.id = "\(dic["id"] ?? "")"
        self.status_details = "\(dic["status_details"] ?? "")"
        self.type = "\(dic["type"] ?? "")"
        self.user_name = "\(dic["user_name"] ?? "")"
    }
}

struct RFIDOrder {
    var status:String = String()
    var datetime:String = String()
    var url:String = String()
    var rfid_status:String = String()

    init(dic:NSDictionary) {
        self.status = "\(dic["status"] ?? "")"
        self.datetime = "\(dic["datetime"] ?? "")"
        self.url = "\(dic["url"] ?? "")"
        self.rfid_status = "\(dic["rfid_status"] ?? "")"
    }
}

struct RFIDList {
    var id_tag:String = String()
    var expiry_date:String = String()
    var ocpp_tag_pk:String = String()
    var blocked:String = String()

    init(dic:NSDictionary) {
        self.id_tag = "\(dic["id_tag"] ?? "")"
        self.expiry_date = "\(dic["expiry_date"] ?? "")"
        self.ocpp_tag_pk = "\(dic["ocpp_tag_pk"] ?? "")"
        self.blocked = "\(dic["blocked"] ?? "")"
    }
}


//"id_tag": "7894561230",
//"expiry_date": "2021-12-31 00:00:00",
//"ocpp_tag_pk": "3"


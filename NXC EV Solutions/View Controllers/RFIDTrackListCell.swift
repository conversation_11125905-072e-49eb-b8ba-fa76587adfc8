//
//  RFIDTrackListCell.swift
//  RFIDTrackListCell
//
//  Created by <PERSON><PERSON><PERSON> on 23/09/21.
//

import UIKit

class RFIDTrackListCell: UITableViewCell {


    @IBOutlet weak var viewMain: UIView!
    @IBOutlet weak var nameLabel: UILabel!
    @IBOutlet weak var timeLabel: UILabel!
    @IBOutlet weak var dateLabel: UILabel!
    @IBOutlet weak var viewIcon: UIView!
    @IBOutlet weak var imgCard: UIImageView!
    @IBOutlet weak var btnReplace: UIButton!

//    @IBOutlet weak var btnTrack: UIButton!

    @IBOutlet weak var btnBlock: UIButton!
    @IBOutlet weak var blockLabel: UILabel!

    override func awakeFromNib() {
        super.awakeFromNib()


    }

    override func setSelected(_ selected: Bool, animated: Bool) {
        super.setSelected(selected, animated: animated)


    }

    // Method to update the block label text
    func updateBlockLabel(isBlocked: Bool) {
        // Find the Block label in the cell's content view
        for subview in contentView.subviews {
            if let cardView = subview as? UIView {
                for innerView in cardView.subviews {
                    if let stackView = innerView as? UIStackView {
                        for stackItem in stackView.arrangedSubviews {
                            if let innerStackView = stackItem as? UIStackView {
                                if let label = innerStackView.arrangedSubviews.last as? UILabel,
                                   label.text == "Block" || label.text == "Unblock" {
                                    // This is the "Block/Unblock" label
                                    label.text = isBlocked ? "Unblock" : "Block"
                                    return
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

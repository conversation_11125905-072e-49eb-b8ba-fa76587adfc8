//
//  RFIDTrackListVC.swift
//  RFIDTrackListVC
//
//  Created by Developer on 23/09/21.
//

import UIKit
import Alamofire

class RFIDTrackListVC: UIViewController {
    
    // MARK: - IBOutlets
    @IBOutlet weak var viewNoData: UIView!
    @IBOutlet weak var tableTrackRFID: UITableView!
    @IBOutlet weak var btnBack: UIButton!
    var traceRFIDList:[TraceRFID] = []
    
    var state: State = .noData {
        didSet {
            switch state {
            case .loaded:
                tableTrackRFID.isHidden = false
                viewNoData.isHidden = true
            case .noData:
                tableTrackRFID.isHidden = true
                viewNoData.isHidden = false
            }
        }
    }
    
    // MARK: - View LifeCycle Methods
    override func viewDidLoad() {
        super.viewDidLoad()

        tableTrackRFID.delegate = self
        tableTrackRFID.dataSource = self
        
    }
    
    override func viewWillAppear(_ animated: Bool) {
        getOrderRFID()
    }
    
    override func viewDidAppear(_ animated: Bool) {
        AppDelegate.shared.isSwipeBack = 1
        navigationController?.interactivePopGestureRecognizer?.delegate = self
        navigationController?.interactivePopGestureRecognizer?.isEnabled = true
    }
    
    override func viewDidLayoutSubviews() {
        btnBack.addBorder(color: Constants.backArrowBorderColor!, width: Int(1))
        btnBack.maskClipCorner(cornerRadius: 10)
    }

    // MARK: - Button Actions
    @IBAction func backButtonTapped(_ sender: UIButton) {
        navigationController?.popViewController(animated: true)
    }
    
    
    // MARK: - WebService
    func getOrderRFID() {
        
        let url = Constants.BASE_URL + API.TRACE_RFID
        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]
        AF.request(url, method: .get, parameters: nil, encoding:  URLEncoding.default, headers: headers).responseJSON { response in
            print(response)
            AppDelegate.shared.hideHUD()
            switch response.result {
            case .success:
                
                let JSON = response.value as! NSDictionary
                print(JSON)
                
                if "\(JSON["code"]!)" == "200" && "\(JSON["status"]!)" == "1" {
                    
                    let jsonData = JSON["data"] as! NSDictionary
                    print(jsonData)
                    
                    self.traceRFIDList.removeAll()
                    for item in jsonData["order_data"] as! NSArray {
                        self.traceRFIDList.append(TraceRFID(dic: item as! NSDictionary))
                    }
                    
                    self.tableTrackRFID.reloadData()
                    
                    if self.traceRFIDList.count > 0 {
                        self.state = .loaded
                    } else {
                        self.state = .noData
                    }
                    
                } else {
                    AppDelegate.shared.hideHUD()
                    if "\(JSON["code"]!)" == "100" {
                        AppDelegate.shared.apiKeyLogout()
                    }
                    
                    if self.traceRFIDList.count > 0 {
                        self.state = .loaded
                    } else {
                        self.state = .noData
                    }
                }
                break
            case .failure(let error):
                print(error)
                AppDelegate.shared.hideHUD()
            }
        }
    }
    
}
extension RFIDTrackListVC: UITableViewDelegate, UITableViewDataSource {
        
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return traceRFIDList.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "cell", for: indexPath) as! RFIDTrackListCell
        
        cell.timeLabel.text = "\(traceRFIDList[indexPath.row].status_details)"
        cell.nameLabel.text = traceRFIDList[indexPath.row].user_name
        cell.viewIcon.addBorder(color: Constants.backArrowBorderColor!, width: Int(1))
        cell.viewIcon.maskClipCorner(cornerRadius: Int(cell.viewIcon.frame.height/2))
        cell.layoutIfNeeded()
        cell.updateConstraints()
        return cell
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        AppDelegate.shared.romID = traceRFIDList[indexPath.row].id
        let vc = TrackRFIDStatusVC.instantiate(appStoryboard: .RFID)
        navigationController?.pushViewController(vc, animated: true)
    }
}
extension RFIDTrackListVC:UIGestureRecognizerDelegate {
    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldBeRequiredToFailBy otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        return true
    }
}

//
//  ReplaceCardVC.swift
//  ReplaceCardVC
//
//  Created by <PERSON><PERSON><PERSON> on 24/09/21.
//

import UIKit
import Alamofire

class ReplaceCardVC: UIViewController {

    
    // MARK: - IBOutlets
    @IBOutlet weak var backButton: UIButton!
    @IBOutlet weak var viewNotWorking: UIView!
    @IBOutlet weak var imgNotWorking: UIImageView!
    @IBOutlet weak var viewDefective: UIView!
    @IBOutlet weak var imgDefective: UIImageView!
    @IBOutlet weak var txtOtherReason: UITextView!
    @IBOutlet weak var btnReplaceNow: UIButton!
    
    var paramReplace:[String:Any] = [:]
    var strReplaceStatus = String()
    
    
    // MARK: - View LifeCycle Methods
    override func viewDidLoad() {
        super.viewDidLoad()

        txtOtherReason.textColor = Constants.textNotSelectedColor
        txtOtherReason.text = "Enter Description"
        
        txtOtherReason.delegate = self
    }
    
    override func viewDidAppear(_ animated: Bool) {
        AppDelegate.shared.isSwipeBack = 1
        navigationController?.interactivePopGestureRecognizer?.delegate = self
        navigationController?.interactivePopGestureRecognizer?.isEnabled = true
    }
    
    override func viewDidLayoutSubviews() {
        backButton.addBorder(color: Constants.backArrowBorderColor!, width: Int(1))
        backButton.maskClipCorner(cornerRadius: 10)
        
        btnReplaceNow.shadowWithCRadius(radius: 12, color: Constants.backArrowBorderColor!)
        
        txtOtherReason.addBorder(color: Constants.textNotSelectedColor!, width: Int(1))
        txtOtherReason.maskClipCorner(cornerRadius: 8)
        
        viewDefective.addBorder(color: Constants.textNotSelectedColor!, width: Int(1))
        viewDefective.maskClipCorner(cornerRadius: 12)
        
        viewNotWorking.addBorder(color: Constants.textNotSelectedColor!, width: Int(1))
        viewNotWorking.maskClipCorner(cornerRadius: 12)
        
//        viewNotWorking.maskClipCorner(cornerRadius: 8)
//        viewGujarati.maskClipCorner(cornerRadius: 8)
    }
    

    // MARK: - Button Actions
    @IBAction func backButtonTapped(_ sender: Any) {
        navigationController?.popViewController(animated: true)
    }
    
    @IBAction func notWorkingTapped(_ sender: UIButton) {
        strReplaceStatus = "1"
        imgNotWorking.isHighlighted = true
        imgDefective.isHighlighted = false
       
        viewNotWorking.addBorder(color: Constants.primaryColor!, width: Int(2))
        viewDefective.addBorder(color: Constants.languageDefaultColor!, width: Int(2))
    }
    
    @IBAction func defectiveTapped(_ sender: UIButton) {
        strReplaceStatus = "2"
        imgNotWorking.isHighlighted = false
        imgDefective.isHighlighted = true
        
        viewNotWorking.addBorder(color: Constants.languageDefaultColor!, width: Int(2))
        viewDefective.addBorder(color: Constants.primaryColor!, width: Int(2))
    }
        
    @IBAction func replaceButtonTapped(_ sender: UIButton) {
        replaceRFID()
    }
    
    // MARK: - WebService
    func replaceRFID() {
        let url = Constants.BASE_URL + API.REPLACE_RFID
        paramReplace = [ "replace_reason"     :   strReplaceStatus,
                         "replace_text"       :   "\(self.txtOtherReason.text!)",
                         "old_ocpp_tag"       :   AppDelegate.shared.replaceTagID]

        print("paramReplace:-",paramReplace)
        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]
        AF.request(url, method: .post, parameters: paramReplace, encoding:  URLEncoding.default, headers: headers).responseJSON { response in
            print(response)
            AppDelegate.shared.hideHUD()
            switch response.result {
            case .success:
                
                let JSON = response.value as! NSDictionary
                print(JSON)
                
                if "\(JSON["code"]!)" == "200" && "\(JSON["status"]!)" == "1" {

                    let jsonData = JSON["data"] as! NSDictionary
                    print(jsonData)
                                        
                    let alertController = UIAlertController(title: NSLocalizedString("Replace RFID", comment: ""), message: NSLocalizedString("\(JSON["msg"]!)", comment: ""), preferredStyle: .alert)
                    let settingsAction = UIAlertAction(title: NSLocalizedString("OK", comment: ""), style: .default) { (UIAlertAction) in
                        self.navigationController?.popViewController(animated: true)
                    }
                    alertController.addAction(settingsAction)
                    self.present(alertController, animated: true, completion: nil)
                    
                } else {
                    AppDelegate.shared.hideHUD()
                    if "\(JSON["code"]!)" == "100" {
                        AppDelegate.shared.apiKeyLogout()
                    }
                }
                break
            case .failure(let error):
                print(error)
                self.alert(title: "", message: "\(error.localizedDescription)")
                AppDelegate.shared.hideHUD()
            }
        }
    }
    
}
extension ReplaceCardVC: UITextViewDelegate {
    
    func textViewDidBeginEditing(_ textView: UITextView) {
        if textView.text == "Enter Description" {
            textView.text = nil
            textView.textColor = .black
        }
    }
    
    func textViewDidEndEditing(_ textView: UITextView) {
        if textView.text.isEmpty {
            textView.text = "Enter Description"
            textView.textColor = Constants.textNotSelectedColor
        }
    }
}
extension ReplaceCardVC:UIGestureRecognizerDelegate {
    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldBeRequiredToFailBy otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        return true
    }
}

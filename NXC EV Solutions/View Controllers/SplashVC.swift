//
//  SplashVC.swift
//  NXC EV Solutions
//
//  Created by Dev<PERSON>per on 16/07/21.
//

import UIKit
import LanguageManager_iOS
import Foundation
class SplashVC: UIViewController {

    override func viewDidLoad() {
        super.viewDidLoad()

        // Set language based on UserDefaults
        if let languageValue = UserDefaults.standard.string(forKey: Constants.LANGUAGE) {
            print("🌐 SplashVC - Setting language from UserDefaults: \(languageValue)")

            if languageValue == "1" {
                print("🇬🇧 SplashVC - Setting language to English")
                LanguageManager.shared.setLanguage(language: .en)
                Bundle.setLanguage("en")

                // Update LanguageManager's internal storage
                UserDefaults.standard.set("en", forKey: "LanguageManagerSelectedLanguage")

                // Update AppDelegate's currentLanguage property
                if let appDelegate = UIApplication.shared.delegate as? AppDelegate {
                    appDelegate.currentLanguage = "en"
                    appDelegate.strLanguage = "1"
                }
            } else if languageValue == "2" {
                print("🇮🇳 SplashVC - Setting language to Gujarati")
                // Use the Gujarati language directly from the Languages enum
                LanguageManager.shared.setLanguage(language: .gu)
                Bundle.setLanguage("gu")

                // Update LanguageManager's internal storage
                UserDefaults.standard.set("gu", forKey: "LanguageManagerSelectedLanguage")

                // Update AppDelegate's currentLanguage property
                if let appDelegate = UIApplication.shared.delegate as? AppDelegate {
                    appDelegate.currentLanguage = "gu"
                    appDelegate.strLanguage = "2"
                }
            } else if languageValue == "3" {
                print("🇮🇳 SplashVC - Setting language to Hindi")
                LanguageManager.shared.setLanguage(language: .hi)
                Bundle.setLanguage("hi")

                // Update LanguageManager's internal storage
                UserDefaults.standard.set("hi", forKey: "LanguageManagerSelectedLanguage")

                // Update AppDelegate's currentLanguage property
                if let appDelegate = UIApplication.shared.delegate as? AppDelegate {
                    appDelegate.currentLanguage = "hi"
                    appDelegate.strLanguage = "3"
                }
            }
        } else {
            // Default to English
            print("🇬🇧 SplashVC - No language found, defaulting to English")
            LanguageManager.shared.setLanguage(language: .en)
            Bundle.setLanguage("en")
            UserDefaults.standard.set("1", forKey: Constants.LANGUAGE)
            UserDefaults.standard.set("en", forKey: "LanguageManagerSelectedLanguage")

            // Update AppDelegate's currentLanguage property
            if let appDelegate = UIApplication.shared.delegate as? AppDelegate {
                appDelegate.currentLanguage = "en"
                appDelegate.strLanguage = "1"
            }
        }

        if "\(UserDefaults.standard.value(forKey: Constants.IS_ONBOARDING)!)" == "1" {
            if "\(UserDefaults.standard.value(forKey: Constants.IS_LOGIN)!)" == "1" {
                if "\(UserDefaults.standard.value(forKey: Constants.PROFILE_EDIT)!)" == "1" {
                    if "\(UserDefaults.standard.value(forKey: Constants.VEHICLE_EDIT)!)" == "1" {
                        let vc = HomeTabVC.instantiate(appStoryboard: .Home)
                        AppDelegate.shared.intPaymentTab = 0
                        self.navigationController?.pushViewController(vc, animated: true)
                    } else {
                        AppDelegate.shared.isFromStartView = true
                        let vc = VehicleVC.instantiate(appStoryboard: .Vehicle)
                        self.navigationController?.pushViewController(vc, animated: true)
                    }
                } else {
                    AppDelegate.shared.isFromSplash = "1"
                    AppDelegate.shared.isFromOTPView = true
                    let vc = UserInfoVC.instantiate(appStoryboard: .Profile)
                    self.navigationController?.pushViewController(vc, animated: true)
                }
            } else {
                let vc = LoginVC.instantiate(appStoryboard: .PreLogin)
                navigationController?.pushViewController(vc, animated: true)
            }
        } else {
            let vc = OnboardingVC.instantiate(appStoryboard: .PreLogin)
            navigationController?.pushViewController(vc, animated: true)
        }
    }
}

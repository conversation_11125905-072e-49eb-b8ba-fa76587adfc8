//
//  TrackRFIDStatusVC.swift
//  TrackRFIDStatusVC
//
//  Created by Dev<PERSON><PERSON> on 23/09/21.
//

import UIKit
import Alamofire

class TrackRFIDStatusVC: UIViewController {

    // MARK: - IBOutlets
    @IBOutlet weak var backButton: UIButton!
    @IBOutlet weak var viewMain: UIView!
    @IBOutlet weak var tableTrackStatus: UITableView!
    @IBOutlet weak var tableHeight: NSLayoutConstraint!

    var viewOrderList:[RFIDOrder] = []
    var paramOrderRFID:[String:Any] = [:]


    // MARK: - View LifeCycle
    override func viewDidLoad() {
        super.viewDidLoad()

        tableTrackStatus.delegate = self
        tableTrackStatus.dataSource = self
    }

    override func viewWillAppear(_ animated: Bool) {
        viewRFIDOrder()
    }

    override func viewDidAppear(_ animated: Bool) {
        AppDelegate.shared.isSwipeBack = 1
        navigationController?.interactivePopGestureRecognizer?.delegate = self
        navigationController?.interactivePopGestureRecognizer?.isEnabled = true
    }

    override func viewDidLayoutSubviews() {
        viewMain.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)

        backButton.addBorder(color: Constants.backArrowBorderColor!, width: Int(1))
        backButton.maskClipCorner(cornerRadius: 10)
    }

    // MARK: - Button Actions
    @IBAction func backButtonTapped(_ sender: UIButton) {
        navigationController?.popViewController(animated: true)
    }

    func viewRFIDOrder() {
        let url = Constants.BASE_URL + API.VIEW_RFID_ORDER
        paramOrderRFID = [ "rom_id"       :   AppDelegate.shared.romID]
        print("paramOrderRFID:-",paramOrderRFID)
        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]
        AF.request(url, method: .post, parameters: paramOrderRFID, encoding:  URLEncoding.default, headers: headers).responseJSON { response in
            print(response)
            AppDelegate.shared.hideHUD()
            switch response.result {
            case .success:

                let JSON = response.value as! NSDictionary
                print(JSON)

                if "\(JSON["code"]!)" == "200" && "\(JSON["status"]!)" == "1" {

                    let jsonData = JSON["data"] as! NSDictionary
                    print(jsonData)

                    self.viewOrderList.removeAll()
                    for item in jsonData["order_data"] as! NSArray {
                        self.viewOrderList.append(RFIDOrder(dic: item as! NSDictionary))
                    }

                    self.tableHeight.constant = CGFloat(self.viewOrderList.count * 70)
                    self.tableTrackStatus.reloadData()

                    if self.viewOrderList.count == 0 {
                        self.viewMain.isHidden = true
                    }

                } else {
                    AppDelegate.shared.hideHUD()
                    if "\(JSON["code"]!)" == "100" {
                        AppDelegate.shared.apiKeyLogout()
                    }
                }
                break
            case .failure(let error):
                print(error)
                self.alert(title: "", message: "\(error.localizedDescription)")
                AppDelegate.shared.hideHUD()
            }
        }
    }
}
extension TrackRFIDStatusVC: UITableViewDelegate, UITableViewDataSource {

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 70
    }

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return viewOrderList.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "cell", for: indexPath) as! TrackRFIDStatusCell
        cell.lblStatus.text = viewOrderList[indexPath.row].status

        if viewOrderList[indexPath.row].rfid_status == "1" {

            if indexPath.row == 0 {
                cell.imgLineTop.isHidden = true
                cell.imgLineBottom.isHidden = false
            } else {
                cell.imgLineTop.isHidden = false
                if viewOrderList.count == indexPath.row + 1 {
                    cell.imgLineBottom.isHidden = true
                } else {
                    cell.imgLineBottom.isHidden = false
                }
            }

            if !viewOrderList[indexPath.row].datetime.isEmpty {
                if let localTime = "\(viewOrderList[indexPath.row].datetime)".utcToLocal(dateFromValue: "yyyy-MM-dd HH:mm:ss", dateToValue: "dd MMM yyyy hh:mm a", dateStr: "\(viewOrderList[indexPath.row].datetime)") {
                    cell.lblTime.text = localTime
                } else {
                    cell.lblTime.text = "--"
                }
            } else {
                cell.lblTime.text = "--"
            }
            cell.imgTick.image = UIImage(named: "ic_ticked")
        } else {

            cell.imgLineBottom.isHidden = true
            cell.imgLineTop.isHidden = true

            cell.lblStatus.text = viewOrderList[indexPath.row].status
            cell.lblTime.text = "--"
            cell.imgTick.image = UIImage(named: "ic_uncheck")
        }
        cell.layoutIfNeeded()
        cell.updateConstraints()
        return cell
    }
}
extension TrackRFIDStatusVC:UIGestureRecognizerDelegate {
    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldBeRequiredToFailBy otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        return true
    }
}

//
//  TransactionDetailsVC.swift
//  TransactionDetailsVC
//
//  Created by <PERSON><PERSON><PERSON> on 28/09/21.
//

import UIKit
import Alamofire
import PDFKit

class TransactionDetailsVC: UIViewController {

    // MARK: - IBOutlets
    @IBOutlet var lblTitle: UILabel!
    @IBOutlet weak var btnBack: UIButton!
    @IBOutlet weak var btnMenu: UIButton!

    @IBOutlet var scrollDetails: UIScrollView!

    @IBOutlet weak var viewTransaction: UIView!
    @IBOutlet var lblTransactionID: UILabel!
    @IBOutlet var lblTime: UILabel!
    @IBOutlet var lblDate: UILabel!

    @IBOutlet weak var viewChargerDetails: UIView!
    @IBOutlet var lblStationName: UILabel!
    @IBOutlet var lblChargerName: UILabel!

    @IBOutlet weak var viewConsumptionCharge: UIView!
    @IBOutlet var lblUsedUnit: UILabel!
    @IBOutlet var lblFreeUnit: UILabel!
    @IBOutlet var lblConsumptionChargeableUnit: UILabel!
    @IBOutlet var lblUnitPrice: UILabel!
    @IBOutlet var lblConsumptionNetTotal: UILabel!
    @IBOutlet var lblConsumptionTax: UILabel!
    @IBOutlet var lblTotalConspCharge: UILabel!
    @IBOutlet var stackFreeUnit: UIStackView!

    @IBOutlet var viewOtherCharge: UIView!
    @IBOutlet var tableOtherCharge: UITableView!
    @IBOutlet var lblOtherTax: UILabel!
    @IBOutlet var lblTotalOtherCharge: UILabel!
    @IBOutlet var lblGrandTotal: UILabel!
    @IBOutlet var tableChargeHeight: NSLayoutConstraint!

    @IBOutlet weak var viewFixCharge: UIView!
    @IBOutlet var lblFixChargeFreeUnit: UILabel!
    @IBOutlet var lblFixChargeNetTotal: UILabel!
    @IBOutlet var lblFixChargeTax: UILabel!
    @IBOutlet var lblFixChargeTotal: UILabel!

//    @IBOutlet var stackTotal: UIStackView!

    @IBOutlet weak var btnAddComplain: UIButton!
    @IBOutlet weak var stackReceipt: UIStackView!

    @IBOutlet weak var viewGrandTotal: UIView!

    var paramTxnInfo:[String:Any] = [:]
    var transData:[TransactionData] = []
    var otherCharge:[OtherCharge] = []
    var rfidTransData:[RFIDTransactionData] = []

    @IBOutlet weak var viewTxnTop: UIView!
    @IBOutlet weak var viewChargeDetailsTop: UIView!
    @IBOutlet weak var viewFixChargeTop: UIView!
    @IBOutlet weak var viewConsumptionTop: UIView!
    @IBOutlet weak var viewOtherChargeTop: UIView!
    @IBOutlet weak var viewGrandTotalTop: UIView!

    // RFID-specific outlets (programmatically created)
    var viewRFIDDetails: UIView!
    var viewRFIDDetailsTop: UIView!
    var lblRFIDTag: UILabel!
    var lblRFIDStatus: UILabel!
    var lblRFIDAmount: UILabel!
    var lblOrderDate: UILabel!
    var lblOrderTime: UILabel!
    var lblDeliveredDate: UILabel!


    // MARK: - View LifeCycle Methods
    override func viewDidLoad() {
        super.viewDidLoad()

        tableOtherCharge.delegate = self
        tableOtherCharge.dataSource = self

        // Hide download receipt functionality as it's not working properly
        stackReceipt.isHidden = true

        // Set localized text for Tax label
        // Note: The Tax label title will be set via localization in the storyboard

        // Create RFID UI programmatically
        createRFIDUI()

        getTransactionInfo()

    }

    override func viewDidAppear(_ animated: Bool) {
        AppDelegate.shared.isSwipeBack = 1
        navigationController?.interactivePopGestureRecognizer?.delegate = self
        navigationController?.interactivePopGestureRecognizer?.isEnabled = true
    }

    override func viewDidLayoutSubviews() {

        viewTransaction.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        viewChargerDetails.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        viewConsumptionCharge.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        viewOtherCharge.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        viewFixCharge.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        viewGrandTotal.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        btnAddComplain.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)

        // RFID view styling
        if viewRFIDDetails != nil {
            viewRFIDDetails.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        }

        viewTxnTop.roundCorners(corners: [.topLeft,.topRight], radius: 12)
        viewChargeDetailsTop.roundCorners(corners: [.topLeft,.topRight], radius: 12)
        viewFixChargeTop.roundCorners(corners: [.topLeft,.topRight], radius: 12)
        viewConsumptionTop.roundCorners(corners: [.topLeft,.topRight], radius: 12)
        viewOtherChargeTop.roundCorners(corners: [.topLeft,.topRight], radius: 12)
        viewGrandTotalTop.roundCorners(corners: [.topLeft,.topRight], radius: 12)

        // RFID view top styling
        if viewRFIDDetailsTop != nil {
            viewRFIDDetailsTop.roundCorners(corners: [.topLeft,.topRight], radius: 12)
        }

        btnMenu.addBorder(color: Constants.backArrowBorderColor!, width: Int(1))
        btnMenu.maskClipCorner(cornerRadius: 10)

        btnBack.addBorder(color: Constants.backArrowBorderColor!, width: Int(1))
        btnBack.maskClipCorner(cornerRadius: 10)

    }

    // MARK: - Button Actions
    @IBAction func backButtonTapped(_ sender: UIButton) {
        navigationController?.popViewController(animated: true)
    }


    @IBAction func downloadReceiptTapped(_ sender: Any) {
        // Download receipt functionality disabled as it's not working properly
        // The stackReceipt is hidden in viewDidLoad to prevent user interaction
        print("Download receipt functionality is currently disabled")
    }

    @IBAction func addComplaintTapped(_ sender: UIButton) {

    }

    // Snapshot functionality disabled along with download receipt
    // func snapshot() -> UIImage? { ... }


    func getDirectoryPath() -> String {
        let paths = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true)
        let documentsDirectory = paths[0]
        return documentsDirectory
    }


    func utcToLocal(dateStr: String) -> String? {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "H:mm:ss"
        dateFormatter.timeZone = TimeZone(abbreviation: "UTC")

        if let date = dateFormatter.date(from: dateStr) {
            dateFormatter.timeZone = TimeZone.current
//            print("dateFormatter.timeZone:-",dateFormatter.timeZone)
            dateFormatter.dateFormat = "hh:mm a"

            return dateFormatter.string(from: date)
        }
        return nil
    }

    // MARK: - Create RFID UI Programmatically
    func createRFIDUI() {
        // Create main RFID details container view
        viewRFIDDetails = UIView()
        viewRFIDDetails.translatesAutoresizingMaskIntoConstraints = false
        viewRFIDDetails.backgroundColor = UIColor.white
        viewRFIDDetails.layer.cornerRadius = 12
        viewRFIDDetails.isHidden = true // Initially hidden

        // Create header view
        viewRFIDDetailsTop = UIView()
        viewRFIDDetailsTop.translatesAutoresizingMaskIntoConstraints = false
        viewRFIDDetailsTop.backgroundColor = Constants.primaryColor

        // Create header label
        let lblRFIDHeader = UILabel()
        lblRFIDHeader.translatesAutoresizingMaskIntoConstraints = false
        lblRFIDHeader.text = "RFID Order Details"
        lblRFIDHeader.textColor = UIColor.white
        lblRFIDHeader.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        lblRFIDHeader.textAlignment = .center

        // Create content container
        let contentView = UIView()
        contentView.translatesAutoresizingMaskIntoConstraints = false
        contentView.backgroundColor = UIColor.white

        // Create all label pairs
        createRFIDLabelPairs(in: contentView)

        // Add header label to header view
        viewRFIDDetailsTop.addSubview(lblRFIDHeader)

        // Add header and content to main view
        viewRFIDDetails.addSubview(viewRFIDDetailsTop)
        viewRFIDDetails.addSubview(contentView)

        // Add main view to scroll view
        scrollDetails.addSubview(viewRFIDDetails)

        // Setup constraints
        setupRFIDConstraints(contentView: contentView, headerLabel: lblRFIDHeader)
    }

    func createRFIDLabelPairs(in contentView: UIView) {
        let labelPairs = [
            ("RFID Tag:", "lblRFIDTag"),
            ("Status:", "lblRFIDStatus"),
            ("Amount:", "lblRFIDAmount"),
            ("Order Date:", "lblOrderDate"),
            ("Order Time:", "lblOrderTime"),
            ("Delivery Date:", "lblDeliveredDate")
        ]

        var previousStackView: UIStackView?

        for (index, pair) in labelPairs.enumerated() {
            // Create title label
            let titleLabel = UILabel()
            titleLabel.translatesAutoresizingMaskIntoConstraints = false
            titleLabel.text = pair.0
            titleLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
            titleLabel.textColor = Constants.secondaryGrayText
            titleLabel.textAlignment = .left

            // Create value label
            let valueLabel = UILabel()
            valueLabel.translatesAutoresizingMaskIntoConstraints = false
            valueLabel.font = UIFont.systemFont(ofSize: 14, weight: .regular)
            valueLabel.textColor = UIColor.black
            valueLabel.textAlignment = .right
            valueLabel.text = "--"

            // Assign to appropriate property
            switch pair.1 {
            case "lblRFIDTag": lblRFIDTag = valueLabel
            case "lblRFIDStatus": lblRFIDStatus = valueLabel
            case "lblRFIDAmount": lblRFIDAmount = valueLabel
            case "lblOrderDate": lblOrderDate = valueLabel
            case "lblOrderTime": lblOrderTime = valueLabel
            case "lblDeliveredDate": lblDeliveredDate = valueLabel
            default: break
            }

            // Create horizontal stack view for the pair
            let stackView = UIStackView(arrangedSubviews: [titleLabel, valueLabel])
            stackView.translatesAutoresizingMaskIntoConstraints = false
            stackView.axis = .horizontal
            stackView.distribution = .fillEqually
            stackView.alignment = .center
            stackView.spacing = 8

            contentView.addSubview(stackView)

            // Setup constraints for stack view
            NSLayoutConstraint.activate([
                stackView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 16),
                stackView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -16),
                stackView.heightAnchor.constraint(equalToConstant: 30)
            ])

            if index == 0 {
                stackView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 16).isActive = true
            } else if let previous = previousStackView {
                stackView.topAnchor.constraint(equalTo: previous.bottomAnchor, constant: 12).isActive = true
            }

            if index == labelPairs.count - 1 {
                stackView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -16).isActive = true
            }

            previousStackView = stackView
        }
    }

    func setupRFIDConstraints(contentView: UIView, headerLabel: UILabel) {
        // Header label constraints
        NSLayoutConstraint.activate([
            headerLabel.centerXAnchor.constraint(equalTo: viewRFIDDetailsTop.centerXAnchor),
            headerLabel.centerYAnchor.constraint(equalTo: viewRFIDDetailsTop.centerYAnchor)
        ])

        // Header view constraints
        NSLayoutConstraint.activate([
            viewRFIDDetailsTop.topAnchor.constraint(equalTo: viewRFIDDetails.topAnchor),
            viewRFIDDetailsTop.leadingAnchor.constraint(equalTo: viewRFIDDetails.leadingAnchor),
            viewRFIDDetailsTop.trailingAnchor.constraint(equalTo: viewRFIDDetails.trailingAnchor),
            viewRFIDDetailsTop.heightAnchor.constraint(equalToConstant: 44)
        ])

        // Content view constraints
        NSLayoutConstraint.activate([
            contentView.topAnchor.constraint(equalTo: viewRFIDDetailsTop.bottomAnchor),
            contentView.leadingAnchor.constraint(equalTo: viewRFIDDetails.leadingAnchor),
            contentView.trailingAnchor.constraint(equalTo: viewRFIDDetails.trailingAnchor),
            contentView.bottomAnchor.constraint(equalTo: viewRFIDDetails.bottomAnchor)
        ])

        // Main RFID view constraints in scroll view
        // Position it between viewTransaction and viewChargerDetails
        NSLayoutConstraint.activate([
            viewRFIDDetails.topAnchor.constraint(equalTo: viewTransaction.bottomAnchor, constant: 16),
            viewRFIDDetails.leadingAnchor.constraint(equalTo: scrollDetails.leadingAnchor, constant: 16),
            viewRFIDDetails.trailingAnchor.constraint(equalTo: scrollDetails.trailingAnchor, constant: -16),
            viewRFIDDetails.widthAnchor.constraint(equalTo: scrollDetails.widthAnchor, constant: -32)
        ])
    }


    // MARK: - WebService
    func getTransactionInfo() {

        AppDelegate.shared.showHUD()
        let url = Constants.BASE_URL + API.GET_TRANSACTION_INFO
        paramTxnInfo = ["transaction_id":"\(AppDelegate.shared.strTransactionID)",
                        "type_id":"\(AppDelegate.shared.strTypeID)"]
        print("paramTxnInfo:-",paramTxnInfo)
        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]
        AF.request(url, method: .post, parameters: paramTxnInfo, encoding:  URLEncoding.default, headers: headers).responseJSON { response in
            print(response)
            AppDelegate.shared.hideHUD()
            switch response.result {
            case .success:
                let JSON = response.value as! NSDictionary
                print(JSON)

                if "\(JSON["code"]!)" == "200" && "\(JSON["status"]!)" == "1" {

                    let jsonData = JSON["data"] as! NSDictionary

                    // Check if this is an RFID transaction (type "3")
                    if AppDelegate.shared.strTypeID == "3" {
                        // Handle RFID transaction
                        self.handleRFIDTransaction(jsonData: jsonData)
                    } else {
                        // Handle charging/wallet transactions (type "1" or "2")
                        self.handleChargingTransaction(jsonData: jsonData)
                    }

                    // Safely set transaction type ID
                    if let trasType = jsonData["tras_type"] {
                        AppDelegate.shared.strTypeID = "\(trasType)"
                    } else {
                        AppDelegate.shared.strTypeID = "1" // Default value
                    }

                } else {

                    AppDelegate.shared.hideHUD()

                    if "\(JSON["code"]!)" == "100" {
                        AppDelegate.shared.apiKeyLogout()
                    }
                }

                break
            case .failure(let error):
                print(error)
                self.alert(title: "", message: "\(error.localizedDescription)")
            }
        }
    }

    // MARK: - Handle Different Transaction Types
    func handleChargingTransaction(jsonData: NSDictionary) {
        self.transData.removeAll()
        self.otherCharge.removeAll()

        // Show charging-related views
        self.showChargingViews()

        for item in jsonData["other_charge"] as! NSArray {
            print(item as! NSDictionary)
            let dicPrice = item as! NSDictionary
            if Double("\(dicPrice["charge_price"]!)") != 0 {
                self.otherCharge.append(OtherCharge(dic: item as! NSDictionary))
            }
        }

        print("self.otherCharge:-",self.otherCharge)

        self.transData.append(TransactionData(dic: jsonData))
        print(self.transData)
        self.tableChargeHeight.constant = CGFloat(34*self.otherCharge.count)
        self.tableOtherCharge.reloadData()

        // Handle Fix Charge section
        let fixChargeData = self.transData[0].fix_charge
        let freeUnit = Double("\(fixChargeData["free_unit"] ?? 0)") ?? 0
        let netCharges = Double("\(fixChargeData["net_charges"] ?? 0)") ?? 0
        let taxAmount = Double("\(fixChargeData["tax_amount"] ?? 0)") ?? 0
        let tdfFixCharge = Double("\(fixChargeData["tdf_fix_charge"] ?? 0)") ?? 0
        let total = Double("\(fixChargeData["total"] ?? 0)") ?? 0

        // Check if all fix charge values are 0
        if freeUnit == 0 && netCharges == 0 && taxAmount == 0 && tdfFixCharge == 0 && total == 0 {
            self.viewFixCharge.isHidden = true
        } else {
            self.viewFixCharge.isHidden = false
            self.lblFixChargeFreeUnit.text = String(format: "%.2f", freeUnit)
            self.lblFixChargeNetTotal.text = String(format: "₹ %.2f", netCharges)
            self.lblFixChargeTax.text = String(format: "₹ %.2f", taxAmount)
            self.lblFixChargeTotal.text = String(format: "₹ %.2f", total)
        }

        // Handle Consumption Charges section with detailed breakdown
        let consumptionChargeData = self.transData[0].consumption_charge
        let usedUnitConsumption = Double("\(consumptionChargeData["used_unit"] ?? 0)") ?? 0
        let freeUnitFromFixCharge = freeUnit // Use the freeUnit from fix_charge section
        let perUnitPrice = Double("\(consumptionChargeData["per_unit_price"] ?? 0)") ?? 0
        let netChargesConsumption = Double("\(consumptionChargeData["net_charges"] ?? 0)") ?? 0
        let taxAmountConsumption = Double("\(consumptionChargeData["tax_amount"] ?? 0)") ?? 0
        let totalConsumption = Double("\(consumptionChargeData["total"] ?? 0)") ?? 0

        // Calculate chargeable unit (Used Unit - Free Unit)
        let chargeableUnit = max(0, usedUnitConsumption - freeUnitFromFixCharge)

        // Set consumption charge labels with detailed breakdown
        self.lblUsedUnit.text = String(format: "%.2f", usedUnitConsumption)
        self.lblFreeUnit.text = String(format: "%.2f", freeUnitFromFixCharge)
        self.lblConsumptionChargeableUnit.text = String(format: "%.2f", chargeableUnit)
        self.lblUnitPrice.text = String(format: "₹ %.2f", perUnitPrice)
        self.lblConsumptionNetTotal.text = String(format: "₹ %.2f", netChargesConsumption)
        self.lblConsumptionTax.text = String(format: "₹ %.2f", taxAmountConsumption)
        self.lblTotalConspCharge.text = String(format: "₹ %.2f", totalConsumption)

        // Hide Free Unit row if free unit is 0
        if freeUnitFromFixCharge == 0 {
            self.stackFreeUnit.isHidden = true
        } else {
            self.stackFreeUnit.isHidden = false
        }

        // Safely set transaction details
        if let transactionId = self.transData[0].transaction["transaction_id"] {
            self.lblTransactionID.text = "\(transactionId)"
        } else {
            self.lblTransactionID.text = "--"
        }

        if let stationName = self.transData[0].transaction["cs_name"] {
            self.lblStationName.text = "\(stationName)"
        } else {
            self.lblStationName.text = "--"
        }

        if let chargerName = self.transData[0].transaction["charger"] {
            self.lblChargerName.text = "\(chargerName)"
        } else {
            self.lblChargerName.text = "--"
        }

        let dateFormatter1 = DateFormatter()
        dateFormatter1.timeZone = .current
        dateFormatter1.locale = Locale(identifier: "en_US")
        dateFormatter1.dateFormat = "HH:mm:ss"

        // Safely handle time conversion
        if let timeValue = self.transData[0].transaction["time"] {
            let timeStr = "\(timeValue)"
            if let localTime = timeStr.utcToLocal(dateFromValue: "HH:mm:ss", dateToValue: "hh:mm a", dateStr: timeStr) {
                self.lblTime.text = localTime
            } else {
                self.lblTime.text = "--:--"
            }
        } else {
            self.lblTime.text = "--:--"
        }

        // Safely handle date conversion
        let dateFormatter2 = DateFormatter()
        dateFormatter2.timeZone = .current
        dateFormatter2.locale = Locale(identifier: "en_US")
        dateFormatter2.dateFormat = "yyyy-MM-dd"

        if let dateValue = self.transData[0].transaction["date"] {
            let dateStr = "\(dateValue)"
            if let date2 = dateFormatter2.date(from: dateStr) {
                dateFormatter2.dateFormat = "dd MMM yyyy"
                let finalDate = dateFormatter2.string(from: date2)
                self.lblDate.text = finalDate
            } else {
                self.lblDate.text = "--/--/----"
            }
        } else {
            self.lblDate.text = "--/--/----"
        }

        // Safely handle other charge total and tax
        let otherChargeTotalStr = jsonData["other_charge_total"] != nil ? "\(jsonData["other_charge_total"]!)" : "0"
        let doubleOtherChargeTotal = Double(otherChargeTotalStr) ?? 0

        let otherTaxTotalStr = jsonData["other_tax_total"] != nil ? "\(jsonData["other_tax_total"]!)" : "0"
        let doubleOtherTaxTotal = Double(otherTaxTotalStr) ?? 0

        if doubleOtherChargeTotal == 0 {
            self.viewOtherCharge.isHidden = true
        } else {
            self.viewOtherCharge.isHidden = false
            self.lblTotalOtherCharge.text = String(format: "₹ %.2f", doubleOtherChargeTotal)
            self.lblOtherTax.text = String(format: "₹ %.2f", doubleOtherTaxTotal)
        }

        // Safely set grand total with proper formatting
        if let grandTotal = jsonData["grand_total"] {
            let doubleGrandTotal = Double("\(grandTotal)") ?? 0
            self.lblGrandTotal.text = String(format: "₹ %.2f", doubleGrandTotal)
        } else {
            self.lblGrandTotal.text = String(format: "₹ %.2f", 0.0)
        }
    }

    func handleRFIDTransaction(jsonData: NSDictionary) {
        
        self.rfidTransData.removeAll()

        // Show RFID-related views
        self.showRFIDViews()

        self.rfidTransData.append(RFIDTransactionData(dic: jsonData))


        // Set RFID-specific data
        if let rfidData = self.rfidTransData.first {
            self.lblTransactionID.text = rfidData.transaction_id.isEmpty ? "--" : rfidData.transaction_id
            self.lblRFIDTag.text = rfidData.rfid_tag.isEmpty ? "--" : rfidData.rfid_tag
            self.lblRFIDStatus.text = rfidData.status.isEmpty ? "--" : rfidData.status.capitalized
            self.lblRFIDAmount.text = String(format: "₹ %.2f", Double(rfidData.amount) ?? 0.0)

            // Handle order date
            if !rfidData.order_date.isEmpty {
                let dateFormatter = DateFormatter()
                dateFormatter.dateFormat = "yyyy-MM-dd"
                if let date = dateFormatter.date(from: rfidData.order_date) {
                    dateFormatter.dateFormat = "dd MMM yyyy"
                    self.lblOrderDate.text = dateFormatter.string(from: date)
                    self.lblDate.text = dateFormatter.string(from: date)
                } else {
                    self.lblOrderDate.text = rfidData.order_date
                    self.lblDate.text = rfidData.order_date
                }
            } else {
                self.lblOrderDate.text = "--"
            }

            // Handle time
            if !rfidData.time.isEmpty {
                if let localTime = rfidData.time.utcToLocal(dateFromValue: "HH:mm:ss", dateToValue: "hh:mm a", dateStr: rfidData.time) {
                    self.lblOrderTime.text = localTime
                    self.lblTime.text = localTime
                } else {
                    self.lblOrderTime.text = rfidData.time
                    self.lblTime.text = rfidData.time
                }
            } else {
                self.lblOrderTime.text = "--"
            }

            // Handle delivered date
            if !rfidData.delivered_date.isEmpty && rfidData.delivered_date != "null" {
                let dateFormatter = DateFormatter()
                dateFormatter.dateFormat = "yyyy-MM-dd"
                if let date = dateFormatter.date(from: rfidData.delivered_date) {
                    dateFormatter.dateFormat = "dd MMM yyyy"
                    self.lblDeliveredDate.text = dateFormatter.string(from: date)
                } else {
                    self.lblDeliveredDate.text = rfidData.delivered_date
                }
            } else {
                self.lblDeliveredDate.text = "Pending Delivery"
            }
        }
    }

    func showChargingViews() {
        // Hide RFID views
        if viewRFIDDetails != nil {
            viewRFIDDetails.isHidden = true
        }

        // Show date and time in top transaction section for charging transactions
        self.lblDate.isHidden = false
        self.lblTime.isHidden = false

        // Show charging views
        viewChargerDetails.isHidden = false
        viewConsumptionCharge.isHidden = false
        viewFixCharge.isHidden = false
        viewOtherCharge.isHidden = false
        viewGrandTotal.isHidden = false
    }

    func showRFIDViews() {
        // Hide charging views
        viewChargerDetails.isHidden = true
        viewConsumptionCharge.isHidden = true
        viewFixCharge.isHidden = true
        viewOtherCharge.isHidden = true
        viewGrandTotal.isHidden = true

        // Show RFID views
        if viewRFIDDetails != nil {
            viewRFIDDetails.isHidden = false
        }
    }
}
extension TransactionDetailsVC: UITableViewDelegate,UITableViewDataSource {

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return self.otherCharge.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {

        let cell = tableView.dequeueReusableCell(withIdentifier: "cell", for: indexPath) as! TransactionCell

        cell.lblTitle.text = self.otherCharge[indexPath.row].charge_name
        let chargePrice = Double(self.otherCharge[indexPath.row].charge_price) ?? 0
        cell.lblValue.text = String(format: "₹ %.2f", chargePrice)

        return cell
    }
}

// UIScrollView screenshot extension disabled along with download receipt functionality
// extension UIScrollView { ... }

extension TransactionDetailsVC:UIGestureRecognizerDelegate {
    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldBeRequiredToFailBy otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        return true
    }
}

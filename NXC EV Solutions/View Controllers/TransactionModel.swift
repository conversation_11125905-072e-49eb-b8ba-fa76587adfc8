//
//  TransactionModel.swift
//  TransactionModel
//
//  Created by Developer on 29/09/21.
//

import Foundation

struct TransactionData {
    var transaction:NSDictionary = NSDictionary()
    var fix_charge:NSDictionary = NSDictionary()
    var consumption_charge:NSDictionary = NSDictionary()
    var other_charge:[OtherCharge] = []
    var other_charge_total:String = String()
    var other_tax_total:String = String()
    var grand_total:String = String()
    var tras_type:String = String()

    init(dic:NSDictionary) {

        self.transaction = dic["transaction"] as! NSDictionary
        self.fix_charge = dic["fix_charge"] as! NSDictionary
        self.consumption_charge = dic["consumption_charge"] as! NSDictionary
        if !"\(dic["other_charge"]!)".isEmpty {
            self.other_charge.removeAll()
            for item in dic["other_charge"] as! NSArray {
                print(item)
                self.other_charge.append(OtherCharge(dic: item as! NSDictionary))
            }
        }
        self.other_charge_total = "\(dic["other_charge_total"]!)"
        self.other_tax_total = "\(dic["other_tax_total"]!)"
        self.grand_total = "\(dic["grand_total"]!)"
        self.tras_type = "\(dic["tras_type"]!)"
    }
}


struct Transaction {
    var charger:String = String()
    var cs_name:String = String()
    var transaction_id:String = String()
    var date:String = String()
    var time:String = String()

    init(dic:NSDictionary) {
        self.charger = "\(dic["charger"]!)"
        self.cs_name = "\(dic["cs_name"]!)"
        self.transaction_id = "\(dic["transaction_id"]!)"
        self.date = "\(dic["date"]!)"
        self.time = "\(dic["time"]!)"
    }
}

struct FixCharge {
    var tdf_fix_charge:String = String()
    var total:String = String()

    init(dic:NSDictionary) {
        self.tdf_fix_charge = "\(dic["tdf_fix_charge"]!)"
        self.total = "\(dic["total"]!)"
    }
}

struct ConsumptionCharge {
    var used_unit:String = String()
    var free_unit:String = String()
    var per_unit_price:String = String()
    var total:String = String()

    init(dic:NSDictionary) {
        self.used_unit = "\(dic["used_unit"]!)"
        self.free_unit = "\(dic["free_unit"]!)"
        self.per_unit_price = "\(dic["per_unit_price"]!)"
        self.total = "\(dic["total"]!)"
    }
}


struct OtherCharge {
    var charge_name:String = String()
    var charge_price:String = String()

    init(dic:NSDictionary) {
        self.charge_name = "\(dic["charge_name"]!)"
        self.charge_price = "\(dic["charge_price"]!)"
    }
}

struct RFIDTransactionData {
    var order_date:String = String()
    var time:String = String()
    var status:String = String()
    var rfid_tag:String = String()
    var amount:String = String()
    var delivered_date:String = String()
    var transaction_id:String = String()

    init(dic:NSDictionary) {
        self.order_date = "\(dic["order_date"] ?? "")"
        self.time = "\(dic["time"] ?? "")"
        self.status = "\(dic["status"] ?? "")"
        self.rfid_tag = "\(dic["rfid_tag"] ?? "")"
        self.amount = "\(dic["amount"] ?? "")"
        self.delivered_date = "\(dic["delivered_date"] ?? "")"
        self.transaction_id = "\(dic["transaction_id"] ?? "")"
    }
}

//
//  UserInfoVC.swift
//  NXC EV Solutions
//
//  Created by <PERSON><PERSON><PERSON> on 16/07/21.
//

import UIKit
import Alamofire

class UserInfoVC: UIViewController {

    
    // MARK: - IBOutlets
    @IBOutlet weak var scrollDetails: UIScrollView!
    @IBOutlet weak var btnNext: UIButton!
    
    @IBOutlet weak var viewBg: UIView!
    @IBOutlet weak var viewMain: UIView!
    
    @IBOutlet weak var datePicker: UIDatePicker!
    @IBOutlet weak var btnOK: UIButton!
    
    @IBOutlet weak var txtFirstName: UITextField!
    @IBOutlet weak var txtLastName: UITextField!
    @IBOutlet weak var txtEmail: UITextField!
    @IBOutlet weak var txtDOB: UITextField!
    @IBOutlet weak var txtGST: UITextField!
    
    @IBOutlet weak var imgMale: UIImageView!
    @IBOutlet weak var imgFemale: UIImageView!
    
    @IBOutlet weak var DOBButton: UIButton!
    
    var userDetails:[ProfileDetails] = []
    var strMainDate = String()
    var strGender = String()
    
    var profileDetails:[ProfileDetails] = []
    

    // MARK: - View LifeCycle
    override func viewDidLoad() {
        super.viewDidLoad()

        viewBg.isHidden = true
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "dd MMM yyyy"
        datePicker.maximumDate = Date()
    
    }
    
    override func viewWillAppear(_ animated: Bool) {
        if AppDelegate.shared.isProfileEdit == "1" {
            self.viewProfile()
        }
    }
    
    override func viewDidAppear(_ animated: Bool) {
        AppDelegate.shared.isSwipeBack = 1
        navigationController?.interactivePopGestureRecognizer?.delegate = self
        navigationController?.interactivePopGestureRecognizer?.isEnabled = true
    }
    
    override func viewDidLayoutSubviews() {
        btnNext.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        scrollDetails.roundCorners(corners: [.topLeft,.topRight], radius: 16)
        viewMain.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        btnOK.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
    }
    
    // MARK: - Ratings Methods
    func genderType(typeID:Int) {
        if typeID == 101 {
            imgMale.isHighlighted = true
            imgFemale.isHighlighted = false
            strGender = "1"
        } else if typeID == 102 {
            imgMale.isHighlighted = false
            imgFemale.isHighlighted = true
            strGender = "2"
        }
    }

    // MARK: - Resign Textfield Methods
    func resignTextField() {
        let _ = self.txtFirstName.resignFirstResponder()
        let _ = self.txtLastName.resignFirstResponder()
    }
    
    // MARK: - Button Actions
    @IBAction func backAction(_ sender: UIButton) {
        navigationController?.popViewController(animated: true)
    }
    
    @IBAction func nextAction(_ sender: UIButton) {
        resignTextField()
        
        if self.txtFirstName.text?.count == 0 {
            self.alert(title: "First Name", message: "Please enter first name")
       
        } else if self.txtLastName.text?.count == 0 {
            self.alert(title: "Last Name", message: "Please enter last name")
            
        } else {
            userDetails.removeAll()
            AppDelegate.shared.profileDetails.removeAll()
            var item:NSDictionary = NSDictionary()
            
            // Convert numeric gender to display string for UI if needed
            let displayGender = strGender == "1" ? "Male" : (strGender == "2" ? "Female" : "")
            
            item = ["first_name"        : "\(txtFirstName.text!)",
                    "last_name"         : "\(txtLastName.text!)",
                    "e_mail"            : "\(txtEmail.text ?? "")",
                    "gender"            : strGender,  // Already numeric from genderType()
                    "birth_day"         : strMainDate,
                    "gst"               : "\(txtGST.text ?? "")"]
            
            userDetails.append(ProfileDetails(dic: item))
            AppDelegate.shared.profileDetails.append(contentsOf: userDetails)
            print("AppDelegate.shared.profileDetails:-",AppDelegate.shared.profileDetails)
            let vc = AddressVC.instantiate(appStoryboard: .Profile)
            navigationController?.pushViewController(vc, animated: true)
        }
    }
    
    @IBAction func OKAction(_ sender: UIButton) {
        let dateFormatter = DateFormatter()
        dateFormatter.timeZone = .current
        dateFormatter.dateFormat = "dd MMM yyyy"
        txtDOB.text = dateFormatter.string(from: datePicker.date)
        viewBg.isHidden = true
        
        let dateFormatter1 = DateFormatter()
        dateFormatter1.timeZone = .current
        dateFormatter1.locale = Locale(identifier: "en_US")
        dateFormatter1.dateFormat = "yyyy-MM-dd"
        strMainDate = dateFormatter1.string(from: datePicker.date)
    }
    
    @IBAction func cancelAction(_ sender: UIButton) {
        viewBg.isHidden = true
    }
    
    @IBAction func dobTapped(_ sender: UIButton) {
        viewBg.isHidden = false
    }
    
    @IBAction func maleTapped(_ sender: UIButton) {
        genderType(typeID: 101)  // This will set strGender to "1"
    }

    @IBAction func femaleTapped(_ sender: UIButton) {
        genderType(typeID: 102)  // This will set strGender to "2"
    }
    
    // MARK: - Webservice
    func viewProfile() {
        AppDelegate.shared.showHUD()
        let url = Constants.BASE_URL + API.VIEW_PROFILE
        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]
        AF.request(url, method: .get, parameters: nil, encoding: URLEncoding.default, headers: headers).responseJSON { [self] response in
            print(response)
            AppDelegate.shared.hideHUD()
            switch response.result {
            case .success:
                let JSON = response.value as! NSDictionary
                print(JSON)

                if "\(JSON["code"]!)" == "200" && "\(JSON["status"]!)" == "1" {
                    let jsonData = JSON["data"] as! NSDictionary
                    print(jsonData)

                    self.profileDetails.removeAll()
                    
                    // Fix: "profile_data" is a dictionary, not an array
                    if let profileData = jsonData["profile_data"] as? NSDictionary {
                        self.profileDetails.append(ProfileDetails(dic: profileData))
                    }
                    
                    // Update UI with profile data
                    if let profile = self.profileDetails.first {
                        self.txtFirstName.text = profile.first_name
                        self.txtLastName.text = profile.last_name
                        self.txtEmail.text = profile.e_mail
                        self.txtGST.text = profile.gst
                        
                        // Handle both string and numeric gender values
                        if profile.gender == "Male" || profile.gender == "1" {
                            self.strGender = "1"
                            self.imgMale.image = UIImage(named: "ic_checked")
                            self.imgFemale.image = UIImage(named: "ic_unchecked")
                        } else if profile.gender == "Female" || profile.gender == "2" {
                            self.strGender = "2"
                            self.imgMale.image = UIImage(named: "ic_unchecked")
                            self.imgFemale.image = UIImage(named: "ic_checked")
                        }
                        
                        if profile.birth_day != "<null>" && !profile.birth_day.isEmpty {
                            self.txtDOB.text = profile.birth_day.convertDateString(fromFormat: "yyyy-MM-dd", toFormat: "dd MMM yyyy")
                            self.strMainDate = profile.birth_day
                        }
                    }
                } else {
                    if "\(JSON["code"]!)" == "100" {
                        AppDelegate.shared.apiKeyLogout()
                    }
                }
                break
            case .failure(let error):
                print(error)
                AppDelegate.shared.hideHUD()
            }
        }
    }
}

extension UserInfoVC: UITextFieldDelegate {
    
    func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
        
        if textField == txtFirstName || textField == txtLastName {
            do {
                let regex = try NSRegularExpression(pattern: ".*[^A-Za-z ].*", options: [])
                if regex.firstMatch(in: string, options: [], range: NSMakeRange(0, string.count)) != nil {
                    self.alert(title: "Name", message: "Please enter only alphabetical letters") //(message: "Must not contain Number in Name")
                } else {
                    
                }
            } catch {
                
            }
        }
        return true
    }
}
extension UserInfoVC:UIGestureRecognizerDelegate {
    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldBeRequiredToFailBy otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        return true
    }
}

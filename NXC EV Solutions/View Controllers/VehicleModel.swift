//
//  VehicleModel.swift
//  VehicleModel
//
//  Created by Developer on 01/09/21.
//

import Foundation

struct VehicleBrand {
    var vb_id:String = String()
    var vb_name:String = String()

    init(dic:NSDictionary) {
        self.vb_id = "\(dic["vb_id"]!)"
        self.vb_name = "\(dic["vb_name"]!)"
    }
}

struct VehicleType {
    var vt_id:String = String()
    var vt_name:String = String()

    init(dic:NSDictionary) {
        self.vt_id = "\(dic["vt_id"]!)"
        self.vt_name = "\(dic["vt_name"]!)"
    }
}

struct VehicleModel {
    var vm_id:String = String()
    var vm_name:String = String()
    var vm_brand_id:String = String()
    var vm_type_id:String = String()

    init(dic:NSDictionary) {
        self.vm_id = "\(dic["vm_id"]!)"
        self.vm_name = "\(dic["vm_name"]!)"
        self.vm_brand_id = "\(dic["vm_brand_id"]!)"
        self.vm_type_id = "\(dic["vm_type_id"]!)"
    }
}

struct VehicleDetails {
    var vehicle_id:String = String()
    var brand_id:String = String()
    var brand_text:String = String()
    var type_id:String = String()
    var type_text:String = String()
    var model_id:String = String()
    var model_text:String = String()
    var reg_num:String = String()
    var brand_name:String = String()
    var model_no:String = String()
    var type_name:String = String()
    
    init(dic:NSDictionary) {
        self.vehicle_id = "\(dic["vehicle_id"]!)"
        self.brand_id = "\(dic["brand_id"]!)"
        self.brand_text = "\(dic["brand_text"]!)"
        self.type_id = "\(dic["type_id"]!)"
        self.type_text = "\(dic["type_text"]!)"
        self.model_id = "\(dic["model_id"]!)"
        self.model_text = "\(dic["model_text"]!)"
        self.reg_num = "\(dic["reg_num"]!)"
        self.brand_name = "\(dic["brand_name"]!)"
        self.model_no = "\(dic["model_no"]!)"
        self.type_name = "\(dic["type_name"]!)"
    }
}

//
//  VehicleVC.swift
//  NXC EV Solutions
//
//  Created by <PERSON><PERSON><PERSON> on 20/07/21.
//

import UIKit
import DropDown
import Alamofire

class VehicleVC: UIViewController {
   
    
    // MARK: - IBOutlets
    @IBOutlet weak var scrollDetails: UIScrollView!
    @IBOutlet weak var txtBrand: UITextField!
    @IBOutlet weak var txtType: UITextField!
    @IBOutlet weak var txtModel: UITextField!
    @IBOutlet weak var txtRegNo: UITextField!
    @IBOutlet weak var btnNext: UIButton!
    @IBOutlet weak var btnSkip: UIButton!
    
    
    @IBOutlet weak var brandButton: UIButton!
    @IBOutlet weak var typeButton: UIButton!
    @IBOutlet weak var modelButton: UIButton!
    
    var paramBrand:[String:Any] = [:]
    var paramType:[String:Any] = [:]
    var paramModel:[String:Any] = [:]
    var paramReg:[String:Any] = [:]

    var vehicleBrandList:[VehicleBrand] = []
    var vehicleTypeList:[VehicleType] = []
    var vehicleModelList:[VehicleModel] = []

    var strBrandID:String = String()
    var strTypeID:String = String()
    var strModelID:String = String()

    var strBrandName:String = String()
    var strTypeName:String = String()
    var strModelName:String = String()

    let BrandDropDown = DropDown()
    let TypeDropDown = DropDown()
    let ModelDropDown = DropDown()
    
    @IBOutlet weak var viewOtherBrand: UIView!
    @IBOutlet weak var txtOtherBrand: UITextField!
    
    @IBOutlet weak var viewOtherModel: UIView!
    @IBOutlet weak var txtOtherModel: UITextField!
    
    var vehicleDetails:[VehicleDetails] = []
    
    // MARK: - View LifeCycle
    override func viewDidLoad() {
        super.viewDidLoad()

        BrandDropDown.dataSource = ["Select brand"]
        TypeDropDown.dataSource = ["Select type"]
        ModelDropDown.dataSource = ["Select model"]

        self.txtBrand.text = BrandDropDown.dataSource[0]
        self.txtType.text = TypeDropDown.dataSource[0]
        self.txtModel.text = ModelDropDown.dataSource[0]
        
//        txtRegNo.delegate = self
        
        txtRegNo.placeholder = "MH01XX1111"
        txtOtherBrand.placeholder = "Enter brand"
        txtOtherModel.placeholder = "Enter model"

        self.viewOtherBrand.isHidden = true
        self.viewOtherModel.isHidden = true

//        if AppDelegate.shared.isFromStartView == true {
//            btnBack.alpha = 0
//        } else {
//            btnBack.alpha = 1
//        }

        
        setupDropDowns()
        getBrand()
        
        if AppDelegate.shared.isVehicleEdit == "1" {
            btnSkip.isHidden = true
            self.viewProfile()
        } else {
            btnSkip.isHidden = false
        }

    }
    
    override func viewDidAppear(_ animated: Bool) {
        AppDelegate.shared.isSwipeBack = 1
        navigationController?.interactivePopGestureRecognizer?.delegate = self
        navigationController?.interactivePopGestureRecognizer?.isEnabled = true
    }
    
    //MARK: -  DropDown Methods
    func setupDropDowns() {
        setupMfdDropDown()
        setupTypeDropDown()
        setupModelDropDown()
    }

    func setupMfdDropDown() {
        BrandDropDown.anchorView = brandButton
        BrandDropDown.bottomOffset = CGPoint(x: 0, y: brandButton.bounds.height)
        BrandDropDown.selectionAction = { [weak self] (index, item) in
            self?.txtBrand.text = item
            self?.strBrandID = "\(self!.vehicleBrandList[index].vb_id)"
//            print("self?.strBrandID:-",self?.strBrandID)
            if item == "Other Brand" {
                self?.txtOtherBrand.text = ""
                self?.txtOtherModel.text = ""
                self?.viewOtherBrand.isHidden = false
                self?.viewOtherModel.isHidden = false
            } else {
                self?.viewOtherBrand.isHidden = true
                self?.viewOtherModel.isHidden = true
            }
        }
    }

    func setupTypeDropDown() {
        TypeDropDown.anchorView = typeButton
        TypeDropDown.bottomOffset = CGPoint(x: 0, y: typeButton.bounds.height)
        TypeDropDown.selectionAction = { [weak self] (index, item) in
            self?.txtType.text = item
            self?.strTypeID = "\(self!.vehicleTypeList[index].vt_id)"
            if item == "Other Type" {
                self?.txtOtherModel.text = ""

                self?.vehicleModelList.removeAll()
                self?.ModelDropDown.dataSource.removeAll()
                var dicModel:NSDictionary = NSDictionary()
                dicModel = ["vm_id":"0",
                            "vm_name":"Other Model",
                            "vm_brand_id":"0",
                            "vm_type_id":"0"]
                self?.vehicleModelList.append(VehicleModel(dic: dicModel))
                for item in self!.vehicleModelList {
                    self?.ModelDropDown.dataSource.append(item.vm_name)
                }
                self?.ModelDropDown.reloadAllComponents()
                AppDelegate.shared.hideHUD()

            } else {
                self?.getModel()
            }
        }
    }

    func setupModelDropDown() {
        ModelDropDown.anchorView = modelButton
        ModelDropDown.bottomOffset = CGPoint(x: 0, y: modelButton.bounds.height)
        ModelDropDown.selectionAction = { [weak self] (index, item) in
            self?.txtModel.text = item
            self?.strModelID = "\(self!.vehicleModelList[index].vm_id)"
            if item == "Other Model" {
                self?.txtOtherModel.text = ""
                self?.viewOtherModel.isHidden = false
            } else {
                self?.viewOtherModel.isHidden = true
            }
        }
    }
    
    override func viewDidLayoutSubviews() {
        btnNext.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        scrollDetails.roundCorners(corners: [.topLeft,.topRight], radius: 16)
        
        BrandDropDown.backgroundColor = .white
        BrandDropDown.textColor = .black
        BrandDropDown.selectedTextColor = .white
        BrandDropDown.selectionBackgroundColor = Constants.primaryColor!
        
        TypeDropDown.backgroundColor = .white
        TypeDropDown.textColor = .black
        TypeDropDown.selectedTextColor = .white
        TypeDropDown.selectionBackgroundColor = Constants.primaryColor!
        
        ModelDropDown.backgroundColor = .white
        ModelDropDown.textColor = .black
        ModelDropDown.selectedTextColor = .white
        ModelDropDown.selectionBackgroundColor = Constants.primaryColor!
        
        BrandDropDown.maskClipCorner(cornerRadius: 8)
        TypeDropDown.maskClipCorner(cornerRadius: 8)
        ModelDropDown.maskClipCorner(cornerRadius: 8)
    }

    // MARK: - Button Actions
    @IBAction func skipAction(_ sender: UIButton) {
        
    }
    
    @IBAction func backAction(_ sender: UIButton) {
        navigationController?.popViewController(animated: true)
    }
    
    @IBAction func brandTapped(_ sender: UIButton) {
        BrandDropDown.show()
    }
    
    @IBAction func typeTapped(_ sender: UIButton) {
        TypeDropDown.show()
    }
    
    @IBAction func modelTapped(_ sender: UIButton) {
        ModelDropDown.show()
    }
    
    @IBAction func nextAction(_ sender: UIButton) {
        
//        str = "MH/14/AA/2000"
//        var pattern = "/[A-Z][A-Z]\/[0-9][0-9]\/[A-Z][A-Z]\/[0-9][0-9][0-9][0-9]$/i"
//        var result = str.match(pattern);
//        console.log(result);
        
        if txtBrand.text == "Select brand".localiz() {
            self.alert(title: "Brand".localiz(), message: "Please select brand".localiz())

        } else if strBrandID == "0" && self.txtOtherBrand.text == "" {
            self.alert(title: "Brand".localiz(), message: "Please enter other brand".localiz())

        } else if txtType.text == "Select type".localiz() {
            self.alert(title: "Type".localiz(), message: "Please select type".localiz())

        } else if strTypeID == "0" {
            self.alert(title: "Type".localiz(), message: "Please enter other type".localiz())

        } else if txtModel.text == "Select model".localiz() {
            self.alert(title: "Model".localiz(), message: "Please select model".localiz())

        } else if strModelID == "0" && self.txtOtherModel.text == ""  {
            self.alert(title: "Model".localiz(), message: "Please enter other model".localiz())

        } else if txtRegNo.text?.isEmpty == true {
            self.alert(title: "Registration number".localiz(), message: "Please enter registration number".localiz())

        } else if !txtRegNo.text!.isValidRegistrationNumber && txtRegNo.text?.isEmpty == false {
            self.alert(title: "Registration number".localiz(), message: "Please enter valid registration number".localiz())

        } else {

            if strBrandID == "0" {
                self.strBrandName = "\(self.txtOtherBrand.text!)"
            } else {
                self.strBrandName = "\(self.txtBrand.text!)"
            }

            if strTypeID == "0" {
//                self.strTypeName = "\(self.txtAddType.text!)"
            } else {
                self.strTypeName = "\(self.txtModel.text!)"
            }

            if strModelID == "0" {
                self.strModelName = "\(self.txtOtherModel.text!)"
            } else {
                self.strModelName = "\(self.txtModel.text!)"
            }

            if AppDelegate.shared.isVehicleEdit == "1" {
                self.editVehicle()
            } else {
                self.carRegistration()
            }
        }
    }
    
    
    //MARK: - Webservice Methods
    func getBrand() {

        let url = Constants.BASE_URL + API.GET_VEHICLE_BRAND
        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]
        AF.request(url, method: .get, parameters: nil, encoding:  URLEncoding.default, headers: headers).responseJSON { response in
        print(response)
        AppDelegate.shared.hideHUD()

            switch response.result {
            case .success:

                let JSON = response.value as! NSDictionary
                print(JSON)

                if "\(JSON["code"]!)" == "200" && "\(JSON["status"]!)" == "1" {
                    
                    let jsonData = JSON["data"] as! NSDictionary
                    print(jsonData)

                    self.vehicleBrandList.removeAll()
                    self.BrandDropDown.dataSource.removeAll()
                    var dicBrand:NSDictionary = NSDictionary()
                    dicBrand = ["vb_id":"0",
                                "vb_name":"Other Brand"]
                    self.vehicleBrandList.append(VehicleBrand(dic: dicBrand))
                    for item in jsonData["vehicle_brand_data"] as! NSArray {
                        self.vehicleBrandList.append(VehicleBrand(dic: item as! NSDictionary))
                    }
                    print(self.vehicleBrandList)
                    for item in self.vehicleBrandList {
                        self.BrandDropDown.dataSource.append(item.vb_name)
                    }
                    self.BrandDropDown.reloadAllComponents()
                    self.getType()
                } else {
                    self.vehicleBrandList.removeAll()
                    self.BrandDropDown.dataSource.removeAll()
                    var dicBrand:NSDictionary = NSDictionary()
                    dicBrand = ["vb_id":"0",
                                "vb_name":"Other Brand"]
                    self.vehicleBrandList.append(VehicleBrand(dic: dicBrand))
                    for item in self.vehicleBrandList {
                        self.BrandDropDown.dataSource.append(item.vb_name)
                    }
                    self.BrandDropDown.reloadAllComponents()
                    AppDelegate.shared.hideHUD()
                }
                break
            case .failure(let error):
                print(error)
                AppDelegate.shared.hideHUD()
            }
        }
    }

    func getType() {

        let url = Constants.BASE_URL + API.GET_VEHICLE_TYPE
        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]
        AF.request(url, method: .get, parameters: nil, encoding:  URLEncoding.default, headers: headers).responseJSON { response in
        print(response)
        AppDelegate.shared.hideHUD()

            switch response.result {
            case .success:

                let JSON = response.value as! NSDictionary
                print(JSON)

                if "\(JSON["code"]!)" == "200" && "\(JSON["status"]!)" == "1" {
                    
                    let jsonData = JSON["data"] as! NSDictionary
                    print(jsonData)

                    self.vehicleTypeList.removeAll()
                    self.TypeDropDown.dataSource.removeAll()
//                    var dicVehicle:NSDictionary = NSDictionary()
//                    dicVehicle = ["vt_id":"0",
//                                  "vt_name":"Other Type"]
//                    self.vehicleTypeList.append(VehicleType(dic: dicVehicle))
                    for item in jsonData["vehicle_type_data"] as! NSArray {
                        self.vehicleTypeList.append(VehicleType(dic: item as! NSDictionary))
                    }
                    print(self.vehicleTypeList)
                    for item in self.vehicleTypeList {
                        self.TypeDropDown.dataSource.append(item.vt_name)
                    }
                    self.TypeDropDown.reloadAllComponents()
                } else {
                    self.vehicleTypeList.removeAll()
                    self.TypeDropDown.dataSource.removeAll()
//                    var dicVehicle:NSDictionary = NSDictionary()
//                    dicVehicle = ["vt_id":"0",
//                                  "vt_name":"Other Type"]
//                    self.vehicleTypeList.append(VehicleType(dic: dicVehicle))
                    for item in self.vehicleTypeList {
                        self.TypeDropDown.dataSource.append(item.vt_name)
                    }
                    self.TypeDropDown.reloadAllComponents()
                    AppDelegate.shared.hideHUD()
                }
                break

            case .failure(let error):

                print(error)
                AppDelegate.shared.hideHUD()
            }
        }
    }

    func getModel() {

        let url = Constants.BASE_URL + API.GET_VEHICLE_MODEL
        let paramModel = ["brand_id":"\(strBrandID)",
                        "type_id":"\(strTypeID)"]
        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]
        AF.request(url, method: .post, parameters: paramModel, encoding:  URLEncoding.default, headers: headers).responseJSON { response in
        print(response)
        AppDelegate.shared.hideHUD()

            switch response.result {
            case .success:

                let JSON = response.value as! NSDictionary
                print(JSON)

                if "\(JSON["code"]!)" == "200" && "\(JSON["status"]!)" == "1" {
                    
                    let jsonData = JSON["data"] as! NSDictionary
                    print(jsonData)
                    
                    self.vehicleModelList.removeAll()
                    self.ModelDropDown.dataSource.removeAll()
                    var dicModel:NSDictionary = NSDictionary()
                    dicModel = ["vm_id":"0",
                                "vm_name":"Other Model",
                                "vm_brand_id":"0",
                                "vm_type_id":"0"]
                    self.vehicleModelList.append(VehicleModel(dic: dicModel))
                    for item in jsonData["vehicle_model_data"] as! NSArray {
                        self.vehicleModelList.append(VehicleModel(dic: item as! NSDictionary))
                    }
                    print(self.vehicleModelList)
                    for item in self.vehicleModelList {
                        self.ModelDropDown.dataSource.append(item.vm_name)
                    }
                    self.ModelDropDown.reloadAllComponents()
                } else {
                    self.vehicleModelList.removeAll()
                    self.ModelDropDown.dataSource.removeAll()
                    var dicModel:NSDictionary = NSDictionary()
                    dicModel = ["vm_id":"0",
                                "vm_name":"Other Model",
                                "vm_brand_id":"0",
                                "vm_type_id":"0"]
                    self.vehicleModelList.append(VehicleModel(dic: dicModel))
                    for item in self.vehicleModelList {
                        self.ModelDropDown.dataSource.append(item.vm_name)
                    }
                    self.ModelDropDown.reloadAllComponents()
                    AppDelegate.shared.hideHUD()
                }
                break
            case .failure(let error):
                print(error)
                AppDelegate.shared.hideHUD()
            }
        }
    }

    func carRegistration() {
        AppDelegate.shared.showHUD()
        let url = Constants.BASE_URL + API.CAR_REGISTRATION
        paramReg = ["vehicle_brand_id":"\(strBrandID)",
                        "vehicle_brand_text":"\(strBrandName)",
                        "vehicle_type_id":"\(strTypeID)",
                        "vehicle_type_text":"\(strTypeName)",
                        "model_id":"\(strModelID)",
                        "model_text":"\(strModelName)",
                        "vehicle_reg_num":"\(self.txtRegNo.text!)",
                        "user_id":"\(UserDefaults.standard.value(forKey: Constants.USER_ID)!)"]

        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]
        AF.request(url, method: .post, parameters: paramReg, encoding:  URLEncoding.default, headers: headers).responseJSON { response in
        print(response)
        AppDelegate.shared.hideHUD()

            switch response.result {
            case .success:

                let JSON = response.value as! NSDictionary
                print(JSON)
                if "\(JSON["code"]!)" == "200" && "\(JSON["status"]!)" == "1" {
                    
                    if AppDelegate.shared.isVehicleEdit == "1" {
                        let alertController = UIAlertController(title: "Vehicle Details".localiz(), message: "Vehicle details edited successfully".localiz(), preferredStyle: .alert)
                        let okAction = UIAlertAction(title: "OK", style: UIAlertAction.Style.default) {
                            UIAlertAction in
                            self.navigationController?.popViewController(animated: true)
                        }
                        alertController.addAction(okAction)
                        self.present(alertController, animated: true, completion: nil)
                    } else {
                        let alertController = UIAlertController(title: "Vehicle Registration".localiz(), message: "Vehicle registered successfully".localiz(), preferredStyle: .alert)
                        let okAction = UIAlertAction(title: "OK", style: UIAlertAction.Style.default) {
                            UIAlertAction in
                            UserDefaults.standard.set("1", forKey: Constants.VEHICLE_EDIT)
                            let vc = HomeTabVC.instantiate(appStoryboard: .Home)
                            AppDelegate.shared.intPaymentTab = 0
                            self.navigationController?.pushViewController(vc, animated: true)
                        }
                        alertController.addAction(okAction)
                        self.present(alertController, animated: true, completion: nil)
                    }
                    

                } else {
                    AppDelegate.shared.hideHUD()
                    if "\(JSON["code"]!)" == "100" {
                        AppDelegate.shared.apiKeyLogout()
                    }
                }
                break
            case .failure(let error):
                print(error)
                AppDelegate.shared.hideHUD()
            }
        }
    }
    
    func editVehicle() {
        AppDelegate.shared.showHUD()
        let url = Constants.BASE_URL + API.EDIT_VEHICLE
        paramReg = [    "vehicle_brand_id":"\(strBrandID)",
                        "vehicle_brand_text":"\(strBrandName)",
                        "vehicle_type_id":"\(strTypeID)",
                        "vehicle_type_text":"\(strTypeName)",
                        "model_id":"\(strModelID)",
                        "model_text":"\(strModelName)",
                        "vehicle_reg_num":"\(self.txtRegNo.text!)",
                        "vehicle_id":AppDelegate.shared.strVehicleID]

        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]
        AF.request(url, method: .post, parameters: paramReg, encoding:  URLEncoding.default, headers: headers).responseJSON { response in
        print(response)
        AppDelegate.shared.hideHUD()

            switch response.result {
            case .success:

                let JSON = response.value as! NSDictionary
                print(JSON)
                if "\(JSON["code"]!)" == "200" && "\(JSON["status"]!)" == "1" {
                    
                    if AppDelegate.shared.isVehicleEdit == "1" {
                        let alertController = UIAlertController(title: "Vehicle Details".localiz(), message: "Vehicle details edited successfully".localiz(), preferredStyle: .alert)
                        let okAction = UIAlertAction(title: "OK", style: UIAlertAction.Style.default) {
                            UIAlertAction in
                            self.navigationController?.popViewController(animated: true)
                        }
                        alertController.addAction(okAction)
                        self.present(alertController, animated: true, completion: nil)
                    } else {
                        let alertController = UIAlertController(title: "Vehicle Registration".localiz(), message: "Vehicle registered successfully".localiz(), preferredStyle: .alert)
                        let okAction = UIAlertAction(title: "OK", style: UIAlertAction.Style.default) {
                            UIAlertAction in
                            UserDefaults.standard.set("1", forKey: Constants.VEHICLE_EDIT)
                            let vc = HomeTabVC.instantiate(appStoryboard: .Home)
                            AppDelegate.shared.intPaymentTab = 0
                            self.navigationController?.pushViewController(vc, animated: true)
                        }
                        alertController.addAction(okAction)
                        self.present(alertController, animated: true, completion: nil)
                    }
                    

                } else {
                    AppDelegate.shared.hideHUD()
                    if "\(JSON["code"]!)" == "100" {
                        AppDelegate.shared.apiKeyLogout()
                    }
                }
                break
            case .failure(let error):
                print(error)
                AppDelegate.shared.hideHUD()
            }
        }
    }
    
    func viewProfile() {
        AppDelegate.shared.showHUD()
        let url = Constants.BASE_URL + API.VIEW_PROFILE
        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]
        AF.request(url, method: .get, parameters: nil, encoding:  URLEncoding.default, headers: headers).responseJSON { response in
            debugPrint(response)
        print(response)
        AppDelegate.shared.hideHUD()
        switch response.result {
        case .success:

            print(response)
            let JSON = response.value as! NSDictionary
            print(JSON)

            if "\(JSON["code"]!)" == "200" && "\(JSON["status"]!)" == "1" {

                let jsonData = JSON["data"] as! NSDictionary
                print(jsonData)

                self.vehicleDetails.removeAll()
                for item in jsonData["vehicle_data"] as! NSArray {
                    self.vehicleDetails.append(VehicleDetails(dic: item as! NSDictionary))
                }
                
                for item in self.vehicleDetails {
                    if item.vehicle_id == AppDelegate.shared.strVehicleID {
                        self.txtBrand.text = item.brand_text
                        self.strBrandID = item.brand_id
                        
                        self.txtType.text = item.type_name
                        self.strTypeID = item.type_id
                        
                        self.txtModel.text = item.model_text
                        self.strModelID = item.model_id
                        
                        self.txtRegNo.text = item.reg_num
                    }
                }
                
                self.getModel()

                
            } else {
                if "\(JSON["code"]!)" == "100" {
                    AppDelegate.shared.apiKeyLogout()
                }
            }
            break
        case .failure(let error):
            print(error)
            AppDelegate.shared.hideHUD()
            }
        }
    }
}
extension VehicleVC:UIGestureRecognizerDelegate {
    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldBeRequiredToFailBy otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        return true
    }
}

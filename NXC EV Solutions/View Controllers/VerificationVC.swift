//
//  VerificationVC.swift
//  NXC EV Solutions
//
//  Created by <PERSON><PERSON><PERSON> on 16/07/21.
//

import UIKit
import Alamofire

class VerificationVC: UIViewController {
    
    // MARK: - IBOutlets    
    @IBOutlet weak var btnBack: UIButton!
    @IBOutlet weak var txtOTP1: UITextField!
    @IBOutlet weak var txtOTP2: UITextField!
    @IBOutlet weak var txtOTP3: UITextField!
    @IBOutlet weak var txtOTP4: UITextField!
    @IBOutlet weak var txtOTP5: UITextField!
    @IBOutlet weak var txtOTP6: UITextField!
    
    @IBOutlet weak var lblTimer: UILabel!
    
    @IBOutlet weak var btnVerify: UIButton!
    @IBOutlet weak var btnResendOTP: UIButton!
    
    var testVerificationCode  = ""
    var strFinalOTP:String = String()
    
    var countdownTimer: Timer!
    var totalTime = 60
    var strNavigate:String = "0"
    var paramOTP:[String:Any] = [:]
    var paramLogin:[String:Any] = [:]
    
    
    // MARK: - View LifeCycle Methods
    override func viewDidLoad() {
        super.viewDidLoad()
        
        txtOTP1.delegate = self
        txtOTP2.delegate = self
        txtOTP3.delegate = self
        txtOTP4.delegate = self
        txtOTP5.delegate = self
        txtOTP6.delegate = self
        
        txtOTP1.addTarget(self, action: #selector(self.textFieldDidChange(textField:)), for: .editingChanged)
        txtOTP2.addTarget(self, action: #selector(self.textFieldDidChange(textField:)), for: .editingChanged)
        txtOTP3.addTarget(self, action: #selector(self.textFieldDidChange(textField:)), for: .editingChanged)
        txtOTP4.addTarget(self, action: #selector(self.textFieldDidChange(textField:)), for: .editingChanged)
        txtOTP5.addTarget(self, action: #selector(self.textFieldDidChange(textField:)), for: .editingChanged)
        txtOTP6.addTarget(self, action: #selector(self.textFieldDidChange(textField:)), for: .editingChanged)
        
        btnResendOTP.isHidden = true
        
//        txtOTP1.becomeFirstResponder()
        
    }
    
    override func viewWillAppear(_ animated: Bool) {
        lblTimer.text = "01:00"
        startTimer()
    }
    
    override func viewDidLayoutSubviews() {
        btnVerify.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
                
        txtOTP1.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        txtOTP2.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        txtOTP3.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        txtOTP4.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        txtOTP5.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        txtOTP6.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        
        btnBack.addBorder(color: Constants.backArrowBorderColor!, width: Int(1))
        btnBack.maskClipCorner(cornerRadius: 10)
    }
    
    //MARK: - Timer Methods
    func startTimer() {
        lblTimer.alpha = 1
        lblTimer.text = "01:00"
        countdownTimer = Timer.scheduledTimer(timeInterval: 1, target: self, selector: #selector(updateTime), userInfo: nil, repeats: true)
    }
    
    @objc func updateTime() {
        lblTimer.text = "\(timeFormatted(totalTime))"
        
        if totalTime != 0 {
            btnResendOTP.isHidden = true
            totalTime -= 1
        } else {
            btnResendOTP.isHidden = false
            lblTimer.alpha = 0
            endTimer()
        }
    }
    
    func endTimer() {
        countdownTimer.invalidate()
    }
    
    func timeFormatted(_ totalSeconds: Int) -> String {
        let seconds: Int = totalSeconds % 60
        let minutes: Int = (totalSeconds / 60) % 60
        //     let hours: Int = totalSeconds / 3600
        return String(format: "%02d:%02d", minutes, seconds)
    }
    
    
    // MARK: - Button Actions
    @IBAction func backAction(_ sender: UIButton) {
        navigationController?.popViewController(animated: true)
    }
    
    @IBAction func resendAction(_ sender: UIButton) {
        login()
    }
    
    @IBAction func verifyAction(_ sender: UIButton) {
//        let vc = UserInfoVC.instantiate(appStoryboard: .Profile)
//        navigationController?.pushViewController(vc, animated: true)
        strFinalOTP = "\(self.txtOTP1.text!)" + "\(self.txtOTP2.text!)" + "\(self.txtOTP3.text!)" + "\(self.txtOTP4.text!)" + "\(self.txtOTP5.text!)" + "\(self.txtOTP6.text!)"
        if strFinalOTP.count != 6 {
            alert(title: "", message: "Please enter valid OTP".localiz())
        } else {
            self.checkOTP()
        }
    }
    
    //MARK: - Webservice Methods
    func checkOTP() {
        
        let uuid = UIDevice.current.identifierForVendor?.uuidString
        let fcmToken = UserDefaults.standard.value(forKey: Constants.FCM_TOKEN)
        AppDelegate.shared.showHUD()
        let url = Constants.BASE_URL + API.CHECK_OTP
        paramOTP = ["mobile_no":"\(AppDelegate.shared.strLoginNo)",
                     "otp":"\(self.strFinalOTP)",
                    "fcm_token":fcmToken ?? "fcm",
                    "imei_no":"uuid"]
        print("paramOTP:-",paramOTP)
        AF.request(url, method: .post, parameters: paramOTP, encoding: URLEncoding.default).responseJSON { response in
            AppDelegate.shared.hideHUD()
            switch response.result {
            case .success:
                
                print(response)
                let JSON = response.value as! NSDictionary
                print(JSON)
                
                if "\(JSON["code"]!)" == "200" && "\(JSON["status"]!)" == "1" {
                    
                    let data = JSON["data"] as! NSDictionary
                    print(data)
                    
                    UserDefaults.standard.set("\(data["email"]!)", forKey: Constants.USER_EMAIL)
                    UserDefaults.standard.set("\(data["phone"]!)", forKey: Constants.PHONE)
                    UserDefaults.standard.set("\(data["user_id"]!)", forKey: Constants.USER_ID)
                    UserDefaults.standard.set("\(data["profile_edit"]!)", forKey: Constants.PROFILE_EDIT)
                    UserDefaults.standard.set("\(data["vehicle_edit"]!)", forKey: Constants.VEHICLE_EDIT)
                    UserDefaults.standard.set("\(data["jwt_token"]!)", forKey: Constants.JWT_TOKEN)
                    UserDefaults.standard.set("1", forKey: Constants.IS_LOGIN)
                    UserDefaults.standard.set("1", forKey: Constants.IS_OTP)
                    
                    if "\(UserDefaults.standard.value(forKey: Constants.PROFILE_EDIT)!)" == "1" {
                        if "\(UserDefaults.standard.value(forKey: Constants.VEHICLE_EDIT)!)" == "1" {
                            let vc = HomeTabVC.instantiate(appStoryboard: .Home)
                            AppDelegate.shared.intPaymentTab = 0
                            self.navigationController?.pushViewController(vc, animated: true)
                        } else {
                            AppDelegate.shared.isFromStartView = true
                            let vc = VehicleVC.instantiate(appStoryboard: .Vehicle)
                            self.navigationController?.pushViewController(vc, animated: true)
                        }
                    } else {
                        AppDelegate.shared.isFromSplash = "1"
                        AppDelegate.shared.isFromOTPView = true
                        let vc = UserInfoVC.instantiate(appStoryboard: .Profile)
                        self.navigationController?.pushViewController(vc, animated: true)
                    }
                } else {
                    self.alert(title: "", message: "\(JSON["msg"]!)")
                }
                break
            case .failure(let error):
                print(error)
                self.alert(title: "", message: "\(error.localizedDescription)")
            }
        }
    }
    
    func login() {
        
        AppDelegate.shared.showHUD()
        let url = Constants.BASE_URL + API.LOGIN
        paramLogin = ["mobile_no":"\(AppDelegate.shared.strLoginNo)"]
        AF.request(url, method: .post, parameters: paramLogin, encoding: URLEncoding.default).responseJSON { response in
            AppDelegate.shared.hideHUD()
            switch response.result {
            case .success:
                
                print(response)
                let JSON = response.value as! NSDictionary
                print(JSON)
                
                if "\(JSON["code"]!)" == "200" && "\(JSON["status"]!)" == "1" {

                    self.endTimer()
                    self.totalTime = 60
                    self.lblTimer.alpha = 1
                    self.btnResendOTP.isHidden = true
                    self.startTimer()
                    
                } else {
                    self.alert(title: "", message: "\(JSON["msg"]!)")
                }
                
                break
            case .failure(let error):
                print(error)
                self.alert(title: "", message: "\(error.localizedDescription)")
            }
        }
    }
}
extension VerificationVC: UITextFieldDelegate {
    
    @objc func textFieldDidChange(textField: UITextField){
        let text = textField.text
        if  text?.count == 1 {
            switch textField{
            case txtOTP1:
                txtOTP2.becomeFirstResponder()
            case txtOTP2:
                txtOTP3.becomeFirstResponder()
            case txtOTP3:
                txtOTP4.becomeFirstResponder()
            case txtOTP4:
                txtOTP5.becomeFirstResponder()
            case txtOTP5:
                txtOTP6.becomeFirstResponder()
            case txtOTP6:
                txtOTP6.resignFirstResponder()
            default:
                break
            }
        }
        if  text?.count == 0 {
            switch textField{
            case txtOTP1:
                txtOTP1.text = ""
                txtOTP2.text = ""
                txtOTP3.text = ""
                txtOTP4.text = ""
                txtOTP5.text = ""
                txtOTP6.text = ""
                txtOTP1.becomeFirstResponder()
            case txtOTP2:
                txtOTP1.becomeFirstResponder()
            case txtOTP3:
                txtOTP2.becomeFirstResponder()
            case txtOTP4:
                txtOTP3.becomeFirstResponder()
            case txtOTP5:
                txtOTP4.becomeFirstResponder()
            case txtOTP6:
                txtOTP5.becomeFirstResponder()
            default:
                break
            }
        } else {
            
        }
    }
        
    func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
        
        let maxLength = 1
        let currentString: NSString = textField.text! as NSString
        let newString: NSString =
        currentString.replacingCharacters(in: range, with: string) as NSString
        return newString.length <= maxLength
    }
    
//    func textFieldDidBeginEditing(_ textField: UITextField) {
//        if textField == txtOTP1 {
//            txtOTP1.text = ""
//            txtOTP2.text = ""
//            txtOTP3.text = ""
//            txtOTP4.text = ""
//            txtOTP5.text = ""
//            txtOTP6.text = ""
//        }
//    }
    
//    func textFieldDidEndEditing(_ textField: UITextField) {
//        if textField == txtUnit {
//            if textField.text!.isEmpty {
//                textField.text = "Enter Unit"
//                textField.textColor = .darkGray
//            }
//        }
//    }
}

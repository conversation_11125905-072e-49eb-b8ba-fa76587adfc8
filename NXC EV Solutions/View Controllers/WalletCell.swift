//
//  WalletCell.swift
//  NXC EV Solutions
//
//  Created by <PERSON><PERSON><PERSON> on 06/08/21.
//

import UIKit

class WalletCell: UITableViewCell {

    // MARK: - IBOutlets
    @IBOutlet weak var viewMain: UIView!
    @IBOutlet weak var imgWallet: UIImageView!
    @IBOutlet weak var lblType: UILabel!
    @IBOutlet weak var lblStatus: UILabel!
    @IBOutlet weak var lblAmount: UILabel!
    @IBOutlet weak var lblDate: UILabel!
   
    @IBOutlet weak var viewTypeBorder: UIView!
    
    // MARK: - View LifeCycle Methods
    override func awakeFromNib() {
        super.awakeFromNib()

//        viewMain.shadowWithCRadius(radius: 12, color: Constants.primaryColor!)
    }

    override func setSelected(_ selected: <PERSON><PERSON>, animated: Bo<PERSON>) {
        super.setSelected(selected, animated: animated)


    }
}

//
//  WalletModel.swift
//  WalletModel
//
//  Created by Developer on 02/09/21.
//

import Foundation

struct Wallet {
    var date:String = String()
    var walletData:[WalletData] = []

    init(dic:NSDictionary) {
        self.date = "\(dic["date"]!)"
        if !"\(dic["data"]!)".isEmpty {
            for item in dic["data"] as! NSArray {
                self.walletData.append(WalletData(dic: item as! NSDictionary))
            }
        }
    }
}

struct WalletData {
    var amount:String = String()
    var discription:String = String()
    var order_id:String = String()
    var status:String = String()
    var time:String = String()
    var transaction_id:String = String()
    var type:String = String()

    init(dic:NSDictionary) {
        self.amount = "\(dic["amount"]!)"
        self.discription = "\(dic["description"]!)"
        self.order_id = "\(dic["order_id"]!)"
        self.status = "\(dic["status"]!)"
        self.time = "\(dic["time"]!)"
        self.transaction_id = "\(dic["transaction_id"]!)"
        self.type = "\(dic["type"]!)"
    }
}

//"date": "2021-09-01",
//                "data": [
//                    {
//                        "type": "1",
//                        "amount": 0,
//                        "discription": "Paid for Charging",
//                        "order_id": "T - 35",
//                        "status": "",
//                        "time": "00:00:00",
//                        "transaction_id": "35"
//                    },


struct PaymentDetails {

    var payment_type:String = String()
    var balance_amount:String = String()
    var start_time:String = String()

    init(dic:NSDictionary) {
        self.payment_type = "\(dic["payment_type"]!)"
        self.balance_amount = "\(dic["balance_amount"]!)"
        self.start_time = "\(dic["start_time"]!)"
    }
}

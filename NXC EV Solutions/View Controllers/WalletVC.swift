//
//  WalletVC.swift
//  NXC EV Solutions
//
//  Created by <PERSON><PERSON><PERSON> on 24/07/21.
//

import UIKit
import Alamofire

class WalletVC: UIViewController {


    // MARK: - IBOutlets

    @IBOutlet weak var lblTitle: UILabel!
    @IBOutlet weak var lblAvailableBalance: UILabel!

    @IBOutlet weak var lblAddMoney: UILabel!
    @IBOutlet weak var lblWalletAmt: UILabel!
    @IBOutlet weak var viewAddMoney: UIView!
    @IBOutlet weak var lblSeeActivity: UILabel!
    @IBOutlet weak var tableList: UITableView!
    @IBOutlet weak var btnMore: UIButton!
    @IBOutlet weak var viewDetails: UIView!
    @IBOutlet weak var lblNoData: UILabel!
    @IBOutlet weak var viewNoData: UIView!

    @IBOutlet weak var menuButton: UIButton!
    @IBOutlet weak var viewBgMenu: UIView!
    @IBOutlet weak var viewMainMenu: UIView!
    @IBOutlet weak var viewProfile: UIView!
//    @IBOutlet weak var imgUser: UIImageView!

    @IBOutlet weak var lblInitial: UILabel!
    @IBOutlet weak var lblUserName: UILabel!
    @IBOutlet weak var lblBalance: UILabel!
    @IBOutlet weak var tableMenu: UITableView!
    @IBOutlet weak var tableHeight: NSLayoutConstraint!

    var menuTitleList:[String] = []
    var menuImagesList:[String] = []
    var selectedIndex = Int()

    var paramWallet:[String:Any] = [:]
    var walletList:[Wallet] = []

    var fromDate:String = String()
    var toDate:String = String()

    var state: State = .noData {
        didSet {
            switch state {
            case .loaded:
                tableList.isHidden = false
                viewNoData.isHidden = true
                lblSeeActivity.alpha = 1
            case .noData:
                tableList.isHidden = true
                viewNoData.isHidden = false
                lblSeeActivity.alpha = 0
            }
        }
    }

    // MARK: - View LifeCycle Methods
    override func viewDidLoad() {
        super.viewDidLoad()

        tableList.delegate = self
        tableList.dataSource = self

        let dateFormatter = DateFormatter()
        dateFormatter.timeZone = .current
        dateFormatter.dateFormat = "yyyy-MM-dd"
        toDate = dateFormatter.string(from: Date())
        print("toDate:-",toDate)

        let calendar = Calendar.current
        let date = calendar.date(byAdding: .day, value: -30, to: Date())
        fromDate = dateFormatter.string(from: date!)
        print("fromDate:-",fromDate)

        tableMenu.delegate = self
        tableMenu.dataSource = self

        viewBgMenu.isHidden = true

        menuTitleList = ["Profile","Change Language","Get Your RFID Card","News",
                         "Buy Charger","Games","Help","Complaint","About Us"]
        menuImagesList = ["ic_profile","ic_change_language","ic_complain","ic_news","ic_buy_charger","ic_game_icon","ic_help","ic_complain","ic_about"]

        tableHeight.constant = CGFloat(menuTitleList.count * 60)
    }

    override func viewWillAppear(_ animated: Bool) {

//        lblAvailableBalance.text = "Available Balance".localiz()
//        lblSeeActivity.text = "See Activity".localiz()
//        lblTitle.text = "Wallet".localiz()
//        lblAddMoney.text = "Add Money".localiz()
//        btnMore.setTitle("View More".localiz(), for: .normal)

        viewBgMenu.isHidden = true
        AppDelegate.shared.selectedIndex = 0
        self.tableMenu.reloadData()
        getWalletBalance()
        getWalletList()
    }

    override func viewDidAppear(_ animated: Bool) {
        AppDelegate.shared.isSwipeBack = 1
        self.navigationController?.interactivePopGestureRecognizer?.isEnabled = false
    }

    override func viewDidLayoutSubviews() {
        viewDetails.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        viewAddMoney.shadowWithCRadius(radius: 12, color: .clear)
        viewAddMoney.addBorder(color: Constants.textBorderColor!, width: Int(1))

        menuButton.shadowWithCRadius(radius: menuButton.frame.height/2, color: Constants.secondaryGrayText!)
        menuButton.maskClipCorner(cornerRadius: Int(menuButton.frame.height/2))

        viewProfile.addBorder(color: Constants.backArrowBorderColor!, width: Int(1))
        viewProfile.maskClipCorner(cornerRadius: 10)

        lblInitial.maskClipCorner(cornerRadius: Int(lblInitial.frame.height/2))

    }

    func openCloseMenu() {
        UIView.animate(withDuration: 2, delay: 0, options: [.curveEaseOut], animations: {
            if self.viewBgMenu.isHidden == true {
                self.viewBgMenu.isHidden = false
            } else {
                self.viewBgMenu.isHidden = true
            }
        }, completion: nil)
    }

    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        let touch = touches.first!
        if(touch.view == viewBgMenu){
            openCloseMenu()
        }
    }

    // MARK: - Button Actions
    @IBAction func moreAction(_ sender: UIButton) {
        let vc = HistoryVC.instantiate(appStoryboard: .Wallet)
        navigationController?.pushViewController(vc, animated: true)
    }

    @IBAction func addMoneyAction(_ sender: UIButton) {
        let vc = AddMoneyVC.instantiate(appStoryboard: .Wallet)
        navigationController?.pushViewController(vc, animated: true)
    }

    @IBAction func menuButtonTapped(_ sender: UIButton) {
        AppDelegate.shared.isMenuOpen = 1
        let notificationShowHideMenu = Notification.Name("notificationShowHideMenu")
        NotificationCenter.default.post(name: notificationShowHideMenu, object: nil)
        openCloseMenu()
    }


    @IBAction func logoutTapped(_ sender: UIButton) {
        viewBgMenu.isHidden = true
        let alertController = UIAlertController(title: "Logout".localiz(), message: "Logout Message".localiz(), preferredStyle: .alert)

        // Create the actions
        let cancelAction = UIAlertAction(title: "NO".localiz(), style: UIAlertAction.Style.cancel) {
            UIAlertAction in
            print("Cancel Pressed")
        }

        let okAction = UIAlertAction(title: "YES".localiz(), style: UIAlertAction.Style.default) {
            UIAlertAction in
            print("OK Pressed")
            AppDelegate.shared.apiKeyLogout()
        }

        // Add the actions
        alertController.addAction(cancelAction)
        alertController.addAction(okAction)

        // Present the controller
        self.present(alertController, animated: true, completion: nil)
    }

    //MARK: - Webservice Methods
    func getWalletList() {
//        AppDelegate.shared.showHUD()
        let url = Constants.BASE_URL + API.GET_WALLET_HISTORY
        paramWallet = ["start_date"     :   fromDate,
                       "last_date"      :   toDate,
                       "db_cr_status"   :   "3"]
        print("paramWallet:-",paramWallet)
        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]
        AF.request(url, method: .post, parameters: paramWallet, encoding:  URLEncoding.default, headers: headers).responseJSON { response in
        print(response)
//        AppDelegate.shared.hideHUD()

            switch response.result {
            case .success:

                let JSON = response.value as! NSDictionary
                print(JSON)

                if "\(JSON["code"]!)" == "200" && "\(JSON["status"]!)" == "1" {

                    let jsonData = JSON["data"] as! NSDictionary

                    self.lblWalletAmt.text = "₹ \(jsonData["wallet_amount"]!)"

                    self.walletList.removeAll()
                    for item in jsonData["wallet_data"] as! NSArray {
                        self.walletList.append(Wallet(dic: item as! NSDictionary))
                    }
                   // print("walletList:-",self.walletList)

                    self.tableList.reloadData()

                    if self.walletList.count > 0 {
                        self.state = .loaded
                    } else {
                        self.state = .noData
                    }

                } else {

                    if "\(JSON["code"]!)" == "100" {
                        AppDelegate.shared.apiKeyLogout()
                    }

                    if self.walletList.count > 0 {
                        self.state = .loaded
                    } else {
                        self.state = .noData
                    }
                    AppDelegate.shared.hideHUD()
                }
                break
            case .failure(let error):
                print(error)
                AppDelegate.shared.hideHUD()
            }
        }
    }

    func getWalletBalance() {

        //AppDelegate.shared.showHUD()
        let url = Constants.BASE_URL + API.WALLET_BALANCE
        let headers: HTTPHeaders = ["Authorization":"\(UserDefaults.standard.value(forKey: Constants.JWT_TOKEN)!)"]
        AF.request(url, method: .get, parameters: nil, encoding:  URLEncoding.default, headers: headers).responseJSON { response in
            print(response)
            AppDelegate.shared.hideHUD()
            switch response.result {
            case .success:

                let JSON = response.value as! NSDictionary
//                print(JSON)

                if "\(JSON["code"]!)" == "200" && "\(JSON["status"]!)" == "1" {

                    let jsonData = JSON["data"] as! NSDictionary
//                    print(jsonData)

                    self.lblInitial.text = "\(jsonData["user_name"]!)".prefix(1).uppercased()
                    self.lblUserName.text = "\(jsonData["user_name"]!)".capitalizingFirstLetter()
                    self.lblBalance.text = "Avail Balance :".localiz() + " ₹\(jsonData["balance"]!)"

                } else {
                    AppDelegate.shared.apiKeyLogout()
                }
                break
            case .failure(let error):
                print(error)
                AppDelegate.shared.hideHUD()
            }
        }
    }
}

extension WalletVC: UITableViewDelegate, UITableViewDataSource {

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        if tableView == tableMenu {
            return 55
        } else {
            return UITableView.automaticDimension
        }
    }

    func numberOfSections(in tableView: UITableView) -> Int {
        if tableView == tableMenu {
            return 1
        } else {
            return self.walletList.count
        }
    }

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        if tableView == tableMenu {
            return menuTitleList.count
        } else {
            return self.walletList[section].walletData.count
        }
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {

        if tableView == tableMenu {
            let cell = tableView.dequeueReusableCell(withIdentifier: "cell", for: indexPath) as! MenuCell
            cell.lblMenu.text = "\(self.menuTitleList[indexPath.row])".localiz()
            cell.imgMenu.image = UIImage(named: "\(self.menuImagesList[indexPath.row])")

            if indexPath.row+1 == selectedIndex {
                cell.lblMenu.textColor = Constants.primaryColor
                cell.imgMenu!.image = cell.imgMenu!.image?.withRenderingMode(.alwaysTemplate)
                cell.imgMenu!.tintColor = Constants.primaryColor
            } else {
                cell.lblMenu.textColor = Constants.menuTextColor
                cell.imgMenu!.image = cell.imgMenu!.image?.withRenderingMode(.alwaysTemplate)
                cell.imgMenu!.tintColor = Constants.menuIconColor
            }

            return cell
        } else {
            let cell = tableView.dequeueReusableCell(withIdentifier: "cell", for: indexPath) as! WalletCell

            let walletInfo = walletList[indexPath.section].walletData

            let valueDate = "\(walletList[indexPath.section].date)" + " \(walletInfo[indexPath.row].time)"
            cell.lblDate.text = valueDate.utcToLocal(dateFromValue: "yyyy-MM-dd HH:mm:ss", dateToValue: "dd MMM yyyy", dateStr: valueDate)

            if "\(walletInfo[indexPath.row].type)" == "1" {
                cell.imgWallet.image = UIImage(named: "ic_car_wallet")
                cell.lblType.text = "Paid to NXC EV"
                cell.lblStatus.text = "Paid for charging"
                cell.lblStatus.isHidden = false
                cell.lblAmount.text = "- ₹ \(walletInfo[indexPath.row].amount)"
                cell.lblAmount.textColor = .systemRed
            } else if "\(walletInfo[indexPath.row].type)" == "2" {
                cell.imgWallet.image = UIImage(named: "ic_money_added")
                cell.lblType.text = "Money added to wallet"
                cell.lblStatus.text = "Added to wallet"
                cell.lblStatus.isHidden = false
                cell.lblAmount.text = "+ ₹ \(walletInfo[indexPath.row].amount)"
                cell.lblAmount.textColor = Constants.creditColor
            } else {
                cell.imgWallet.image = UIImage(named: "ic_money_added")
                cell.lblType.text = "Purchased RFID Card"
                cell.lblStatus.isHidden = true
                cell.lblAmount.text = "- ₹ \(walletInfo[indexPath.row].amount)"
                cell.lblAmount.textColor = .systemRed
            }

            cell.layoutIfNeeded()
            cell.updateConstraints()

            return cell
        }
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {

        if tableView == tableMenu {
            AppDelegate.shared.isMenuOpen = 0
            AppDelegate.shared.selectedIndex = indexPath.row+1
            openCloseMenu()
            self.tableMenu.reloadData()

            if AppDelegate.shared.selectedIndex == 1 {
                let vc = ProfileDetailsVC.instantiate(appStoryboard: .Profile)
                navigationController?.pushViewController(vc, animated: true)
            } else if AppDelegate.shared.selectedIndex == 2 {
                let vc = LanguageVC.instantiate(appStoryboard: .Language)
                navigationController?.pushViewController(vc, animated: true)
            } else if AppDelegate.shared.selectedIndex == 3 {
                AppDelegate.shared.tabIndex = 1
                let vc = IssueNewCardVC.instantiate(appStoryboard: .RFID)
                navigationController?.pushViewController(vc, animated: true)
            } else if AppDelegate.shared.selectedIndex == 4 {
                let vc = NewsVC.instantiate(appStoryboard: .Help)
                navigationController?.pushViewController(vc, animated: true)
            } else if AppDelegate.shared.selectedIndex == 5 {
                let vc = BuyChargerVC.instantiate(appStoryboard: .Help)
                navigationController?.pushViewController(vc, animated: true)
            } else if AppDelegate.shared.selectedIndex == 6 {
                let vc = GameListVC.instantiate(appStoryboard: .Help)
                navigationController?.pushViewController(vc, animated: true)
            } else if AppDelegate.shared.selectedIndex == 7 {
                let vc = HelpVC.instantiate(appStoryboard: .Help)
                navigationController?.pushViewController(vc, animated: true)
            } else if AppDelegate.shared.selectedIndex == 8 {
                let vc = ComplainListVC.instantiate(appStoryboard: .Complain)
                navigationController?.pushViewController(vc, animated: true)
            } else if AppDelegate.shared.selectedIndex == 9 {
                let vc = AboutUsVC.instantiate(appStoryboard: .Help)
                navigationController?.pushViewController(vc, animated: true)
            }

        } else {

            let walletInfo = walletList[indexPath.section].walletData
            AppDelegate.shared.strTransactionID = walletInfo[indexPath.row].transaction_id
            AppDelegate.shared.strTypeID = walletInfo[indexPath.row].type
            if walletInfo[indexPath.row].type == "1" || walletInfo[indexPath.row].type == "3" {
                let vc = TransactionDetailsVC.instantiate(appStoryboard: .Wallet)
                self.navigationController?.pushViewController(vc, animated: true)
            } else {
                let vc = PaymentDetailsVC.instantiate(appStoryboard: .Wallet)
                self.navigationController?.pushViewController(vc, animated: true)
            }
        }
    }
}


/* Class = "UILabel"; text = "RazorPay"; ObjectID = "0rX-fx-K1Y"; */
"0rX-fx-K1Y.text" = "RazorPay";

/* Class = "UILabel"; text = "History"; ObjectID = "0sk-uq-DZO"; */
"0sk-uq-DZO.text" = "History";

/* Class = "UILabel"; text = "Date"; ObjectID = "1AY-cN-XbA"; */
"1AY-cN-XbA.text" = "Date";

/* Class = "UILabel"; text = "No transactions available"; ObjectID = "1aa-JM-4yv"; */
"1aa-JM-4yv.text" = "No transactions available";

/* Class = "UILabel"; text = "Note : Money should be added in multiples of 10"; ObjectID = "2Sf-sd-v6Z"; */
"2Sf-sd-v6Z.text" = "Note : Money should be added in multiples of 10";

/* Class = "UILabel"; text = "₹ 5000"; ObjectID = "2uc-Nm-VMg"; */
"2uc-Nm-VMg.text" = "₹ 5000";

/* Class = "UIButton"; normalTitle = "Button"; ObjectID = "4SB-9W-Q6O"; */
"4SB-9W-Q6O.normalTitle" = "Button";

/* Class = "UIButton"; normalTitle = "View More"; ObjectID = "5Ul-hH-Wdd"; */
"5Ul-hH-Wdd.normalTitle" = "View More Guj";

/* Class = "UIButton"; normalTitle = "Add Complaint"; ObjectID = "6M5-5o-GLL"; */
"6M5-5o-GLL.normalTitle" = "Add Complaint";

/* Class = "UILabel"; text = "Add Money"; ObjectID = "6Pm-W2-Fpp"; */
"6Pm-W2-Fpp.text" = "Add Money";

/* Class = "UILabel"; text = "20 Aug 2021 03:15 PM"; ObjectID = "6Wa-xI-FV9"; */
"6Wa-xI-FV9.text" = "20 Aug 2021 03:15 PM";

/* Class = "UILabel"; text = "Jobin Macwan"; ObjectID = "7Mg-9I-ewK"; */
"7Mg-9I-ewK.text" = "Jobin Macwan";

/* Class = "UILabel"; text = "Profile"; ObjectID = "843-26-Vmj"; */
"843-26-Vmj.text" = "Profile";

/* Class = "UILabel"; text = "Consumption Charges"; ObjectID = "8Vu-sY-QUW"; */
"8Vu-sY-QUW.text" = "Consumption Charges";

/* Class = "UILabel"; text = "To"; ObjectID = "91S-ik-z7Y"; */
"91S-ik-z7Y.text" = "To";

/* Class = "UILabel"; text = "Payment Sent"; ObjectID = "96B-xc-e72"; */
"96B-xc-e72.text" = "Payment Sent";

/* Class = "UILabel"; text = "Transaction ID"; ObjectID = "9ra-gC-bH3"; */
"9ra-gC-bH3.text" = "Transaction ID";

/* Class = "UILabel"; text = "oiuytrertyu"; ObjectID = "ANe-we-04v"; */
"ANe-we-04v.text" = "oiuytrertyu";

/* Class = "UILabel"; text = "Other Charges"; ObjectID = "BBT-Hg-zLB"; */
"BBT-Hg-zLB.text" = "Other Charges";

/* Class = "UILabel"; text = "₹ "; ObjectID = "BcS-d5-eY2"; */
"BcS-d5-eY2.text" = "₹ ";

/* Class = "UILabel"; text = "- ₹ 390"; ObjectID = "BcX-J3-Hyg"; */
"BcX-J3-Hyg.text" = "- ₹ 390";

/* Class = "UILabel"; text = "Label"; ObjectID = "CfY-Kt-Beb"; */
"CfY-Kt-Beb.text" = "Label";

/* Class = "UILabel"; text = "Enter Promo Code"; ObjectID = "D8E-lB-CAP"; */
"D8E-lB-CAP.text" = "Enter Promo Code";

/* Class = "UILabel"; text = "See Activity"; ObjectID = "DsX-ZJ-pcf"; */
"DsX-ZJ-pcf.text" = "See Activity";

/* Class = "UILabel"; text = "Copy"; ObjectID = "DuE-by-Wuc"; */
"DuE-by-Wuc.text" = "Copy";

/* Class = "UILabel"; text = "Fix Charge"; ObjectID = "Efw-gE-clV"; */
"Efw-gE-clV.text" = "Fix Charge";

/* Class = "UILabel"; text = "Date/Time"; ObjectID = "GcB-EE-dRM"; */
"GcB-EE-dRM.text" = "Date/Time";

/* Class = "UILabel"; text = "Label"; ObjectID = "HaS-Cw-PWQ"; */
"HaS-Cw-PWQ.text" = "Label";

/* Class = "UIButton"; normalTitle = "Proceed"; ObjectID = "JUk-LY-Ulx"; */
"JUk-LY-Ulx.normalTitle" = "Proceed";

/* Class = "UILabel"; text = "Total"; ObjectID = "Js8-kZ-jLH"; */
"Js8-kZ-jLH.text" = "Total";

/* Class = "UILabel"; text = "Label"; ObjectID = "KKW-6b-McC"; */
"KKW-6b-McC.text" = "Label";

/* Class = "UILabel"; text = "Terms & Conditions"; ObjectID = "L8q-2n-lSr"; */
"L8q-2n-lSr.text" = "Terms & Conditions";

/* Class = "UILabel"; text = "Payment Sent"; ObjectID = "LJx-nK-fCv"; */
"LJx-nK-fCv.text" = "Payment Sent";

/* Class = "UIButton"; normalTitle = "Paid for charging"; ObjectID = "LuA-R5-jHH"; */
"LuA-R5-jHH.normalTitle" = "Paid for charging";

/* Class = "UILabel"; text = "No transactions available"; ObjectID = "N4X-i2-VTn"; */
"N4X-i2-VTn.text" = "No transactions available";

/* Class = "UILabel"; text = "Select Vehicle"; ObjectID = "NGH-YC-7Ic"; */
"NGH-YC-7Ic.text" = "Select Vehicle";

/* Class = "UILabel"; text = "Total"; ObjectID = "Pt1-QH-f2a"; */
"Pt1-QH-f2a.text" = "Total";

/* Class = "UILabel"; text = "Use code and get ₹30 cashback on your first transaction"; ObjectID = "QBn-6u-fZk"; */
"QBn-6u-fZk.text" = "Use code and get ₹30 cashback on your first transaction";

/* Class = "UILabel"; text = "Grand Total"; ObjectID = "Qmd-bd-4fG"; */
"Qmd-bd-4fG.text" = "Grand Total";

/* Class = "UILabel"; text = "Payment Details"; ObjectID = "QtA-pn-olt"; */
"QtA-pn-olt.text" = "Payment Details";

/* Class = "UILabel"; text = "J"; ObjectID = "Rro-pF-9A0"; */
"Rro-pF-9A0.text" = "J";

/* Class = "UILabel"; text = "Enter Amount"; ObjectID = "S4F-PG-j4G"; */
"S4F-PG-j4G.text" = "Enter Amount";

/* Class = "UIButton"; normalTitle = "Done"; ObjectID = "Shn-Bu-SPU"; */
"Shn-Bu-SPU.normalTitle" = "Done";

/* Class = "UILabel"; text = "Per Unit Price"; ObjectID = "TA6-Zb-tz0"; */
"TA6-Zb-tz0.text" = "Per Unit Price";

/* Class = "UILabel"; text = "Chargeable Unit"; ObjectID = "dXz-G2-JMI"; */
"dXz-G2-JMI.text" = "Chargeable Unit";

/* Class = "UILabel"; text = "Net Total"; ObjectID = "hXz-G6-JMM"; */
"hXz-G6-JMM.text" = "Net Total";

/* Class = "UILabel"; text = "Tax"; ObjectID = "lXz-GA-JMQ"; */
"lXz-GA-JMQ.text" = "Tax";

/* Class = "UILabel"; text = "Terms and Conditions"; ObjectID = "TIj-FE-dlW"; */
"TIj-FE-dlW.text" = "Terms and Conditions";

/* Class = "UILabel"; text = "Label"; ObjectID = "Tqy-JU-ect"; */
"Tqy-JU-ect.text" = "Label";

/* Class = "UILabel"; text = "Time"; ObjectID = "UI8-UP-o2t"; */
"UI8-UP-o2t.text" = "Time";

/* Class = "UILabel"; text = "Filter By"; ObjectID = "UmC-TZ-4sX"; */
"UmC-TZ-4sX.text" = "Filter By";

/* Class = "UILabel"; text = "Paid to EVC Charge"; ObjectID = "VGg-MU-B8a"; */
"VGg-MU-B8a.text" = "Paid to EVC Charge";

/* Class = "UILabel"; text = "Transaction"; ObjectID = "VKK-JB-aTN"; */
"VKK-JB-aTN.text" = "Transaction";

/* Class = "UILabel"; text = "₹ 2500"; ObjectID = "XpU-u1-lQY"; */
"XpU-u1-lQY.text" = "₹ 2500";

/* Class = "UILabel"; text = "Wallet"; ObjectID = "YbI-Fi-fQS"; */
"YbI-Fi-fQS.text" = "Wallet";

/* Class = "UILabel"; text = "Promotions"; ObjectID = "a2r-TO-1Rl"; */
"a2r-TO-1Rl.text" = "Promotions";

/* Class = "UIButton"; normalTitle = "Button"; ObjectID = "aJM-mA-ekR"; */
"aJM-mA-ekR.normalTitle" = "Button";

/* Class = "UILabel"; text = "Total Credited"; ObjectID = "awt-cf-V10"; */
"awt-cf-V10.text" = "Total Credited";

/* Class = "UILabel"; text = "Charger"; ObjectID = "bWQ-Ge-cKX"; */
"bWQ-Ge-cKX.text" = "Charger";

/* Class = "UILabel"; text = "Total Debited"; ObjectID = "be0-Zw-Ho8"; */
"be0-Zw-Ho8.text" = "Total Debited";

/* Class = "UIButton"; normalTitle = "Apply"; ObjectID = "bzo-76-m4N"; */
"bzo-76-m4N.normalTitle" = "Apply";

/* Class = "UILabel"; text = "Payment Mode"; ObjectID = "cLB-pB-Nwc"; */
"cLB-pB-Nwc.text" = "Payment Mode";

/* Class = "UILabel"; text = "15 Aug "; ObjectID = "d1A-lo-xbb"; */
"d1A-lo-xbb.text" = "15 Aug ";

/* Class = "UILabel"; text = "Promo code is not valid"; ObjectID = "dSP-JL-HSl"; */
"dSP-JL-HSl.text" = "Promo code is not valid";

/* Class = "UILabel"; text = "Select Date Of Birth"; ObjectID = "dVM-qa-GDJ"; */
"dVM-qa-GDJ.text" = "Select Date Of Birth";

/* Class = "UILabel"; text = "Success"; ObjectID = "dqU-B1-h98"; */
"dqU-B1-h98.text" = "Success";

/* Class = "UILabel"; text = "Amount"; ObjectID = "dxC-dC-Pdw"; */
"dxC-dC-Pdw.text" = "Amount";

/* Class = "UILabel"; text = "15 Aug "; ObjectID = "e7m-jj-IA9"; */
"e7m-jj-IA9.text" = "15 Aug ";

/* Class = "UILabel"; text = "Used Unit"; ObjectID = "eFc-Tk-HO1"; */
"eFc-Tk-HO1.text" = "Used Unit";

/* Class = "UILabel"; text = "Payment Status"; ObjectID = "eOq-o5-lK1"; */
"eOq-o5-lK1.text" = "Payment Status";

/* Class = "UIButton"; normalTitle = "Proceed"; ObjectID = "fK6-ED-Jqs"; */
"fK6-ED-Jqs.normalTitle" = "Proceed";

/* Class = "UILabel"; text = "Avail Balance : ₹ 25000"; ObjectID = "fTv-rz-izR"; */
"fTv-rz-izR.text" = "Avail Balance : ₹ 25000";

/* Class = "UILabel"; text = "₹ 2500"; ObjectID = "fh4-Ab-Con"; */
"fh4-Ab-Con.text" = "₹ 2500";

/* Class = "UILabel"; text = "#89865763"; ObjectID = "frS-lN-Gjw"; */
"frS-lN-Gjw.text" = "#89865763";

/* Class = "UILabel"; text = "Charger Details"; ObjectID = "gar-Ne-323"; */
"gar-Ne-323.text" = "Charger Details";

/* Class = "UIButton"; normalTitle = "Button"; ObjectID = "guI-EJ-u8p"; */
"guI-EJ-u8p.normalTitle" = "Button";

/* Class = "UILabel"; text = "- ₹ 390"; ObjectID = "lDu-uU-tcV"; */
"lDu-uU-tcV.text" = "- ₹ 390";

/* Class = "UILabel"; text = "Free Unit"; ObjectID = "lQi-Li-jRI"; */
"lQi-Li-jRI.text" = "Free Unit";

/* Class = "UILabel"; text = "Payment Details"; ObjectID = "lWh-Ya-U41"; */
"lWh-Ya-U41.text" = "Payment Details";

/* Class = "UILabel"; text = "Paid to EVC Charge"; ObjectID = "ldx-D9-Lzl"; */
"ldx-D9-Lzl.text" = "Paid to EVC Charge";

/* Class = "UILabel"; text = "Select Date"; ObjectID = "mQm-ZH-QWY"; */
"mQm-ZH-QWY.text" = "Select Date";

/* Class = "UIButton"; normalTitle = "Add Complaint"; ObjectID = "ok3-D0-AJU"; */
"ok3-D0-AJU.normalTitle" = "Add Complaint";

/* Class = "UILabel"; text = "Download Receipt"; ObjectID = "pK2-UC-WZu"; */
"pK2-UC-WZu.text" = "Download Receipt";

/* Class = "UILabel"; text = "Label"; ObjectID = "pyU-0t-vYc"; */
"pyU-0t-vYc.text" = "Label";

/* Class = "UILabel"; text = "Use code and get ₹30 cashback on your first transaction"; ObjectID = "qI9-GD-J7G"; */
"qI9-GD-J7G.text" = "Use code and get ₹30 cashback on your first transaction";

/* Class = "UIButton"; normalTitle = "Added To Wallet"; ObjectID = "qUj-9t-TSB"; */
"qUj-9t-TSB.normalTitle" = "Added To Wallet";

/* Class = "UILabel"; text = "Transaction ID"; ObjectID = "rSx-jk-QiK"; */
"rSx-jk-QiK.text" = "Transaction ID";

/* Class = "UILabel"; text = "Add Money"; ObjectID = "rcX-Fg-Q3L"; */
"rcX-Fg-Q3L.text" = "Add Money";

/* Class = "UILabel"; text = "Get 50% cashback"; ObjectID = "unR-1P-8kK"; */
"unR-1P-8kK.text" = "Get 50% cashback";

/* Class = "UILabel"; text = "Total"; ObjectID = "vYZ-yA-9A7"; */
"vYZ-yA-9A7.text" = "Total";

/* Class = "UILabel"; text = "Free Unit"; ObjectID = "w06-Uz-hen"; */
"w06-Uz-hen.text" = "Free Unit";

/* Class = "UILabel"; text = "Net Total"; ObjectID = "Pt1-QH-f2a"; */
"Pt1-QH-f2a.text" = "Net Total";

/* Class = "UILabel"; text = "Tax"; ObjectID = "mXz-G2-JMI"; */
"mXz-G2-JMI.text" = "Tax";

/* Class = "UILabel"; text = "Tax"; ObjectID = "tax-label-title"; */
"tax-label-title.text" = "Tax";

/* Class = "UILabel"; text = "Total"; ObjectID = "sXz-G6-JMM"; */
"sXz-G6-JMM.text" = "Total";

/* Class = "UILabel"; text = "Station Name"; ObjectID = "w7C-18-zIp"; */
"w7C-18-zIp.text" = "Station Name";

/* Class = "UIButton"; normalTitle = "All"; ObjectID = "xvo-R0-dMk"; */
"xvo-R0-dMk.normalTitle" = "All";

/* Class = "UIButton"; normalTitle = "OK"; ObjectID = "yVg-Ax-fc7"; */
"yVg-Ax-fc7.normalTitle" = "OK";

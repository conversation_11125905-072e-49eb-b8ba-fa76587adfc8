
/* Class = "UILabel"; text = "J"; ObjectID = "07S-FT-ZRW"; */
"07S-FT-ZRW.text" = "J";

/* Class = "UILabel"; text = "Per kWh"; ObjectID = "0a3-N0-uzB"; */
"0a3-N0-uzB.text" = "Per kWh";

/* Class = "UILabel"; text = "Jobin Macwan"; ObjectID = "36F-WG-BsU"; */
"36F-WG-BsU.text" = "Jobin Macwan";

/* Class = "UILabel"; text = "2 Available"; ObjectID = "4HJ-Qn-SQe"; */
"4HJ-Qn-SQe.text" = "2 Available";

/* Class = "UILabel"; text = "We"; ObjectID = "6Ah-i8-M1y"; */
"6Ah-i8-M1y.text" = "We";

/* Class = "UILabel"; text = "Avail Balance : ₹ 25000"; ObjectID = "8BW-UZ-1hH"; */
"8BW-UZ-1hH.text" = "Avail Balance : ₹ 25000";

/* Class = "UIButton"; normalTitle = "Private"; ObjectID = "9QX-tp-wuj"; */
"9QX-tp-wuj.normalTitle" = "Private";

/* Class = "UILabel"; text = "Open"; ObjectID = "9T6-3P-Foa"; */
"9T6-3P-Foa.text" = "Open";

/* Class = "UILabel"; text = "EV Charging Station"; ObjectID = "AWQ-0y-610"; */
"AWQ-0y-610.text" = "EV Charging Station";

/* Class = "UIButton"; normalTitle = "Public"; ObjectID = "BSU-Z8-uDd"; */
"BSU-Z8-uDd.normalTitle" = "Public";

/* Class = "UIButton"; normalTitle = "Reserve Slot"; ObjectID = "CBp-Ti-QgI"; */
"CBp-Ti-QgI.normalTitle" = "Reserve Slot";

/* Class = "UILabel"; text = "8 km . 30 min"; ObjectID = "Cje-Vi-T3X"; */
"Cje-Vi-T3X.text" = "8 km . 30 min";

/* Class = "UILabel"; text = "Wed . 10:00 - 17:00"; ObjectID = "Cw4-zZ-7Cx"; */
"Cw4-zZ-7Cx.text" = "Wed . 10:00 - 17:00";

/* Class = "UIButton"; normalTitle = "Button"; ObjectID = "DGz-hN-FOZ"; */
"DGz-hN-FOZ.normalTitle" = "Button";

/* Class = "UILabel"; text = "4.5 Out Of 5"; ObjectID = "DPZ-3t-9E6"; */
"DPZ-3t-9E6.text" = "4.5 Out Of 5";

/* Class = "UILabel"; text = "Unit"; ObjectID = "FHO-iM-Xag"; */
"FHO-iM-Xag.text" = "Unit";

/* Class = "UILabel"; text = "AC Type 2"; ObjectID = "Fq5-dI-BBm"; */
"Fq5-dI-BBm.text" = "AC Type 2";

/* Class = "UILabel"; text = "Price"; ObjectID = "Fzb-Tw-CU5"; */
"Fzb-Tw-CU5.text" = "Price";

/* Class = "UISearchBar"; placeholder = "Search Charge Station"; ObjectID = "IiN-sd-NDe"; */
"IiN-sd-NDe.placeholder" = "चार्ज स्टेशन खोजें";

/* Class = "UILabel"; text = "55 kWh"; ObjectID = "JMj-PQ-4eo"; */
"JMj-PQ-4eo.text" = "55 kWh";

/* Class = "UIButton"; normalTitle = "Apply"; ObjectID = "JR4-a8-lJj"; */
"JR4-a8-lJj.normalTitle" = "Apply";

/* Class = "UILabel"; text = "Unit"; ObjectID = "KUy-f3-Xtn"; */
"KUy-f3-Xtn.text" = "Unit";

/* Class = "UILabel"; text = "Unit"; ObjectID = "Mel-B0-xQh"; */
"Mel-B0-xQh.text" = "Unit";

/* Class = "UILabel"; text = "Label"; ObjectID = "P6r-bc-NsK"; */
"P6r-bc-NsK.text" = "Label";

/* Class = "UILabel"; text = "& Up"; ObjectID = "Q1z-VQ-hcO"; */
"Q1z-VQ-hcO.text" = "& Up";

/* Class = "UILabel"; text = "& Up"; ObjectID = "QL8-rP-WE2"; */
"QL8-rP-WE2.text" = "& Up";

/* Class = "UILabel"; text = "& Up"; ObjectID = "Qgz-V5-vHM"; */
"Qgz-V5-vHM.text" = "& Up";

/* Class = "UILabel"; text = "Get Directions"; ObjectID = "Ri2-ij-h1G"; */
"Ri2-ij-h1G.text" = "Get Directions";

/* Class = "UILabel"; text = "15 min"; ObjectID = "SYV-a1-5kR"; */
"SYV-a1-5kR.text" = "15 min";

/* Class = "UILabel"; text = "2 Reserved"; ObjectID = "UFq-A4-L9U"; */
"UFq-A4-L9U.text" = "2 Reserved";

/* Class = "UILabel"; text = "By Ratings"; ObjectID = "VYK-fD-jA8"; */
"VYK-fD-jA8.text" = "By Ratings";

/* Class = "UILabel"; text = "Label"; ObjectID = "W9o-Kd-lhv"; */
"W9o-Kd-lhv.text" = "Label";

/* Class = "UILabel"; text = "Charge Station Timings"; ObjectID = "WQE-I7-zQp"; */
"WQE-I7-zQp.text" = "Charge Station Timings";

/* Class = "UILabel"; text = "12:00 AM - 11:59 PM"; ObjectID = "Wki-Lq-QVH"; */
"Wki-Lq-QVH.text" = "12:00 AM - 11:59 PM";

/* Class = "UIButton"; normalTitle = "Button"; ObjectID = "XLu-9i-E7Y"; */
"XLu-9i-E7Y.normalTitle" = "Button";

/* Class = "UIButton"; normalTitle = "Button"; ObjectID = "Y4M-IX-L68"; */
"Y4M-IX-L68.normalTitle" = "Button";

/* Class = "UILabel"; text = "Profile"; ObjectID = "Ype-Iv-eCW"; */
"Ype-Iv-eCW.text" = "Profile";

/* Class = "UILabel"; text = "Only Favourite"; ObjectID = "ZnA-2B-hrE"; */
"ZnA-2B-hrE.text" = "Only Favourite";

/* Class = "UILabel"; text = "Select Reserve Time"; ObjectID = "aUf-j2-BWJ"; */
"aUf-j2-BWJ.text" = "Select Reserve Time";

/* Class = "UIButton"; normalTitle = "Reserve Slot"; ObjectID = "bHG-nO-Uzq"; */
"bHG-nO-Uzq.normalTitle" = "Reserve Slot";

/* Class = "UILabel"; text = "By Connector Type"; ObjectID = "bRt-yY-VhB"; */
"bRt-yY-VhB.text" = "By Connector Type";

/* Class = "UILabel"; text = "CHAdeMO"; ObjectID = "bhx-jm-CwN"; */
"bhx-jm-CwN.text" = "CHAdeMO";

/* Class = "UILabel"; text = "Commando"; ObjectID = "btT-06-Cyy"; */
"btT-06-Cyy.text" = "Commando";

/* Class = "UILabel"; text = "By Charger Type"; ObjectID = "ckT-sQ-Qoh"; */
"ckT-sQ-Qoh.text" = "By Charger Type";

/* Class = "UILabel"; text = "Label"; ObjectID = "co1-gZ-WCE"; */
"co1-gZ-WCE.text" = "Label";

/* Class = "UILabel"; text = "Connector Status"; ObjectID = "cxx-QU-3OM"; */
"cxx-QU-3OM.text" = "Connector Status";

/* Class = "UILabel"; text = "& Up"; ObjectID = "dy2-VQ-N2L"; */
"dy2-VQ-N2L.text" = "& Up";

/* Class = "UILabel"; text = "Plug 1"; ObjectID = "eD9-6i-Kt0"; */
"eD9-6i-Kt0.text" = "Plug 1";

/* Class = "UILabel"; text = "NXC Controls Pvt. Ltd."; ObjectID = "eiS-G6-a9a"; */
"eiS-G6-a9a.text" = "NXC Controls Pvt. Ltd.";

/* Class = "UILabel"; text = "Nikol, Ahmedabad"; ObjectID = "fNT-DM-ReR"; */
"fNT-DM-ReR.text" = "Nikol, Ahmedabad";

/* Class = "UILabel"; text = "Get Directions"; ObjectID = "fVd-fL-pdP"; */
"fVd-fL-pdP.text" = "Get Directions";

/* Class = "UILabel"; text = "Only Available Chargers"; ObjectID = "g0b-ZD-VaN"; */
"g0b-ZD-VaN.text" = "Only Available Chargers";

/* Class = "UILabel"; text = "By Charger Type"; ObjectID = "g5Z-Um-eed"; */
"g5Z-Um-eed.text" = "By Charger Type";

/* Class = "UILabel"; text = "2 UnAvailable"; ObjectID = "hOu-6R-Mou"; */
"hOu-6R-Mou.text" = "2 UnAvailable";

/* Class = "UILabel"; text = "45 min"; ObjectID = "l2G-8i-Jkr"; */
"l2G-8i-Jkr.text" = "45 min";

/* Class = "UILabel"; text = "NXC Controls Pvt. Ltd."; ObjectID = "lHx-tG-f3I"; */
"lHx-tG-f3I.text" = "NXC Controls Pvt. Ltd.";

/* Class = "UIButton"; normalTitle = "Button"; ObjectID = "mOb-pz-lWp"; */
"mOb-pz-lWp.normalTitle" = "Button";

/* Class = "UILabel"; text = "₹ 0.80"; ObjectID = "pGc-Tc-huC"; */
"pGc-Tc-huC.text" = "₹ 0.80";

/* Class = "UILabel"; text = "DC"; ObjectID = "pSf-Ob-Jhw"; */
"pSf-Ob-Jhw.text" = "DC";

/* Class = "UILabel"; text = "NXC Controls. Pvt. Ltd."; ObjectID = "rev-Ms-TC2"; */
"rev-Ms-TC2.text" = "NXC Controls. Pvt. Ltd.";

/* Class = "UILabel"; text = "60 min"; ObjectID = "sC1-r2-102"; */
"sC1-r2-102.text" = "60 min";

/* Class = "UILabel"; text = "2 In Use"; ObjectID = "t8c-B0-8rj"; */
"t8c-B0-8rj.text" = "2 In Use";

/* Class = "UILabel"; text = "30 min"; ObjectID = "t8d-Yi-pll"; */
"t8d-Yi-pll.text" = "30 min";

/* Class = "UILabel"; text = "Filter"; ObjectID = "tLu-Qf-XHO"; */
"tLu-Qf-XHO.text" = "Filter";

/* Class = "UILabel"; text = "Book Charger Slot"; ObjectID = "u5G-Ox-GTA"; */
"u5G-Ox-GTA.text" = "Book Charger Slot";

/* Class = "UIButton"; normalTitle = "Start Charging"; ObjectID = "u7e-Yy-oMA"; */
"u7e-Yy-oMA.normalTitle" = "Start Charging";

/* Class = "UILabel"; text = "~ ₹ 0.40/kWh"; ObjectID = "uLv-cR-zAn"; */
"uLv-cR-zAn.text" = "~ ₹ 0.40/kWh";

/* Class = "UIButton"; normalTitle = "All"; ObjectID = "umz-ID-vmw"; */
"umz-ID-vmw.normalTitle" = "All";

/* Class = "UILabel"; text = "Fast"; ObjectID = "vRn-75-yxY"; */
"vRn-75-yxY.text" = "Fast";

/* Class = "UILabel"; text = "Available"; ObjectID = "yZ9-rB-tOg"; */
"yZ9-rB-tOg.text" = "Available";

/* Class = "UILabel"; text = "Commando"; ObjectID = "zQU-ea-0nL"; */
"zQU-ea-0nL.text" = "Commando";

/* Class = "UILabel"; text = "~ ₹ 0.40/kWh"; ObjectID = "zso-un-qmD"; */
"zso-un-qmD.text" = "~ ₹ 0.40/kWh";

/* Class = "UILabel"; text = "Nikol, Ahmedabad"; ObjectID = "zxw-5t-ylD"; */
"zxw-5t-ylD.text" = "Nikol, Ahmedabad";

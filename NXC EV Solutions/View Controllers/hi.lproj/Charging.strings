
/* Class = "UILabel"; text = "Charge Point"; ObjectID = "082-bf-cB7"; */
"082-bf-cB7.text" = "Charge Point";

/* Class = "UIButton"; normalTitle = "Scan QR Code"; ObjectID = "6je-cS-yf2"; */
"6je-cS-yf2.normalTitle" = "Scan QR Code";

/* Class = "UILabel"; text = "Total Charges"; ObjectID = "Bt7-U3-QbP"; */
"Bt7-U3-QbP.text" = "Total Charges";

/* Class = "UILabel"; text = "Jobin Macwan"; ObjectID = "C70-uh-AjW"; */
"C70-uh-AjW.text" = "Jobin Macwan";

/* Class = "UILabel"; text = "Profile"; ObjectID = "DsH-dA-7uw"; */
"DsH-dA-7uw.text" = "Profile";

/* Class = "UILabel"; text = "Full Charged"; ObjectID = "EoZ-a0-xvq"; */
"EoZ-a0-xvq.text" = "Full Charged";

/* Class = "UILabel"; text = "%"; ObjectID = "Fha-vO-IlX"; */
"Fha-vO-IlX.text" = "%";

/* Class = "UILabel"; text = "Select Vehicle"; ObjectID = "KUR-b0-aEG"; */
"KUR-b0-aEG.text" = "Select Vehicle";

/* Class = "UILabel"; text = "Charging Station Name"; ObjectID = "MWx-1O-LaE"; */
"MWx-1O-LaE.text" = "Charging Station Name";

/* Class = "UILabel"; text = "Charging"; ObjectID = "NzL-uN-OSK"; */
"NzL-uN-OSK.text" = "Charging";

/* Class = "UILabel"; text = "Total Charges"; ObjectID = "PoV-CA-yfT"; */
"PoV-CA-yfT.text" = "Total Charges";

/* Class = "UILabel"; text = "SUCCESSFUL!"; ObjectID = "S3i-jN-Jkz"; */
"S3i-jN-Jkz.text" = "SUCCESSFUL!";

/* Class = "UIButton"; normalTitle = "I have connected"; ObjectID = "W4x-zG-GCy"; */
"W4x-zG-GCy.normalTitle" = "I have connected";

/* Class = "UIButton"; normalTitle = "Cancel"; ObjectID = "WKr-bO-A8d"; */
"WKr-bO-A8d.normalTitle" = "Cancel";

/* Class = "UILabel"; text = "Start Time : 00:15:30"; ObjectID = "YtF-oT-272"; */
"YtF-oT-272.text" = "Start Time : 00:15:30";

/* Class = "UILabel"; text = "Please Connect your vehicle"; ObjectID = "YxW-j1-7Q7"; */
"YxW-j1-7Q7.text" = "Please Connect your vehicle";

/* Class = "UILabel"; text = "How was your experience?"; ObjectID = "Z1s-qz-rVd"; */
"Z1s-qz-rVd.text" = "How was your experience?";

/* Class = "UILabel"; text = "J"; ObjectID = "Z9v-Kq-8l6"; */
"Z9v-Kq-8l6.text" = "J";

/* Class = "UILabel"; text = "Start Time"; ObjectID = "bDR-Yj-Nmb"; */
"bDR-Yj-Nmb.text" = "Start Time";

/* Class = "UILabel"; text = "Unit"; ObjectID = "bxk-xN-JhT"; */
"bxk-xN-JhT.text" = "Unit";

/* Class = "UILabel"; text = "Scan QR"; ObjectID = "d3w-q1-yzJ"; */
"d3w-q1-yzJ.text" = "Scan QR";

/* Class = "UILabel"; text = "Select Charge Type"; ObjectID = "eEg-N1-h2R"; */
"eEg-N1-h2R.text" = "Select Charge Type";

/* Class = "UILabel"; text = "Consumed Units"; ObjectID = "fKI-os-fsZ"; */
"fKI-os-fsZ.text" = "Consumed Units";

/* Class = "UILabel"; text = "Avail Balance : ₹ 25000"; ObjectID = "fWB-Wc-jiT"; */
"fWB-Wc-jiT.text" = "Avail Balance : ₹ 25000";

/* Class = "UILabel"; text = "Time"; ObjectID = "g2B-in-pgg"; */
"g2B-in-pgg.text" = "Time";

/* Class = "UIButton"; normalTitle = "Skip"; ObjectID = "knq-GK-E26"; */
"knq-GK-E26.normalTitle" = "Skip";

/* Class = "UIButton"; normalTitle = "More Info"; ObjectID = "l61-qb-pgg"; */
"l61-qb-pgg.normalTitle" = "More Info";

/* Class = "UILabel"; text = "70 kwh"; ObjectID = "mGJ-OX-H8p"; */
"mGJ-OX-H8p.text" = "70 kwh";

/* Class = "UIButton"; normalTitle = "Start Charging"; ObjectID = "mfZ-Ih-Yhj"; */
"mfZ-Ih-Yhj.normalTitle" = "Start Charging";

/* Class = "UILabel"; text = "Scan the QR Code"; ObjectID = "oZE-4B-COd"; */
"oZE-4B-COd.text" = "Scan the QR Code";

/* Class = "UIButton"; normalTitle = "Cancel"; ObjectID = "rYL-lf-5Ec"; */
"rYL-lf-5Ec.normalTitle" = "Cancel";

/* Class = "UIButton"; normalTitle = "Rate"; ObjectID = "tMK-hJ-AwD"; */
"tMK-hJ-AwD.normalTitle" = "Rate";

/* Class = "UILabel"; text = "Duration"; ObjectID = "vmg-DS-aGw"; */
"vmg-DS-aGw.text" = "Duration";

/* Class = "UILabel"; text = "Your transaction was successful"; ObjectID = "vsg-k0-rrS"; */
"vsg-k0-rrS.text" = "Your transaction was successful";

/* Class = "UIButton"; normalTitle = "Cancel"; ObjectID = "xq2-e4-tJO"; */
"xq2-e4-tJO.normalTitle" = "Cancel";

/* Class = "UILabel"; text = "₹50"; ObjectID = "z1Z-om-Wb8"; */
"z1Z-om-Wb8.text" = "₹50";

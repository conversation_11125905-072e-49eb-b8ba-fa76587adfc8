
/* Class = "UILabel"; text = "InProgress"; ObjectID = "1nR-0l-fvi"; */
"1nR-0l-fvi.text" = "InProgress";

/* Class = "UILabel"; text = "Date"; ObjectID = "2YG-17-wPl"; */
"2YG-17-wPl.text" = "Date";

/* Class = "UIButton"; normalTitle = "Add Complaint"; ObjectID = "2qI-8z-Rw8"; */
"2qI-8z-Rw8.normalTitle" = "Add Complaint";

/* Class = "UILabel"; text = "Filter By"; ObjectID = "308-oF-99A"; */
"308-oF-99A.text" = "Filter By";

/* Class = "UILabel"; text = "Transaction ID"; ObjectID = "3va-cH-kKh"; */
"3va-cH-kKh.text" = "Transaction ID";

/* Class = "UILabel"; text = "20 Aug 2021"; ObjectID = "6Di-L6-EMn"; */
"6Di-L6-EMn.text" = "20 Aug 2021";

/* Class = "UIButton"; normalTitle = "Button"; ObjectID = "6dd-ac-DIg"; */
"6dd-ac-DIg.normalTitle" = "Button";

/* Class = "UIButton"; normalTitle = "Button"; ObjectID = "ADV-89-uVB"; */
"ADV-89-uVB.normalTitle" = "Button";

/* Class = "UILabel"; text = "Many desktop publishing packages and web page editors now use Lorem Ipsum as their default model text, and a search for 'lorem ipsum' will uncover many web sites still in their infancy"; ObjectID = "CbH-xx-e5O"; */
"CbH-xx-e5O.text" = "Many desktop publishing packages and web page editors now use Lorem Ipsum as their default model text, and a search for 'lorem ipsum' will uncover many web sites still in their infancy";

/* Class = "UILabel"; text = "All"; ObjectID = "D57-JW-7ie"; */
"D57-JW-7ie.text" = "All";

/* Class = "UILabel"; text = "Add Complaint"; ObjectID = "Eqr-zE-w4a"; */
"Eqr-zE-w4a.text" = "Add Complaint";

/* Class = "UILabel"; text = "Select Transaction"; ObjectID = "GR4-Ad-bCz"; */
"GR4-Ad-bCz.text" = "Select Transaction";

/* Class = "UILabel"; text = "Status"; ObjectID = "HdU-Hz-I25"; */
"HdU-Hz-I25.text" = "Status";

/* Class = "UILabel"; text = "  Details"; ObjectID = "IB3-b6-4bj"; */
"IB3-b6-4bj.text" = "  Details";

/* Class = "UIButton"; normalTitle = "Submit"; ObjectID = "JZo-Np-SqZ"; */
"JZo-Np-SqZ.normalTitle" = "Submit";

/* Class = "UILabel"; text = "Pending"; ObjectID = "KIr-eW-d1X"; */
"KIr-eW-d1X.text" = "Pending";

/* Class = "UILabel"; text = "Complaint Details"; ObjectID = "ObQ-PK-bdq"; */
"ObQ-PK-bdq.text" = "Complaint Details";

/* Class = "UILabel"; text = "Label"; ObjectID = "SI2-mk-G7H"; */
"SI2-mk-G7H.text" = "Label";

/* Class = "UIButton"; normalTitle = "Button"; ObjectID = "SfP-AO-2OU"; */
"SfP-AO-2OU.normalTitle" = "Button";

/* Class = "UIButton"; normalTitle = "Apply Filter"; ObjectID = "VvD-Tz-U4q"; */
"VvD-Tz-U4q.normalTitle" = "Apply Filter";

/* Class = "UILabel"; text = "Select Complaint Type"; ObjectID = "cMd-lq-sd1"; */
"cMd-lq-sd1.text" = "Select Complaint Type";

/* Class = "UILabel"; text = "Status"; ObjectID = "e7j-kp-Y8j"; */
"e7j-kp-Y8j.text" = "Status";

/* Class = "UILabel"; text = "Resolved"; ObjectID = "ecA-QL-YjI"; */
"ecA-QL-YjI.text" = "Resolved";

/* Class = "UILabel"; text = "Time"; ObjectID = "fnB-xU-g60"; */
"fnB-xU-g60.text" = "Time";

/* Class = "UILabel"; text = "To"; ObjectID = "gEU-oY-n6I"; */
"gEU-oY-n6I.text" = "To";

/* Class = "UILabel"; text = "Select Date"; ObjectID = "hJC-RA-hnx"; */
"hJC-RA-hnx.text" = "Select Date";

/* Class = "UILabel"; text = "No complaints available"; ObjectID = "ief-aG-erZ"; */
"ief-aG-erZ.text" = "No complaints available";

/* Class = "UIButton"; normalTitle = "Button"; ObjectID = "lR8-rB-tUo"; */
"lR8-rB-tUo.normalTitle" = "Button";

/* Class = "UILabel"; text = "Select Date"; ObjectID = "pSZ-F1-2eO"; */
"pSZ-F1-2eO.text" = "Select Date";

/* Class = "UILabel"; text = "In Progress"; ObjectID = "pf3-eG-mS9"; */
"pf3-eG-mS9.text" = "In Progress";

/* Class = "UILabel"; text = "Label"; ObjectID = "q1o-jM-EGK"; */
"q1o-jM-EGK.text" = "Label";

/* Class = "UILabel"; text = "15"; ObjectID = "sLx-11-OBP"; */
"sLx-11-OBP.text" = "15";

/* Class = "UIButton"; normalTitle = "Button"; ObjectID = "tcU-PP-Gg2"; */
"tcU-PP-Gg2.normalTitle" = "Button";

/* Class = "UILabel"; text = "Charger"; ObjectID = "wob-7Z-YWt"; */
"wob-7Z-YWt.text" = "Charger";

/* Class = "UIButton"; normalTitle = "Button"; ObjectID = "yaR-UP-laa"; */
"yaR-UP-laa.normalTitle" = "Button";

/* Class = "UILabel"; text = "No Complain Available"; ObjectID = "yjV-hk-rD8"; */
"yjV-hk-rD8.text" = "No Complain Available";

/* Class = "UIButton"; normalTitle = "OK"; ObjectID = "zWB-Bl-bQP"; */
"zWB-Bl-bQP.normalTitle" = "OK";

/* Class = "UILabel"; text = "08:00 AM"; ObjectID = "zzD-b1-aNx"; */
"zzD-b1-aNx.text" = "08:00 AM";

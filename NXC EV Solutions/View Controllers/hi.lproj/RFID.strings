
/* Class = "UILabel"; text = "Track Order"; ObjectID = "07C-bP-Iy5"; */
"07C-bP-Iy5.text" = "Track Order";

/* Class = "UIButton"; normalTitle = "OK"; ObjectID = "0h5-oS-Msm"; */
"0h5-oS-Msm.normalTitle" = "OK";

/* Class = "UILabel"; text = "Track Order"; ObjectID = "1pX-8I-uRQ"; */
"1pX-8I-uRQ.text" = "Track Order";

/* Class = "UILabel"; text = "RFID Card"; ObjectID = "22D-gS-XvE"; */
"22D-gS-XvE.text" = "RFID Card";

/* Class = "UILabel"; text = "Defective"; ObjectID = "2gt-I6-fa7"; */
"2gt-I6-fa7.text" = "Defective";

/* Class = "UILabel"; text = "<PERSON><PERSON> Macwan"; ObjectID = "2jD-2H-eXg"; */
"2jD-2H-eXg.text" = "Jobin <PERSON>wan";

/* Class = "UILabel"; text = "Delivered"; ObjectID = "5x9-Dv-lKn"; */
"5x9-Dv-lKn.text" = "Delivered";

/* Class = "UILabel"; text = "State"; ObjectID = "7gc-rw-bog"; */
"7gc-rw-bog.text" = "State";

/* Class = "UILabel"; text = "You will receive your RFID Card in 8-10 working days, you will get updates regarding delivery on your registered number."; ObjectID = "82x-bn-63F"; */
"82x-bn-63F.text" = "You will receive your RFID Card in 8-10 working days, you will get updates regarding delivery on your registered number.";

/* Class = "UILabel"; text = "Full Name"; ObjectID = "BjZ-AU-T3u"; */
"BjZ-AU-T3u.text" = "Full Name";

/* Class = "UIButton"; normalTitle = "Replace Now"; ObjectID = "DPc-Fj-us9"; */
"DPc-Fj-us9.normalTitle" = "Replace Now";

/* Class = "UILabel"; text = "Tracking Status"; ObjectID = "Dw8-Xs-FKF"; */
"Dw8-Xs-FKF.text" = "Tracking Status";

/* Class = "UILabel"; text = "Pincode"; ObjectID = "FQw-8X-MVE"; */
"FQw-8X-MVE.text" = "Pincode";

/* Class = "UILabel"; text = "Area Street"; ObjectID = "Fcj-DH-9Z1"; */
"Fcj-DH-9Z1.text" = "Area Street";

/* Class = "UILabel"; text = "Does not work properly"; ObjectID = "JPW-yJ-y6r"; */
"JPW-yJ-y6r.text" = "Does not work properly";

/* Class = "UILabel"; text = "Label"; ObjectID = "JqY-bu-LIw"; */
"JqY-bu-LIw.text" = "Label";

/* Class = "UIButton"; normalTitle = "No"; ObjectID = "KYc-0b-XjB"; */
"KYc-0b-XjB.normalTitle" = "No";

/* Class = "UILabel"; text = "Approved"; ObjectID = "LHj-s8-Gsw"; */
"LHj-s8-Gsw.text" = "Approved";

/* Class = "UILabel"; text = "Replace"; ObjectID = "MMn-wk-Qb8"; */
"MMn-wk-Qb8.text" = "Replace";

/* Class = "UILabel"; text = "Landmark"; ObjectID = "NKt-Cx-Rvf"; */
"NKt-Cx-Rvf.text" = "Landmark";

/* Class = "UILabel"; text = "New Card"; ObjectID = "OqZ-dh-IZo"; */
"OqZ-dh-IZo.text" = "New Card";

/* Class = "UIButton"; normalTitle = "Order RFID"; ObjectID = "Pbi-ki-u2P"; */
"Pbi-ki-u2P.normalTitle" = "Order RFID";

/* Class = "UILabel"; text = "Are you sure you want to block the card?"; ObjectID = "QNb-Pl-cjo"; */
"QNb-Pl-cjo.text" = "Are you sure you want to block the card?";

/* Class = "UIButton"; normalTitle = "Yes"; ObjectID = "Ras-ol-fKH"; */
"Ras-ol-fKH.normalTitle" = "Yes";

/* Class = "UILabel"; text = "Please verify your address details. $50 will be charged from your wallet. "; ObjectID = "S9C-3J-UxS"; */
"S9C-3J-UxS.text" = "Please verify your address details. $50 will be charged from your wallet. ";

/* Class = "UILabel"; text = "Replacement Charges : ₹250"; ObjectID = "WGS-Xh-d2V"; */
"WGS-Xh-d2V.text" = "Replacement Charges : ₹250";

/* Class = "UILabel"; text = "Replace RFID Card"; ObjectID = "YdC-yj-eQs"; */
"YdC-yj-eQs.text" = "Replace RFID Card";

/* Class = "UILabel"; text = "Paid to EVC Charge"; ObjectID = "b2C-WX-sO0"; */
"b2C-WX-sO0.text" = "Paid to EVC Charge";

/* Class = "UILabel"; text = "Flat, House No. , Building"; ObjectID = "b6f-8b-ZBs"; */
"b6f-8b-ZBs.text" = "Flat, House No. , Building";

/* Class = "UIButton"; normalTitle = "Order Now"; ObjectID = "bIx-Mm-evw"; */
"bIx-Mm-evw.normalTitle" = "Order Now";

/* Class = "UILabel"; text = "20 Sep 2021, 10:20 AM"; ObjectID = "eUZ-Ww-Vcp"; */
"eUZ-Ww-Vcp.text" = "20 Sep 2021, 10:20 AM";

/* Class = "UILabel"; text = "Order RFID"; ObjectID = "fX8-ri-gBr"; */
"fX8-ri-gBr.text" = "Order RFID";

/* Class = "UILabel"; text = "Track"; ObjectID = "fbp-C5-bnn"; */
"fbp-C5-bnn.text" = "Track";

/* Class = "UILabel"; text = "RFID Card Charges : ₹250"; ObjectID = "gNF-Gy-3h4"; */
"gNF-Gy-3h4.text" = "RFID Card Charges : ₹250";

/* Class = "UILabel"; text = "Phone Number"; ObjectID = "gd0-PD-HH2"; */
"gd0-PD-HH2.text" = "Phone Number";

/* Class = "UILabel"; text = "Track Card"; ObjectID = "iW1-Ow-8U7"; */
"iW1-Ow-8U7.text" = "Track Card";

/* Class = "UILabel"; text = "Label"; ObjectID = "jIb-9B-AwR"; */
"jIb-9B-AwR.text" = "Label";

/* Class = "UILabel"; text = "Town/City"; ObjectID = "jLo-cf-YMF"; */
"jLo-cf-YMF.text" = "Town/City";

/* Class = "UILabel"; text = "Block"; ObjectID = "jMp-Hx-L4V"; */
"jMp-Hx-L4V.text" = "Block";

/* Class = "UILabel"; text = "Other Reason"; ObjectID = "jX2-vn-f4K"; */
"jX2-vn-f4K.text" = "Other Reason";

/* Class = "UILabel"; text = "RFID Card"; ObjectID = "jx0-yL-SQQ"; */
"jx0-yL-SQQ.text" = "RFID Card";

/* Class = "UILabel"; text = "No Records Found"; ObjectID = "kye-E8-0zd"; */
"kye-E8-0zd.text" = "No Records Found";

/* Class = "UILabel"; text = "Your RFID Order"; ObjectID = "nMq-La-el7"; */
"nMq-La-el7.text" = "Your RFID Order";

/* Class = "UILabel"; text = "RFID Card Order"; ObjectID = "tKN-rc-zXN"; */
"tKN-rc-zXN.text" = "RFID Card Order";

/* Class = "UILabel"; text = "Issue New Card"; ObjectID = "yvx-xK-nH9"; */
"yvx-xK-nH9.text" = "Issue New Card";

/* Class = "UILabel"; text = "Block Card"; ObjectID = "zWL-Rx-jaz"; */
"zWL-Rx-jaz.text" = "Block Card";

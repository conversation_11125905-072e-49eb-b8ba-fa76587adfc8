/*
  Localizable.strings
  NXC EV Solutions

  Created by Developer on 01/11/21.

*/

// MARK: - Menu
"Profile" = "પ્રોફાઇલ";
"Change Language" = "ભાષા બદલો";
"Get Your RFID Card" = "તમારું RFID કાર્ડ મેળવો";
"News" = "સમાચાર";
"Games" = "ગેમ્સ";
"Buy Charger" = "ચાર્જર ખરીદો";
"Help" = "મદદ";
"Complaint" = "ફરિયાદ";
"About Us" = "અમારા વિશે";
"Avail Balance :" = "ઉપલબ્ધ બેલેન્સ :";

// MARK: - Filter
"Filter" = "ફિલ્ટર";
"By Charger Type" = "ચાર્જર પ્રકાર દ્વારા";
"By Connector Type" = "કનેક્ટર પ્રકાર દ્વારા";
"By Ratings" = "રેટિંગ્સ દ્વારા";
"Only Favourite" = "માત્ર મનપસંદ";
"Only Available Chargers" = "માત્ર ઉપલબ્ધ ચાર્જર્સ";
"& Up" = "અને ઉપર";
"Public" = "જાહેર";
"Private" = "ખાનગી";


// MARK: - Wallet
"Wallet" = "વૉલેટ";
"Available Balance" = "ઉપલબ્ધ બેલેન્સ";
"Add Money" = "પૈસા ઉમેરો";
"See Activity" = "વ્યવહાર જુઓ";
"View More" = "વધુ જોવો";

"Search Charge Station" = "ચાર્જ સ્ટેશન શોધો";

// MARK: - History
"History" = "ઇતિહાસ";
"Filter By" = "ફિલ્ટર";
"Select Vehicle" = "વાહન પસંદ કરો";
"Select Date" = "તારીખ પસંદ કરો";
"Proceed" = "આગળ વધો";

"All" = "બધા";
"Paid For Charging" = "ચાર્જિંગ માટે ચૂકવણી";
"Added To Wallet" = "વૉલેટમાં ઉમેર્યું";
"Total Debited" = "કુલ ડેબિટ";
"Total Credited" = "કુલ ક્રેડિટ";
"No transactions available" = "No transactions available";


// MARK: - Add Money
"Enter Amount" = "રકમ દાખલ કરો";
"Enter Promo Code" = "પ્રોમો કોડ દાખલ કરો";
"Apply" = "અરજી";
"Note : Money should be added in multiples of 10" = "નોંધ: પૈસા 10 ના ગુણાંકમાં ઉમેરવા જોઈએ";
"Please add amount in multiples of 10" = "કૃપા કરીને 10 ના ગુણાંકમાં રકમ ઉમેરો";
"Promotions" = "પ્રમોશન";
"Terms & Conditions" = "શરતો અને નિયમો";
"Terms and Conditions" = "શરતો અને નિયમો";
"Done" = "Done";
"Please enter amount to continue with the transaction" = "વ્યવહાર ચાલુ રાખવા માટે કૃપા કરીને રકમ દાખલ કરો";


// MARK: - Complaint
"Complaint Details" = "ફરિયાદ વિગતો";
"Add Complaint" = "ફરિયાદ ઉમેરો";
"Transaction ID" = "ટ્રાન્ઝેક્શન આઈડી";
"Status" = "સ્થિતિ";
"Date" = "તારીખ";
"Time" = "સમય";
"  Details" = "  વિગતો";
"Charging" = "ચાર્જિંગ";
"Charger" = "ચાર્જર";
"Pending" = "બાકી";
"InProgress" = "પ્રગતિમાં";
"Resolved" = "ઉકેલાઈ";
"Apply Filter" = "ફિલ્ટર લાગુ કરો";
"No complaints available" = "કોઈ ફરિયાદ ઉપલબ્ધ નથી";


// MARK: - Add Complaint
"Select Complaint Type" = "ફરિયાદનો પ્રકાર પસંદ કરો";
"Select Transaction" = "ટ્રાન્ઝેક્શન પસંદ કરો";
"Enter Description" = "વર્ણન દાખલ કરો";
"Submit" = "સબમિટ કરો";

"Complaint Type" = "ફરિયાદનો પ્રકાર";
"Please select complaint type" = "કૃપા કરીને ફરિયાદનો પ્રકાર પસંદ કરો";
"Transaction" = "ટ્રાન્ઝેક્શન";
"Please select transaction" = "કૃપા કરીને વ્યવહાર પસંદ કરો";
"Description" = "વર્ણન";
"Please enter description" = "કૃપા કરીને વર્ણન દાખલ કરો";


// MARK: - RFID Card
"RFID Card" = "RFID કાર્ડ";
"Issue New Card" = "નવું કાર્ડ ઇસ્યુ કરો";
"Track Order" = "ટ્રેક ઓર્ડર";


// MARK: - Order RFID
"Order RFID" = "ઓર્ડર RFID";
"Full Name" = "પૂરું નામ";
"Phone Number" = "ફોન નંબર";
"Flat, House No. , Building" = "ફ્લેટ, મકાન નંબર, બિલ્ડીંગ";
"Area Street" = "વિસ્તાર સ્ટ્રીટ";
"Landmark" = "લેન્ડમાર્ક";
"Pincode" = "પીન કોડ";
"State" = "રાજ્ય";
"Town/City" = "શહેર";
"RFID Card Charges : " = "RFID કાર્ડ શુલ્ક:";

"Order RFID Card" = "RFID કાર્ડ ઓર્ડર કરો";
"Please verify your address details." = "કૃપા કરીને તમારા સરનામાંની વિગતો ચકાસો.";
"will be charged from your wallet" = "તમારા વોલેટમાંથી શુલ્ક લેવામાં આવશે";
"Order Now" = "ઓર્ડર કરો";

"You will receive your RFID Card in 8-10 working days, you will get updates regarding delivery on your registered number." = "તમને તમારું RFID કાર્ડ 8-10 કાર્યકારી દિવસોમાં પ્રાપ્ત થશે, તમને તમારા નોંધાયેલા નંબર પર ડિલિવરી સંબંધિત અપડેટ્સ મળશે.";

"House No." = "ઘર નં.";
"Please enter flat/house no, buiding" = "મહેરબાની કરીને ફ્લેટ/મકાન નંબર, મકાન દાખલ કરો";
"Area, Street" = "વિસ્તાર, શેરી";
"Please enter area, street" = "કૃપા કરીને વિસ્તાર, શેરી દાખલ કરો";
"Please enter landmark" = "કૃપા કરીને લેન્ડમાર્ક દાખલ કરો";
"Please enter pincode" = "કૃપા કરીને પિનકોડ દાખલ કરો";
"Please enter state" = "કૃપા કરીને રાજ્ય દાખલ કરો";
"Please enter town/city" = "કૃપા કરીને શહેર દાખલ કરો";
"Insufficient balance in you wallet." = "તમારા વૉલેટમાં અપૂરતું બેલેન્સ.";
"Please top-up your wallet." = "કૃપા કરીને તમારું વૉલેટ ટોપ-અપ કરો.";
"Insufficient Balance" = "અપર્યાપ્ત બેલેન્સ";
"Top-up Now" = "ટોપ-અપ કરો";
"Please verify your address details." = "કૃપા કરીને તમારા સરનામાંની વિગતો ચકાસો.";
" will be charged from your wallet." = " તમારા વોલેટમાંથી શુલ્ક લેવામાં આવશે.";
"Order Now" = "ઓર્ડર કરો";

"Your RFID Order" = "તમારો RFID ઓર્ડર";
"No Records Found" = "કોઈ રેકોર્ડ મળ્યા નથી";


// MARK: - Profile
"User Profile" = "વપરાશકર્તા પ્રોફાઇલ";
"Personal Details" = "અંગત વિગતો";
"Car Profile" = "કાર પ્રોફાઇલ";
"Delete Account" = "એકાઉન્ટ કાઢી નાખો";
"Are you sure you want to delete your account?" = "શું તમે ખરેખર તમારું એકાઉન્ટ કાઢી નાખવા માંગો છો?";
"You will lose all your data and your account will be permanently deleted." = "તમે તમારો બધો ડેટા ગુમાવશો અને તમારું એકાઉન્ટ કાયમ માટે કાઢી નાખવામાં આવશે.";
"DELETE" = "કાઢી નાખો";
"Refund Your Money" = "તમારા પૈસા મેળવો";
"Please enter bank details to enter money in your wallet" = "તમારા વૉલેટમાં પૈસા દાખલ કરવા માટે કૃપા કરીને બેંક વિગતો દાખલ કરો";
"Your account will be permanently deleted" = "તમારું એકાઉન્ટ કાયમ માટે કાઢી નાખવામાં આવશે";


// MARK: - Charging
"Start Charging" = "ચાર્જ કરવાનું શરૂ કરો";
"Stop Charging" = "ચાર્જ કરવાનું બંધ કરો";
"Please connect your vehicle" = "કૃપા કરીને તમારું વાહન કનેક્ટ કરો";
"I have connected" = "મેં કનેક્ટ કર્યું છે";
"Cancel" = "રદ કરો";
"Start Time : " = "પ્રારંભ સમય:";
"Consumed Units" = "વપરાશ કરેલ એકમો";
"Total Charges" = "કુલ શુલ્ક";

// MARK: - Transaction Details
"Tax" = "કર";

"SUCCESSFUL" = "સફળ";
"Your transaction was successful" = "તમારો વ્યવહાર સફળ રહ્યો";
"Start Time" = "પ્રારંભ સમય";
"Duration" = "અવધિ";
"Charge Point" = "ચાર્જ પોઈન્ટ";
"Total Charges" = "કુલ શુલ્ક";
"More Info" = "વધુ માહિતી";

"How was your experience?" = "તમારો અનુભવ કેવો હતો?";
"Skip" = "છોડો";
"Rate" = "રેટિંગ્સ";

"Get Directions" = "દિશા - નિર્દેશો મેળવો";
"EV Charging Station" = "EV ચાર્જિંગ સ્ટેશન";
"Charge Station Timings" = "ચાર્જિંગ સ્ટેશનનો સમય";


// MARK: - Logout
"Logout" = "લૉગ આઉટ";
"Logout Message" = "શું તમે ખરેખર લોગઆઉટ કરવા માંગો છો?";
"YES" = "હા";
"NO" = "ના";

// MARK: - Login
"Login" = "લૉગિન";
"By logging in, you agree to our Terms and Conditions" = "લૉગ ઇન કરીને, તમે અમારા નિયમો અને શરતો સાથે સંમત થાઓ છો";
"A NXC Controls Pvt. Ltd. Product" = "A NXC Controls Pvt. Ltd. Product";
"Please enter phone number" = "કૃપા કરીને ફોન નંબર દાખલ કરો";
"Please enter valid phone number" = "કૃપા કરીને માન્ય ફોન નંબર દાખલ કરો";


// MARK: - Phone Verification
"Phone Verification" = "ફોન ચકાસણી";
"Enter your 6-digit OTP code" = "તમારો 6-અંકનો OTP કોડ દાખલ કરો";
"VERIFY" = "ચકાસો";
"Didn't receive the code? Resend Code" = "કોડ પ્રાપ્ત થયો નથી? કોડ ફરીથી મોકલો";




//"Vehicle Regsitration" = "Vehicle Regsitration";
//"Complain" = "Complain";
//"Logout" = "Logout";
//
//"Logout Message" = "Are you sure you want to logout?";
//"YES" = "YES";
//"NO" = "NO";
//
//"Map" = "Map";
//"Charging" = "Charging";
//"Nearby" = "Nearby";
//"Wallet" = "Wallet";
//"Add Money" = "Add Money";
//
//"Login" = "Login";
//"Share" = "Share";
//"By Signing, you agree to our Terms and Conditions" = "By Signing, you agree to our Terms and Conditions";
//
//"Ratings" = "Ratings";
//"START CHARGING" = "START CHARGING";
//"STOP CHARGING" = "STOP CHARGING";
//
//"Enter Amount" = "Enter Amount";
//"Please enter amount to continue with the transaction" = "Please enter amount to continue with the transaction";
//"Payment Mode" = "Payment Mode";
//"Please add amount in multiples of 10" = "Please add amount in multiples of 10";
//"Please select payment mode to continue with the transaction" = "Please select payment mode to continue with the transaction";
//"Proceed To Pay" = "Proceed To Pay";
//
//
//"Wallet history not available" = "Wallet history not available";
//
//
//"Search Charge Station" = "ચાર્જ સ્ટેશન શોધો";
//
//"Please wait" = "Please wait";
//
//"Select Vehicle" = "Select Vehicle";
//
//"We can improve the accuracy of your sharing data if we know which vehicle you drive" = "We can improve the accuracy of your sharing data if we know which vehicle you drive";
//
//
//"Brand" = "Brand";
//"Select brand" = "Select Brand";
//"Please select brand" = "Please select brand";
//"Please enter other brand" = "Please enter other brand";
//
//"Type" = "Type";
//"Select type" = "Select type";
//"Please select type" = "Please select type";
//"Please enter other type" = "Please enter other type";
//
//"Model" = "Model";
//"Select model" = "Select model";
//"Please select model" = "Please select model";
//"Please enter other model" = "Please enter other model";
//
//"Enter registration number" = "Enter registration number";
//"Registration number" = "Registration number";
//"Please add registration number" = "Please add registration number";
//
//
//"Please select vehicle to start charging" = "Please select vehicle to start charging";
//
//
//"Please enter phone number" = "Please enter phone number";
//"Please enter valid phone number" = "Please enter valid phone number";
//
//
//"Please enter correct OTP" = "Please enter correct OTP";
//"Please enter valid OTP" = "Please enter valid OTP";
//
//
//"Not Available" = "Not Available";
//"This charger is not available for reservation" = "This charger is not available for reservation";
//
//"Access denied" = "Access denied पहुंच अस्वीकृत";
//
//"Please select gender to proceed" = "Please select gender to proceed";
//
//"First name" = "First name";
//"Please enter firstname" = "Please enter first name";
//
//"Last name" = "Last name";
//"Please enter last name" = "Please enter last name";
//
//"Date of birth" = "Date of birth";
//"Please select date of birth" = "Please select date of birth";
//
//"Gender" = "Gender";
//"Please select gender" = "Please select gender";
//
//"Please enter valid email address" = "Please enter valid email address";
//
//"Address" = "Address";
//"Please enter address" = "Please enter address";
//
//"Pincode" = "Pincode";
//"Please enter pincode" = "Please enter pincode";
//
//"Country" = "Country";
//"Please select country" = "Please select country";
//
//"State" = "State";
//"Please select state" = "Please select state";
//
//"City" = "City";
//"Please select city" = "Please select city";
//
//"Please enter only alphabetical letters" = "Please enter only alphabetical letters";
//
//"Name" = "Name";
//
//"Type" = "Type";
//"Please select complain type" = "Please select complain type";
//"Transaction" = "Transaction";
//"Please select transaction" = "Please select transaction";
//
//
//"Please enter new password" = "Please enter new password";
//"Please select complain type" = "Please select complain type";
//"Transaction" = "Transaction";
//"Please select transaction" = "Please select transaction";
//"Type" = "Type";
//"Please select complain type" = "Please select complain type";
//"Transaction" = "Transaction";
//"Please select transaction" = "Please select transaction";
//"Type" = "Type";
//"Please select complain type" = "Please select complain type";
//"Transaction" = "Transaction";
//"Please select transaction" = "Please select transaction";
//"Type" = "Type";
//"Please select complain type" = "Please select complain type";
//"Transaction" = "Transaction";
//"Please select transaction" = "Please select transaction";
//
//"Vehicle registration" = "Vehicle registration";
//"Vehicle registered successfully" = "Vehicle registered successfully";
//
//"You will get SMS with a confirmation OTP on this number" = "You will get SMS with a confirmation OTP on this number";
//"Search" = "Search";
//
//"Profile added" = "Profile added";
//"Your profile added successfully" = "Your profile added successfully";
//"Profile edited" = "Profile edited";
//"Your profile edited successfully" = "Your profile edited successfully";
//
//"Energy Charges Calculator" = "Energy Charges Calculator";
//"ENERGY CHARGES CALCULATOR" = "ENERGY CHARGES CALCULATOR";
//
//"Select complain type" = "Select complain type";
//"Select transaction" = "Select transaction";
//"Enter description" = "Enter description";
//
//"Resolved" = "Resolved";
//"All" = "All";
//
//"Time & Charges" = "Time & Charges";
//
//"Could not connect to the server" = "Could not connect to the server";
//"Please check your internet connection" = "Please check your internet connection";
//
//
//"First Name"="First Name";
//"Last Name"="Last Name";
//"Date Of Birth"="Date Of Birth";
//"Gender"="Gender";
//"Address"="Address";
//"Pincode"="Pincode";
//"Country"="Country";
//"State"="State";
//"City"="City";
//
//
//

/*
  Localizable.strings
  NXC EV Solutions

  Created by Developer on 01/11/21.

*/

// MARK: - Menu
"Profile" = "प्रोफ़ाइल";
"Change Language" = "भाषा बदलें";
"Get Your RFID Card" = "अपना RFID कार्ड प्राप्त करें";
"News" = "समाचार";
"Games" = "गेम्स";
"Buy Charger" = "चार्जर खरीदें";
"Help" = "मदद";
"Complaint" = "शिकायत";
"About Us" = "हमारे बारे में";
"Avail Balance :" = "उपलब्ध राशि :";

// MARK: - Filter
"Filter" = "फिल्टर";
"By Charger Type" = "चार्जर प्रकार द्वारा";
"By Connector Type" = "कनेक्टर प्रकार द्वारा";
"By Ratings" = "रेटिंग के अनुसार";
"Only Favourite" = "केवल पसंदीदा";
"Only Available Chargers" = "केवल उपलब्ध चार्जर";
"& Up" = "& ऊपर";
"Public" = "सार्वजनिक";
"Private" = "निजी";

// MARK: - Wallet
"Wallet" = "बटुआ";
"Available Balance" = "उपलब्ध राशि";
"Add Money" = "पैसे जोड़ें";
"See Activity" = "लेन-देन देखें";
"View More" = "और देखो";

"Search Charge Station" = "चार्ज स्टेशन खोजें";


// MARK: - History
"History" = "इतिहास";
"Filter By" = "फिल्टर";
"Select Vehicle" = "वाहन का चयन करें";
"Select Date" = "तारीख़ चुनें";
"Proceed" = "आगे बढ़ो";

"All" = "सभी";
"Paid For Charging" = "चार्ज करने के लिए भुगतान";
"Added To Wallet" = "वॉलेट में जोड़ा गया";
"Total Debited" = "कुल डेबिट";
"Total Credited" = "कुल क्रेडिट";
"No transactions available" = "No transactions available";


// MARK: - Add Money
"Enter Amount" = "राशी डालें";
"Enter Promo Code" = "प्रोमो कोड दर्ज करें";
"Apply" = "लागू";
"Note : Money should be added in multiples of 10" = "नोट : पैसा 10 . के गुणकों में जोड़ा जाना चाहिए";
"Please add amount in multiples of 10" = "कृपया राशि को 10 . के गुणकों में जोड़ें";
"Promotions" = "पदोन्नति";
"Terms & Conditions" = "नियम एवं शर्तें";
"Terms and Conditions" = "नियम एवं शर्तें";
"Done" = "Done";
"Please enter amount to continue with the transaction" = "लेन-देन जारी रखने के लिए कृपया राशि दर्ज करें";


// MARK: - Complaint
"Complaint Details" = "शिकायत विवरण";
"Add Complaint" = "शिकायत जोड़ें";
"Transaction ID" = "लेन-देन आईडी";
"Status" = "स्थिति";
"Date" = "दिनांक";
"Time" = "समय";
"  Details" = "  विवरण";
"Charging" = "चार्जिंग";
"Charger" = "चार्जर";
"Pending" = "लंबित";
"InProgress" = "चालू";
"Resolved" = "हल किया";
"Apply Filter" = "फिल्टर लागू करें";
"No complaints available" = "कोई शिकायत उपलब्ध नहीं";


// MARK: - Add Complaint
"Select Complaint Type" = "शिकायत का प्रकार चुनें";
"Select Transaction" = "लेन-देन का चयन करें";
"Enter Description" = "विवरण दर्ज करें";
"Submit" = "प्रस्तुत करना";

"Complaint Type" = "शिकायत प्रकार";
"Please select complaint type" = "कृपया शिकायत प्रकार चुनें";
"Transaction" = "लेन-देन";
"Please select transaction" = "कृपया लेनदेन का चयन करें";
"Description" = "विवरण";
"Please enter description" = "कृपया विवरण दर्ज करें";


// MARK: - RFID Card
"RFID Card" = "RFID कार्ड";
"Issue New Card" = "नया कार्ड जारी करें";
"Track Order" = "ऑर्डर पर नज़र रखें";


// MARK: - Order RFID
"Order RFID" = "ऑर्डर RFID";
"Full Name" = "पूरा नाम";
"Phone Number" = "फ़ोन नंबर";
"Flat, House No. , Building" = "फ्लैट, मकान नं., भवन";
"Area Street" = "एरिया स्ट्रीट";
"Landmark" = "सीमाचिह्न";
"Pincode" = "पिन कोड";
"State" = "राज्य";
"Town/City" = "शहर";
"RFID Card Charges : " = "RFID कार्ड शुल्क:";

"Order RFID Card" = "RFID कार्ड ऑर्डर करें";
"Please verify your address details." = "कृपया अपना पता विवरण सत्यापित करें।";
"will be charged from your wallet" = "आपके वॉलेट से शुल्क लिया जाएगा";
"Order Now" = "ऑर्डर दें";

"You will receive your RFID Card in 8-10 working days, you will get updates regarding delivery on your registered number." = "आपको 8-10 कार्य दिवसों में अपना RFID कार्ड प्राप्त हो जाएगा, आपको अपने पंजीकृत नंबर पर डिलीवरी के संबंध में अपडेट प्राप्त होंगे।";


"House No." = "मकान नंबर।";
"Please enter flat/house no, buiding" = "कृपया फ्लैट/मकान नंबर दर्ज करें";
"Area, Street" = "क्षेत्र, स्ट्रीट";
"Please enter area, street" = "कृपया क्षेत्र दर्ज करें";
"Please enter landmark" = "कृपया लैंडमार्क दर्ज करें";
"Please enter pincode" = "कृपया पिनकोड दर्ज करें";
"Please enter state" = "कृपया राज्य दर्ज करें";
"Please enter town/city" = "कृपया शहर/शहर दर्ज करें";
"Insufficient balance in you wallet." = "आपके बटुए में अपर्याप्त शेष।";
"Please top-up your wallet." = "कृपया अपने वॉलेट को टॉप-अप करें।";
"Insufficient Balance" = "अपर्याप्त शेष";
"Top-up Now" = "टॉप-अप करें";
"Please verify your address details." = "कृपया अपना पता विवरण सत्यापित करें।";
" will be charged from your wallet." = " आपके वॉलेट से चार्ज किया जाएगा।";
"Order Now" = "आदेश करें";

"Your RFID Order" = "आपका RFID ऑर्डर";
"No Records Found" = "कोई रिकॉर्ड नहीं मिला";


// MARK: - Profile
"User Profile" = "उपयोगकर्ता प्रोफ़ाइल";
"Personal Details" = "व्यक्तिगत विवरण";
"Car Profile" = "कार प्रोफाइल";
"Delete Account" = "खाता हटा दो";
"Are you sure you want to delete your account?" = "क्या आप इस खाते को हटाने के लिए सुनिश्चित हैं?";
"You will lose all your data and your account will be permanently deleted." = "आप अपना सारा डेटा खो देंगे और आपका खाता स्थायी रूप से हटा दिया जाएगा।";
"DELETE" = "हटाएं";
"Refund Your Money" = "अपना पैसा प्राप्त करें";
"Please enter bank details to enter money in your wallet" = "अपने वॉलेट में पैसे डालने के लिए कृपया बैंक विवरण दर्ज करें";
"Your account will be permanently deleted" = "आपका खाता स्थायी रूप से हटा दिया जाएगा";


// MARK: - Charging
"Start Charging" = "चार्ज करना शुरू करें";
"Stop Charging" = "चार्ज करना बंद करें";
"Please connect your vehicle" = "कृपया अपना वाहन कनेक्ट करें";
"I have connected" = "मैंने कनेक्ट किया है";
"Cancel" = "रद्द करें";
"Start Time : " = "समय शुरू :";
"Consumed Units" = "खपत इकाइयाँ";
"Total Charges" = "कुल शुल्क";

// MARK: - Transaction Details
"Tax" = "कर";

"SUCCESSFUL" = "सफल";
"Your transaction was successful" = "आपका लेन-देन सफल रहा";
"Start Time" = "समय शुरू";
"Duration" = "अवधि";
"Charge Point" = "चार्ज प्वाइंट";
"Total Charges" = "कुल शुल्क";
"More Info" = "और जानकारी";

"How was your experience?" = "आपका अनुभव कैसा था?";
"Skip" = "छोड़ें";
"Rate" = "रेटिंग्स";

"Get Directions" = "दिशा - निर्देश प्राप्त करें";
"EV Charging Station" = "ईवी चार्जिंग स्टेशन";
"Charge Station Timings" = "चार्ज स्टेशन का समय";


// MARK: - Logout
"Logout" = "लॉग आउट";
"Logout Message" = "क्या आप लॉग आउट करना चाहते हैं?";
"YES" = "हां";
"NO" = "नहीं";


// MARK: - Login
"Login" = "लॉगइन";
"By logging in, you agree to our Terms and Conditions" = "लॉग इन करके, आप हमारे नियमों और शर्तों से सहमत होते हैं";
"A NXC Controls Pvt. Ltd. Product" = "A NXC Controls Pvt. Ltd. Product";
"Please enter phone number" = "कृपया नंबर डालें";
"Please enter valid phone number" = "कृपया एक वैध नंबर डालें";


// MARK: - Phone Verification
"Phone Verification" = "फोन सत्यापन";
"Enter your 6-digit OTP code" = "अपना 6 अंकों का ओटीपी कोड दर्ज करें";
"VERIFY" = "सत्यापित करें";
"Didn't receive the code? Resend Code" = "कोड प्राप्त नहीं हुआ? पुन: कोड भेजे";






//"Vehicle Regsitration" = "Vehicle Regsitration";
//"Complain" = "Complain";
//"Logout" = "Logout";
//
//"Logout Message" = "Are you sure you want to logout?";
//"YES" = "YES";
//"NO" = "NO";
//
//"Map" = "Map";
//"Charging" = "Charging";
//"Nearby" = "Nearby";
//"Wallet" = "Wallet";
//"Add Money" = "Add Money";
//
//"Login" = "Login";
//"Share" = "Share";
//"By Signing, you agree to our Terms and Conditions" = "By Signing, you agree to our Terms and Conditions";
//
//"Ratings" = "Ratings";
//"START CHARGING" = "START CHARGING";
//"STOP CHARGING" = "STOP CHARGING";
//
//"Enter Amount" = "Enter Amount";
//"Please enter amount to continue with the transaction" = "Please enter amount to continue with the transaction";
//"Payment Mode" = "Payment Mode";
//"Please add amount in multiples of 10" = "Please add amount in multiples of 10";
//"Please select payment mode to continue with the transaction" = "Please select payment mode to continue with the transaction";
//"Proceed To Pay" = "Proceed To Pay";
//
//
//"Wallet history not available" = "Wallet history not available";
//
//
//"Search Charge Station" = "चार्ज स्टेशन खोजें";
//
//"Please wait" = "Please wait";
//
//"Select Vehicle" = "Select Vehicle";
//
//"We can improve the accuracy of your sharing data if we know which vehicle you drive" = "We can improve the accuracy of your sharing data if we know which vehicle you drive";
//
//
//"Brand" = "Brand";
//"Select brand" = "Select Brand";
//"Please select brand" = "Please select brand";
//"Please enter other brand" = "Please enter other brand";
//
//"Type" = "Type";
//"Select type" = "Select type";
//"Please select type" = "Please select type";
//"Please enter other type" = "Please enter other type";
//
//"Model" = "Model";
//"Select model" = "Select model";
//"Please select model" = "Please select model";
//"Please enter other model" = "Please enter other model";
//
//"Enter registration number" = "Enter registration number";
//"Registration number" = "Registration number";
//"Please add registration number" = "Please add registration number";
//
//
//"Please select vehicle to start charging" = "Please select vehicle to start charging";
//
//
//"Please enter phone number" = "Please enter phone number";
//"Please enter valid phone number" = "Please enter valid phone number";
//
//
//"Please enter correct OTP" = "Please enter correct OTP";
//"Please enter valid OTP" = "Please enter valid OTP";
//
//
//"Not Available" = "Not Available";
//"This charger is not available for reservation" = "This charger is not available for reservation";
//
//"Access denied" = "Access denied पहुंच अस्वीकृत";
//
//"Please select gender to proceed" = "Please select gender to proceed";
//
//"First name" = "First name";
//"Please enter firstname" = "Please enter first name";
//
//"Last name" = "Last name";
//"Please enter last name" = "Please enter last name";
//
//"Date of birth" = "Date of birth";
//"Please select date of birth" = "Please select date of birth";
//
//"Gender" = "Gender";
//"Please select gender" = "Please select gender";
//
//"Please enter valid email address" = "Please enter valid email address";
//
//"Address" = "Address";
//"Please enter address" = "Please enter address";
//
//"Pincode" = "Pincode";
//"Please enter pincode" = "Please enter pincode";
//
//"Country" = "Country";
//"Please select country" = "Please select country";
//
//"State" = "State";
//"Please select state" = "Please select state";
//
//"City" = "City";
//"Please select city" = "Please select city";
//
//"Please enter only alphabetical letters" = "Please enter only alphabetical letters";
//
//"Name" = "Name";
//
//"Type" = "Type";
//"Please select complain type" = "Please select complain type";
//"Transaction" = "Transaction";
//"Please select transaction" = "Please select transaction";
//
//
//"Please enter new password" = "Please enter new password";
//"Please select complain type" = "Please select complain type";
//"Transaction" = "Transaction";
//"Please select transaction" = "Please select transaction";
//"Type" = "Type";
//"Please select complain type" = "Please select complain type";
//"Transaction" = "Transaction";
//"Please select transaction" = "Please select transaction";
//"Type" = "Type";
//"Please select complain type" = "Please select complain type";
//"Transaction" = "Transaction";
//"Please select transaction" = "Please select transaction";
//"Type" = "Type";
//"Please select complain type" = "Please select complain type";
//"Transaction" = "Transaction";
//"Please select transaction" = "Please select transaction";
//
//"Vehicle registration" = "Vehicle registration";
//"Vehicle registered successfully" = "Vehicle registered successfully";
//
//"You will get SMS with a confirmation OTP on this number" = "You will get SMS with a confirmation OTP on this number";
//"Search" = "Search";
//
//"Profile added" = "Profile added";
//"Your profile added successfully" = "Your profile added successfully";
//"Profile edited" = "Profile edited";
//"Your profile edited successfully" = "Your profile edited successfully";
//
//"Energy Charges Calculator" = "Energy Charges Calculator";
//"ENERGY CHARGES CALCULATOR" = "ENERGY CHARGES CALCULATOR";
//
//"Select complain type" = "Select complain type";
//"Select transaction" = "Select transaction";
//"Enter description" = "Enter description";
//
//"Resolved" = "Resolved";
//"All" = "All";
//
//"Time & Charges" = "Time & Charges";
//
//"Could not connect to the server" = "Could not connect to the server";
//"Please check your internet connection" = "Please check your internet connection";
//
//
//"First Name"="First Name";
//"Last Name"="Last Name";
//"Date Of Birth"="Date Of Birth";
//"Gender"="Gender";
//"Address"="Address";
//"Pincode"="Pincode";
//"Country"="Country";
//"State"="State";
//"City"="City";
//
//
//

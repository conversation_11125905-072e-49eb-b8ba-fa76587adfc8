# Add this at the top of your Podfile
platform :ios, '11.0' # Use your minimum iOS version

target 'NXC EV Solutions' do
  # Comment the next line if you don't want to use dynamic frameworks
  use_frameworks!

pod 'Alamofire'
pod 'SDWebImage'
pod 'DropDown'
pod 'ACFloatingTextfield-Swift'
pod 'IQKeyboardManagerSwift'
pod 'LinearProgressBar'
pod 'JGProgressHUD'
pod 'Firebase/CoreOnly'
pod 'Firebase/Messaging'
pod 'Firebase/Auth'
pod 'Firebase/Crashlytics'
# Removed Firebase/Analytics
pod 'GoogleMaps'
pod 'GooglePlaces'
pod 'SlideMenuControllerSwift'
pod 'swiftScan', '~> 1.1.2'
pod 'Starscream', '~> 3.0.2'
pod 'CarbonKit'
pod 'AZTabBar'
pod 'Cosmos', '~> 21.0'
pod 'LanguageManager-iOS'

pod 'SnapKit'
pod 'SwiftyJSON'
pod 'Google-Maps-iOS-Utils', '~> 3.10.3'
pod 'SwiftQRScanner'
pod 'MercariQRScanner'


  # Pods for NXC EV Solutions

end

# Add this before the end of your target block
post_install do |installer|
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      config.build_settings['EXCLUDED_ARCHS[sdk=iphonesimulator*]'] = 'arm64'
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '11.0'
    end
  end
end

# Gujarati Language Support

This document explains how Gujarati language support was added to the NXC EV Solutions app and what needs to be done if the project is cleaned or the LanguageManager-iOS library is updated.

## Current Implementation

Gujarati language support was added by:

1. Modifying the `Languages` enum in the LanguageManager-iOS library to include `gu` (Gujarati)
2. Using `Languages.gu` directly in the app code, similar to how Hindi and English are used

## What to Do After Cleaning or Updating the Project

If you clean the project or update the LanguageManager-iOS library, you'll need to re-add Gujarati support to the library:

### 1. Modify the Languages Enum

Open the file:

```
Pods/LanguageManager-iOS/LanguageManager-iOS/Classes/Constants/Languages.swift
```

Add `gu` to the list of languages in the enum:

```swift
public enum Languages: String {
  case ar, en, nl, ja, ko, vi, ru, sv, fr, es, pt, it, de, da, fi, nb, tr, el, id,
  ms, th, hi, hu, pl, cs, sk, uk, hr, ca, ro, he, ur, fa, ku, arc, sl, ml, am, zh, mn, ka, sw, gu
  // ... rest of the enum
}
```

### 2. Rebuild the Project

After making this change, rebuild the project to ensure the changes take effect.

## Alternative Approaches

If you prefer not to modify the library directly, you could:

1. Fork the LanguageManager-iOS library, add Gujarati support, and use your fork
2. Create a custom extension to handle Gujarati language (see the previous implementation in git history)
3. Replace LanguageManager-iOS with a different localization library that supports Gujarati

## Files That Use Gujarati Language Support

The following files have been updated to use `Languages.gu`:

1. `AppDelegate.swift`
2. `LanguageVC.swift`
3. `SplashVC.swift`

## Language Persistence

The app persists the selected language using UserDefaults with the key `Constants.LANGUAGE`:

- "1" for English
- "2" for Gujarati
- "3" for Hindi

The language is restored in two places:

1. In `application(_:didFinishLaunchingWithOptions:)` when the app first launches
2. In `applicationDidBecomeActive(_:)` when the app becomes active after being in the background

This ensures the language preference is maintained even when the app is closed and reopened.

### Important Note About LanguageManager-iOS

The LanguageManager-iOS library has a special behavior in its `defaultLanguage` setter. When the app starts, it checks if a default language has been set before, and if it has, it calls `setLanguage(language: currentLanguage)` which resets the language to the current language.

To work around this behavior, we:

1. Only set `defaultLanguage` the first time the app runs
2. Use `setLanguage(language:)` instead of setting `defaultLanguage` for subsequent launches
3. Check for the existence of "LanguageManager_defaultLanguage" in UserDefaults to determine if this is the first launch
4. Directly update the LanguageManager's internal UserDefaults keys

### UserDefaults Keys

The app uses the following UserDefaults keys for language management:

1. `Constants.LANGUAGE` - Our app's language setting ("1" for English, "2" for Gujarati, "3" for Hindi)
2. `"LanguageManagerSelectedLanguage"` - LanguageManager-iOS library's internal key for the selected language
3. `"LanguageManagerDefaultLanguage"` - LanguageManager-iOS library's internal key for the default language

This approach ensures that the language preference is maintained across app restarts.

## Troubleshooting

If Gujarati language is not working after an update or if language persistence issues occur:

1. Check that `gu` is in the `Languages` enum
2. Verify that the app is using `Languages.gu` and not trying to create a custom instance
3. Ensure that the Gujarati localization files (in `gu.lproj`) are included in the app bundle
4. Check that `Bundle.setLanguage("gu")` is being called when Gujarati is selected
5. Verify that both `Constants.LANGUAGE` and `"LanguageManagerSelectedLanguage"` UserDefaults keys are being set
6. Check the debug logs for language-related messages (look for 🌐 emoji)
7. Make sure the app is not setting `LanguageManager.shared.defaultLanguage` after the first launch

## Contact

If you have any questions or issues with the Gujarati language support, please contact the development team.

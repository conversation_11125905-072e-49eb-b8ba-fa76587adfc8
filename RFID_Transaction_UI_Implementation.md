# RFID Transaction Details UI Implementation

## Overview

This document outlines the implementation of RFID transaction details UI in the TransactionDetailsVC. The implementation allows the app to display different UI elements based on the transaction type.

## Transaction Types

- **Type "1"**: Charging transactions (Paid for charging)
- **Type "2"**: Wallet transactions (Money added to wallet)
- **Type "3"**: RFID transactions (Purchased RFID Card) - **NEW**

## API Response Structure for RFID Transactions

```json
{
  "order_date": "2025-05-09",
  "time": "12:52:09",
  "status": "pending|payment confirmed|accepted|dispatch|delivered",
  "RFID TAG": "unique_rfid_id_string",
  "amount": "500.00",
  "delivered_date": "2025-05-09", // can be null
  "transaction_id": "transaction_id_string"
}
```

## Code Changes Made

### 1. TransactionModel.swift

Added new `RFIDTransactionData` struct to handle RFID-specific data:

```swift
struct RFIDTransactionData {
    var order_date: String
    var time: String
    var status: String
    var rfid_tag: String
    var amount: String
    var delivered_date: String
    var transaction_id: String
}
```

### 2. TransactionDetailsVC.swift

#### Added RFID-specific outlets:

```swift
@IBOutlet weak var viewRFIDDetails: UIView!
@IBOutlet weak var viewRFIDDetailsTop: UIView!
@IBOutlet weak var lblRFIDTag: UILabel!
@IBOutlet weak var lblRFIDStatus: UILabel!
@IBOutlet weak var lblRFIDAmount: UILabel!
@IBOutlet weak var lblOrderDate: UILabel!
@IBOutlet weak var lblOrderTime: UILabel!
@IBOutlet weak var lblDeliveredDate: UILabel!
```

#### Added transaction type handling:

- `handleChargingTransaction()` - for types "1" and "2"
- `handleRFIDTransaction()` - for type "3"
- `showChargingViews()` - shows/hides charging-related UI
- `showRFIDViews()` - shows/hides RFID-related UI

## Programmatic UI Implementation ✅ COMPLETED

### RFID Details View Structure (Programmatically Created)

The RFID UI is now created programmatically in the `createRFIDUI()` method:

```
viewRFIDDetails (UIView) - Programmatically created
├── viewRFIDDetailsTop (UIView) - Header with primary color background
│   └── "RFID Order Details" (UILabel) - Header title
└── Content View (UIView)
    ├── RFID Tag Row (UIStackView)
    │   ├── "RFID Tag:" (UILabel)
    │   └── lblRFIDTag (UILabel)
    ├── Status Row (UIStackView)
    │   ├── "Status:" (UILabel)
    │   └── lblRFIDStatus (UILabel)
    ├── Amount Row (UIStackView)
    │   ├── "Amount:" (UILabel)
    │   └── lblRFIDAmount (UILabel)
    ├── Order Date Row (UIStackView)
    │   ├── "Order Date:" (UILabel)
    │   └── lblOrderDate (UILabel)
    ├── Order Time Row (UIStackView)
    │   ├── "Order Time:" (UILabel)
    │   └── lblOrderTime (UILabel)
    └── Delivery Status Row (UIStackView)
        ├── "Delivery Status:" (UILabel)
        └── lblDeliveredDate (UILabel)
```

### Programmatic Implementation Features

- **Automatic Layout**: Uses Auto Layout constraints matching existing design
- **Consistent Styling**: Matches existing transaction detail views (shadows, corners, colors)
- **Responsive Design**: Adapts to different screen sizes
- **Proper Positioning**: Positioned between transaction details and charging views
- **Dynamic Creation**: Created in `viewDidLoad()` and styled in `viewDidLayoutSubviews()`

## Status Display Logic

- **Status values**: "pending", "payment confirmed", "accepted", "dispatch", "delivered"
- **Delivered Date**: Shows "Pending Delivery" if null or empty, otherwise shows formatted date

## UI Behavior for RFID Transactions

- **Top Transaction Section**: Date and time labels are hidden for RFID transactions since RFID has its own order date/time
- **RFID Section**: Shows dedicated order date and time fields with proper formatting
- **View Switching**: Date/time labels are automatically shown/hidden based on transaction type

## Navigation Updates

Updated navigation logic in:

- **HistoryVC.swift**: Sets `strTypeID` from wallet data type
- **WalletVC.swift**: Sets `strTypeID` from wallet data type
- **ChargingVC.swift**: Sets `strTypeID` to "1" for charging transactions

## Testing

To test the RFID transaction UI:

1. ✅ **UI Creation**: RFID UI is automatically created programmatically in `viewDidLoad()`
2. Set `AppDelegate.shared.strTypeID = "3"` before navigating to TransactionDetailsVC
3. Verify the API returns RFID-specific data structure
4. Check that charging views are hidden and RFID views are shown
5. Test with sample RFID data to verify proper formatting and display

### Sample Test Data

```swift
// For testing, you can manually set RFID data:
let testData: [String: Any] = [
    "order_date": "2025-01-15",
    "time": "14:30:00",
    "status": "pending",
    "RFID TAG": "RFID123456789",
    "amount": "500.00",
    "delivered_date": "", // or null for "Pending Delivery"
    "transaction_id": "TXN123456"
]
```

## Implementation Methods Added

- `createRFIDUI()` - Creates all RFID UI elements programmatically
- `createRFIDLabelPairs(in:)` - Creates the 6 label pairs with proper styling
- `setupRFIDConstraints(contentView:headerLabel:)` - Sets up Auto Layout constraints

## Notes

- ✅ **Fully Implemented**: No storyboard changes required
- ✅ **Backward Compatible**: Existing charging/wallet transactions unchanged
- ✅ **Responsive Design**: Adapts to different screen sizes
- ✅ **Consistent Styling**: Matches existing design patterns
- RFID views are only shown when transaction type is "3"
- All existing functionality for charging transactions remains unchanged
